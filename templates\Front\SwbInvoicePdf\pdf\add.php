<!-- templates/Invoices/pdf/invoice.php -->
<!DOCTYPE html>
<html lang="ja">

<head>
    <meta charset="UTF-8">
    <title>請求書</title>
    <style>
        body {
            font-family: 'NotoSansJP', sans-serif;
            font-size: 14px;
            /* フォントサイズを小さく */
            line-height: 1;
            /* 行間を狭く */
        }

        .font-h2 {
            font-size: 1.7em;
            /* 大きなフォントサイズ */
        }

        .font-h3 {
            font-size: 1.5em;
        }

        .font-h4 {
            font-size: 1.1em;
        }

        .font-small {
            font-size: 0.9em;
            /* 小さなフォントサイズ */
        }

        table {
            width: 100%;
            border-collapse: collapse;
        }

        th,
        td {
            border: 1px solid #000;
            padding: 3px;
            /* セルのpaddingを小さく */
        }

        /* p { */
            /* word-wrap: break-word; 長い単語やURLを強制的に折り返す */
            /* overflow-wrap: break-word; word-wrap のエイリアス (推奨) */
            /* white-space: normal; 通常の折り返しを有効にする (念のため) */
        /* } */

        .no-border>td {
            border: 0px;
        }

        .right {
            text-align: right;
        }

        .center {
            text-align: center;
        }

        .bold {
            font-weight: bolder;
        }

        .logo {
            width: 150px;
        }

        .company-info {
            position: relative;
            /* 相対位置指定 */
            float: right;
            font-size: 0.9em;
            text-align: left;
            /* テキストを左寄せに */
            padding-right: 20px;
            /* 印鑑スペース確保のため右Padding追加 */
        }

        .seal {
            position: absolute;
            /* 絶対位置指定 */
            top: 100px;
            /* 上からの位置 */
            right: -10px;
            /* 右からの位置 (調整が必要な場合あり) */
            width: 100px;
            /* 印鑑画像の幅 (調整が必要な場合あり) */
            opacity: 1;
            /* 印鑑を少し薄く (必要に応じて調整) */
            z-index: 10;
            /* 他の要素より前面に表示 */
        }

        .message-area {
            position: relative;
            /* 相対配置：挨拶文の基準位置 */
            margin-top: 5px;
            /* 調整用：必要に応じて変更 */
            height: 70px;
            /* 調整用：必要に応じて適切な高さを設定。挨拶文が収まる程度 */
        }

        .message-text {
            position: absolute;
            /* 絶対配置：位置を固定 */
            top: 0;
            /* 上からの位置を調整 */
            left: 0;
            /* 左からの位置 (必要に応じて調整) */
            font-size: 0.9em;
            /* font-small クラスのスタイルを適用 */
            width: 100%;
            /* 必要に応じて幅を調整 */
        }
    </style>
</head>

<body>

    <h2 class="font-h2" style="margin-top: -10px;">請求書</h2> <!-- h2のフォントサイズも調整 -->
    <div class="font-small" style="float: right;"> <!-- 右側の情報もフォントサイズを調整 -->
        請求書番号: <?= h($invoice['customer_code'] ?? '') ?><br>
        <?= h($invoice['last_day_of_month'] ?? '') ?> 締切分
    </div><br><br><br>

    <div class="company-info"> <!-- 右側の会社情報もフォントサイズを調整 -->
        <img src="data:image/png;base64,<?= $invoice['logo_base64'] ?>" alt="ロゴ" class="logo"><br>
        ソーウェルバー株式会社<br>
        登録番号：T2010701042916 <br>
        〒220-0011<br>
        神奈川県横浜市西区高島1-2-5 <br>
        ビジネスエアポート横浜 <br>
        TEL:************ <br>
        担当者：<?= h($invoice['contact_name'] ?? '') ?>
        <img src="data:image/png;base64,<?= $invoice['company_seal_base64'] ?>" alt="ロゴ" class="seal">
    </div>
    <p class="font-small"> <!-- 左側の顧客情報もフォントサイズを調整 -->
        <?= nl2br(h($invoice['billing_address'] ?? '')) ?>
    </p>

    <br>

    <div class="message-area"> <!-- message-area で囲む -->
        <p class="message-text"> <!-- message-text クラスを適用 -->
            毎度格別のお引き立てを賜り、厚く御礼申し上げます。<br>
            下記の通りご請求申し上げますので、よろしくお願い申し上げます。
        </p>
    </div>

    <h4 class="right font-h4" style="margin: 0.5em 0;">御請求項目：カバーミー広告出稿費用</h4> <!-- h4のフォントサイズも調整 -->
    <h3 class="right font-h3" style="margin: 1em 0;"><span style="border: 2px solid #000; padding: 0.5em;">御請求額：¥<?= number_format($invoice['total_price'] + floor($invoice['total_price'] * $invoice['tax_rate']) ?? 0) ?></span></h3> <!-- h3のフォントサイズも調整 -->


    <table>
        <thead>
            <tr>
                <th>項目</th>
                <th>数量</th>
                <th>金額</th>
            </tr>
        </thead>
        <tbody>
            <?php foreach ($invoice['invoices_by_product'] as $invoiceByProduct) { ?>
                <tr>
                    <td>広告名：<?= h($invoiceByProduct['product_name'] ?? '') ?></td>
                    <td></td>
                    <td></td>
                </tr>
                <tr>
                    <td>■月額費用</td>
                    <td class="right">0</td>
                    <td class="right">¥0</td>
                </tr>
                <tr>
                    <td>■掲載費用 (グロス)</td>
                    <td class="right"><?= number_format(h($invoiceByProduct['total_quantity']) ?? '') ?></td>
                    <td class="right">¥<?= number_format($invoiceByProduct['total_amount'] ?? 0) ?></td>
                </tr>
                <?php if ($invoiceByProduct['adjustment_amount'] !== 0) { ?>
                    <tr>
                        <td>■調整金額</td>
                        <td class="right"><?= number_format(h($invoiceByProduct['adjustment_quantity']) ?? '') ?></td>
                        <td class="right">¥<?= number_format($invoiceByProduct['adjustment_amount'] ?? 0) ?></td>
                    </tr>
                <?php } ?>
            <?php } ?>
            <tr class="no-border">
                <td></td>
                <td class="bold">小計:</td>
                <td class="right">¥<?= number_format($invoice['total_price'] ?? 0) ?></td>
            </tr>
            <tr class="no-border">
                <td></td>
                <td class="bold">合計:</td>
                <td class="right">¥<?= number_format($invoice['total_price'] ?? 0) ?></td>
            </tr>
            <tr class="no-border">
                <td></td>
                <td class="bold">消費税(<?= h($invoice['tax_rate'] * 100) ?>%):</td>
                <td class="right">¥<?= number_format(floor($invoice['total_price'] * $invoice['tax_rate']) ?? 0) ?></td>
            </tr>
            <tr class="no-border">
                <td></td>
                <td class="bold">御請求額:</td>
                <td class="right">¥<?= number_format($invoice['total_price'] + floor($invoice['total_price'] * $invoice['tax_rate']) ?? 0) ?></td>
            </tr>
        </tbody>
    </table>


    <p class="font-small"><b>お支払い期日：<?= h($invoice['billing_date'] ?? '') ?></b></p> <!-- お支払い期日もフォントサイズを調整 -->
    <p class="font-small"> <!-- 銀行情報もフォントサイズを調整 -->
        お振込先銀行：三菱UFJ銀行<br>
        銀行支店名：本店<br>
        口座種別：普通<br>
        口座番号：2547282<br>
        口座名義：ソーウェルバー（カ
    </p>

    <p class="font-small">※お振込手数料はお客様ご負担にてお願い致します。</p> <!-- 注意書きもフォントサイズを調整 -->

    <p class="font-small">備考</p> <!-- 備考欄のタイトルもフォントサイズを調整 -->
    <hr>
    <p class="font-small" style="word-wrap: break-word; overflow-wrap: break-word; white-space: normal; ">
        <?php foreach ($invoice['invoices_by_product'] as $invoiceByProduct) { ?>
            <?php if (!empty($invoiceByProduct['adjustment_note'])) { ?>
                <?= nl2br(h($invoiceByProduct['adjustment_note'])) ?>
            <?php } ?>
        <?php } ?>

    </p>
</body>

</html>