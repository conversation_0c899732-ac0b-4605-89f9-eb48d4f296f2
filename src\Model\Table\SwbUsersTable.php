<?php
declare(strict_types=1);

namespace App\Model\Table;

use Cake\ORM\Table;
use Cake\Validation\Validator;

/**
 * SWB管理者ユーザーテーブル
 * 
 * @property \App\Model\Table\SwbUserTokensTable&\Cake\ORM\Association\HasMany $SwbUserTokens
 * @method \App\Model\Entity\SwbUser newEmptyEntity()
 * @method \App\Model\Entity\SwbUser newEntity(array $data, array $options = [])
 * @method \App\Model\Entity\SwbUser[] newEntities(array $data, array $options = [])
 * @method \App\Model\Entity\SwbUser get($primaryKey, $options = [])
 * @method \App\Model\Entity\SwbUser findOrCreate($search, ?callable $callback = null, $options = [])
 * @method \App\Model\Entity\SwbUser patchEntity(\Cake\Datasource\EntityInterface $entity, array $data, array $options = [])
 * @method \App\Model\Entity\SwbUser[] patchEntities(iterable $entities, array $data, array $options = [])
 * @method \App\Model\Entity\SwbUser|false save(\Cake\Datasource\EntityInterface $entity, $options = [])
 * @method \App\Model\Entity\SwbUser saveOrFail(\Cake\Datasource\EntityInterface $entity, $options = [])
 * @method \App\Model\Entity\SwbUser[]|\Cake\Datasource\ResultSetInterface|false saveMany(iterable $entities, $options = [])
 * @method \App\Model\Entity\SwbUser[]|\Cake\Datasource\ResultSetInterface saveManyOrFail(iterable $entities, $options = [])
 * @method \App\Model\Entity\SwbUser[]|\Cake\Datasource\ResultSetInterface|false deleteMany(iterable $entities, $options = [])
 * @method \App\Model\Entity\SwbUser[]|\Cake\Datasource\ResultSetInterface deleteManyOrFail(iterable $entities, $options = [])
 */
class SwbUsersTable extends Table
{
    public function initialize(array $config): void
    {
        parent::initialize($config);

        $this->setTable('swb_users');
        $this->setDisplayField('email');
        $this->setPrimaryKey('id');

        $this->hasMany('SwbUserTokens', [
            'foreignKey' => 'swb_user_id',
            'dependent' => true,
        ]);

        // TrashBehaviorを使用
        $this->addBehavior('Muffin/Trash.Trash', [
            'field' => 'deleted',
            'events' => ['Model.beforeFind']
        ]);
    }

    public function validationDefault(Validator $validator): Validator
    {
        $validator
            ->integer('id')
            ->allowEmptyString('id', null, 'create');

        $validator
            ->integer('authority_id')
            ->requirePresence('authority_id', 'create')
            ->notEmptyString('authority_id')
            ->inList('authority_id', [100, 101], 'Invalid authority level');

        $validator
            ->email('email')
            ->requirePresence('email', 'create')
            ->notEmptyString('email')
            ->add('email', 'unique', ['rule' => 'validateUnique', 'provider' => 'table']);

        $validator
            ->scalar('password')
            ->maxLength('password', 255)
            ->allowEmptyString('password');

        return $validator;
    }

    /**
     * メールアドレスでユーザーを検索
     */
    public function findByEmail(string $email)
    {
        return $this->find()
            ->where(['email' => $email, 'deleted IS' => null])
            ->first();
    }

    /**
     * 権限レベルでユーザーを検索
     */
    public function findByAuthority(int $authorityId)
    {
        return $this->find()
            ->where(['authority_id' => $authorityId, 'deleted IS' => null]);
    }
}

