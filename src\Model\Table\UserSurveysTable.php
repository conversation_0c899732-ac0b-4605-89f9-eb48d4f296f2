<?php
declare(strict_types=1);

namespace App\Model\Table;

use Cake\ORM\Query;
use Cake\ORM\RulesChecker;
use Cake\ORM\Table;
use Cake\Validation\Validator;
use App\Model\Entity\UserSurvey;

/**
 * UserSurveys Table
 *
 * @property \App\Model\Table\GeneralUsersTable&\Cake\ORM\Association\BelongsTo $GeneralUsers
 * @method \App\Model\Entity\UserSurvey newEmptyEntity()
 * @method \App\Model\Entity\UserSurvey newEntity(array $data, array $options = [])
 * @method \App\Model\Entity\UserSurvey[] newEntities(array $data, array $options = [])
 * @method \App\Model\Entity\UserSurvey get($primaryKey, $options = [])
 * @method \App\Model\Entity\UserSurvey findOrCreate($search, ?callable $callback = null, $options = [])
 * @method \App\Model\Entity\UserSurvey patchEntity(\Cake\Datasource\EntityInterface $entity, array $data, array $options = [])
 * @method \App\Model\Entity\UserSurvey[] patchEntities(iterable $entities, array $data, array $options = [])
 * @method \App\Model\Entity\UserSurvey|false save(\Cake\Datasource\EntityInterface $entity, $options = [])
 * @method \App\Model\Entity\UserSurvey saveOrFail(\Cake\Datasource\EntityInterface $entity, $options = [])
 */
class UserSurveysTable extends Table
{
    /**
     * Initialize method
     *
     * @param array $config The configuration for the Table.
     * @return void
     */
    public function initialize(array $config): void
    {
        parent::initialize($config);

        $this->setTable('user_surveys');
        $this->setDisplayField('id');
        $this->setPrimaryKey('id');

        $this->belongsTo('GeneralUsers', [
            'foreignKey' => 'general_user_id',
            'joinType' => 'INNER',
        ]);
    }

    /**
     * Default validation rules.
     *
     * @param \Cake\Validation\Validator $validator Validator instance.
     * @return \Cake\Validation\Validator
     */
    public function validationDefault(Validator $validator): Validator
    {
        $validator
            ->integer('general_user_id')
            ->notEmptyString('general_user_id');

        $validator
            ->integer('year')
            ->requirePresence('year', 'create')
            ->notEmptyString('year');

        $validator
            ->integer('child_sex')
            ->allowEmptyString('child_sex');

        $validator
            ->integer('budget')
            ->allowEmptyString('budget');

        $validator
            ->boolean('question_1_1')
            ->allowEmptyString('question_1_1');

        $validator
            ->boolean('question_1_2')
            ->allowEmptyString('question_1_2');

        $validator
            ->boolean('question_1_3')
            ->allowEmptyString('question_1_3');

        $validator
            ->boolean('question_1_4')
            ->allowEmptyString('question_1_4');

        $validator
            ->boolean('question_2_1')
            ->allowEmptyString('question_2_1');

        $validator
            ->boolean('question_2_2')
            ->allowEmptyString('question_2_2');

        $validator
            ->boolean('question_2_3')
            ->allowEmptyString('question_2_3');

        $validator
            ->boolean('question_2_4')
            ->allowEmptyString('question_2_4');

        $validator
            ->boolean('question_2_5')
            ->allowEmptyString('question_2_5');

        return $validator;
    }

    /**
     * Returns a rules checker object that will be used for validating
     * application integrity.
     *
     * @param \Cake\ORM\RulesChecker $rules The rules object to be modified.
     * @return \Cake\ORM\RulesChecker
     */
    public function buildRules(RulesChecker $rules): RulesChecker
    {
        $rules->add($rules->existsIn('general_user_id', 'GeneralUsers'), ['errorField' => 'general_user_id']);

        return $rules;
    }
}