<?php
declare(strict_types=1);

namespace App\Model\Entity;

use Cake\ORM\Entity;

/**
 * ライブ配信メーカーエンティティ
 * 
 * @property int $id
 * @property int $live_stream_id
 * @property int $maker_id
 * @property \Cake\I18n\FrozenTime $created
 * @property \Cake\I18n\FrozenTime $modified
 * @property \App\Model\Entity\LiveStream $live_stream
 * @property \App\Model\Entity\Maker $maker
 */
class LiveStreamMaker extends Entity
{
    protected $_accessible = [
        'live_stream_id' => true,
        'maker_id' => true,
        'created' => true,
        'modified' => true,
        'live_stream' => true,
        'maker' => true,
    ];

    protected $_hidden = [];
}
