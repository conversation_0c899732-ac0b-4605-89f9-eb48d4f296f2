import axios from "axios";
import { TOKEN_HEADER_KEY } from "~~/src/config";
import type { RuntimeConfig } from "nuxt/schema";

/**
 * @deprecated
 */
export default abstract class KurokoFrontStaticApiClient {
    protected config: RuntimeConfig;

    constructor(protected runTimeConfig: RuntimeConfig) {
        this.config = runTimeConfig;
    }

    static create<T extends KurokoFrontStaticApiClient>(
        this: new (config: RuntimeConfig) => T,
        runTimeConfig: RuntimeConfig,
    ): T {
        return new this(runTimeConfig);
    }

    protected abstract getResource(): string;

    private getHeader(): ObjType {
        return {
            [TOKEN_HEADER_KEY]: String(this.config.public.staticApiKey),
        };
    }

    public post<T, R>(data?: T): Promise<R> {
        return new Promise((resolve, reject) => {
            axios
                .post(
                    `${this.config.public.apiBase}${this.getResource()}`,
                    data,
                    {
                        headers: this.getHeader(),
                        withCredentials: true, // 認証情報を含めるために true に設定
                        // httpsAgent,
                    },
                )
                .then((response) => {
                    resolve(response.data);
                })
                .catch(reject);
        });
    }
}
