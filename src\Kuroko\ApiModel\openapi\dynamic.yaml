openapi: 3.1.0
info:
  title: dynamic
  version: '1.0'
  description: 動的
servers:
  -
    url: 'https://swb-sbpg-dev.g.kuroco.app'
    description: 'API Backend'
paths:
  /rcms-api/5/login:
    post:
      tags:
        - 認証
      summary: ''
      description: |
        
        ### **Login::login_challenge (v1)**
        
        
      parameters:
        -
          name: _output_format
          schema:
            type: string
            format: ''
          in: query
          required: false
          style: form
          explode: true
          description: '形式 (json|xml|csv|zip)'
        -
          name: _lang
          schema:
            type: string
            format: ''
          in: query
          required: false
          style: form
          explode: true
          description: 言語
        -
          name: _charset
          schema:
            type: string
            format: ''
          in: query
          required: false
          style: form
          explode: true
          description: 文字コード
      responses:
        200:
          description: 'Login OK'
      operationId: postRcmsApi5Login
      requestBody:
        required: true
        description: ''
        content:
          application/json:
            schema:
              type: object
              properties:
                email:
                  type: string
                  default: ''
                  example: ''
                  description: ログインID
                password:
                  type: string
                  writeOnly: true
                  default: ''
                  example: ''
                  description: ログインパスワード
                login_save:
                  type: integer
                  format: int32
                  minimum: -2147483648
                  maximum: 2147483647
                  enum:
                    - 0
                    - 1
                  default: 0
                  example: 0
                  description: 次回から自動的にログインする
  /rcms-api/5/login/token:
    post:
      tags:
        - 認証
      summary: ''
      description: |
        
        ### **Login::token (v1)**
        
        
        ## Controller parameters
        
        > **access_token_lifespan** `3600`
        
      parameters:
        -
          name: _output_format
          schema:
            type: string
            format: ''
          in: query
          required: false
          style: form
          explode: true
          description: '形式 (json|xml|csv|zip)'
        -
          name: _lang
          schema:
            type: string
            format: ''
          in: query
          required: false
          style: form
          explode: true
          description: 言語
        -
          name: _charset
          schema:
            type: string
            format: ''
          in: query
          required: false
          style: form
          explode: true
          description: 文字コード
      responses:
        200:
          description: 'Token successfully generated'
      operationId: postRcmsApi5LoginToken
      requestBody:
        required: true
        description: ''
        content:
          application/json:
            schema:
              type: object
              properties:
                grant_token:
                  type: string
                  default: ''
                  example: ''
                  description: リソース付与トークン
  /rcms-api/5/login/logout:
    post:
      tags:
        - 認証
      summary: ログアウト
      description: |
        
        ### **Login::logout (v1)**
        
        
      parameters:
        -
          name: _output_format
          schema:
            type: string
            format: ''
          in: query
          required: false
          style: form
          explode: true
          description: '形式 (json|xml|csv|zip)'
        -
          name: _lang
          schema:
            type: string
            format: ''
          in: query
          required: false
          style: form
          explode: true
          description: 言語
        -
          name: _charset
          schema:
            type: string
            format: ''
          in: query
          required: false
          style: form
          explode: true
          description: 文字コード
      responses:
        200:
          description: 'Logout OK'
      security:
        -
          Token-Auth: []
      operationId: postRcmsApi5LoginLogout
  /rcms-api/5/login/reminder:
    post:
      tags:
        - 認証
      summary: '[カスタム]一時トークン・パスワード発行'
      description: |
        
        ### **Login::reminder (v1)**
        
        
        ## Controller parameters
        
        > **token_fragment_flg** ``
        
      parameters:
        -
          name: _output_format
          schema:
            type: string
            format: ''
          in: query
          required: false
          style: form
          explode: true
          description: '形式 (json|xml|csv|zip)'
        -
          name: _lang
          schema:
            type: string
            format: ''
          in: query
          required: false
          style: form
          explode: true
          description: 言語
        -
          name: _charset
          schema:
            type: string
            format: ''
          in: query
          required: false
          style: form
          explode: true
          description: 文字コード
      responses:
        200:
          description: 'Email sent'
      operationId: postRcmsApi5LoginReminder
      requestBody:
        required: true
        description: ''
        content:
          application/json:
            schema:
              oneOf:
                -
                  type: object
                  properties:
                    email:
                      type: string
                      default: ''
                      example: ''
                      description: メールアドレス
                  required:
                    - email
                  additionalProperties: false
                -
                  type: object
                  properties:
                    token:
                      type: string
                      default: ''
                      example: ''
                      description: トークン
                    temp_pwd:
                      type: string
                      format: password
                      default: ''
                      example: ''
                      description: 仮パスワード
                    login_pwd:
                      type: string
                      format: password
                      default: ''
                      example: ''
                      description: 新しいパスワード
                  required:
                    - token
                    - temp_pwd
                    - login_pwd
                  additionalProperties: false
            examples:
              Send_Email:
                value:
                  email: my@email
              Update_Password:
                value:
                  token: '<my_password_reset_token>'
                  temp_pwd: '<my_temporary_password>'
                  login_pwd: '<my_new_password>'
  /rcms-api/5/login/reset-password:
    post:
      tags:
        - 認証
      summary: ''
      description: |
        
        ### **Login::reset_password (v1)**
        
        
      parameters:
        -
          name: _output_format
          schema:
            type: string
            format: ''
          in: query
          required: false
          style: form
          explode: true
          description: '形式 (json|xml|csv|zip)'
        -
          name: _lang
          schema:
            type: string
            format: ''
          in: query
          required: false
          style: form
          explode: true
          description: 言語
        -
          name: _charset
          schema:
            type: string
            format: ''
          in: query
          required: false
          style: form
          explode: true
          description: 文字コード
      responses:
        200:
          description: 'Password updated'
        401:
          description: 'ID/Old password mismatch'
        422:
          description: 'New password is invalid'
      security:
        -
          Token-Auth: []
      operationId: postRcmsApi5LoginResetPassword
      requestBody:
        required: true
        description: ''
        content:
          application/json:
            schema:
              type: object
              properties:
                login_id:
                  type: string
                  default: ''
                  example: ''
                  description: トークン
                current_password:
                  type: string
                  format: password
                  default: ''
                  example: ''
                  description: 現在のパスワード
                new_password:
                  type: string
                  format: password
                  default: ''
                  example: ''
                  description: 新しいパスワード
              required:
                - login_id
                - current_password
                - new_password
              additionalProperties: false
  /rcms-api/5/member/regist:
    post:
      tags:
        - メンバー
      summary: ''
      description: |
        
        ### **Member::insert (v1)**
        
        
        ## Controller parameters
        
        > **default_group_id** `104`
        
        > **ignore_force_chpwd** `true`
        
      parameters:
        -
          name: _output_format
          schema:
            type: string
            format: ''
          in: query
          required: false
          style: form
          explode: true
          description: '形式 (json|xml|csv|zip)'
        -
          name: _lang
          schema:
            type: string
            format: ''
          in: query
          required: false
          style: form
          explode: true
          description: 言語
        -
          name: _charset
          schema:
            type: string
            format: ''
          in: query
          required: false
          style: form
          explode: true
          description: 文字コード
      responses:
        201:
          description: 'Member is created'
      security:
        -
          Token-Auth: []
      operationId: postRcmsApi5MemberRegist
      requestBody:
        required: true
        description: ''
        content:
          application/json:
            schema:
              type: object
              properties:
                name1:
                  type: string
                  format: ''
                  description: 姓
                  example: ''
                name2:
                  type: string
                  format: ''
                  description: 名
                  example: ''
                nickname:
                  type: string
                  format: ''
                  description: ニックネーム
                  example: ''
                email:
                  type: string
                  format: ''
                  description: メールアドレス
                  example: <EMAIL>
                  pattern: '^([a-zA-Z0-9\-_\.]+([a-zA-Z0-9\-_\.\/\+]+)?@[a-zA-Z0-9\-_\.]+\.[a-zA-Z0-9\-_\.]+)?$'
                zip_code:
                  type: string
                  format: ''
                  description: 郵便番号
                  example: '1234567'
                  pattern: '^([A-Za-z0-9-]+)?$'
                tdfk_cd:
                  type: string
                  format: ''
                  description: 都道府県
                  example: '13'
                  enum:
                    - ''
                    - '01'
                    - '02'
                    - '03'
                    - '04'
                    - '05'
                    - '06'
                    - '07'
                    - '08'
                    - '09'
                    - '10'
                    - '11'
                    - '12'
                    - '13'
                    - '14'
                    - '15'
                    - '16'
                    - '17'
                    - '18'
                    - '19'
                    - '20'
                    - '21'
                    - '22'
                    - '23'
                    - '24'
                    - '25'
                    - '26'
                    - '27'
                    - '28'
                    - '29'
                    - '30'
                    - '31'
                    - '32'
                    - '33'
                    - '34'
                    - '35'
                    - '36'
                    - '37'
                    - '38'
                    - '39'
                    - '40'
                    - '41'
                    - '42'
                    - '43'
                    - '44'
                    - '45'
                    - '46'
                    - '47'
                    - '99'
                address1:
                  type: string
                  format: ''
                  description: 住所1
                  example: ''
                address2:
                  type: string
                  format: ''
                  description: 住所2
                  example: ''
                address3:
                  type: string
                  format: ''
                  description: 住所3
                  example: ''
                tel:
                  type: string
                  format: ''
                  description: 電話番号
                  example: 0123-4444-5555
                  pattern: '^([\+\d][\(\)0-9+-]{5,14}\d)?$'
                email_send_ng_flg:
                  anyOf:
                    -
                      type: boolean
                      format: ''
                      description: email_send_ng_flg
                    -
                      type: integer
                      format: int32
                      minimum: -2147483648
                      maximum: 2147483647
                      description: email_send_ng_flg
                      enum:
                        - 0
                        - 1
                tel_send_ng_flg:
                  anyOf:
                    -
                      type: boolean
                      format: ''
                      description: tel_send_ng_flg
                    -
                      type: integer
                      format: int32
                      minimum: -2147483648
                      maximum: 2147483647
                      description: tel_send_ng_flg
                      enum:
                        - 0
                        - 1
                login_pwd:
                  type: string
                  format: password
                  description: login_pwd
                login_id:
                  type: string
                  format: ''
                  description: ログインID
                  example: ''
                login_ok_flg:
                  type: integer
                  format: int32
                  minimum: -2147483648
                  maximum: 2147483647
                  description: /label/login_ok_flg
                  enum:
                    - 0
                    - 1
                  example: 1
                  default: 1
                login_ok_ymd:
                  anyOf:
                    -
                      type: string
                      format: date
                    -
                      type: string
                      maxLength: 0
                  description: ログイン許可期限
                hire_date:
                  description: 'Hire date'
                  anyOf:
                    -
                      anyOf:
                        -
                          type: string
                          format: date
                        -
                          type: string
                          maxLength: 0
                    -
                      type: string
                      pattern: ^$
                  example: '2024-10-02'
                department:
                  type: string
                  format: ''
                  description: Department
                  example: ''
                position:
                  type: string
                  format: ''
                  description: Position
                  example: ''
                pull_down:
                  description: |
                    Office
                    * 1 => Tokyo
                    * 2 => Osaka
                    * 3 => Malaysia
                  anyOf:
                    -
                      type: object
                      properties:
                        key:
                          type: string
                          format: ''
                          enum:
                            - '1'
                            - '2'
                            - '3'
                        label:
                          type: string
                          format: ''
                          enum:
                            - Tokyo
                            - Osaka
                            - Malaysia
                      additionalProperties: false
                    -
                      type: string
                      enum:
                        - ''
                        - '1'
                        - '2'
                        - '3'
                  example: {  }
                  default: ''
                multiple_check:
                  description: |
                    Hobby
                    * 1 => Reading
                    * 2 => Watching TV
                    * 3 => Family Time
                    * 4 => Going to Movies
                    * 5 => Fishing
                    * 6 => Computer
                    * 7 => Gardening
                    * 8 => Renting Movies
                    * 9 => Walking
                    * 10 => Exercise
                  type: array
                  uniqueItems: true
                  items:
                    anyOf:
                      -
                        type: object
                        properties:
                          key:
                            type: string
                            format: ''
                            enum:
                              - '1'
                              - '2'
                              - '3'
                              - '4'
                              - '5'
                              - '6'
                              - '7'
                              - '8'
                              - '9'
                              - '10'
                          label:
                            type: string
                            format: ''
                            enum:
                              - Reading
                              - 'Watching TV'
                              - 'Family Time'
                              - 'Going to Movies'
                              - Fishing
                              - Computer
                              - Gardening
                              - 'Renting Movies'
                              - Walking
                              - Exercise
                        additionalProperties: false
                      -
                        type: string
                        enum:
                          - '1'
                          - '2'
                          - '3'
                          - '4'
                          - '5'
                          - '6'
                          - '7'
                          - '8'
                          - '9'
                          - '10'
                  default: []
                  example: []
                notes:
                  type: string
                  format: ''
                  description: Notes
                  example: ''
                profileimage:
                  description: Avatar
                  type: object
                  properties:
                    file_id:
                      type: string
                      default: ''
                      pattern: '^(files\/temp\/.+|files\/topics\/.+|https:\/\/.s3.ap-northeast-1.amazonaws.com\/files\/.+|https:\/\/player.vimeo.com\/video\/.+)$'
                      example: ''
                      description: アップロードされたファイルID
                    file_nm:
                      type: string
                      default: ''
                      example: ''
                      description: ファイル名
                    desc:
                      type: string
                      default: ''
                      example: ''
                      description: 説明
                  additionalProperties: false
                status:
                  description: |
                    会員ステータス
                    * 1 => 未認証
                    * 2 => 認証済み
                  anyOf:
                    -
                      type: object
                      properties:
                        key:
                          type: string
                          format: ''
                          enum:
                            - '1'
                            - '2'
                        label:
                          type: string
                          format: ''
                          enum:
                            - 未認証
                            - 認証済み
                      additionalProperties: false
                      minProperties: 1
                    -
                      type: string
                      enum:
                        - ''
                        - '1'
                        - '2'
                  example: {  }
                  default: ''
                pre_form_id:
                  type: integer
                  format: int32
                  minimum: -2147483648
                  maximum: 2147483647
                  description: 仮登録フォームID
                  example: 0
                name2_hurigana:
                  type: string
                  format: ''
                  description: メイ
                  example: ''
                name1_hurigana:
                  type: string
                  format: ''
                  description: セイ
                  example: ''
                maker_id:
                  type: integer
                  format: int32
                  minimum: -2147483648
                  maximum: 2147483647
                  description: メーカーID
                  example: 0
                validate_only:
                  type: boolean
                  format: ''
                  example: false
                  default: false
                  description: 入力チェックする
                auto_login:
                  type: integer
                  format: int32
                  minimum: -2147483648
                  maximum: 2147483647
              additionalProperties: false
  /rcms-api/5/member/update-status-and-get-form-info:
    post:
      tags:
        - メンバー
      summary: '[カスタム]メンバー情報更新後、メンバーIDに紐づいているフォーム情報を返却'
      description: |
        
        ### **Member::update (v1)**
        
        メンバーの会員ステータス更新後、メンバーIDに紐づいているフォーム情報を返却
        ## Controller parameters
        
        > **self_only** `true`
        
        > **use_columns** `status`
        
      parameters:
        -
          name: _output_format
          schema:
            type: string
            format: ''
          in: query
          required: false
          style: form
          explode: true
          description: '形式 (json|xml|csv|zip)'
        -
          name: _lang
          schema:
            type: string
            format: ''
          in: query
          required: false
          style: form
          explode: true
          description: 言語
        -
          name: _charset
          schema:
            type: string
            format: ''
          in: query
          required: false
          style: form
          explode: true
          description: 文字コード
      responses:
        200:
          description: 'My member infomation is updated'
      security:
        -
          Token-Auth: []
      operationId: postRcmsApi5MemberUpdateStatusAndGetFormInfo
      requestBody:
        required: true
        description: ''
        content:
          application/json:
            schema:
              type: object
              properties:
                current_password:
                  type: string
                  format: password
                  example: current_password
                  default: ''
                  description: /label/current_password
                status:
                  description: |
                    会員ステータス
                    * 1 => 未認証
                    * 2 => 認証済み
                  anyOf:
                    -
                      type: object
                      properties:
                        key:
                          type: string
                          format: ''
                          enum:
                            - '1'
                            - '2'
                        label:
                          type: string
                          format: ''
                          enum:
                            - 未認証
                            - 認証済み
                      additionalProperties: false
                    -
                      type: string
                      enum:
                        - ''
                        - '1'
                        - '2'
                  example: {  }
                  default: ''
                group_id:
                  type: array
                  items:
                    type: integer
                    format: int32
                    minimum: -2147483648
                    maximum: 2147483647
                    enum:
                      - 1
                      - 2
                      - 104
                      - 105
                  minItems: 0
                  description: /modules/group
                  example: []
                  default: []
                login_ok_flg:
                  type: integer
                  format: int32
                  minimum: -2147483648
                  maximum: 2147483647
                  description: /label/login_ok_flg
                  enum:
                    - 0
                    - 1
                  example: 1
                  default: 1
                validate_only:
                  type: boolean
                  format: ''
                  example: false
                  default: false
                  description: 入力チェックする
                auto_login:
                  type: integer
                  format: int32
                  minimum: -2147483648
                  maximum: 2147483647
              additionalProperties: false
  /rcms-api/5/member/me:
    get:
      tags:
        - メンバー
      summary: ''
      description: |
        
        ### **Member::details (v1)**
        
        
        ## Controller parameters
        
        > **self_only** `true`
        
      parameters:
        -
          name: _output_format
          schema:
            type: string
            format: ''
          in: query
          required: false
          style: form
          explode: true
          description: '形式 (json|xml|csv|zip)'
        -
          name: _lang
          schema:
            type: string
            format: ''
          in: query
          required: false
          style: form
          explode: true
          description: 言語
        -
          name: _charset
          schema:
            type: string
            format: ''
          in: query
          required: false
          style: form
          explode: true
          description: 文字コード
      responses:
        200:
          description: 'Fetch the details for a given member'
      security:
        -
          Token-Auth: []
      operationId: getRcmsApi5MemberMe
  /rcms-api/5/member/update:
    post:
      tags:
        - メンバー
      summary: ''
      description: |
        
        ### **Member::update (v1)**
        
        
        ## Controller parameters
        
        > **self_only** `true`
        
        > **use_columns** `name1,name2,name1_hurigana,name2_hurigana,zip_code,tdfk_cd,address1,address2,address3,tel,email,email_send_ng_flg`
        
      parameters:
        -
          name: _output_format
          schema:
            type: string
            format: ''
          in: query
          required: false
          style: form
          explode: true
          description: '形式 (json|xml|csv|zip)'
        -
          name: _lang
          schema:
            type: string
            format: ''
          in: query
          required: false
          style: form
          explode: true
          description: 言語
        -
          name: _charset
          schema:
            type: string
            format: ''
          in: query
          required: false
          style: form
          explode: true
          description: 文字コード
      responses:
        200:
          description: 'My member infomation is updated'
      security:
        -
          Token-Auth: []
      operationId: postRcmsApi5MemberUpdate
      requestBody:
        required: true
        description: ''
        content:
          application/json:
            schema:
              type: object
              properties:
                current_password:
                  type: string
                  format: password
                  example: current_password
                  default: ''
                  description: /label/current_password
                name1:
                  type: string
                  format: ''
                  description: 姓
                  example: ''
                name2:
                  type: string
                  format: ''
                  description: 名
                  example: ''
                email:
                  type: string
                  format: ''
                  description: メールアドレス
                  example: <EMAIL>
                  pattern: '^([a-zA-Z0-9\-_\.]+([a-zA-Z0-9\-_\.\/\+]+)?@[a-zA-Z0-9\-_\.]+\.[a-zA-Z0-9\-_\.]+)?$'
                zip_code:
                  type: string
                  format: ''
                  description: 郵便番号
                  example: '1234567'
                  pattern: '^([A-Za-z0-9-]+)?$'
                tdfk_cd:
                  type: string
                  format: ''
                  description: 都道府県
                  example: '13'
                  enum:
                    - ''
                    - '01'
                    - '02'
                    - '03'
                    - '04'
                    - '05'
                    - '06'
                    - '07'
                    - '08'
                    - '09'
                    - '10'
                    - '11'
                    - '12'
                    - '13'
                    - '14'
                    - '15'
                    - '16'
                    - '17'
                    - '18'
                    - '19'
                    - '20'
                    - '21'
                    - '22'
                    - '23'
                    - '24'
                    - '25'
                    - '26'
                    - '27'
                    - '28'
                    - '29'
                    - '30'
                    - '31'
                    - '32'
                    - '33'
                    - '34'
                    - '35'
                    - '36'
                    - '37'
                    - '38'
                    - '39'
                    - '40'
                    - '41'
                    - '42'
                    - '43'
                    - '44'
                    - '45'
                    - '46'
                    - '47'
                    - '99'
                address1:
                  type: string
                  format: ''
                  description: 住所1
                  example: ''
                address2:
                  type: string
                  format: ''
                  description: 住所2
                  example: ''
                address3:
                  type: string
                  format: ''
                  description: 住所3
                  example: ''
                tel:
                  type: string
                  format: ''
                  description: 電話番号
                  example: 0123-4444-5555
                  pattern: '^([\+\d][\(\)0-9+-]{5,14}\d)?$'
                email_send_ng_flg:
                  anyOf:
                    -
                      type: boolean
                      format: ''
                      description: email_send_ng_flg
                    -
                      type: integer
                      format: int32
                      minimum: -2147483648
                      maximum: 2147483647
                      description: email_send_ng_flg
                      enum:
                        - 0
                        - 1
                name2_hurigana:
                  type: string
                  format: ''
                  description: メイ
                  example: ''
                name1_hurigana:
                  type: string
                  format: ''
                  description: セイ
                  example: ''
                group_id:
                  type: array
                  items:
                    type: integer
                    format: int32
                    minimum: -2147483648
                    maximum: 2147483647
                    enum:
                      - 1
                      - 2
                      - 104
                      - 105
                  minItems: 0
                  description: /modules/group
                  example: []
                  default: []
                login_ok_flg:
                  type: integer
                  format: int32
                  minimum: -2147483648
                  maximum: 2147483647
                  description: /label/login_ok_flg
                  enum:
                    - 0
                    - 1
                  example: 1
                  default: 1
                validate_only:
                  type: boolean
                  format: ''
                  example: false
                  default: false
                  description: 入力チェックする
                auto_login:
                  type: integer
                  format: int32
                  minimum: -2147483648
                  maximum: 2147483647
              additionalProperties: false
  /rcms-api/5/member/regist-validate-only:
    post:
      tags:
        - メンバー
      summary: ''
      description: |
        
        ### **Member::insert (v1)**
        
        
        ## Controller parameters
        
        > **default_group_id** `104`
        
        > **unuse_columns** `status,pre_form_id,login_ok_flg`
        
        > **validate_only** `true`
        
      parameters:
        -
          name: _output_format
          schema:
            type: string
            format: ''
          in: query
          required: false
          style: form
          explode: true
          description: '形式 (json|xml|csv|zip)'
        -
          name: _lang
          schema:
            type: string
            format: ''
          in: query
          required: false
          style: form
          explode: true
          description: 言語
        -
          name: _charset
          schema:
            type: string
            format: ''
          in: query
          required: false
          style: form
          explode: true
          description: 文字コード
      responses:
        201:
          description: 'Member is created'
      security:
        -
          Token-Auth: []
      operationId: postRcmsApi5MemberRegistValidateOnly
      requestBody:
        required: true
        description: ''
        content:
          application/json:
            schema:
              type: object
              properties:
                name1:
                  type: string
                  format: ''
                  description: 姓
                  example: ''
                name2:
                  type: string
                  format: ''
                  description: 名
                  example: ''
                nickname:
                  type: string
                  format: ''
                  description: ニックネーム
                  example: ''
                email:
                  type: string
                  format: ''
                  description: メールアドレス
                  example: <EMAIL>
                  pattern: '^([a-zA-Z0-9\-_\.]+([a-zA-Z0-9\-_\.\/\+]+)?@[a-zA-Z0-9\-_\.]+\.[a-zA-Z0-9\-_\.]+)?$'
                zip_code:
                  type: string
                  format: ''
                  description: 郵便番号
                  example: '1234567'
                  pattern: '^([A-Za-z0-9-]+)?$'
                tdfk_cd:
                  type: string
                  format: ''
                  description: 都道府県
                  example: '13'
                  enum:
                    - ''
                    - '01'
                    - '02'
                    - '03'
                    - '04'
                    - '05'
                    - '06'
                    - '07'
                    - '08'
                    - '09'
                    - '10'
                    - '11'
                    - '12'
                    - '13'
                    - '14'
                    - '15'
                    - '16'
                    - '17'
                    - '18'
                    - '19'
                    - '20'
                    - '21'
                    - '22'
                    - '23'
                    - '24'
                    - '25'
                    - '26'
                    - '27'
                    - '28'
                    - '29'
                    - '30'
                    - '31'
                    - '32'
                    - '33'
                    - '34'
                    - '35'
                    - '36'
                    - '37'
                    - '38'
                    - '39'
                    - '40'
                    - '41'
                    - '42'
                    - '43'
                    - '44'
                    - '45'
                    - '46'
                    - '47'
                    - '99'
                address1:
                  type: string
                  format: ''
                  description: 住所1
                  example: ''
                address2:
                  type: string
                  format: ''
                  description: 住所2
                  example: ''
                address3:
                  type: string
                  format: ''
                  description: 住所3
                  example: ''
                tel:
                  type: string
                  format: ''
                  description: 電話番号
                  example: 0123-4444-5555
                  pattern: '^([\+\d][\(\)0-9+-]{5,14}\d)?$'
                email_send_ng_flg:
                  anyOf:
                    -
                      type: boolean
                      format: ''
                      description: email_send_ng_flg
                    -
                      type: integer
                      format: int32
                      minimum: -2147483648
                      maximum: 2147483647
                      description: email_send_ng_flg
                      enum:
                        - 0
                        - 1
                tel_send_ng_flg:
                  anyOf:
                    -
                      type: boolean
                      format: ''
                      description: tel_send_ng_flg
                    -
                      type: integer
                      format: int32
                      minimum: -2147483648
                      maximum: 2147483647
                      description: tel_send_ng_flg
                      enum:
                        - 0
                        - 1
                login_pwd:
                  type: string
                  format: password
                  description: login_pwd
                login_id:
                  type: string
                  format: ''
                  description: ログインID
                  example: ''
                login_ok_ymd:
                  anyOf:
                    -
                      type: string
                      format: date
                    -
                      type: string
                      maxLength: 0
                  description: ログイン許可期限
                hire_date:
                  description: 'Hire date'
                  anyOf:
                    -
                      anyOf:
                        -
                          type: string
                          format: date
                        -
                          type: string
                          maxLength: 0
                    -
                      type: string
                      pattern: ^$
                  example: '2024-10-02'
                department:
                  type: string
                  format: ''
                  description: Department
                  example: ''
                position:
                  type: string
                  format: ''
                  description: Position
                  example: ''
                pull_down:
                  description: |
                    Office
                    * 1 => Tokyo
                    * 2 => Osaka
                    * 3 => Malaysia
                  anyOf:
                    -
                      type: object
                      properties:
                        key:
                          type: string
                          format: ''
                          enum:
                            - '1'
                            - '2'
                            - '3'
                        label:
                          type: string
                          format: ''
                          enum:
                            - Tokyo
                            - Osaka
                            - Malaysia
                      additionalProperties: false
                    -
                      type: string
                      enum:
                        - ''
                        - '1'
                        - '2'
                        - '3'
                  example: {  }
                  default: ''
                multiple_check:
                  description: |
                    Hobby
                    * 1 => Reading
                    * 2 => Watching TV
                    * 3 => Family Time
                    * 4 => Going to Movies
                    * 5 => Fishing
                    * 6 => Computer
                    * 7 => Gardening
                    * 8 => Renting Movies
                    * 9 => Walking
                    * 10 => Exercise
                  type: array
                  uniqueItems: true
                  items:
                    anyOf:
                      -
                        type: object
                        properties:
                          key:
                            type: string
                            format: ''
                            enum:
                              - '1'
                              - '2'
                              - '3'
                              - '4'
                              - '5'
                              - '6'
                              - '7'
                              - '8'
                              - '9'
                              - '10'
                          label:
                            type: string
                            format: ''
                            enum:
                              - Reading
                              - 'Watching TV'
                              - 'Family Time'
                              - 'Going to Movies'
                              - Fishing
                              - Computer
                              - Gardening
                              - 'Renting Movies'
                              - Walking
                              - Exercise
                        additionalProperties: false
                      -
                        type: string
                        enum:
                          - '1'
                          - '2'
                          - '3'
                          - '4'
                          - '5'
                          - '6'
                          - '7'
                          - '8'
                          - '9'
                          - '10'
                  default: []
                  example: []
                notes:
                  type: string
                  format: ''
                  description: Notes
                  example: ''
                profileimage:
                  description: Avatar
                  type: object
                  properties:
                    file_id:
                      type: string
                      default: ''
                      pattern: '^(files\/temp\/.+|files\/topics\/.+|https:\/\/.s3.ap-northeast-1.amazonaws.com\/files\/.+|https:\/\/player.vimeo.com\/video\/.+)$'
                      example: ''
                      description: アップロードされたファイルID
                    file_nm:
                      type: string
                      default: ''
                      example: ''
                      description: ファイル名
                    desc:
                      type: string
                      default: ''
                      example: ''
                      description: 説明
                  additionalProperties: false
                name2_hurigana:
                  type: string
                  format: ''
                  description: メイ
                  example: ''
                name1_hurigana:
                  type: string
                  format: ''
                  description: セイ
                  example: ''
                maker_id:
                  type: integer
                  format: int32
                  minimum: -2147483648
                  maximum: 2147483647
                  description: メーカーID
                  example: 0
                login_ok_flg:
                  type: integer
                  format: int32
                  minimum: -2147483648
                  maximum: 2147483647
                  description: /label/login_ok_flg
                  enum:
                    - 0
                    - 1
                  example: 1
                  default: 1
                auto_login:
                  type: integer
                  format: int32
                  minimum: -2147483648
                  maximum: 2147483647
              additionalProperties: false
  '/rcms-api/5/member/delete-expired-member/{member_id}':
    post:
      tags:
        - メンバー
      summary: '[バッチ処理]未認証且つ新規登録日時1時間前の対象者の削除'
      description: |
        
        ### **Member::delete (v1)**
        
        
        ## Controller parameters
        
        > **allowed_group_ids** `104`
        
      parameters:
        -
          name: member_id
          schema:
            type: integer
            format: int32
            minimum: -2147483648
            maximum: 2147483647
          in: path
          required: true
          style: simple
        -
          name: _output_format
          schema:
            type: string
            format: ''
          in: query
          required: false
          style: form
          explode: true
          description: '形式 (json|xml|csv|zip)'
        -
          name: _lang
          schema:
            type: string
            format: ''
          in: query
          required: false
          style: form
          explode: true
          description: 言語
        -
          name: _charset
          schema:
            type: string
            format: ''
          in: query
          required: false
          style: form
          explode: true
          description: 文字コード
      responses:
        200:
          description: 'My member infomation is updated'
      security:
        -
          Token-Auth: []
      operationId: postRcmsApi5MemberDeleteExpiredMemberMemberId
      requestBody:
        required: false
        description: ''
        content:
          application/json:
            schema:
              type: object
              properties: {  }
              additionalProperties: false
  /rcms-api/5/member/list:
    get:
      tags:
        - メンバー
      summary: ''
      description: |
        
        ### **Member::list (v1)**
        
        
        ## Controller parameters
        
        > **cnt** `10000`
        
      parameters:
        -
          name: _output_format
          schema:
            type: string
            format: ''
          in: query
          required: false
          style: form
          explode: true
          description: '形式 (json|xml|csv|zip)'
        -
          name: _lang
          schema:
            type: string
            format: ''
          in: query
          required: false
          style: form
          explode: true
          description: 言語
        -
          name: _charset
          schema:
            type: string
            format: ''
          in: query
          required: false
          style: form
          explode: true
          description: 文字コード
        -
          name: 'id[]'
          schema:
            type: array
            items:
              type: integer
              format: int32
              minimum: -2147483648
              maximum: 2147483647
            minItems: 0
          in: query
          required: false
          style: form
          explode: true
          description: メンバーID
        -
          name: cnt
          schema:
            type: integer
            format: int32
            minimum: -2147483648
            maximum: 2147483647
          in: query
          required: false
          style: form
          explode: true
          description: 1ページの行数
        -
          name: pageID
          schema:
            type: integer
            format: int32
            minimum: -2147483648
            maximum: 2147483647
          in: query
          required: false
          style: form
          explode: true
          description: ページID
        -
          name: 'group_id[]'
          schema:
            type: array
            items:
              type: integer
              format: int32
              minimum: -2147483648
              maximum: 2147483647
            minItems: 0
          in: query
          required: false
          style: form
          explode: true
          description: メンバー検索
        -
          name: filter
          schema:
            type: string
            format: ''
          in: query
          required: false
          style: form
          explode: true
          description: フィルタークエリ
      responses:
        200:
          description: メンバー一覧
      security:
        -
          Token-Auth: []
      operationId: getRcmsApi5MemberList
  /rcms-api/5/inquiry/list:
    get:
      tags:
        - フォーム
      summary: ''
      description: |
        
        ### **InquiryMessage::list (v1)**
        
        
        ## Controller parameters
        
        > **inquiry_id** `3`
        
        > **cnt** `10000`
        
      parameters:
        -
          name: _output_format
          schema:
            type: string
            format: ''
          in: query
          required: false
          style: form
          explode: true
          description: '形式 (json|xml|csv|zip)'
        -
          name: _lang
          schema:
            type: string
            format: ''
          in: query
          required: false
          style: form
          explode: true
          description: 言語
        -
          name: _charset
          schema:
            type: string
            format: ''
          in: query
          required: false
          style: form
          explode: true
          description: 文字コード
        -
          name: s_category
          schema:
            type: integer
            format: int32
            minimum: -2147483648
            maximum: 2147483647
          in: query
          required: false
          style: form
          explode: true
          description: カテゴリ
        -
          name: s_status
          schema:
            type: integer
            format: int32
            minimum: -2147483648
            maximum: 2147483647
          in: query
          required: false
          style: form
          explode: true
          description: ''
        -
          name: keyword
          schema:
            type: string
            format: ''
          in: query
          required: false
          style: form
          explode: true
          description: キーワード
        -
          name: pageID
          schema:
            type: integer
            format: int32
            minimum: -2147483648
            maximum: 2147483647
          in: query
          required: false
          style: form
          explode: true
          description: ページID
        -
          name: cnt
          schema:
            type: integer
            format: int32
            minimum: -2147483648
            maximum: 2147483647
          in: query
          required: false
          style: form
          explode: true
          description: １ページあたりの表示件数
        -
          name: from_dt
          schema:
            type: string
            format: ''
          in: query
          required: false
          style: form
          explode: true
          description: 受信日時
        -
          name: to_dt
          schema:
            type: string
            format: ''
          in: query
          required: false
          style: form
          explode: true
          description: 受信日時
        -
          name: from_id
          schema:
            type: integer
            format: int32
            minimum: -2147483648
            maximum: 2147483647
          in: query
          required: false
          style: form
          explode: true
          description: フォーム対象ID
        -
          name: from_module
          schema:
            type: string
            format: ''
          in: query
          required: false
          style: form
          explode: true
          description: フォーム対象機能
        -
          name: filter
          schema:
            type: string
            format: ''
          in: query
          required: false
          style: form
          explode: true
          description: フィルタークエリ
        -
          name: order_query
          schema:
            type: string
            format: ''
          in: query
          required: false
          style: form
          explode: true
          description: '並び順を設定する 例) foo=ASC,bar=DESC'
        -
          name: 'parent_inquiry_bn_id[]'
          schema:
            type: array
            items:
              type: integer
              format: int32
              minimum: -2147483648
              maximum: 2147483647
            minItems: 0
          in: query
          required: false
          style: form
          explode: true
          description: ''
      responses:
        200:
          description: 'Messages of the specified inquiry form were fetched'
        404:
          description: 'No messages found for the specified inquiry form ID'
      security:
        -
          Token-Auth: []
      operationId: getRcmsApi5InquiryList
  /rcms-api/5/ec/order/purchase:
    post:
      tags:
        - EC
      summary: '[カスタム]注文登録後、ユーザー＆商品情報を返却'
      description: |
        
        ### **ECOrder::purchase (v1)**
        
        
      parameters:
        -
          name: _output_format
          schema:
            type: string
            format: ''
          in: query
          required: false
          style: form
          explode: true
          description: '形式 (json|xml|csv|zip)'
        -
          name: _lang
          schema:
            type: string
            format: ''
          in: query
          required: false
          style: form
          explode: true
          description: 言語
        -
          name: _charset
          schema:
            type: string
            format: ''
          in: query
          required: false
          style: form
          explode: true
          description: 文字コード
      responses:
        200:
          description: 'Order for cart items successfully sent'
        422:
          description: ERR
      security:
        -
          Token-Auth: []
      operationId: postRcmsApi5EcOrderPurchase
      requestBody:
        required: true
        description: ''
        content:
          application/json:
            schema:
              type: object
              properties:
                ec_cart_id:
                  type: integer
                  format: int64
                  description: カートID
                order_products:
                  type: array
                  items:
                    type: object
                    properties:
                      product_id:
                        type: integer
                        format: int32
                        minimum: 0
                        maximum: 2147483647
                        description: 商品ID
                      quantity:
                        type: integer
                        format: int32
                        minimum: 0
                        maximum: 2147483647
                        description: 個数
                  additionalProperties: false
                product_id:
                  type: integer
                  format: int32
                  minimum: -2147483648
                  maximum: 2147483647
                  description: 商品ID
                quantity:
                  type: integer
                  format: int32
                  minimum: -2147483648
                  maximum: 2147483647
                  description: 個数
                ec_payment_id:
                  type: integer
                  format: int32
                  minimum: 1
                  maximum: 2147483647
                  description: 支払い方法ID
                discount:
                  type: object
                  properties:
                    point:
                      type: integer
                      format: int32
                      minimum: 0
                      maximum: 2147483647
                      description: 使用ポイント
                    serial_code:
                      type: string
                      format: ''
                      description: クーポンコード
                  description: 値引き額を使用する
                shipping_address:
                  anyOf:
                    -
                      type: string
                      format: ''
                      enum:
                        - default
                    -
                      type: object
                      properties:
                        name1:
                          type: string
                          format: ''
                          description: 姓
                        name2:
                          type: string
                          format: ''
                          description: 名
                        zip_code:
                          type: string
                          format: ''
                          description: ''
                          pattern: '^[0-9]+$'
                          example: '000'
                        tdfk_cd:
                          type: string
                          format: ''
                          description: |
                            * 01 => 北海道
                            * 02 => 青森県
                            * 03 => 岩手県
                            * 04 => 宮城県
                            * 05 => 秋田県
                            * 06 => 山形県
                            * 07 => 福島県
                            * 08 => 茨城県
                            * 09 => 栃木県
                            * 10 => 群馬県
                            * 11 => 埼玉県
                            * 12 => 千葉県
                            * 13 => 東京都
                            * 14 => 神奈川県
                            * 15 => 新潟県
                            * 16 => 富山県
                            * 17 => 石川県
                            * 18 => 福井県
                            * 19 => 山梨県
                            * 20 => 長野県
                            * 21 => 岐阜県
                            * 22 => 静岡県
                            * 23 => 愛知県
                            * 24 => 三重県
                            * 25 => 滋賀県
                            * 26 => 京都府
                            * 27 => 大阪府
                            * 28 => 兵庫県
                            * 29 => 奈良県
                            * 30 => 和歌山県
                            * 31 => 鳥取県
                            * 32 => 島根県
                            * 33 => 岡山県
                            * 34 => 広島県
                            * 35 => 山口県
                            * 36 => 徳島県
                            * 37 => 香川県
                            * 38 => 愛媛県
                            * 39 => 高知県
                            * 40 => 福岡県
                            * 41 => 佐賀県
                            * 42 => 長崎県
                            * 43 => 熊本県
                            * 44 => 大分県
                            * 45 => 宮崎県
                            * 46 => 鹿児島県
                            * 47 => 沖縄県
                            * 99 => 海外
                          enum:
                            - '01'
                            - '02'
                            - '03'
                            - '04'
                            - '05'
                            - '06'
                            - '07'
                            - '08'
                            - '09'
                            - '10'
                            - '11'
                            - '12'
                            - '13'
                            - '14'
                            - '15'
                            - '16'
                            - '17'
                            - '18'
                            - '19'
                            - '20'
                            - '21'
                            - '22'
                            - '23'
                            - '24'
                            - '25'
                            - '26'
                            - '27'
                            - '28'
                            - '29'
                            - '30'
                            - '31'
                            - '32'
                            - '33'
                            - '34'
                            - '35'
                            - '36'
                            - '37'
                            - '38'
                            - '39'
                            - '40'
                            - '41'
                            - '42'
                            - '43'
                            - '44'
                            - '45'
                            - '46'
                            - '47'
                            - '99'
                        address1:
                          type: string
                          format: ''
                          description: 住所1
                        address2:
                          type: string
                          format: ''
                          description: 住所2
                        address3:
                          type: string
                          format: ''
                          description: 住所3
                        tel:
                          type: string
                          format: ''
                          description: 電話番号
                          pattern: '(^$|^[\+\d][\(\)0-9+-]{5,14}\d$)'
                      additionalProperties: false
                  description: 配送先住所
                  example:
                    name1: ''
                    name2: ''
                    zip_code: '000'
                    tdfk_cd: ''
                    address1: ''
                    address2: ''
                    address3: ''
                    tel: ''
                sp_career_info:
                  type: object
                  properties:
                    sp_career:
                      type: integer
                      format: int32
                      minimum: 1
                      maximum: 2147483647
                      description: スマホキャリア
                    open_id:
                      type: string
                      format: ''
                      minimum: 1
                      description: ''
                    return_url:
                      type: string
                      format: ''
                      description: ''
                    cancel_url:
                      type: string
                      format: ''
                      description: ''
                    other_url:
                      type: string
                      format: ''
                      description: ''
                    outline:
                      type: string
                      format: ''
                      description: ''
                orderer:
                  type: object
                  properties:
                    name1:
                      type: string
                      format: ''
                      description: 姓
                    name2:
                      type: string
                      format: ''
                      description: 名
                    zip_code:
                      type: string
                      format: ''
                      description: ''
                      pattern: '^[0-9]+$'
                      example: '000'
                    tdfk_cd:
                      type: string
                      format: ''
                      description: |
                        * 01 => 北海道
                        * 02 => 青森県
                        * 03 => 岩手県
                        * 04 => 宮城県
                        * 05 => 秋田県
                        * 06 => 山形県
                        * 07 => 福島県
                        * 08 => 茨城県
                        * 09 => 栃木県
                        * 10 => 群馬県
                        * 11 => 埼玉県
                        * 12 => 千葉県
                        * 13 => 東京都
                        * 14 => 神奈川県
                        * 15 => 新潟県
                        * 16 => 富山県
                        * 17 => 石川県
                        * 18 => 福井県
                        * 19 => 山梨県
                        * 20 => 長野県
                        * 21 => 岐阜県
                        * 22 => 静岡県
                        * 23 => 愛知県
                        * 24 => 三重県
                        * 25 => 滋賀県
                        * 26 => 京都府
                        * 27 => 大阪府
                        * 28 => 兵庫県
                        * 29 => 奈良県
                        * 30 => 和歌山県
                        * 31 => 鳥取県
                        * 32 => 島根県
                        * 33 => 岡山県
                        * 34 => 広島県
                        * 35 => 山口県
                        * 36 => 徳島県
                        * 37 => 香川県
                        * 38 => 愛媛県
                        * 39 => 高知県
                        * 40 => 福岡県
                        * 41 => 佐賀県
                        * 42 => 長崎県
                        * 43 => 熊本県
                        * 44 => 大分県
                        * 45 => 宮崎県
                        * 46 => 鹿児島県
                        * 47 => 沖縄県
                        * 99 => 海外
                      enum:
                        - '01'
                        - '02'
                        - '03'
                        - '04'
                        - '05'
                        - '06'
                        - '07'
                        - '08'
                        - '09'
                        - '10'
                        - '11'
                        - '12'
                        - '13'
                        - '14'
                        - '15'
                        - '16'
                        - '17'
                        - '18'
                        - '19'
                        - '20'
                        - '21'
                        - '22'
                        - '23'
                        - '24'
                        - '25'
                        - '26'
                        - '27'
                        - '28'
                        - '29'
                        - '30'
                        - '31'
                        - '32'
                        - '33'
                        - '34'
                        - '35'
                        - '36'
                        - '37'
                        - '38'
                        - '39'
                        - '40'
                        - '41'
                        - '42'
                        - '43'
                        - '44'
                        - '45'
                        - '46'
                        - '47'
                        - '99'
                    address1:
                      type: string
                      format: ''
                      description: 住所1
                    address2:
                      type: string
                      format: ''
                      description: 住所2
                    address3:
                      type: string
                      format: ''
                      description: 住所3
                    tel:
                      type: string
                      format: ''
                      description: 電話番号
                      pattern: '(^$|^[\+\d][\(\)0-9+-]{5,14}\d$)'
                    email:
                      type: string
                      format: email
                      pattern: '^[a-zA-Z0-9\-_\.]+([a-zA-Z0-9\-_\.\/\+]+)?@[a-zA-Z0-9\-_\.]+\.[a-zA-Z0-9\-_\.]+$'
                      description: '[Only for guest] メールアドレス'
                      example: <EMAIL>
                  description: 注文者情報
                  additionalProperties: false
                card_token:
                  type: string
                  format: ''
                  description: トークン
                card_options:
                  type: object
                  properties:
                    card_3dsec_return_url:
                      type: string
                      format: ''
                      description: 'クレジットカード決済3Dセキュア戻りURL (空欄の場合はフロントTOPページ）'
                      pattern: '^(/|https://swb-sbpg-dev.g.kuroco-front.app)'
                order_note:
                  type: string
                  format: ''
                  description: ''
                conveni:
                  type: integer
                  format: int32
                  minimum: -2147483648
                  maximum: 2147483647
                  description: |
                    
                    * 1 => セブン-イレブン
                    * 2 => ローソン
                    * 3 => ファミリーマート
                    * 4 => セイコーマート
                    * 5 => サークルK・サンクス
                    * 6 => ミニストップ
                    * 7 => デイリーヤマザキ
                  enum:
                    - 1
                    - 2
                    - 3
                    - 4
                    - 5
                    - 6
                    - 7
                validate_only:
                  type: boolean
                  format: ''
                  example: false
                  default: false
                  description: 入力チェックする
              additionalProperties: false
              required:
                - ec_payment_id
  /rcms-api/5/ec/order/list:
    get:
      tags:
        - EC
      summary: '[カスタム]注文リスト＆商品名を返却'
      description: |
        
        ### **ECOrder::list (v1)**
        
        
        ## Controller parameters
        
        > **self_only** `true`
        
      parameters:
        -
          name: _output_format
          schema:
            type: string
            format: ''
          in: query
          required: false
          style: form
          explode: true
          description: '形式 (json|xml|csv|zip)'
        -
          name: _lang
          schema:
            type: string
            format: ''
          in: query
          required: false
          style: form
          explode: true
          description: 言語
        -
          name: _charset
          schema:
            type: string
            format: ''
          in: query
          required: false
          style: form
          explode: true
          description: 文字コード
        -
          name: cnt
          schema:
            type: integer
            format: int32
            minimum: -2147483648
            maximum: 2147483647
          in: query
          required: false
          style: form
          explode: true
          description: 1ページの行数
        -
          name: pageID
          schema:
            type: integer
            format: int32
            minimum: -2147483648
            maximum: 2147483647
          in: query
          required: false
          style: form
          explode: true
          description: ページID
        -
          name: ec_order_id
          schema:
            type: integer
            format: int32
            minimum: -2147483648
            maximum: 2147483647
          in: query
          required: false
          style: form
          explode: true
          description: 注文番号
        -
          name: order_name
          schema:
            type: string
            format: ''
          in: query
          required: false
          style: form
          explode: true
          description: 注文者名
        -
          name: order_kana
          schema:
            type: string
            format: ''
          in: query
          required: false
          style: form
          explode: true
          description: フリガナ
        -
          name: order_email
          schema:
            type: string
            format: ''
          in: query
          required: false
          style: form
          explode: true
          description: メールアドレス
        -
          name: order_pref
          schema:
            type: string
            format: ''
          in: query
          required: false
          style: form
          explode: true
          description: 都道府県
        -
          name: order_address
          schema:
            type: string
            format: ''
          in: query
          required: false
          style: form
          explode: true
          description: 住所
        -
          name: order_tel
          schema:
            type: string
            format: ''
          in: query
          required: false
          style: form
          explode: true
          description: 電話番号
        -
          name: order_memo
          schema:
            type: string
            format: ''
          in: query
          required: false
          style: form
          explode: true
          description: メモ
        -
          name: delivery_status
          schema:
            type: integer
            format: int32
            minimum: -2147483648
            maximum: 2147483647
          in: query
          required: false
          style: form
          explode: true
          description: 配送状態
        -
          name: is_canceled
          schema:
            type: integer
            format: int32
            minimum: -2147483648
            maximum: 2147483647
          in: query
          required: false
          style: form
          explode: true
          description: キャンセル済み
        -
          name: is_subscription_canceled
          schema:
            type: integer
            format: int32
            minimum: -2147483648
            maximum: 2147483647
          in: query
          required: false
          style: form
          explode: true
          description: 自動継続キャンセル済み
        -
          name: is_regular
          schema:
            type: integer
            format: int32
            minimum: -2147483648
            maximum: 2147483647
          in: query
          required: false
          style: form
          explode: true
          description: 定期購入
        -
          name: is_subscription
          schema:
            type: integer
            format: int32
            minimum: -2147483648
            maximum: 2147483647
          in: query
          required: false
          style: form
          explode: true
          description: 自動継続
        -
          name: is_on_demand
          schema:
            type: integer
            format: int32
            minimum: -2147483648
            maximum: 2147483647
          in: query
          required: false
          style: form
          explode: true
          description: 受注生産
        -
          name: 'total[from]'
          schema:
            type: integer
            format: int32
            minimum: -2147483648
            maximum: 2147483647
          in: query
          required: false
          style: form
          explode: true
          description: 購入金額
        -
          name: 'total[to]'
          schema:
            type: integer
            format: int32
            minimum: -2147483648
            maximum: 2147483647
          in: query
          required: false
          style: form
          explode: true
          description: 購入金額
        -
          name: 'inst_ymdhi[from]'
          schema:
            type: string
            format: ''
          in: query
          required: false
          style: form
          explode: true
          description: 受注日
        -
          name: 'inst_ymdhi[to]'
          schema:
            type: string
            format: ''
          in: query
          required: false
          style: form
          explode: true
          description: 受注日
        -
          name: 'update_ymdhi[from]'
          schema:
            type: string
            format: ''
          in: query
          required: false
          style: form
          explode: true
          description: 更新日時
        -
          name: 'update_ymdhi[to]'
          schema:
            type: string
            format: ''
          in: query
          required: false
          style: form
          explode: true
          description: 更新日時
        -
          name: 'payment_date[from]'
          schema:
            type: string
            format: ''
          in: query
          required: false
          style: form
          explode: true
          description: 入金日
        -
          name: 'payment_date[to]'
          schema:
            type: string
            format: ''
          in: query
          required: false
          style: form
          explode: true
          description: 入金日
        -
          name: 'auth_sale_date[from]'
          schema:
            type: string
            format: ''
          in: query
          required: false
          style: form
          explode: true
          description: 売上(発送)日
        -
          name: 'auth_sale_date[to]'
          schema:
            type: string
            format: ''
          in: query
          required: false
          style: form
          explode: true
          description: 売上(発送)日
        -
          name: topics_group_id
          schema:
            type: integer
            format: int32
            minimum: -2147483648
            maximum: 2147483647
          in: query
          required: false
          style: form
          explode: true
          description: ''
        -
          name: topics_category_id
          schema:
            type: integer
            format: int32
            minimum: -2147483648
            maximum: 2147483647
          in: query
          required: false
          style: form
          explode: true
          description: コンテンツ定義ID
        -
          name: topics_id
          schema:
            type: integer
            format: int32
            minimum: -2147483648
            maximum: 2147483647
          in: query
          required: false
          style: form
          explode: true
          description: コンテンツID
        -
          name: 'product_id[]'
          schema:
            type: array
            items:
              type: integer
              format: int32
              minimum: -2147483648
              maximum: 2147483647
            minItems: 0
          in: query
          required: false
          style: form
          explode: true
          description: 商品ID
        -
          name: product_name
          schema:
            type: string
            format: ''
          in: query
          required: false
          style: form
          explode: true
          description: 商品名
        -
          name: payment_method
          schema:
            type: integer
            format: int32
            minimum: -2147483648
            maximum: 2147483647
          in: query
          required: false
          style: form
          explode: true
          description: 支払い方法
        -
          name: generic_payment_status
          schema:
            type: string
            format: ''
          in: query
          required: false
          style: form
          explode: true
          description: 支払い状態
        -
          name: without_payment_error
          schema:
            type: integer
            format: int32
            minimum: -2147483648
            maximum: 2147483647
          in: query
          required: false
          style: form
          explode: true
          description: 決済失敗を除く
      responses:
        200:
          description: 'Order list successfully fetched'
        404:
          description: 'Order list could not be found'
      security:
        -
          Token-Auth: []
      operationId: getRcmsApi5EcOrderList
  /rcms-api/5/ec/order/all-list:
    get:
      tags:
        - EC
      summary: '[カスタム]注文リスト＆商品名を返却'
      description: |
        
        ### **ECOrder::list (v1)**
        
        
      parameters:
        -
          name: _output_format
          schema:
            type: string
            format: ''
          in: query
          required: false
          style: form
          explode: true
          description: '形式 (json|xml|csv|zip)'
        -
          name: _lang
          schema:
            type: string
            format: ''
          in: query
          required: false
          style: form
          explode: true
          description: 言語
        -
          name: _charset
          schema:
            type: string
            format: ''
          in: query
          required: false
          style: form
          explode: true
          description: 文字コード
        -
          name: cnt
          schema:
            type: integer
            format: int32
            minimum: -2147483648
            maximum: 2147483647
          in: query
          required: false
          style: form
          explode: true
          description: 1ページの行数
        -
          name: pageID
          schema:
            type: integer
            format: int32
            minimum: -2147483648
            maximum: 2147483647
          in: query
          required: false
          style: form
          explode: true
          description: ページID
        -
          name: ec_order_id
          schema:
            type: integer
            format: int32
            minimum: -2147483648
            maximum: 2147483647
          in: query
          required: false
          style: form
          explode: true
          description: 注文番号
        -
          name: order_name
          schema:
            type: string
            format: ''
          in: query
          required: false
          style: form
          explode: true
          description: 注文者名
        -
          name: order_kana
          schema:
            type: string
            format: ''
          in: query
          required: false
          style: form
          explode: true
          description: フリガナ
        -
          name: order_email
          schema:
            type: string
            format: ''
          in: query
          required: false
          style: form
          explode: true
          description: メールアドレス
        -
          name: order_pref
          schema:
            type: string
            format: ''
          in: query
          required: false
          style: form
          explode: true
          description: 都道府県
        -
          name: order_address
          schema:
            type: string
            format: ''
          in: query
          required: false
          style: form
          explode: true
          description: 住所
        -
          name: order_tel
          schema:
            type: string
            format: ''
          in: query
          required: false
          style: form
          explode: true
          description: 電話番号
        -
          name: order_memo
          schema:
            type: string
            format: ''
          in: query
          required: false
          style: form
          explode: true
          description: メモ
        -
          name: delivery_status
          schema:
            type: integer
            format: int32
            minimum: -2147483648
            maximum: 2147483647
          in: query
          required: false
          style: form
          explode: true
          description: 配送状態
        -
          name: is_canceled
          schema:
            type: integer
            format: int32
            minimum: -2147483648
            maximum: 2147483647
          in: query
          required: false
          style: form
          explode: true
          description: キャンセル済み
        -
          name: is_subscription_canceled
          schema:
            type: integer
            format: int32
            minimum: -2147483648
            maximum: 2147483647
          in: query
          required: false
          style: form
          explode: true
          description: 自動継続キャンセル済み
        -
          name: is_regular
          schema:
            type: integer
            format: int32
            minimum: -2147483648
            maximum: 2147483647
          in: query
          required: false
          style: form
          explode: true
          description: 定期購入
        -
          name: is_subscription
          schema:
            type: integer
            format: int32
            minimum: -2147483648
            maximum: 2147483647
          in: query
          required: false
          style: form
          explode: true
          description: 自動継続
        -
          name: is_on_demand
          schema:
            type: integer
            format: int32
            minimum: -2147483648
            maximum: 2147483647
          in: query
          required: false
          style: form
          explode: true
          description: 受注生産
        -
          name: 'total[from]'
          schema:
            type: integer
            format: int32
            minimum: -2147483648
            maximum: 2147483647
          in: query
          required: false
          style: form
          explode: true
          description: 購入金額
        -
          name: 'total[to]'
          schema:
            type: integer
            format: int32
            minimum: -2147483648
            maximum: 2147483647
          in: query
          required: false
          style: form
          explode: true
          description: 購入金額
        -
          name: 'inst_ymdhi[from]'
          schema:
            type: string
            format: ''
          in: query
          required: false
          style: form
          explode: true
          description: 受注日
        -
          name: 'inst_ymdhi[to]'
          schema:
            type: string
            format: ''
          in: query
          required: false
          style: form
          explode: true
          description: 受注日
        -
          name: 'update_ymdhi[from]'
          schema:
            type: string
            format: ''
          in: query
          required: false
          style: form
          explode: true
          description: 更新日時
        -
          name: 'update_ymdhi[to]'
          schema:
            type: string
            format: ''
          in: query
          required: false
          style: form
          explode: true
          description: 更新日時
        -
          name: 'payment_date[from]'
          schema:
            type: string
            format: ''
          in: query
          required: false
          style: form
          explode: true
          description: 入金日
        -
          name: 'payment_date[to]'
          schema:
            type: string
            format: ''
          in: query
          required: false
          style: form
          explode: true
          description: 入金日
        -
          name: 'auth_sale_date[from]'
          schema:
            type: string
            format: ''
          in: query
          required: false
          style: form
          explode: true
          description: 売上(発送)日
        -
          name: 'auth_sale_date[to]'
          schema:
            type: string
            format: ''
          in: query
          required: false
          style: form
          explode: true
          description: 売上(発送)日
        -
          name: topics_group_id
          schema:
            type: integer
            format: int32
            minimum: -2147483648
            maximum: 2147483647
          in: query
          required: false
          style: form
          explode: true
          description: ''
        -
          name: topics_category_id
          schema:
            type: integer
            format: int32
            minimum: -2147483648
            maximum: 2147483647
          in: query
          required: false
          style: form
          explode: true
          description: コンテンツ定義ID
        -
          name: topics_id
          schema:
            type: integer
            format: int32
            minimum: -2147483648
            maximum: 2147483647
          in: query
          required: false
          style: form
          explode: true
          description: コンテンツID
        -
          name: 'product_id[]'
          schema:
            type: array
            items:
              type: integer
              format: int32
              minimum: -2147483648
              maximum: 2147483647
            minItems: 0
          in: query
          required: false
          style: form
          explode: true
          description: 商品ID
        -
          name: product_name
          schema:
            type: string
            format: ''
          in: query
          required: false
          style: form
          explode: true
          description: 商品名
        -
          name: member_id
          schema:
            type: integer
            format: int32
            minimum: -2147483648
            maximum: 2147483647
          in: query
          required: false
          style: form
          explode: true
          description: メンバーID
        -
          name: is_member_purchase
          schema:
            type: integer
            format: int32
            minimum: -2147483648
            maximum: 2147483647
          in: query
          required: false
          style: form
          explode: true
          description: 会員・非会員
        -
          name: payment_method
          schema:
            type: integer
            format: int32
            minimum: -2147483648
            maximum: 2147483647
          in: query
          required: false
          style: form
          explode: true
          description: 支払い方法
        -
          name: generic_payment_status
          schema:
            type: string
            format: ''
          in: query
          required: false
          style: form
          explode: true
          description: 支払い状態
        -
          name: without_payment_error
          schema:
            type: integer
            format: int32
            minimum: -2147483648
            maximum: 2147483647
          in: query
          required: false
          style: form
          explode: true
          description: 決済失敗を除く
      responses:
        200:
          description: 'Order list successfully fetched'
        404:
          description: 'Order list could not be found'
      security:
        -
          Token-Auth: []
      operationId: getRcmsApi5EcOrderAllList
components:
  schemas: {  }
  securitySchemes:
    Token-Auth:
      type: apiKey
      in: header
      name: X-RCMS-API-ACCESS-TOKEN
