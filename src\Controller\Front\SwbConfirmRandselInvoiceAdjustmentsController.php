<?php

namespace App\Controller\Front;

use App\Controller\FrontController;
use App\Form\DisableParametersForm;
use App\Service\SwbConfirmRandselInvoiceAdjustmentsService;
use Cake\Event\Event;
use Cake\Log\Log;

class SwbConfirmRandselInvoiceAdjustmentsController extends FrontController
{
    public function edit($id = null): void
    {
        $this->_onSuccess(function (Event $event,) {
            $this->_setSuccessResponse([
                'success' => true,
            ]);
        });
        $this->_onError(function (Event $event, array $errors) {
            Log::debug(print_r($errors, true));
        });
        $this->_edit($id, (new SwbConfirmRandselInvoiceAdjustmentsService())
            ->setIdentity($this->Authentication->getIdentity())
            ->setForm(
                new DisableParametersForm()
            ));
    }
}
