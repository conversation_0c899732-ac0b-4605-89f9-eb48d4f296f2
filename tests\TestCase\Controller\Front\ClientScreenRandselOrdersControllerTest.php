<?php

namespace Controller\Front;

use App\Enums\EntityFields\EClientOrderForm;
use App\Test\TestCase\Controller\ApiTestCase;
use Cake\I18n\FrozenTime;
use Cake\Utility\Hash;

class ClientScreenRandselOrdersControllerTest extends ApiTestCase
{

    /**
     * Test edit method
     *
     * @return void
     * @uses \App\Controller\Front\ClientScreenRandselOrdersController::edit()
     * php ./vendor/phpunit/phpunit/phpunit --filter "testEdit" ./tests/TestCase/Controller/Front/ClientScreenRandselOrdersControllerTest.php
     */
    public function testEdit(): void
    {
        $now = FrozenTime::now();
        $this->setFrontAuthorized(true);
        $this->put('/front/client-screen-randsel-orders/' . $now->format("YmdHis") . '.json', [
            'randsel_orders' => [
                [
                    'id' => 1,
                    'maker_id' => 'xxx',
                    'member_id' => 'xxx',
                    'product_id' => 'xxx',
                    'status' => 1,
                ],
                [
                    'id' => 2,
                    'maker_id' => 'xxx',
                    'member_id' => 'xxx',
                    'product_id' => 'xxx',
                    'status' => 2,
                ],
            ],
        ]);
        $response = $this->bodyToArray($this->_response->getBody());
        debug($response);
        $this->assertResponseOk(print_r(Hash::flatten($response), true));
    }
}
