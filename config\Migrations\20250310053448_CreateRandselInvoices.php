<?php

declare(strict_types=1);

use Migrations\AbstractMigration;

class CreateRandselInvoices extends AbstractMigration
{
    /**
     * Change Method.
     *
     * More information on this method is available here:
     * https://book.cakephp.org/phinx/0/en/migrations.html#the-change-method
     * @return void
     */
    public function change(): void
    {
        $table = $this->table('randsel_invoices', ['comment' => 'ランドセル請求金額管理']);
        $table->addColumn('maker_id', 'integer', [
            'null' => false,
            'comment' => 'メーカーID',
            'default' => null,
            'limit' => null,
        ])
            ->addColumn('product_id', 'integer', [
                'null' => false,
                'comment' => '商品ID',
                'default' => null,
                'limit' => null,
            ])
            ->addColumn('billing_year_month', 'string', [
                'null' => false,
                'comment' => '請求年月 (YYYY-MM)',
                'default' => null,
                'limit' => 7,
            ])
            ->addColumn('total_amount', 'integer', [
                'null' => false,
                'comment' => '請求金額',
                'default' => 0,
                'limit' => null,
            ])
            ->addColumn('adjustment_amount', 'integer', [
                'null' => false,
                'comment' => '調整金額',
                'default' => 0,
                'limit' => null,
            ])
            ->addColumn('invoice_amount', 'integer', [
                'null' => false,
                'comment' => '請求書発行金額 (total_amount + adjustment_amount)',
                'default' => 0,
                'limit' => null,
            ])
            ->addColumn('created', 'datetime', [
                'null' => true,
                'comment' => '作成日時',
                'default' => 'CURRENT_TIMESTAMP',
                'limit' => null,
            ])
            ->addColumn('modified', 'datetime', [
                'null' => true,
                'comment' => '更新日時',
                'default' => 'CURRENT_TIMESTAMP',
                'limit' => null,
            ])
            ->addPrimaryKey(['id'])
            ->addIndex(
                ['maker_id', 'billing_year_month', 'product_id'],
                ['unique' => true, 'name' => 'uniq_maker_billing_year_month_product']
            )
            ->addIndex(
                ['maker_id'],
                ['name' => 'idx_maker_id']
            )
            ->addIndex(
                ['billing_year_month'],
                ['name' => 'idx_billing_year_month']
            )
            ->addIndex(
                ['product_id'],
                ['name' => 'idx_product_id']
            )
            ->create();
    }
}
