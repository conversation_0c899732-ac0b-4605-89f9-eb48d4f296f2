<?php

namespace App\Form\Front;

use App\Enums\EFormFieldType;
use App\Enums\EntityFields\ELogin;
use App\Enums\EntityFields\EPasswordReminder;
use App\Enums\EntityFields\ESchoolBagForm;
use App\Form\ApiForm;
use App\Validation\Validator\UserPasswordReminderFormValidator;
use Cake\Form\Schema;
use Cake\Validation\Validator;

class UserPasswordRemindersEditForm extends ApiForm
{

    public function _buildSchema(Schema $schema): Schema
    {
        $schema->addField(EPasswordReminder::TOKEN->value, EFormFieldType::STRING->value);
        $schema->addField(ESchoolBagForm::LOGIN_PWD->value, EFormFieldType::STRING->value);
        return $schema;
    }

    public function validationDefault(Validator $validator): Validator
    {
        return UserPasswordReminderFormValidator::buildValidator($validator, [
            EPasswordReminder::TOKEN->value,
            ESchoolBagForm::LOGIN_PWD->value,
        ]);
    }

    protected function _getAllowFieldsPath(): array
    {
        return [];
    }
}
