<?php

namespace <PERSON>rok<PERSON>\ApiModel;

use App\Enums\EntityFields\ELogin;
use App\Kuroko\ApiModel\KurokoApiDynamic\Logins;
use App\Kuroko\ApiModel\KurokoApiDynamic\Members;
use App\Kuroko\Entity\AccessToken;
use App\Kuroko\Entity\Member;
use App\Test\TestCase\Controller\ApiTestCase;

class LoginsTest extends ApiTestCase
{
    // php ./vendor/phpunit/phpunit/phpunit --filter "testLogins" ./tests/TestCase/Kuroko/ApiModel/LoginsTest.php
    public function testLogins()
    {
        $logins = new Logins();
        $response = $logins->login([
            ELogin::LOGIN_EMAIL->value => "<EMAIL>",
            ELogin::LOGIN_PWD->value => "Coverme@test.com1"
        ]);
        debug(json_encode($response));


        $response = $logins->token($response->getGrantToken());
        debug(json_encode($response));
        $this->assertTrue(true);

        $members = new Members();

        $member = $members->me($response, true);
        debug(json_encode($member));
    }

    // php ./vendor/phpunit/phpunit/phpunit --filter "testLogout" ./tests/TestCase/Kuroko/ApiModel/LoginsTest.php
    public function testLogout()
    {
        $data = [
            'access_token' => [
                'value' => 'OTZiMzJkY2U1Y2E3NTlmMmVjN2ZmNTE0NjMyMzI2MGY2Mjk1NTU3MzZhNTlkOWRlZmRjOWZmNmZiZTI2NjM3MprRufcj4UOAbBut1UNOEZGIuCqmUrif4LelgbpFTVjQTcuRPzVq97J2EhpiArqR5iBmRveQUXFX+tnU9c2aNxr/ZrvetqYXu1H7vkM2A+R/qTlL/ogvL9Ihf+/sqQDusA==',
            ]
        ];
        $accessToken = new AccessToken($data);
        $logins = new Logins();

        $response = $logins->logout($accessToken);
        debug($response);

        $this->assertTrue(true);
    }

    // php ./vendor/phpunit/phpunit/phpunit --filter "testReminder" ./tests/TestCase/Kuroko/ApiModel/LoginsTest.php
    public function testReminder()
    {
        $logins = new Logins();
        $response = $logins->reminder([
            ELogin::LOGIN_EMAIL->value => "<EMAIL>",
        ]);
        debug(json_encode($response));

        $this->assertTrue(true);
    }

    // php ./vendor/phpunit/phpunit/phpunit --filter "testResetPassword" ./tests/TestCase/Kuroko/ApiModel/LoginsTest.php
    public function testResetPassword()
    {
        $member = (new Member([
            "id" => 8,
        ]))->setAccessToken($this->getTestToken());


        $logins = new Logins();

        $response = $logins->resetPassword($member, [
            ELogin::LOGIN_EMAIL->value => "<EMAIL>",
        ]);
        debug($response);

        $this->assertTrue(true);
    }
}
