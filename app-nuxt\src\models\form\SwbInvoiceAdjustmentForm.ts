export class SwbInvoiceAdjustmentForm {
    protected _id: number;
    protected _billingYearMonth: string;
    protected _makerId: number | null;
    protected _productId: number | null;
    protected _adjustmentUnitPrice: number | null;
    protected _adjustmentQuantity: number | null;
    protected _adjustmentNote: string | null;
    protected _status: number | null;
    protected _isChanged: boolean;
    constructor(data?: Partial<SwbInvoiceAdjustmentForm>) {
        this._id = 0;
        this._billingYearMonth = "";
        this._makerId = null;
        this._productId = null;
        this._adjustmentUnitPrice = null;
        this._adjustmentQuantity = null;
        this._adjustmentNote = "";
        this._status = null;
        this._isChanged = false;
        Object.assign(this, data);
    }

    get id(): number {
        return this._id;
    }

    set id(value: number) {
        this._id = value;
    }

    get billingYearMonth(): string {
        return this._billingYearMonth;
    }

    set billingYearMonth(value: string) {
        if (this._billingYearMonth !== value) {
            this._isChanged = true;
        }
        this._billingYearMonth = value;
    }

    get makerId(): number | null {
        return this._makerId;
    }

    set makerId(value: number | null) {
        if (this._makerId !== value) {
            this._isChanged = true;
        }
        this._makerId = value;
    }

    get productId(): number | null {
        return this._productId;
    }

    set productId(value: number | null) {
        if (this._productId !== value) {
            this._isChanged = true;
        }
        this._productId = value;
    }

    get adjustmentUnitPrice(): number | null {
        return this._adjustmentUnitPrice;
    }

    set adjustmentUnitPrice(value: number | null) {
        if (this._adjustmentUnitPrice !== value) {
            this._isChanged = true;
        }
        this._adjustmentUnitPrice = value;
    }

    get adjustmentQuantity(): number | null {
        return this._adjustmentQuantity;
    }

    set adjustmentQuantity(value: number | null) {
        if (this._adjustmentQuantity !== value) {
            this._isChanged = true;
        }
        this._adjustmentQuantity = value;
    }

    get adjustmentNote(): string | null {
        return this._adjustmentNote;
    }

    set adjustmentNote(value: string | null) {
        if (this._adjustmentNote !== value) {
            this._isChanged = true;
        }
        this._adjustmentNote = value;
    }

    get status(): number | null {
        return this._status;
    }

    set status(value: number | null) {
        this._status = value;
    }

    get isChanged(): boolean {
        return this._isChanged;
    }

    set isChanged(value: boolean) {
        this._isChanged = value;
    }

    get data(): Record<string, string | number | string[] | number[] | null> {
        return {
            billing_year_month: this._billingYearMonth,
            maker_id: this.makerId,
            product_id: this.productId,
            adjustment_unit_price: this.adjustmentUnitPrice,
            adjustment_quantity: this.adjustmentQuantity,
            adjustment_note: this.adjustmentNote,
        };
    }

    get isAddable(): boolean {
        return (
            this._makerId !== null &&
            this._productId !== null &&
            this._adjustmentUnitPrice !== null &&
            this._adjustmentQuantity !== null
        );
    }

    get isEditable(): boolean {
        return this._isChanged;
    }
}
