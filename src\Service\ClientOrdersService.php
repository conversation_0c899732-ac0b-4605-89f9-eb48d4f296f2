<?php

namespace App\Service;

use App\Kuroko\ApiModel\KurokoApiDynamic\ECOrders;
use App\Kuroko\ApiModel\KurokoApiDynamic\Inquiries;
use App\Kuroko\ApiModel\KurokoApiDynamic\Members;
use App\Kuroko\Entity\IKurokoEntity;
use App\Kuroko\Entity\Inquiry;
use App\Kuroko\Entity\Member;
use App\Kuroko\Entity\Order;
use Cake\Datasource\EntityInterface;
use Cake\Utility\Hash;

class ClientOrdersService extends OrdersService
{

    public function index(array $data = []): array|EntityInterface|null|IKurokoEntity
    {
        $ecOrders = new ECOrders();
        /** @var Member $member */
        $member = $this->getIdentity();
        /** @var Order[] $orderList */
        $orderList = $ecOrders->listForMaker($member, $data);
        $ids = [];
        foreach ($orderList as $order) {
            if ($memberId = $order->getMemberId()) {
                $ids[$memberId] = $memberId;
            }
        }
        $memberIdToPre = $this->getMemberToFormMap($ids);
        $formToBody = $this->getFormBodyMap();
        foreach ($orderList as $order) {
            if ($memberId = $order->getMemberId()) {
                if ($pre = Hash::get($memberIdToPre, $memberId)) {
                    if ($body = Hash::get($formToBody, $pre['preFormId'])) {
                        $order->setOrderName01Kana($pre['kana1'] ?? '');
                        $order->setOrderName02Kana($pre['kana2'] ?? '');
                        $order->setSendEmailFlag($pre['sendEmailFlag'] ?? null);
                        $order->setInquiryBody($body);
                    }
                }

            }
        }
        return $orderList;
    }

    /**
     * formIdがキーで、値がbody文字列
     * @return array
     */
    protected function getFormBodyMap(): array
    {
        $inquiries = new Inquiries();
        /** @var Inquiry[] $inquiryList */
        $inquiryList = $inquiries->listForMaker();
        $formToBody = [];
        foreach ($inquiryList as $item) {
            $formToBody[$item->getId()] = $item->getBody();
        }
        return $formToBody;
    }

    /**
     * 会員IDがキーで値がformId(pre_form_id)
     * @param array $memberIds メンバーIDの配列
     * @return array
     */
    protected function getMemberToFormMap(array $memberIds): array
    {
        //        debug($ids);
        $members = new Members();
        /** @var Member[] $memberList */
        $memberList = $members->listForMakerByIds(array_values($memberIds));
        $memberIdToPre = [];

        foreach ($memberList as $item) {
            if ($preId = $item->getPreFormid()) {
                $memberIdToPre[$item->getId()] = [
                    'preFormId' => $preId,
                    'kana1' => $item->getName1Kana(),
                    'kana2' => $item->getName2Kana(),
                    'sendEmailFlag' => $item->getSendEmailFlag(),
                ];
            }
        }
        return $memberIdToPre;
    }
}
