<?php

namespace App\Validation\Validator;

use App\Enums\EntityFields\ELogin;
use App\Enums\EntityFields\ESchoolBagForm;
use App\Enums\EntityFields\EPasswordReminder;
use App\Enums\EValidationErrorMessage;
use App\Validation\IValidator;
use App\Validation\ValidatorTrait;
use Cake\Validation\Validator;

class UserPasswordReminderFormValidator implements IValidator
{
    use ValidatorTrait;

    static public function validateEmail(string $field, Validator $validator, array $insertText): Validator
    {
        $validator
            ->maxLength($field, 50, EValidationErrorMessage::MAX_LENGTH->format(
                $insertText + [
                    'max' => 50
                ]
            ))
            ->requirePresence($field, true, EValidationErrorMessage::REQUIRE_PRESENCE->format($insertText))
            ->notEmptyString($field, EValidationErrorMessage::NOT_EMPTY_STRING->format($insertText));
        return $validator;
    }

    static public function validateToken(string $field, Validator $validator, array $insertText): Validator
    {
        $validator
            ->requirePresence($field, true, EValidationErrorMessage::REQUIRE_PRESENCE->format($insertText))
            ->notEmptyString($field, EValidationErrorMessage::NOT_EMPTY_STRING->format($insertText));
        return $validator;
    }

    static public function validateLoginPwd(string $field, Validator $validator, array $insertText): Validator
    {
        $validator
            ->requirePresence($field, true, EValidationErrorMessage::REQUIRE_PRESENCE->format($insertText))
            ->notEmptyString($field, EValidationErrorMessage::NOT_EMPTY_STRING->format($insertText));
        return $validator;
    }


    static private function email(Validator $validator, array $options = []): Validator
    {
        $insertText = [
            'field' => ELogin::LOGIN_EMAIL->description(),
        ];
        $field = ELogin::LOGIN_EMAIL->value;
        return static::validateEmail($field, $validator, $insertText);
    }

    static private function token(Validator $validator, array $options = []): Validator
    {
        $insertText = [
            'field' => EPasswordReminder::TOKEN->description(),
        ];
        $field = EPasswordReminder::TOKEN->value;
        return static::validateToken($field, $validator, $insertText);
    }

    static private function loginPwd(Validator $validator, array $options = []): Validator
    {
        $insertText = [
            'field' => ESchoolBagForm::LOGIN_PWD->description(),
        ];
        $field = ESchoolBagForm::LOGIN_PWD->value;
        return static::validateLoginPwd($field, $validator, $insertText);
    }
}
