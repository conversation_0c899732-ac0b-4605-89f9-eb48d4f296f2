<?php
declare(strict_types=1);

namespace App\Test\Fixture;

use Cake\TestSuite\Fixture\TestFixture;

/**
 * MakersFixture
 */
class MakersFixture extends TestFixture
{
    /**
     * Init method
     *
     * @return void
     */
    public function init(): void
    {
        $this->records = [
            [
                'id' => 1,
                'name' => 'サンプルメーカー1',
                'description' => 'サンプルメーカー1の説明',
                'maker_image_url' => 'https://example.com/maker1.jpg',
                'maker_features_html' => '<p>メーカー1の特徴</p>',
                'other_features_html' => '<p>その他の機能</p>',
                'billing_address' => '東京都渋谷区1-1-1',
                'customer_code' => 'MAKER001',
                'customer_name' => 'サンプル顧客1',
                'billing_cycle' => 1,
                'contact_name' => '田中太郎',
                'deleted' => null,
                'created' => '2024-01-01 00:00:00',
                'modified' => '2024-01-01 00:00:00',
            ],
            [
                'id' => 2,
                'name' => 'サンプルメーカー2',
                'description' => 'サンプルメーカー2の説明',
                'maker_image_url' => 'https://example.com/maker2.jpg',
                'maker_features_html' => '<p>メーカー2の特徴</p>',
                'other_features_html' => '<p>その他の機能</p>',
                'billing_address' => '大阪府大阪市2-2-2',
                'customer_code' => 'MAKER002',
                'customer_name' => 'サンプル顧客2',
                'billing_cycle' => 2,
                'contact_name' => '佐藤花子',
                'deleted' => null,
                'created' => '2024-01-02 00:00:00',
                'modified' => '2024-01-02 00:00:00',
            ],
        ];
        parent::init();
    }
}
