<?php

namespace Controller\Front\Swb;

use App\Enums\EntityFields\ESwbOrderForm;
use App\Test\TestCase\Controller\ApiTestCase;
use Cake\Utility\Hash;

class SwbMonthlyInvoicesControllerTest extends ApiTestCase
{

    /**
     * Test index method
     *
     * @return void
     * @uses \App\Controller\Front\SwbMonthlyInvoicesController::index()
     * php ./vendor/phpunit/phpunit/phpunit --filter "testIndex" ./tests/TestCase/Controller/Front/Swb/SwbMonthlyInvoicesControllerTest.php
     */
    public function testIndex()
    {
        $this->setFrontAuthorized(true);
        $this->get('/front/swb-monthly-invoices.json?' . http_build_query([
            // ESwbOrderForm::MAKER_ID->value => 1,
            ESwbOrderForm::FROM->value => "2024-10-20",
            ESwbOrderForm::TO->value => "2025-01-22",
        ]));
        $response = $this->bodyToArray($this->_response->getBody());
        debug($response);
        $this->assertResponseOk(print_r(Hash::flatten($response), true));
    }
}
