<?php

declare(strict_types=1);

namespace App\Model\Entity;

use Cake\ORM\Entity;

/**
 * RandselInvoiceAdjustment Entity
 *
 * @property int $id
 * @property int $maker_id
 * @property int $product_id
 * @property string $billing_year_month
 * @property int $adjustment_unit_price
 * @property int $adjustment_quantity
 * @property string|null $adjustment_note
 * @property int $status
 * @property int|null $confirmed_by
 * @property \Cake\I18n\FrozenTime|null $confirmed
 * @property int|null $created_by
 * @property \Cake\I18n\FrozenTime|null $created
 * @property \Cake\I18n\FrozenTime|null $modified
 */
class RandselInvoiceAdjustment extends Entity
{
    /**
     * Fields that can be mass assigned using newEntity() or patchEntity().
     *
     * Note that when '*' is set to true, this allows all unspecified fields to
     * be mass assigned. For security purposes, it is advised to set '*' to false
     * (or remove it), and explicitly make individual fields accessible as needed.
     *
     * @var array<string, bool>
     */
    protected $_accessible = [
        'maker_id' => true,
        'product_id' => true,
        'billing_year_month' => true,
        'adjustment_unit_price' => true,
        'adjustment_quantity' => true,
        'adjustment_note' => true,
        'status' => true,
        'confirmed_by' => true,
        'confirmed' => true,
        'created_by' => true,
        'created' => true,
        'modified' => true,
        // 'randsel_invoice_adjustment_histories' => true,
        'histories' => true,
    ];
}
