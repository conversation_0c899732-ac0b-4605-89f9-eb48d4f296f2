<?php
declare(strict_types=1);

namespace App\Model\Table;

use Cake\ORM\Query;
use Cake\ORM\RulesChecker;
use Cake\ORM\Table;
use Cake\Validation\Validator;

/**
 * ライブ配信メーカーテーブル
 */
class LiveStreamMakersTable extends Table
{
    public function initialize(array $config): void
    {
        parent::initialize($config);

        $this->setTable('live_stream_makers');
        $this->setDisplayField('id');
        $this->setPrimaryKey('id');

        // アソシエーション
        $this->belongsTo('LiveStreams', [
            'foreignKey' => 'live_stream_id',
            'joinType' => 'INNER',
        ]);
        
        $this->belongsTo('Makers', [
            'foreignKey' => 'maker_id',
            'joinType' => 'INNER',
        ]);
    }

    public function validationDefault(Validator $validator): Validator
    {
        $validator
            ->integer('live_stream_id')
            ->requirePresence('live_stream_id', 'create')
            ->notEmptyString('live_stream_id');

        $validator
            ->integer('maker_id')
            ->requirePresence('maker_id', 'create')
            ->notEmptyString('maker_id');

        return $validator;
    }

    public function buildRules(RulesChecker $rules): RulesChecker
    {
        $rules->add($rules->existsIn(['live_stream_id'], 'LiveStreams'), __('指定されたライブ配信が存在しません'));
        $rules->add($rules->existsIn(['maker_id'], 'Makers'), __('指定されたメーカーが存在しません'));
        
        // 同一ライブ配信に同一メーカーが重複登録されないようにチェック
        $rules->add($rules->isUnique(['live_stream_id', 'maker_id']), __('このメーカーは既にこのライブ配信に登録されています'));

        return $rules;
    }

    /**
     * ライブ配信別にメーカーを取得するファインダー
     */
    public function findByLiveStream(Query $query, array $options): Query
    {
        $liveStreamId = $options['live_stream_id'] ?? null;
        if ($liveStreamId !== null) {
            $query->where(['live_stream_id' => $liveStreamId]);
        }
        return $query;
    }

    /**
     * メーカー別にライブ配信を取得するファインダー
     */
    public function findByMaker(Query $query, array $options): Query
    {
        $makerId = $options['maker_id'] ?? null;
        if ($makerId !== null) {
            $query->where(['maker_id' => $makerId]);
        }
        return $query;
    }

    /**
     * 指定ライブ配信の参加メーカー一覧を取得
     */
    public function getMakersByLiveStream(int $liveStreamId): Query
    {
        return $this->find()
            ->where(['live_stream_id' => $liveStreamId])
            ->contain(['Makers'])
            ->order(['Makers.name' => 'ASC']);
    }

    /**
     * 指定メーカーの参加ライブ配信一覧を取得
     */
    public function getLiveStreamsByMaker(int $makerId): Query
    {
        return $this->find()
            ->where(['maker_id' => $makerId])
            ->contain(['LiveStreams'])
            ->order(['LiveStreams.start_datetime' => 'ASC']);
    }

    /**
     * ライブ配信とメーカーの関連を追加
     */
    public function addMakerToLiveStream(int $liveStreamId, int $makerId): bool
    {
        // 既に関連が存在するかチェック
        $existing = $this->find()
            ->where([
                'live_stream_id' => $liveStreamId,
                'maker_id' => $makerId
            ])
            ->first();

        if ($existing) {
            return false; // 既に存在する
        }

        $liveStreamMaker = $this->newEntity([
            'live_stream_id' => $liveStreamId,
            'maker_id' => $makerId
        ]);

        return $this->save($liveStreamMaker) !== false;
    }

    /**
     * ライブ配信とメーカーの関連を削除
     */
    public function removeMakerFromLiveStream(int $liveStreamId, int $makerId): bool
    {
        $liveStreamMaker = $this->find()
            ->where([
                'live_stream_id' => $liveStreamId,
                'maker_id' => $makerId
            ])
            ->first();

        if (!$liveStreamMaker) {
            return false; // 関連が存在しない
        }

        return $this->delete($liveStreamMaker);
    }
}
