<?php
declare(strict_types=1);

namespace App\Test\TestCase\Model\Table;

use App\Model\Table\MakersTable;
use Cake\TestSuite\TestCase;

/**
 * App\Model\Table\MakersTable Test Case
 */
class MakersTableTest extends TestCase
{
    /**
     * Test subject
     *
     * @var \App\Model\Table\MakersTable
     */
    protected $Makers;

    /**
     * Fixtures
     *
     * @var array<string>
     */
    protected $fixtures = [
        'app.Makers',
        'app.MakerStores',
        'app.Brands',
        'app.Products',
        'app.MakerUsers',
        'app.ExhibitionMakers',
        'app.LiveStreamMakers',
        'app.RandselOrders',
        'app.RandselInvoices',
        'app.RandselInvoiceAdjustments',
    ];

    /**
     * setUp method
     *
     * @return void
     */
    public function setUp(): void
    {
        parent::setUp();
        $config = $this->getTableLocator()->exists('Makers') ? [] : ['className' => MakersTable::class];
        $this->Makers = $this->getTableLocator()->get('Makers', $config);
    }

    /**
     * tearDown method
     *
     * @return void
     */
    public function tearDown(): void
    {
        unset($this->Makers);

        parent::tearDown();
    }

    /**
     * Test validationDefault method
     *
     * @return void
     */
    public function testValidationDefault(): void
    {
        $validator = $this->Makers->validationDefault($this->Makers->getValidator());
        
        // 必須フィールドのテスト
        $this->assertTrue($validator->hasField('name'));
        $this->assertTrue($validator->isPresenceRequired('name', true));
        
        $this->assertTrue($validator->hasField('billing_cycle'));
        $this->assertTrue($validator->isPresenceRequired('billing_cycle', true));
    }

    /**
     * Test buildRules method
     *
     * @return void
     */
    public function testBuildRules(): void
    {
        $rules = $this->Makers->buildRules($this->Makers->rulesChecker());
        
        // ユニーク制約のテスト用データ
        $maker1 = $this->Makers->newEntity([
            'name' => 'テストメーカー',
            'billing_cycle' => 1,
            'customer_code' => 'TEST001'
        ]);
        
        $result = $this->Makers->save($maker1);
        $this->assertNotFalse($result);
        
        // 同じ名前のメーカーを作成（ユニーク制約違反）
        $maker2 = $this->Makers->newEntity([
            'name' => 'テストメーカー',
            'billing_cycle' => 2,
            'customer_code' => 'TEST002'
        ]);
        
        $result = $this->Makers->save($maker2);
        $this->assertFalse($result);
        $this->assertNotEmpty($maker2->getErrors());
    }

    /**
     * Test findActive finder
     *
     * @return void
     */
    public function testFindActive(): void
    {
        // アクティブなメーカーを作成
        $activeMaker = $this->Makers->newEntity([
            'name' => 'アクティブメーカー',
            'billing_cycle' => 1,
        ]);
        $this->Makers->save($activeMaker);
        
        // 削除されたメーカーを作成
        $deletedMaker = $this->Makers->newEntity([
            'name' => '削除されたメーカー',
            'billing_cycle' => 1,
            'deleted' => new \Cake\I18n\FrozenTime()
        ]);
        $this->Makers->save($deletedMaker);
        
        // findActiveでアクティブなメーカーのみ取得
        $activeMakers = $this->Makers->find('active')->toArray();
        
        $this->assertCount(1, $activeMakers);
        $this->assertEquals('アクティブメーカー', $activeMakers[0]->name);
    }

    /**
     * Test associations
     *
     * @return void
     */
    public function testAssociations(): void
    {
        $this->assertTrue($this->Makers->hasMany('MakerStores'));
        $this->assertTrue($this->Makers->hasMany('Brands'));
        $this->assertTrue($this->Makers->hasMany('Products'));
        $this->assertTrue($this->Makers->hasMany('MakerUsers'));
        $this->assertTrue($this->Makers->hasMany('ExhibitionMakers'));
        $this->assertTrue($this->Makers->hasMany('LiveStreamMakers'));
        $this->assertTrue($this->Makers->hasMany('RandselOrders'));
        $this->assertTrue($this->Makers->hasMany('RandselInvoices'));
        $this->assertTrue($this->Makers->hasMany('RandselInvoiceAdjustments'));
    }
}
