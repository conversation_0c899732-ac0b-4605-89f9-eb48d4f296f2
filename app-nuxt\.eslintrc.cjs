module.exports = {
    root: true, // この設定ファイルをESLintの最上位の設定として扱う
    env: {
        browser: true, // ブラウザ環境での実行を想定
        node: false, // Node.js環境での実行を想定
    },
    parser: "vue-eslint-parser", // Vue.js用のESLintパーサーを指定
    parserOptions: {
        parser: "@typescript-eslint/parser", // スクリプトはTypeScriptとして解析
        ecmaVersion: 2020, // ECMAScript 2020を解析対象とする
        sourceType: "module", // モジュールとしてのソースコードを解析（import/exportを利用）
        ecmaFeatures: {
            jsx: true, // JSX構文の解析を有効にする
        },
    },
    extends: [
        "eslint:recommended", // ESLintの推奨設定を適用
        "plugin:@typescript-eslint/recommended", // TypeScript用の推奨設定を適用
        "plugin:vue/vue3-recommended", // Vue 3向けの推奨設定を適用
        "plugin:prettier/recommended", // Prettierとの統合推奨設定を適用
        "prettier", // Prettierの設定を適用
    ],
    plugins: ["unused-imports", "@typescript-eslint"], // 使用するプラグイン
    rules: {
        //   "@typescript-eslint/no-unused-vars": "off", // TypeScriptの未使用変数ルールを無効
        "unused-imports/no-unused-imports": "error", // 未使用のインポートをエラーとする
        "unused-imports/no-unused-vars": [
            "warn", // 未使用の変数を警告とする
            {
                vars: "all", // すべての変数に適用
                varsIgnorePattern: "^_", // アンダースコアで始まる変数名を無視
                args: "after-used", // 使用された後の引数にのみ適用
                argsIgnorePattern: "^_", // アンダースコアで始まる引数名を無視
            },
        ],
        "vue/multi-word-component-names": "off", // このルールを無効にする
        "@typescript-eslint/explicit-function-return-type": "off", // 関数の戻り値の型指定を強制しない
        "no-console": process.env.NODE_ENV === "production" ? "error" : "warn", // 本番環境ではconsoleの使用をエラー、それ以外では警告
        "no-debugger": process.env.NODE_ENV === "production" ? "error" : "warn", // 本番環境ではdebuggerの使用をエラー、それ以外では警告
        "prettier/prettier": "error", // Prettierのフォーマットに従わない場合はエラー
        "prefer-template": "error", // 文字列連結にテンプレートリテラルの使用を強制
    },
    overrides: [
        {
            files: ["*.ts", "*.js", "*.vue"], // このルールを適用するファイルの種類
            rules: {
                "@typescript-eslint/explicit-function-return-type": "error", // このファイルに限り戻り値の型指定を強制
            },
        },
    ],
};
