import { SwbOrderSearchForm } from "./SwbOrderSearchForm";
export class SwbInvoiceAdjustmentSearchForm extends SwbOrderSearchForm {
    protected _billingYearMonth: string;
    constructor() {
        super();
        this._billingYearMonth = this.getLastMonthFirstDayInJST().substring(
            0,
            7,
        );
        this._makerId = 0;
        this._isChanged = true;
    }

    get billingYearMonth(): string {
        return this._billingYearMonth;
    }

    set billingYearMonth(value: string) {
        if (this._billingYearMonth !== value) {
            this._isChanged = true;
        }
        this._billingYearMonth = value;
    }

    get data(): Record<string, string | number | string[] | number[]> {
        return {
            billing_year_month: this._billingYearMonth,
            // maker_id: this.makerId,
            // product_id: this.productId,
        };
    }
}
