import KurokoContentClient from "~~/src/lib/http/kuroko/KurokoContentClient";
import { Topic } from "~~/src/models/entry/Topic";
import { Model } from "~~/src/models/Model";

/**
 * @deprecated
 */
export default class Contents extends Model {
    public getList(params?: Record<string, string | number>): Promise<Topic[]> {
        const config = this.config;
        return new Promise((resolve) => {
            const kurokoContentListClient = KurokoContentClient.create(config);
            kurokoContentListClient
                .list<TResponseContent>(params)
                .then((response) => {
                    const topics = response.list.map((topic) => {
                        return new Topic(topic);
                    });
                    resolve(topics);
                });
        });
    }
}
