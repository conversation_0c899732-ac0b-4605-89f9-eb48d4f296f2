<?php
declare(strict_types=1);

namespace App\Enums;

/**
 * カタログタイプ定数
 */
enum CatalogType: int
{
    case PAPER = 1;      // 紙カタログ
    case DIGITAL = 2;    // デジタルカタログ

    /**
     * カタログタイプ名を取得
     */
    public function getName(): string
    {
        return match ($this) {
            self::PAPER => '紙',
            self::DIGITAL => 'デジタル',
        };
    }

    /**
     * 全てのカタログタイプを取得
     */
    public static function getAll(): array
    {
        return [
            self::PAPER->value => self::PAPER->getName(),
            self::DIGITAL->value => self::DIGITAL->getName(),
        ];
    }

    /**
     * 有効なカタログタイプの値を取得
     */
    public static function getValidValues(): array
    {
        return [self::PAPER->value, self::DIGITAL->value];
    }
}
