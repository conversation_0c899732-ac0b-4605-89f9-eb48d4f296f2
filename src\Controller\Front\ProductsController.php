<?php
declare(strict_types=1);

namespace App\Controller\Front;

use App\Controller\FrontController;
use App\Form\DisableParametersForm;
use App\Service\ProductsService;
use Cake\Event\Event;
use Cake\Event\EventInterface;
use Cake\Log\Log;

/**
 * Products Controller
 */
class ProductsController extends FrontController
{

    public function beforeFilter(EventInterface $event): void
    {
        parent::beforeFilter($event);
        $this->Authentication->addUnauthenticatedActions(['index']);
    }

    /**
     * Index method
     */
    public function index(): void
    {
        $this->_onSuccess(function (Event $event, array $products) {
            $this->_setSuccessResponse([
                'products' => $products
            ]);
        });
        $this->_onError(function (Event $event, array $errors) {
            Log::debug(print_r($errors, true));
        });

        $this->_index((new ProductsService())->setForm(
            new DisableParametersForm()
        ));
    }

}
