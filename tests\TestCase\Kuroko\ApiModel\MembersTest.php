<?php

namespace <PERSON><PERSON>o\ApiModel;

use App\Kuroko\ApiModel\KurokoApiDynamic\Members;
use App\Kuroko\Entity\Member;
use App\Test\TestCase\Controller\ApiTestCase;

class MembersTest extends ApiTestCase
{

    // php ./vendor/phpunit/phpunit/phpunit --filter "testMembersRegist" ./tests/TestCase/Kuroko/ApiModel/MembersTest.php
    public function testMembersRegist()
    {
        $members = new Members();
        $response = $members->regist([
            "name1" => "ext",
            "name2" => "check",
            "nickname" => "okd",
            "email" => "test-" . uniqid() . "@sowelleber.jp",
            "zip_code" => "1234567",
            "tdfk_cd" => "13",
            "address1" => "tokyo",
            "address2" => "adachi",
            "address3" => "xxx",
            "tel" => "0123-4444-5555",
            "email_send_ng_flg" => true,
            "tel_send_ng_flg" => true,
            "login_pwd" => uniqid(),
            "login_id" => "test-" . uniqid(),
            "hire_date" => "2024-04-12",
            "department" => "",
            "position" => "",
            "notes" => "",
            "validate_only" => false,
            "auto_login" => 0,
            "status" => Member::STATUS_UNVERIFIED,
            "pre_form_id" => 3
        ]);
        debug(json_encode($response));

        $this->assertTrue(true);
    }

    // php ./vendor/phpunit/phpunit/phpunit --filter "testMemberRegistValidateOnly" ./tests/TestCase/Kuroko/ApiModel/MembersTest.php
    public function testMemberRegistValidateOnly()
    {
        $members = new Members();
        $response = $members->registValidateOnly([
            "name1" => "ext",
            "name2" => "check",
            "nickname" => "okd",
            "email" => "test-" . uniqid() . "@sowelleber.jp",
            "zip_code" => "1234567",
            "tdfk_cd" => "13",
            "address1" => "tokyo",
            "address2" => "adachi",
            "address3" => "xxx",
            "tel" => "0123-4444-5555",
            "email_send_ng_flg" => true,
            "tel_send_ng_flg" => true,
            "login_pwd" => uniqid(),
            "login_id" => "test-" . uniqid(),
            "hire_date" => "2024-04-12",
            "department" => "",
            "position" => "",
            "notes" => "",
            "validate_only" => false,
            "auto_login" => 0,
            "status" => Member::STATUS_UNVERIFIED,
            "pre_form_id" => 3
        ]);
        debug(json_encode($response));

        $this->assertTrue(true);
    }

    // php ./vendor/phpunit/phpunit/phpunit --filter "testMemberUpdate" ./tests/TestCase/Kuroko/ApiModel/MembersTest.php
    public function testMemberUpdate()
    {
        $members = new Members();
        $member = (new Member([
            "id" => 8,
        ]))->setAccessToken($this->getTestToken());
        $response = $members->update($member, [
            "name1" => "システムユーザ",
            "name2" => "システムユーザ",
        ]);

        debug($response);
        $this->assertTrue(true);
    }


    // php ./vendor/phpunit/phpunit/phpunit --filter "testMembersUpdateStatusAndGetFormInfo" ./tests/TestCase/Kuroko/ApiModel/MembersTest.php
    public function testMembersUpdateStatusAndGetFormInfo()
    {
        $status = Member::STATUS_VERIFIED;
        $members = new Members();
        $response = $members->updateStatusAndGetFormInfo($this->getTestToken(), $status);

        debug($response->getOrders());
        $this->assertTrue(true);
    }

    // php ./vendor/phpunit/phpunit/phpunit --filter "testMembersMe" ./tests/TestCase/Kuroko/ApiModel/MembersTest.php
    public function testMembersMe()
    {
        $members = new Members();
        $response = $members->me($this->getTestToken(), false);

        debug($response);
        $this->assertTrue(true);
    }

    // php ./vendor/phpunit/phpunit/phpunit --filter "testMembersSwbList" ./tests/TestCase/Kuroko/ApiModel/MembersTest.php
    public function testMembersSwbList()
    {
        $members = new Members();
        $member = (new Member([
            "id" => 8,
        ]))->setAccessToken($this->getTestToken());
        $response = $members->swbList($member);

        debug($response);
        $this->assertTrue(true);
    }
}
