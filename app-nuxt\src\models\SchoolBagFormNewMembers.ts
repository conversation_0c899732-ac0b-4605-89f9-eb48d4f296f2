import { Model } from "~/src/models/Model";
import SchoolBagFormNewMembersClient from "~/src/lib/http/coverme-front/SchoolBagFormNewMembersClient";
import { SCHOOL_BAG_FORM_FROM_ID } from "~/src/config";

export default class SchoolBagFormNewMembers extends Model {
    public add(data: TSchoolBagFormNewMembersAdd): Promise<boolean> {
        const config = this.config;
        return new Promise((resolve) => {
            SchoolBagFormNewMembersClient.create(config)
                .post<TSchoolBagFormNewMembersAdd, TResponseSuccess>(data)
                //@todo レスポンスをオブジェクトクラスにする、レスポンス項目は検討が必要
                .then(({ success }) => resolve(!!success))
                .catch(() => resolve(false));
        });
    }

    public registValidate(
        data: TSchoolBagFormNewMembersAdd,
    ): Promise<TApiResponse> {
        const config = this.config;
        return new Promise((resolve, reject) => {
            SchoolBagFormNewMembersClient.create(config)
                .put<TSchoolBagFormNewMembersAdd, TApiResponse>(
                    SCHOOL_BAG_FORM_FROM_ID.toString(),
                    data,
                )
                //@todo レスポンスをオブジェクトクラスにする、レスポンス項目は検討が必要
                .then((response) => resolve(response))
                .catch((error) => reject({ errors: [error.message] }));
        });
    }
}
