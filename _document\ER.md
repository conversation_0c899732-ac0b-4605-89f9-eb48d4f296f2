```mermaid
erDiagram
    GENERAL_USERS {
        int id PK "一般ユーザーID"
        string email UK "メールアドレス（ログイン用）"
        string password "パスワード（ハッシュ化）"
        datetime deleted "削除日時(論理削除)"
        datetime created "作成日時"
        datetime modified "更新日時"
    }
    
    SWB_USERS {
        int id PK "管理者ユーザーID"
        int authority_id "権限（100:admin, 101:admin_confirmer ）"
        string email UK "メールアドレス（ログイン用）"
        string password "パスワード（ハッシュ化）"
        datetime deleted "削除日時(論理削除)"
        datetime created "作成日時"
        datetime modified "更新日時"
    }

    MAKER_USERS {
        int id PK "ID"
        int maker_id FK "メーカーID (MAKERS.id)"
        string email UK "メールアドレス（ログイン用）"
        string password "パスワード（ハッシュ化）"
        datetime deleted "削除日時(論理削除)"
        datetime created "作成日時"
        datetime modified "更新日時"
    }

    USER_PROFILES {
        int id PK "プロフィールID"
        int general_user_id FK "一般ユーザーID"
        text last_name "姓（暗号化）"
        text first_name "名（暗号化）"
        text last_name_kana "姓カナ（暗号化）"
        text first_name_kana "名カナ（暗号化）"
        text zip_code "郵便番号（暗号化）"
        text prefecture_code "都道府県コード（暗号化）"
        text address1 "住所1（暗号化）"
        text address2 "住所2（暗号化）"
        text address3 "住所3（暗号化）"
        text tel "電話番号（暗号化）"
        boolean email_send_ng_flg "メール送信拒否フラグ"
        text notes "備考（暗号化）"
        datetime created "作成日時"
        datetime modified "更新日時"
    }
    
    USER_SURVEYS {
        int id PK "アンケートID"
        int general_user_id FK "一般ユーザーID"
        int year "年度"
        int child_sex "お子様の性別（1:男, 2:女, 3:その他）"
        int budget "ご予算（1:1万円未満～3万円, 2:3万円～7万円, 3:7万円～10万円, 4:10万円～30万円, 5:30万円以上）"
        boolean question_1_1 "きっかけ1"
        boolean question_1_2 "きっかけ2"
        boolean question_1_3 "きっかけ3"
        boolean question_1_4 "きっかけ4"
        boolean question_2_1 "重視するポイント1"
        boolean question_2_2 "重視するポイント2"
        boolean question_2_3 "重視するポイント3"
        boolean question_2_4 "重視するポイント4"
        boolean question_2_5 "重視するポイント5"
        boolean question_2_6 "重視するポイント6"
        boolean question_2_7 "重視するポイント7"
        boolean question_2_8 "重視するポイント8"
        boolean question_2_9 "重視するポイント9"
        boolean question_2_10 "重視するポイント10"
        boolean question_2_11 "重視するポイント11"
        datetime created "作成日時"
        datetime modified "更新日時"
    }
    
    USER_TOKENS {
        int id PK "トークンID"
        int general_user_id FK "一般ユーザーID"
        string token "トークン文字列"
        string type "トークン種別（password_reset, api_access等）"
        datetime expires "有効期限"
        datetime created "作成日時"
        datetime modified "更新日時"
    }
    
    SWB_USER_TOKENS {
        int id PK "トークンID"
        int swb_user_id FK "管理者ユーザーID"
        string token "トークン文字列"
        string type "トークン種別（password_reset, api_access等）"
        datetime expires "有効期限"
        datetime created "作成日時"
        datetime modified "更新日時"
    }
    
    MAKER_USER_TOKENS {
        int id PK "トークンID"
        int maker_user_id FK "メーカーユーザーID"
        string token "トークン文字列"
        string type "トークン種別（password_reset, api_access等）"
        datetime expires "有効期限"
        datetime created "作成日時"
        datetime modified "更新日時"
    }

    TEMPORARY_REGISTRATIONS {
        int id PK "仮登録ID"
        string email "メールアドレス"
        text mask_password "パスワード(暗号化)"
        text mask_email "メールアドレス(暗号化)"
        text profile_data "プロフィールデータJSON（暗号化）"
        text survey_data "アンケートデータJSON（暗号化）"
        json product_ids "商品ID配列（JSON形式: [1,2,3]）"
        string verification_token UK "認証トークン"
        boolean is_verified "認証済みフラグ（0: 未認証, 1: 認証済み）"
        datetime expires "有効期限"
        datetime created "作成日時"
        datetime modified "更新日時"
    }
    
    RANDSEL_ORDERS {
        int id PK "注文ID"
        int maker_id FK "メーカーID"
        int brand_id FK "ブランドID"
        int general_user_id FK "一般ユーザーID"
        int product_id FK "商品ID"
        int type "カタログタイプ(1:紙, 2:デジタル)"
        string product_name "商品名"
        int price "金額"
        int status "承認状態(1:承認, 2:否認, 0:承認待ち)"
        datetime status_modified "承認日時"
        int approval_type "承認タイプ(1:csv, 2:画面, 3:自動)"
        boolean is_confirmed "請求確定状態"
        datetime confirmed "請求確定日時"
        text name1 "姓（暗号化）"
        text name2 "名（暗号化）"
        text name1_hurigana "姓カナ（暗号化）"
        text name2_hurigana "名カナ（暗号化）"
        text zip_code "郵便番号（暗号化）"
        text tdfk_cd "都道府県コード（暗号化）"
        text address1 "住所1（暗号化）"
        text address2 "住所2（暗号化）"
        text address3 "住所3（暗号化）"
        text tel "電話番号（暗号化）"
        text email "メールアドレス（暗号化）"
        boolean email_send_ng_flg "メルマガ拒否フラグ"
        datetime created "作成日時"
        datetime modified "更新日時"
    }
    
    MAKERS {
        int id PK "メーカーID"
        string name "メーカー名"
        string description "メーカー説明"
        string maker_image_url "メーカー画像URL"
        text maker_features_html "メーカー特徴（HTML形式）"
        text other_features_html "その他機能（HTML形式）"
        string billing_address "請求先住所"
        string customer_code "顧客コード"
        string customer_name "顧客名"
        int billing_cycle "請求サイクル（1:末締め翌月末日払い, 2:末締め翌々10日, 3:末締め翌々20日）"
        string contact_name "担当者名"
        datetime deleted "削除日時(論理削除)"
        datetime created "作成日時"
        datetime modified "更新日時"
    }

    MAKER_STORES {
        int id PK "店舗ID"
        int maker_id FK "メーカーID"
        string name "店舗名"
        string zip_code "郵便番号"
        string prefecture "都道府県"
        string city "市区町村"
        string address "番地以降の住所"
        string building "建物名・部屋番号"
        string tel "電話番号"
        string email "メールアドレス"
        string business_hours "営業時間"
        string holiday "定休日"
        string access_info "アクセス情報"
        text description "店舗説明"
        datetime deleted "削除日時(論理削除)"
        datetime created "作成日時"
        datetime modified "更新日時"
    }

    BRANDS {
        int id PK "ブランドID"
        int maker_id FK "メーカーID"
        string name "ブランド名"
        text description "ブランド説明"
        string logo_url "ブランドロゴURL"
        string brand_image_url "ブランド画像URL"
        text brand_features_html "ブランド特徴（HTML形式）"
        text other_features_html "その他機能（HTML形式）"
        int established_year "設立年"
        int target_age_min "対象年齢（最小）"
        int target_age_max "対象年齢（最大）"
        tinyint target_gender "対象性別（1:男子, 2:女子, 3:男女共用）"
        int price_range_min "価格帯（最小）"
        int price_range_max "価格帯（最大）"
        string feature_tags "特徴タグ（カンマ区切り）"
        string website_url "公式ウェブサイトURL"
        int sort_order "表示順序"
        boolean is_premium "プレミアムブランドフラグ"
        datetime deleted "削除日時(論理削除)"
        datetime created "作成日時"
        datetime modified "更新日時"
    }

    PRODUCTS {
        int id PK "商品ID"
        int maker_id FK "メーカーID"
        int brand_id FK "ブランドID"
        boolean is_display "申し込みフォーム表示フラグ（true: 表示, false: 非表示）"
        int year "年度"
        string display_name "表示商品名（ランドセルカタログ）"
        text description_html "商品説明（HTML形式）"
        text note_html "備考（HTML形式）"
        text mask_image_description "画像に被せる説明文"
        string image_url "画像URL"
        int sort_order "表示順序"
        string pdf_url "pdfカタログURL"
        text price_range "価格帯（例：3万円台、4万円台）"
        text weight_range "重さ帯（例：900g台、1000g台）"
        int material "素材（1:人工皮革, 2:天然皮革, 3:合成皮革, 4:その他）"
        datetime deleted "削除日時(論理削除)"
        datetime created "作成日時"
        datetime modified "更新日時"
    }

    BUDGETS {
        int id PK "ID"
        int product_id FK "商品ID"
        int type "カタログタイプ(1:紙, 2:デジタル)"
        int price "単価"
        int budget_quantity "予算数量"
        boolean is_active "有効フラグ（true: 有効, false: 無効）"
        datetime start_date "予算開始日"
        datetime end_date "予算終了日"
        int priority "優先順位"
        datetime created "作成日時"
        datetime modified "更新日時"
    }

    EXHIBITIONS {
        int id PK "展示会ID"
        string title "展示会タイトル"
        text description "展示会説明"
        datetime start_datetime "開始日時"
        datetime end_datetime "終了日時"
        string venue_name "会場名"
        string address "住所"
        string access_info "アクセス情報"
        int capacity "定員数"
        boolean requires_reservation "予約必須フラグ"
        string reservation_url "予約URL"
        datetime deleted "削除日時(論理削除)"
        datetime created "作成日時"
        datetime modified "更新日時"
    }
    
    EXHIBITION_MAKERS {
        int id PK "ID"
        int exhibition_id FK "展示会ID"
        int maker_id FK "メーカーID"
        datetime created "作成日時"
        datetime modified "更新日時"
    }

    LIVE_STREAMS {
        int id PK "ライブ配信ID"
        string title "配信タイトル"
        text description "配信説明"
        datetime start_datetime "開始日時"
        datetime end_datetime "終了日時"
        string platform "配信プラットフォーム"
        string stream_url "配信URL"
        datetime deleted "削除日時(論理削除)"
        datetime created "作成日時"
        datetime modified "更新日時"
    }

    LIVE_STREAM_MAKERS {
        int id PK "ID"
        int live_stream_id FK "ライブ配信ID"
        int maker_id FK "メーカーID"
        datetime created "作成日時"
        datetime modified "更新日時"
    }

    RANDSEL_INVOICES {
        int id PK "請求ID"
        int maker_id FK "メーカーID"
        int product_id FK "商品ID"
        string billing_year_month "請求年月 (YYYY-MM)"
        int total_amount "請求金額"
        int total_quantity "請求数量"
        int adjustment_amount "調整金額"
        int adjustment_quantity "調整数量"
        text adjustment_note "調整備考内容"
        int invoice_amount "請求書発行金額 (total_amount + adjustment_amount)"
        datetime created "作成日時"
        datetime modified "更新日時"
    }

    RANDSEL_INVOICE_ADJUSTMENTS {
        int id PK "調整金額ID"
        int maker_id FK "メーカーID"
        int product_id FK "商品ID"
        string billing_year_month "請求年月 (YYYY-MM)"
        int adjustment_unit_price "調整単価"
        int adjustment_quantity "調整数量"
        text adjustment_note "備考内容"
        int status "状態(0: 未確定, 1: 確定, 2: 確定後変更あり)"
        int confirmed_by "確定者(kuroco member_id)"
        datetime confirmed "確定日時"
        int created_by "作成者(kuroco member_id)"
        datetime created "作成日時"
        datetime modified "更新日時"
    }

    RANDSEL_INVOICE_ADJUSTMENT_HISTORIES {
        int id PK "履歴ID"
        int invoice_adjustment_id FK "調整金額ID"
        tinyint action_type "操作種類 (1:作成, 2:変更, 3:確定)"
        json changes "変更内容 (JSON形式)"
        int created_by "操作者ID(kuroco member_id)"
        datetime created "作成日時"
    }
    
    GENERAL_USERS ||--o| USER_PROFILES : "has"
    GENERAL_USERS ||--o| USER_SURVEYS : "has"
    GENERAL_USERS ||--o{ USER_TOKENS : "has"
    GENERAL_USERS ||--o{ RANDSEL_ORDERS : "places"
    SWB_USERS ||--o{ SWB_USER_TOKENS : "has"
    MAKER_USERS ||--o{ MAKER_USER_TOKENS : "has"
    RANDSEL_ORDERS }o--|| MAKERS : "belongs to"
    RANDSEL_ORDERS }o--|| BRANDS : "belongs to"
    PRODUCTS }o--|| BRANDS : "belongs to"
    BRANDS }o--|| MAKERS : "belongs to"
    RANDSEL_ORDERS }o--|| PRODUCTS : "includes"
    RANDSEL_INVOICES }o--|| MAKERS : "belongs to"
    RANDSEL_INVOICES }o--|| PRODUCTS : "belongs to"
    RANDSEL_INVOICE_ADJUSTMENTS }o--|| MAKERS : "belongs to"
    RANDSEL_INVOICE_ADJUSTMENTS }o--|| PRODUCTS : "belongs to"
    RANDSEL_INVOICE_ADJUSTMENT_HISTORIES }o--|| RANDSEL_INVOICE_ADJUSTMENTS : "belongs to"
    MAKERS ||--o{ MAKER_STORES : "has"
    PRODUCTS ||--o{ BUDGETS : "has"
    MAKERS ||--o{ MAKER_USERS : "has staff"
    EXHIBITIONS ||--o{ EXHIBITION_MAKERS : "has"
    EXHIBITION_MAKERS }o--|| MAKERS : "belongs to"
    LIVE_STREAMS ||--o{ LIVE_STREAM_MAKERS : "has"
    LIVE_STREAM_MAKERS }o--|| MAKERS : "belongs to"
```