<script setup lang="ts">
import { ref } from "vue";
import { useRoute } from "vue-router";
import ThePasswordReset from "~/components/member/ThePasswordReset.vue";

// ルートを取得
const route = useRoute(); //this.$route

// クエリパラメータのtを格納する変数を定義
const queryToken = ref(route.query as object);
</script>

<template>
    <!-- <div>{{ $route.query.t }}</div> -->
    <the-password-reset :query-token="queryToken"></the-password-reset>
</template>

<style scoped></style>
