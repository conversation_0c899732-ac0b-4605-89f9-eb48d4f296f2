<script setup lang="ts">
import SchoolBagForm from "~~/src/form/SchoolBagForm";
import { reactive, ref, onMounted } from "vue";
import { useRoute } from "vue-router";
import { useRuntimeConfig } from "#app";
import SchoolBagFormNewMembers from "~/src/models/SchoolBagFormNewMembers";
import ClientZipCodeApiClient from "~/src/lib/http/zip-code/ClientZipCodeApiClient";
import moment from "moment";

const props = defineProps({
    productsData: {
        type: Array,
        required: true,
    },
});
//  http://localhost:3000/catalog/form/randsel
const schoolBagForm = reactive(new SchoolBagForm(props.productsData as []));

// ルートを取得
const route = useRoute(); //this.$route
const showListingForm = ref(false);
const showDirectForm = ref(false);
if (route.query.adtype === "google") {
    showListingForm.value = true;
} else if (route.query.direct === "y") {
    showDirectForm.value = true;
}

const addressHint = [
    "例：○－△－□",
    "※番地がない方は「無番地」をご入力ください。",
];

const stepEdit = "edit";
const stepConf = "conf";
const stepComplete = "complete";
const text = ref(
    "カタログは各メーカーから直接発送されます。\n※発送時期はメーカーによって異なります。",
);

const isLoading = ref(false);
const step = ref(stepEdit);
const config = useRuntimeConfig();
const formRef = ref();
const tdfkCdField = ref();
const address1Field = ref();

const autokana1 = ref();
const autokana2 = ref();
onMounted(async () => {
    if (process.browser) {
        const AutoKana = await import("vanilla-autokana");
        autokana1.value = AutoKana.bind("#name1", "#name1_hurigana", {
            katakana: true,
        });
        autokana2.value = AutoKana.bind("#name2", "#name2_hurigana", {
            katakana: true,
        });
    }
});

const handleName1Input = (): void => {
    schoolBagForm.clearError("name1");
    schoolBagForm.name1_hurigana = autokana1.value.getFurigana();
};

const handleName2Input = (): void => {
    schoolBagForm.clearError("name2");
    schoolBagForm.name2_hurigana = autokana2.value.getFurigana();
};

const toConf = async (): Promise<void> => {
    const form = formRef.value;
    const { valid, errors } = await form.validate();

    const customValidate = schoolBagForm.customValidate();

    Object.keys(errors).forEach((key) => {
        schoolBagForm.errors[key] = errors[key].errorMessages.join(",");
    });
    if (valid && customValidate) {
        isLoading.value = true;

        SchoolBagFormNewMembers.create(config)
            .registValidate(schoolBagForm.data)
            .then((response) => {
                if (response.errors && response.errors.length === 0) {
                    step.value = stepConf;
                } else {
                    response.errors.forEach((item) => {
                        schoolBagForm.errors[item.field] = item.message;
                    });
                }
            })
            .finally(() => {
                isLoading.value = false;
            });
    } else {
        // バリデーションが失敗した場合、ページのトップにスクロールする
        window.scrollTo({
            top: 0,
            behavior: "smooth", // スムーズにスクロールする場合
        });
    }
};

const toEdit = (): void => {
    step.value = stepEdit;
};

const toComplete = (): void => {
    isLoading.value = true;
    SchoolBagFormNewMembers.create(config)
        .add(schoolBagForm.data)
        .then((success) => {
            if (success) {
                step.value = stepComplete;
            } else {
                console.log("error");
            }
            isLoading.value = false;
        });
};

const setInitialDate = (): void => {
    if (schoolBagForm.child_birthdate === "") {
        schoolBagForm.child_birthdate = moment()
            .subtract(5, "years")
            .month(3)
            .date(1)
            .format("YYYY-MM-DD");
    }
};

// 郵便番号検索メソッド
const searchZipCode = async (): Promise<void> => {
    const zipCode = schoolBagForm.zip_code;

    // 都道府県と住所１の値とバリデーションチェックをクリアする
    tdfkCdField.value.reset();
    address1Field.value.reset();

    // 郵便番号検索APIを呼び出す
    ClientZipCodeApiClient.create(config)
        .get(zipCode)
        .then((response: TZipCodeApiResponse[]) => {
            if (response.length > 0) {
                schoolBagForm.tdfk_cd = response[0].prefCode;
                schoolBagForm.address1 = response[0].city + response[0].town;
            } else {
                schoolBagForm.errors.zip_code = "住所の検索が失敗しました。";
            }
        })
        .catch((error) => {
            console.error("郵便番号検索エラー:", error);
            schoolBagForm.errors.zip_code = "郵便番号の検索に失敗しました。";
        });
};
</script>

<template>
    <div>
        <!-- {{ schoolBagForm.order_product_ids }}<br />
        {{ schoolBagForm.data }}<br />
        {{ schoolBagForm.valid }} -->
        <template v-if="isLoading">
            <v-progress-circular indeterminate></v-progress-circular>
        </template>
        <template v-else>
            <template v-if="step === stepEdit">
                <template v-if="showDirectForm === true && $device.isMobile">
                    <v-img class="my-4" src="~/assets/directform_4.jpg">
                    </v-img>
                </template>
                <template v-else-if="showListingForm === true">
                    <v-img class="my-4" src="~/assets/listingform.png"> </v-img>
                </template>
                <template v-else>
                    <div class="text-h6 font-weight-bold pa-4">
                        2026年度ランドセルカタログ一括請求申込
                    </div>
                    <v-divider class="my-4"></v-divider>
                </template>
                <v-form
                    ref="formRef"
                    v-model="schoolBagForm.valid"
                    class="coverme-catalog-form"
                    validate-on="blur"
                >
                    <v-container class="border-md border-primary pa-3 pb-0">
                        <v-row
                            v-for="(error, key) in schoolBagForm.errors"
                            :key="key"
                        >
                            <v-alert v-if="error" type="error">
                                {{ error }}
                            </v-alert>
                        </v-row>
                        <v-row>
                            <v-col cols="12" class="pa-0">
                                <div
                                    class="text-h6 text-center font-weight-bold bg-primary text-white pa-1"
                                >
                                    カタログを選ぶ
                                </div>
                            </v-col>
                        </v-row>
                        <v-row>
                            <v-col
                                v-for="product of schoolBagForm.products"
                                :key="product.id"
                                class="coverme-catalog-checkbox"
                                cols="12"
                                md="6"
                            >
                                <v-card
                                    variant="outlined"
                                    class="coverme-card fill-height d-flex flex-column"
                                >
                                    <v-checkbox
                                        v-model="
                                            schoolBagForm.order_product_ids
                                        "
                                        density="comfortable"
                                        :disabled="
                                            product.display_type ===
                                            'sold_out'
                                        "
                                        :value="product.id"
                                        hide-details
                                        class="font-weight-bold"
                                    >
                                        <template #label>
                                            <div>
                                                <span
                                                    >{{ product.display_type }}<br
                                                /></span>
                                                <span
                                                    v-if="
                                                        product.id ==
                                                        41219
                                                    "
                                                    class="text-red bg-yellow"
                                                    >【恐竜好きならチェック】<br
                                                /></span>
                                                <span
                                                    v-if="
                                                        product.id ==
                                                        41221
                                                    "
                                                    class="text-red bg-yellow"
                                                    >【東海エリアの方はチェック】<br
                                                /></span>
                                                <span
                                                    v-if="
                                                        product.id ==
                                                        41215
                                                    "
                                                    class="text-red bg-yellow"
                                                    >【ふわりぃは軽い！最軽量880g～】<br
                                                /></span>

                                                {{ product.display_name }}
                                            </div>
                                        </template>
                                    </v-checkbox>
                                    <div class="mx-4 d-flex justify-center">
                                        <div style="width: 150px">
                                            <v-img
                                                :src="product.image_url"
                                                width="150"
                                                aspect-ratio="1"
                                            >
                                            </v-img>
                                            <!-- <div class="overlay">
                                                <span class="pause-text"
                                                    >一時停止</span
                                                >
                                            </div> -->
                                        </div>
                                    </div>
                                    <div class="ma-4 text-pre-wrap" v-html="product.description_html.replace(/\\r\\n/g, '<br>') || ''">
                                    </div>
                                    <div
                                        class="ma-4 mt-auto text-center text-primary"
                                    >
                                        {{ product.note_html }}
                                    </div>
                                </v-card>
                            </v-col>
                        </v-row>
                        <v-row class="ma-0 mt-3">
                            <div class="text-body-2 text-pre-wrap">
                                {{ text }}
                            </div>
                        </v-row>
                    </v-container>
                    <v-container class="border-md border-primary pa-3 mt-4">
                        <v-row>
                            <v-col cols="12" class="pa-0">
                                <div
                                    class="text-h6 text-center font-weight-bold bg-primary text-white pa-1"
                                >
                                    基本情報入力
                                </div>
                                <div class="float-right text-body-2">
                                    （<span class="text-red">*</span>は必須）
                                </div>
                            </v-col>
                        </v-row>
                        <v-row no-gutters class="mt-1">
                            <v-col cols="12" md="12">
                                <v-label
                                    >お名前[漢字]<span class="text-red"
                                        >*</span
                                    ></v-label
                                >
                            </v-col>
                        </v-row>
                        <v-row>
                            <v-col cols="12" md="6" class="py-1">
                                <v-text-field
                                    id="name1"
                                    v-model="schoolBagForm.name1"
                                    density="comfortable"
                                    :counter="10"
                                    :rules="schoolBagForm.name1_rules"
                                    :label="schoolBagForm.labelkey_map.name1"
                                    :error-messages="schoolBagForm.errors.name1"
                                    required
                                    @input="handleName1Input"
                                ></v-text-field>
                            </v-col>
                            <v-col cols="12" md="6" class="py-1">
                                <v-text-field
                                    id="name2"
                                    v-model="schoolBagForm.name2"
                                    density="comfortable"
                                    :counter="10"
                                    :rules="schoolBagForm.name2_rules"
                                    :label="schoolBagForm.labelkey_map.name2"
                                    :error-messages="schoolBagForm.errors.name2"
                                    required
                                    @input="handleName2Input"
                                >
                                </v-text-field>
                            </v-col>
                        </v-row>
                        <v-row no-gutters class="mt-2">
                            <v-col cols="12" md="12">
                                <v-label
                                    >お名前[フリガナ]<span class="text-red"
                                        >*</span
                                    ></v-label
                                >
                            </v-col>
                        </v-row>
                        <v-row>
                            <v-col cols="12" md="6" class="py-1">
                                <v-text-field
                                    id="name1_hurigana"
                                    v-model="schoolBagForm.name1_hurigana"
                                    density="comfortable"
                                    :counter="10"
                                    :rules="schoolBagForm.name1_hurigana_rules"
                                    :label="
                                        schoolBagForm.labelkey_map
                                            .name1_hurigana
                                    "
                                    :error-messages="
                                        schoolBagForm.errors.name1_hurigana
                                    "
                                    required
                                    @input="
                                        schoolBagForm.clearError(
                                            'name1_hurigana',
                                        )
                                    "
                                ></v-text-field>
                            </v-col>
                            <v-col cols="12" md="6" class="py-1">
                                <v-text-field
                                    id="name2_hurigana"
                                    v-model="schoolBagForm.name2_hurigana"
                                    density="comfortable"
                                    :counter="10"
                                    :rules="schoolBagForm.name2_hurigana_rules"
                                    :label="
                                        schoolBagForm.labelkey_map
                                            .name2_hurigana
                                    "
                                    :error-messages="
                                        schoolBagForm.errors.name2_hurigana
                                    "
                                    required
                                    @input="
                                        schoolBagForm.clearError(
                                            'name2_hurigana',
                                        )
                                    "
                                >
                                </v-text-field>
                            </v-col>
                        </v-row>
                        <v-row no-gutters class="mt-2">
                            <v-col cols="12" md="12">
                                <v-label>
                                    メールアドレス
                                    <span class="text-red">*</span>
                                </v-label>
                                <div class="text-body-2 text-accent">
                                    入力されたメールアドレスに認証用メールを送信します。受信可能なメールアドレスをご入力ください。
                                </div>
                            </v-col>
                        </v-row>
                        <v-row class="mt-0">
                            <v-col cols="12" md="6" class="py-1">
                                <v-text-field
                                    v-model="schoolBagForm.email"
                                    density="comfortable"
                                    :counter="50"
                                    :rules="schoolBagForm.email_rules"
                                    type="email"
                                    :error-messages="schoolBagForm.errors.email"
                                    required
                                    @input="schoolBagForm.clearError('email')"
                                >
                                </v-text-field>
                            </v-col>
                            <v-col cols="12" md="6" class="py-1">
                                <v-text-field
                                    v-model="schoolBagForm.email_confirmation"
                                    density="comfortable"
                                    :counter="50"
                                    :rules="
                                        schoolBagForm.email_confirmation_rules
                                    "
                                    :label="`確認用`"
                                    type="email"
                                    required
                                    @input="
                                        schoolBagForm.clearError(
                                            'email_confirmation',
                                        )
                                    "
                                ></v-text-field>
                            </v-col>
                        </v-row>
                        <v-row no-gutters class="mt-2">
                            <v-col cols="12" md="12">
                                <v-label
                                    >パスワード（半角英数字8～30文字）<span
                                        class="text-red"
                                        >*</span
                                    ></v-label
                                >
                            </v-col>
                        </v-row>
                        <v-row>
                            <v-col cols="12" md="6" class="py-1">
                                <v-text-field
                                    v-model="schoolBagForm.login_pwd"
                                    density="comfortable"
                                    :counter="30"
                                    :rules="schoolBagForm.login_pwd_rules"
                                    type="password"
                                    required
                                    autocomplete="new-password"
                                    @input="
                                        schoolBagForm.clearError('login_pwd')
                                    "
                                ></v-text-field>
                            </v-col>
                            <v-col cols="12" md="6" class="py-1">
                                <v-text-field
                                    v-model="
                                        schoolBagForm.login_pwd_confirmation
                                    "
                                    density="comfortable"
                                    :counter="30"
                                    :rules="
                                        schoolBagForm.login_pwd_confirmation_rules
                                    "
                                    :label="`確認用`"
                                    type="password"
                                    required
                                    @input="
                                        schoolBagForm.clearError(
                                            'login_pwd_confirmation',
                                        )
                                    "
                                ></v-text-field>
                            </v-col>
                        </v-row>
                        <v-row>
                            <!-- <v-radio-group
                                v-model="schoolBagForm.newsletter_opt_in"
                                inline
                            >
                                <template #label>
                                    <div
                                        style="
                                            display: flex;
                                            flex-direction: column;
                                        "
                                    >
                                        <div>
                                            カバーミーのメルマガの受け取り希望<span
                                                class="text-red"
                                                >*</span
                                            >
                                        </div>
                                        <div class="text-body-2 text-accent">
                                            ※新着カタログ情報などをお届けします。<br />
                                            ※申し込んだメーカーからメールが送られる可能性があります
                                        </div>
                                    </div>
                                </template>
                                <v-radio
                                    label="希望する"
                                    :value="true"
                                ></v-radio>
                                <v-radio
                                    label="希望しない"
                                    :value="false"
                                ></v-radio>
                            </v-radio-group> -->
                        </v-row>
                        <v-row no-gutters class="mt-2">
                            <v-col cols="12" md="12">
                                <v-label>
                                    郵便番号（ハイフンなし・半角）<span
                                        class="text-red"
                                        >*</span
                                    >
                                </v-label>
                            </v-col>
                        </v-row>
                        <v-row>
                            <v-col cols="12" md="6" class="py-1">
                                <v-text-field
                                    v-model="schoolBagForm.zip_code"
                                    density="comfortable"
                                    :counter="7"
                                    :rules="schoolBagForm.zip_code_rules"
                                    hint="例：1234567"
                                    :error-messages="
                                        schoolBagForm.errors.zip_code
                                    "
                                    single-line
                                    persistent-hint
                                    required
                                    @input="
                                        schoolBagForm.clearError('zip_code')
                                    "
                                >
                                    <template #append>
                                        <v-btn
                                            variant="flat"
                                            color="primary"
                                            @click="searchZipCode"
                                        >
                                            住所検索
                                        </v-btn>
                                    </template>
                                </v-text-field>
                            </v-col>
                        </v-row>
                        <v-row no-gutters class="mt-4">
                            <v-col cols="12" md="12">
                                <v-label
                                    >住所（国内住所のみ）<span class="text-red"
                                        >*</span
                                    ></v-label
                                >
                            </v-col>
                        </v-row>
                        <v-row>
                            <v-col cols="12" md="6" class="py-1">
                                <v-select
                                    ref="tdfkCdField"
                                    v-model="schoolBagForm.tdfk_cd"
                                    :items="schoolBagForm.prefectures"
                                    item-title="label"
                                    item-value="value"
                                    density="comfortable"
                                    :label="schoolBagForm.labelkey_map.tdfk_cd"
                                    :rules="schoolBagForm.tdfk_cd_rules"
                                    :error-messages="
                                        schoolBagForm.errors.tdfk_cd
                                    "
                                    @input="schoolBagForm.clearError('tdfk_cd')"
                                ></v-select>
                            </v-col>
                            <v-col cols="12" md="6" class="py-1">
                                <v-text-field
                                    ref="address1Field"
                                    v-model="schoolBagForm.address1"
                                    density="comfortable"
                                    :counter="50"
                                    :rules="schoolBagForm.address1_rules"
                                    :label="schoolBagForm.labelkey_map.address1"
                                    :error-messages="
                                        schoolBagForm.errors.address1
                                    "
                                    required
                                    hint="例：横浜市西区高島"
                                    persistent-hint
                                    @input="
                                        schoolBagForm.clearError('address1')
                                    "
                                ></v-text-field>
                            </v-col>
                        </v-row>
                        <v-row>
                            <v-col cols="12" md="6" class="py-1">
                                <v-text-field
                                    v-model="schoolBagForm.address2"
                                    class="coverme-input-address2"
                                    density="comfortable"
                                    :counter="50"
                                    :rules="schoolBagForm.address2_rules"
                                    :label="schoolBagForm.labelkey_map.address2"
                                    :error-messages="
                                        schoolBagForm.errors.address2
                                    "
                                    :messages="addressHint"
                                    persistent-hint
                                    required
                                    @input="
                                        schoolBagForm.clearError('address2')
                                    "
                                >
                                    <!-- <template #details>
                                        <div class="v-messages">
                                            例：１－２－５
                                        </div>
                                        <div
                                            class="text-body-2 text-accent my-1"
                                        >
                                            ※住居表示に番地がない方は「無番地」とご入力ください。
                                        </div>
                                    </template> -->
                                </v-text-field>
                            </v-col>
                            <v-col cols="12" md="6" class="py-1">
                                <v-text-field
                                    v-model="schoolBagForm.address3"
                                    density="comfortable"
                                    :counter="50"
                                    :rules="schoolBagForm.address3_rules"
                                    :label="schoolBagForm.labelkey_map.address3"
                                    :error-messages="
                                        schoolBagForm.errors.address3
                                    "
                                    hint="例：××ビル １０１"
                                    persistent-hint
                                    @input="
                                        schoolBagForm.clearError('address3')
                                    "
                                ></v-text-field>
                            </v-col>
                        </v-row>
                        <v-row no-gutters class="mt-4">
                            <v-col cols="12" md="12">
                                <v-label
                                    >電話番号（半角）<span class="text-red"
                                        >*</span
                                    ></v-label
                                >
                            </v-col>
                        </v-row>
                        <v-row class="flex-nowrap mt-2" no-gutters>
                            <v-col cols="3" class="py-1">
                                <v-text-field
                                    v-model="schoolBagForm.tel_1"
                                    density="comfortable"
                                    :rules="schoolBagForm.tel1_rules"
                                    :error-messages="schoolBagForm.errors.tel"
                                    single-line
                                    required
                                    @input="schoolBagForm.clearError('tel')"
                                ></v-text-field>
                            </v-col>
                            <div class="mt-5">ー</div>
                            <v-col cols="3" class="py-1">
                                <v-text-field
                                    v-model="schoolBagForm.tel_2"
                                    density="comfortable"
                                    :rules="schoolBagForm.tel2_rules"
                                    :error-messages="schoolBagForm.errors.tel"
                                    single-line
                                    required
                                    @input="schoolBagForm.clearError('tel')"
                                ></v-text-field>
                            </v-col>
                            <div class="mt-5">ー</div>
                            <v-col cols="3" class="py-1">
                                <v-text-field
                                    v-model="schoolBagForm.tel_3"
                                    density="comfortable"
                                    :rules="schoolBagForm.tel3_rules"
                                    :error-messages="schoolBagForm.errors.tel"
                                    single-line
                                    required
                                    @input="schoolBagForm.clearError('tel')"
                                ></v-text-field>
                            </v-col>
                        </v-row>
                    </v-container>
                    <v-container class="border-md border-primary pa-3 mt-4">
                        <v-row>
                            <v-col cols="12" class="pa-0">
                                <div
                                    class="text-h6 text-center font-weight-bold bg-primary text-white pa-1"
                                >
                                    アンケート（任意回答）
                                </div>
                            </v-col>
                        </v-row>
                        <v-row class="mt-6">
                            <v-radio-group
                                v-model="schoolBagForm.child_sex"
                                :label="schoolBagForm.labelkey_map.child_sex"
                                inline
                                hide-details
                                density="comfortable"
                            >
                                <v-radio
                                    v-for="sex of schoolBagForm.sexes"
                                    :key="sex.value"
                                    :label="sex.label"
                                    :value="sex.value"
                                ></v-radio>
                            </v-radio-group>
                        </v-row>
                        <!-- <v-row>
                            <v-col cols="12" md="12" class="py-0">
                                <v-label>お子さまの生年月日</v-label>
                            </v-col>
                        </v-row>
                        <v-row>
                            <v-col cols="12" md="6">
                                <v-text-field
                                    v-model="schoolBagForm.child_birthdate"
                                    class="coverme-input-calendar"
                                    type="date"
                                    hide-details
                                    @focus="setInitialDate"
                                ></v-text-field>
                            </v-col>
                        </v-row> -->
                        <v-row>
                            <v-radio-group
                                v-model="schoolBagForm.custom_budget"
                                class="mt-4"
                                :label="
                                    schoolBagForm.labelkey_map.custom_budget
                                "
                                density="comfortable"
                                inline
                                hide-details
                            >
                                <v-radio
                                    v-for="budget of schoolBagForm.budgets"
                                    :key="budget.value"
                                    :label="budget.label"
                                    :value="budget.value"
                                ></v-radio>
                            </v-radio-group>
                        </v-row>
                        <div class="mt-6 mb-3 pl-1">
                            {{
                                schoolBagForm.labelkey_map
                                    .custom_catalog_request_triggers
                            }}
                        </div>
                        <v-row class="my-0">
                            <v-col
                                v-for="catalog_request_trigger of schoolBagForm.catalog_request_triggers"
                                :key="catalog_request_trigger.value"
                                class="py-0"
                                cols="6"
                                md="2"
                            >
                                <v-checkbox
                                    v-model="
                                        schoolBagForm.custom_catalog_request_triggers
                                    "
                                    :label="catalog_request_trigger.label"
                                    :value="catalog_request_trigger.value"
                                    density="compact"
                                    hide-details
                                ></v-checkbox>
                            </v-col>
                        </v-row>

                        <div class="mt-6 mb-3 pl-1">
                            {{ schoolBagForm.labelkey_map.custom_key_points }}
                        </div>
                        <v-row class="my-0">
                            <v-col
                                v-for="key_point of schoolBagForm.key_points"
                                :key="key_point.value"
                                class="py-0"
                                cols="6"
                                md="3"
                            >
                                <v-checkbox
                                    v-model="schoolBagForm.custom_key_points"
                                    :label="key_point.label"
                                    :value="key_point.value"
                                    density="compact"
                                    hide-details
                                ></v-checkbox>
                            </v-col>
                        </v-row>
                    </v-container>
                    <v-container>
                        <p class="text-center">
                            <a
                                href="https://coverme.jp/kiyaku"
                                target="_blank"
                                rel="noopener noreferrer"
                            >
                                利用規約
                            </a>
                            および
                            <a
                                href="https://coverme.jp/privacy-policy"
                                target="_blank"
                                rel="noopener noreferrer"
                            >
                                プライバシーポリシー
                            </a>
                            はリンク先でご確認いただけます。
                        </p>
                        <p class="text-center">
                            ご同意いただけましたら、下記のチェックボックスにチェックを入れてから次へお進みください。
                        </p>
                        <v-row>
                            <v-col
                                cols="10"
                                offset="2"
                                sm="4"
                                offset-sm="5"
                                md="3"
                                offset-md="5"
                                class="pa-0 pt-4"
                            >
                                <v-checkbox
                                    v-model="schoolBagForm.kiyaku_checked"
                                    density="compact"
                                    label="カバーミー利用規約"
                                    hide-details
                                ></v-checkbox>
                            </v-col>
                        </v-row>
                        <v-row>
                            <v-col
                                cols="10"
                                offset="2"
                                sm="4"
                                offset-sm="5"
                                md="3"
                                offset-md="5"
                                class="pa-0 pb-4"
                            >
                                <v-checkbox
                                    v-model="
                                        schoolBagForm.privacy_policy_checked
                                    "
                                    density="compact"
                                    label="プライバシーポリシー"
                                    hide-details
                                ></v-checkbox>
                            </v-col>
                        </v-row>
                        <p class="text-center text-caption">
                            カタログ請求完了と同時に、カバーミーの会員登録が完了します。
                        </p>
                        <p class="text-center text-caption">
                            カバーミーでは、請求したカタログの管理や展示会情報など、ラン活に役立つ情報をお受け取りいただけます。
                        </p>
                    </v-container>

                    <v-container>
                        <v-row justify="center">
                            <v-col cols="12" md="6">
                                <v-btn
                                    block
                                    color="primary"
                                    rounded="xl"
                                    size="large"
                                    @click="toConf"
                                >
                                    次へ
                                </v-btn>
                            </v-col>
                        </v-row>
                    </v-container>
                </v-form>
            </template>
            <template v-else-if="step === stepConf">
                <p class="text-h5 mb-5 text-red font-weight-bold">
                    まだカタログ申込は完了していません。
                </p>
                <p class="text-body-1 font-weight-bold">
                    完了するにはメールに記載のURLから認証手続きが必要です。<br />
                    入力された内容をご確認いただき、「認証用メール送信」を押してください。<br /><br />
                </p>
                <v-container class="border-md border-primary pa-3 mb-4">
                    <v-row>
                        <v-col cols="12" class="pa-0">
                            <div
                                class="text-h6 text-center font-weight-bold bg-primary text-white pa-1"
                            >
                                選択したカタログ
                            </div>
                        </v-col>
                    </v-row>
                    <v-row>
                        <v-list :lines="false">
                            <template
                                v-for="product of schoolBagForm.products"
                                :key="product.id"
                            >
                                <v-list-item
                                    v-if="
                                        schoolBagForm.order_product_ids.includes(
                                            product.id,
                                        )
                                    "
                                    class="text-h3"
                                    :subtitle="product.display_name"
                                ></v-list-item>
                            </template>
                        </v-list>
                    </v-row>
                </v-container>
                <v-container class="border-md border-primary pa-3 mb-4">
                    <v-row>
                        <v-col cols="12" class="pa-0">
                            <div
                                class="text-h6 text-center font-weight-bold bg-primary text-white pa-1"
                            >
                                基本情報
                            </div>
                        </v-col>
                    </v-row>
                    <v-row>
                        <v-list :lines="false">
                            <v-list-item
                                :subtitle="
                                    schoolBagForm.name1 +
                                    ' ' +
                                    schoolBagForm.name2
                                "
                                title="お名前[漢字]"
                            >
                                <!-- <template #prepend>
                                    <span class="mr-4 v-list-item-subtitle"
                                        >お名前[漢字]</span
                                    >
                                </template> -->
                            </v-list-item>
                            <v-list-item
                                title="お名前[フリガナ]"
                                :subtitle="
                                    schoolBagForm.name1_hurigana +
                                    ' ' +
                                    schoolBagForm.name2_hurigana
                                "
                            >
                                <!-- <template #prepend>
                                    <span class="mr-4 v-list-item-subtitle"
                                        >お名前[フリガナ]</span
                                    >
                                </template> -->
                            </v-list-item>
                            <v-list-item title="メールアドレス">
                                <!-- <template #prepend>
                                    <span class="mr-4 v-list-item-subtitle"
                                        >メールアドレス</span
                                    >
                                </template> -->
                                <template #subtitle>
                                    <div style="line-height: 1.7">
                                        {{ schoolBagForm.email }}
                                    </div>
                                </template>
                            </v-list-item>
                            <v-list-item
                                title="パスワード"
                                :subtitle="
                                    schoolBagForm.maskPassword(
                                        schoolBagForm.login_pwd,
                                    )
                                "
                            >
                                <!-- <template #prepend>
                                    <span class="mr-4 v-list-item-subtitle"
                                        >パスワード</span
                                    >
                                </template> -->
                            </v-list-item>
                            <v-list-item
                                title="メルマガの受け取り希望"
                                :subtitle="
                                    schoolBagForm.newsletter_opt_in
                                        ? '希望する'
                                        : '希望しない'
                                "
                            >
                                <!-- <template #prepend>
                                    <span class="mr-4 v-list-item-subtitle"
                                        >メルマガの受け取り希望</span
                                    >
                                </template> -->
                            </v-list-item>
                            <v-list-item
                                title="郵便番号"
                                :subtitle="'〒 ' + schoolBagForm.zip_code"
                            >
                                <!-- <template #prepend>
                                    <span class="mr-4 v-list-item-subtitle"
                                        >郵便番号</span
                                    >
                                </template> -->
                            </v-list-item>
                            <v-list-item
                                title="住所（都道府県）"
                                :subtitle="schoolBagForm.display_tdfk_cd"
                            >
                                <!-- <template #prepend>
                                    <span class="mr-4 v-list-item-subtitle"
                                        >住所（都道府県）</span
                                    >
                                </template> -->
                            </v-list-item>
                            <v-list-item
                                title="市区町村・町名"
                                :subtitle="schoolBagForm.address1"
                            >
                                <!-- <template #prepend>
                                    <span class="mr-4 v-list-item-subtitle"
                                        >市区町村</span
                                    >
                                </template> -->
                            </v-list-item>
                            <v-list-item
                                title="番地"
                                :subtitle="schoolBagForm.address2"
                            >
                                <!-- <template #prepend>
                                    <span class="mr-4 v-list-item-subtitle"
                                        >町名・番地</span
                                    >
                                </template> -->
                            </v-list-item>
                            <v-list-item
                                title="建物名・部屋番号"
                                :subtitle="schoolBagForm.address3"
                            >
                                <!-- <template #prepend>
                                    <span class="mr-4 v-list-item-subtitle"
                                        >建物名・部屋番号</span
                                    >
                                </template> -->
                            </v-list-item>
                            <v-list-item
                                title="電話番号"
                                :subtitle="schoolBagForm.tel"
                            >
                                <!-- <template #prepend>
                                    <span class="mr-4 v-list-item-subtitle"
                                        >電話番号</span
                                    >
                                </template> -->
                            </v-list-item>
                        </v-list>
                    </v-row>
                </v-container>
                <v-container class="border-md border-primary pa-3 mb-4">
                    <v-row>
                        <v-col cols="12" class="pa-0">
                            <div
                                class="text-h6 text-center font-weight-bold bg-primary text-white pa-1"
                            >
                                アンケート（任意回答）
                            </div>
                        </v-col>
                    </v-row>
                    <v-row>
                        <v-list :lines="false">
                            <v-list-item
                                title="お子さまの性別"
                                :subtitle="schoolBagForm.display_child_sex"
                            >
                            </v-list-item>
                            <!-- <v-list-item
                                title="お子さまの生年月日"
                                :subtitle="schoolBagForm.child_birthdate"
                            >
                            </v-list-item> -->
                            <v-list-item
                                title="ご予算"
                                :subtitle="schoolBagForm.display_custom_budget"
                            >
                            </v-list-item>
                            <v-list-item
                                title="カタログ請求のきっかけ"
                                :subtitle="
                                    schoolBagForm.display_custom_catalog_request_triggers
                                "
                            >
                            </v-list-item>
                            <v-list-item
                                title="特に重視するポイント"
                                :subtitle="
                                    schoolBagForm.display_custom_key_points
                                "
                            >
                            </v-list-item>
                        </v-list>
                    </v-row>
                </v-container>
                <v-container>
                    <div class="stitch-wrapper text-h5">
                        <p>
                            「認証用メール送信」ボタンを押すと、
                            認証手続きの案内メールが送られます。<br />
                            <br />
                        </p>
                        <p>
                            <span class="text-red font-weight-bold">
                                メールに記載されたURLから認証
                            </span>
                            を行ってください（24時間以内）。
                        </p>
                    </div>
                </v-container>
                <v-container>
                    <p class="text-body-2 mb-10">
                        迷惑メール対策をされている方は、受信設定をご確認いただき、カバーミーからのメールが受信できるよう、ドメイン指定受信を設定してください。<br />
                        <span class="text-red"
                            >ドメイン指定受信：coverme.jp</span
                        >
                        <br />
                        ※メール受信設定の方法は、ご利用メールサービスのヘルプページ等をご確認ください。
                    </p>
                    <v-row justify="center">
                        <v-col cols="12" md="4" class="pb-0">
                            <v-btn
                                id="covermeSubmit"
                                block
                                color="primary"
                                rounded="xl"
                                size="large"
                                @click="toComplete"
                            >
                                認証用メール送信
                            </v-btn>
                        </v-col>
                        <v-col cols="12" md="4">
                            <v-btn
                                block
                                color="white"
                                rounded="xl"
                                size="large"
                                class="fix-btn"
                                @click="toEdit"
                            >
                                修正する
                            </v-btn>
                        </v-col>
                    </v-row>
                </v-container>
                <v-container>
                    <p class="text-center impressive-text">
                        ※24時間以内にメール内のURLをクリックしない場合、カタログ申込は自動的にキャンセルとなり、カタログは届きません。
                    </p>
                </v-container>
            </template>
            <template v-else-if="step === stepComplete">
                <v-container>
                    <p class="text-h5 mb-5 text-red font-weight-bold">
                        まだカタログ申込は完了していません。
                    </p>
                    <p class="font-weight-bold text-body-1">
                        入力されたメールアドレス宛に認証手続きの案内メールを送信いたしました。<br />
                        <span class="text-red font-weight-bold">
                            メールに記載されたURLから認証
                        </span>
                        を行ってください（24時間以内）。<br /><br />
                    </p>
                    <p class="text-body-2" style="line-height: 1.4rem">
                        ※24時間以内にメールのURLをクリックしない場合、登録情報は消去されます。<br />
                        ※メール認証いただかないとカタログは届きません。<br />
                        ※メール認証と同時にカバーミーユーザー登録が完了します。<br />
                        ※メールが届かない場合は迷惑メールフォルダをご確認ください。<br />
                        ※メールが届かない場合のお問合せは、カバーミーのX（旧Twitter）またはInstagramアカウントまでDMでご連絡ください。<br />
                        ・X（旧Twitter）公式アカウント<br />
                        　<a href="https://x.com/coverme_randsel"
                            >@coverme_randsel</a
                        ><br />
                        ・Instagram公式アカウント<br />
                        　<a href="https://www.instagram.com/coverme123456/"
                            >@coverme123456</a
                        >
                    </p>
                </v-container>
            </template>
        </template>
    </div>
</template>

<style scoped lang="scss">
.test {
    color: $error-color;
}

div.v-list-item-subtitle {
    font-size: 22px; /* サブタイトルのフォントサイズも変更可能 */
}

.coverme-card {
    border-radius: 14px;
    border: 2px solid rgb(255 217 222);
}
.stitch-wrapper {
    margin: 15px 0 15px;
    border: 2px dashed #ffffff;
    box-shadow: 0 0 0 8px #f7f0e6;
    padding: 15px;
    background: #f7f0e6;
    border-radius: 10px;
    position: relative;
    animation: shake 1.5s ease-in-out infinite;
}
.stitch-wrapper:after {
    position: absolute;
    content: "";
    right: -10px;
    top: -10px;
    border-width: 0 33px 28px 0;
    border-style: solid;
    border-color: #f7f0e6 #fff #f7f0e6;
    box-shadow: -4px 8px 6px rgb(216 161 12 / 15%);
    border-radius: 0 15px;
}
.impressive-text {
    color: #ea1e56;
    font-size: 16px;
    font-weight: bold;
}
.non-shadow {
    box-shadow: none;
}
.fix-btn {
    border: 2px solid #f4f4f4;
    color: #7a7a7a !important;
}
@keyframes shake {
    0% {
        transform: translateY(0);
    }
    20% {
        transform: translateY(0);
    }
    25% {
        transform: translateY(-5px);
    }
    35% {
        transform: translateY(5px);
    }
    40% {
        transform: translateY(0);
    }
    100% {
        transform: translateY(0);
    }
}

.image-cover {
    position: relative;
}

.overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(128, 128, 128, 0.5); /* 灰色のカバー */
    display: flex;
    justify-content: center;
    align-items: center;
    opacity: 0; /* 初期状態ではカバーを隠す */
    transition: opacity 0.3s ease; /* ホバー時のトランジション */
    opacity: 1; /* ホバー時にカバーを表示 */
}

.pause-text {
    color: red; /* 赤文字 */
    font-size: 20px; /* フォントサイズ */
    font-weight: bold; /* 太字 */
}
</style>
