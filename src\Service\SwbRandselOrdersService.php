<?php

namespace App\Service;

use App\Enums\EntityFields\ESwbOrderForm;
use App\Kuroko\Entity\IKurokoEntity;
use App\Kuroko\Entity\Member;
use App\Model\Table\RandselOrdersTable;
use BadMethodCallException;
use Cake\Datasource\EntityInterface;
use Cake\Utility\Hash;
use Cake\Log\Log;

class SwbRandselOrdersService implements IRestService
{
    use ServiceTrait;

    protected array $_defaultConfig = [
        "defaultModel" => RandselOrdersTable::class,
    ];


    public function initialize(): void {}


    /**
     * @param array $data
     * @return array|EntityInterface|IKurokoEntity|EntityInterface[]|null
     */
    public function index(array $data = []): array|EntityInterface|null|IKurokoEntity
    {
        $options = [];
        foreach (
            [
                ESwbOrderForm::FROM,
                ESwbOrderForm::TO,
                ESwbOrderForm::PRODUCT_ID,
                ESwbOrderForm::STATUS,
                ESwbOrderForm::SEARCH_DATE_TYPE,
            ] as $item
        ) {
            if ($value = Hash::get($data, $item->value)) {
                $options = Hash::insert($options, $item->value, $value);
            }
        }
        /** @var Member $member */
        $member = $this->getIdentity();
        return (new RandselOrdersService())->getRandselOrders($options);
    }

    public function add(array $data = []): EntityInterface|null|IKurokoEntity
    {
        throw new BadMethodCallException();
    }

    public function edit(string $id, array $data = []): EntityInterface|null|IKurokoEntity
    {
        throw new BadMethodCallException();
    }

    public function view(string $id, array $data = []): EntityInterface|null|IKurokoEntity
    {
        throw new BadMethodCallException();
    }


    public function delete(string $id, array $data = []): bool
    {
        throw new BadMethodCallException();
    }
}
