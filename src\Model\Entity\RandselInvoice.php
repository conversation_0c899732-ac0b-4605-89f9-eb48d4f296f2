<?php

declare(strict_types=1);

namespace App\Model\Entity;

use Cake\ORM\Entity;

/**
 * RandselInvoice Entity
 *
 * @property int $id
 * @property int $maker_id
 * @property int $product_id
 * @property string $billing_year_month
 * @property int $total_amount
 * @property int $total_quantity
 * @property int $adjustment_amount
 * @property int $adjustment_quantity
 * @property string|null $adjustment_note
 * @property int $invoice_amount
 * @property \Cake\I18n\FrozenTime|null $created
 * @property \Cake\I18n\FrozenTime|null $modified
 */
class RandselInvoice extends Entity
{
    /**
     * Fields that can be mass assigned using newEntity() or patchEntity().
     *
     * Note that when '*' is set to true, this allows all unspecified fields to
     * be mass assigned. For security purposes, it is advised to set '*' to false
     * (or remove it), and explicitly make individual fields accessible as needed.
     *
     * @var array<string, bool>
     */
    protected $_accessible = [
        'maker_id' => true,
        'product_id' => true,
        'billing_year_month' => true,
        'total_amount' => true,
        'total_quantity' => true,
        'adjustment_amount' => true,
        'adjustment_quantity' => true,
        'adjustment_note' => true,
        'invoice_amount' => true,
        'created' => true,
        'modified' => true,
    ];
}
