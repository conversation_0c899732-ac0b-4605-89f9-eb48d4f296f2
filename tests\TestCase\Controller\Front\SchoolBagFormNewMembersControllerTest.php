<?php

declare(strict_types=1);

namespace App\Test\TestCase\Controller\Front;


use App\Enums\EntityFields\ESchoolBagForm;
use App\Test\TestCase\Controller\ApiTestCase;
use Cake\Utility\Hash;

class SchoolBagFormNewMembersControllerTest extends ApiTestCase
{
    /**
     * Test fixtures
     */
    protected $fixtures = [
        'app.GeneralUsers',
    ];
    /**
     * Test add method
     *
     * @return void
     * @uses \App\Controller\Front\SchoolBagFormNewMembersController::add()
     * php ./vendor/phpunit/phpunit/phpunit --filter "testAdd" ./tests/TestCase/Controller/Front/SchoolBagFormNewMembersControllerTest.php
     */
    public function testAdd(): void
    {
        $id = uniqid();
        $this->setFrontAuthorized();

        $data = [
            ESchoolBagForm::NAME1->value => "name-" . $id,
            ESchoolBagForm::NAME2->value => "名-" . $id,
            ESchoolBagForm::NAME1_HURIGANA->value => "セイ-" . $id,
            ESchoolBagForm::NAME2_HURIGANA->value => "メイ-" . $id,
            ESchoolBagForm::EMAIL->value => "test-" . $id . "@sowelleber.jp",
            ESchoolBagForm::LOGIN_PWD->value => $id,
            // ESchoolBagForm::LOGIN_ID->value => "id-" . $id,

            ESchoolBagForm::TEL->value => '21231232',
            ESchoolBagForm::ZIP_CODE->value => '1232624',
            ESchoolBagForm::TDFK_CD->value => '12',
            ESchoolBagForm::ADDRESS1->value => '住所1',
            ESchoolBagForm::ADDRESS2->value => '住所2',
            ESchoolBagForm::ADDRESS3->value => '住所3',
            ESchoolBagForm::EMAIL_SEND_NG_FLG->value => false,
            ESchoolBagForm::BODY->value => '{"order":[{"mid":"メーカーid","pid":"商品id"}]}',
            ESchoolBagForm::EXT_01->value => '{"order":[{"mid":"メーカーid","pid":"商品id"}]}',
        ];
        $this->post('/front/school-bag-form-new-members.json', $data);
        $response = $this->bodyToArray($this->_response->getBody());
        debug($response);
        $this->assertResponseOk(print_r(Hash::flatten($response), true));
    }

    /**
     * Test edit method - メールアドレス重複チェック（成功ケース）
     *
     * @return void
     * @uses \App\Controller\Front\SchoolBagFormNewMembersController::edit()
     * php ./vendor/phpunit/phpunit/phpunit --filter "testEditSuccess" ./tests/TestCase/Controller/Front/SchoolBagFormNewMembersControllerTest.php
     */
    public function testEditSuccess(): void
    {
        $id = uniqid();
        $this->setFrontAuthorized();

        $data = [
            ESchoolBagForm::EMAIL->value => "new-email-" . $id . "@example.com",
        ];
        $this->put('/front/school-bag-form-new-members/1.json', $data);
        $response = $this->bodyToArray($this->_response->getBody());

        // 成功レスポンスの確認
        $this->assertResponseOk();
        $this->assertEquals("", $response['id']);
        $this->assertEquals(["入力チェックしました"], $response['messages']);
        $this->assertEquals([], $response['errors']);
    }

    /**
     * Test edit method - メールアドレス重複チェック（重複エラーケース）
     *
     * @return void
     * @uses \App\Controller\Front\SchoolBagFormNewMembersController::edit()
     * php ./vendor/phpunit/phpunit/phpunit --filter "testEditEmailDuplicate" ./tests/TestCase/Controller/Front/SchoolBagFormNewMembersControllerTest.php
     */
    public function testEditEmailDuplicate(): void
    {
        $this->setFrontAuthorized();

        // 既存ユーザーを作成
        $generalUsersTable = $this->getTableLocator()->get('GeneralUsers');
        $existingUser = $generalUsersTable->newEntity([
            'email' => '<EMAIL>',
            'password' => 'password123',
        ]);
        $generalUsersTable->save($existingUser);

        $data = [
            ESchoolBagForm::EMAIL->value => "<EMAIL>",
        ];
        $this->put('/front/school-bag-form-new-members/1.json', $data);

        // エラーレスポンスの確認
        $this->assertResponseError();
    }

    /**
     * Test edit method - メールアドレス未入力エラーケース
     *
     * @return void
     * @uses \App\Controller\Front\SchoolBagFormNewMembersController::edit()
     * php ./vendor/phpunit/phpunit/phpunit --filter "testEditEmailEmpty" ./tests/TestCase/Controller/Front/SchoolBagFormNewMembersControllerTest.php
     */
    public function testEditEmailEmpty(): void
    {
        $this->setFrontAuthorized();

        $data = [
            // メールアドレスを空にする
        ];
        $this->put('/front/school-bag-form-new-members/1.json', $data);

        // エラーレスポンスの確認
        $this->assertResponseError();
    }
}
