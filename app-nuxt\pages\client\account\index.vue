<script setup lang="ts">
import TheClientMy from "~/components/client/TheClientMy.vue";
import Products from "~~/src/models/Products";
import { Product } from "~~/src/models/entry/Product";
import { useAsyncData, useRuntimeConfig } from "#app";

definePageMeta({
  name: 'ClientAccount',
  type: 'client'
});

const config = useRuntimeConfig();
const { data } = useAsyncData("products", async () => {
    const products = await Products.create(config).getAllList();
    return {
        products: products.map((t) => t.data),
    };
});
const products = Product.creates(data.value?.products || []);
</script>

<template>
    <the-req-login>
        <the-client-my :products="products" />
    </the-req-login>
</template>

<style scoped></style>
