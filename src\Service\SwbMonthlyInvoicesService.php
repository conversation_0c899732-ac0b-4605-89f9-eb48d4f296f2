<?php

namespace App\Service;

use App\Enums\EntityFields\ESwbOrderForm;
use App\Enums\EntityFields\ESwbInvoiceForm;
use App\Kuroko\Entity\IKurokoEntity;
use App\Kuroko\Entity\Member;
use App\Model\Table\RandselOrdersTable;
use App\Model\Table\RandselInvoicesTable;
use BadMethodCallException;
use Cake\Datasource\EntityInterface;
use Cake\Utility\Hash;
use Cake\Log\Log;

class SwbMonthlyInvoicesService implements IRestService
{
    use ServiceTrait;

    protected array $_defaultConfig = [
        "defaultModel" => RandselInvoicesTable::class,
    ];


    public function initialize(): void {}


    /**
     * @param array $data
     * @return array|EntityInterface|IKurokoEntity|EntityInterface[]|null
     */
    public function index(array $data = []): array|EntityInterface|null|IKurokoEntity
    {
        $options = [];
        $from = Hash::get($data, ESwbOrderForm::FROM->value);
        $to = Hash::get($data, ESwbOrderForm::TO->value);
        $makerId = Hash::get($data, ESwbInvoiceForm::MAKER_ID->value);
    
        if ($from && $to) {
            $startDate = new \DateTime($from . '-01');
            $endDate = new \DateTime($to . '-01');
            $yearMonths = [];
    
            while ($startDate <= $endDate) {
                $yearMonths[] = $startDate->format('Y-m');
                $startDate->modify('+1 month');
            }

            $options = Hash::insert($options, ESwbInvoiceForm::BILLING_YEAR_MONTH->value . ' IN', $yearMonths);
        }

        if ($makerId) {
            $options = Hash::insert($options, ESwbInvoiceForm::MAKER_ID->value, $makerId);
        }

        $invoices = (new RandselInvoicesService())->getInvoices($options);

        // グループ化用の配列
        $groupedResults = [];
        foreach ($invoices as $invoice) {
            $key = $invoice->billing_year_month . '_' . $invoice->maker_id;
            if (!isset($groupedResults[$key])) {
                $groupedResults[$key] = [
                    'status_modified_year_month' => $invoice->billing_year_month,
                    'maker_id' => $invoice->maker_id,
                    'total_price' => 0
                ];
            }
            $groupedResults[$key]['total_price'] += $invoice->invoice_amount;
        }

        return array_values($groupedResults);
    }

    public function add(array $data = []): EntityInterface|null|IKurokoEntity
    {
        throw new BadMethodCallException();
    }

    public function edit(string $id, array $data = []): EntityInterface|null|IKurokoEntity
    {
        throw new BadMethodCallException();
    }

    public function view(string $id, array $data = []): EntityInterface|null|IKurokoEntity
    {
        throw new BadMethodCallException();
    }


    public function delete(string $id, array $data = []): bool
    {
        throw new BadMethodCallException();
    }
}
