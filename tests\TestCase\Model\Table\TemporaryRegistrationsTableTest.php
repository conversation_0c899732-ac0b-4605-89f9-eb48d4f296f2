<?php
declare(strict_types=1);

namespace App\Test\TestCase\Model\Table;

use App\Model\Table\TemporaryRegistrationsTable;
use Cake\I18n\FrozenTime;
use Cake\ORM\TableRegistry;
use Cake\TestSuite\TestCase;

/**
 * App\Model\Table\TemporaryRegistrationsTable Test Case
 */
class TemporaryRegistrationsTableTest extends TestCase
{
    /**
     * Test subject
     *
     * @var \App\Model\Table\TemporaryRegistrationsTable
     */
    protected $TemporaryRegistrationsTable;

    /**
     * Fixtures
     *
     * @var array<string>
     */
    protected $fixtures = [
        'app.TemporaryRegistrations',
    ];

    /**
     * setUp method
     *
     * @return void
     */
    protected function setUp(): void
    {
        parent::setUp();
        $config = TableRegistry::getTableLocator()->exists('TemporaryRegistrations') ? [] : ['className' => TemporaryRegistrationsTable::class];
        $this->TemporaryRegistrationsTable = TableRegistry::getTableLocator()->get('TemporaryRegistrations', $config);
    }

    /**
     * tearDown method
     *
     * @return void
     */
    protected function tearDown(): void
    {
        unset($this->TemporaryRegistrationsTable);
        parent::tearDown();
    }

    /**
     * Test validationDefault method
     *
     * @return void
     */
    public function testValidationDefault(): void
    {
        $validator = $this->TemporaryRegistrationsTable->getValidator();
        
        // 有効なデータ
        $data = [
            'email' => '<EMAIL>',
            'mask_password' => 'test_password',
            'verification_token' => 'unique_token_123',
            'is_verified' => false,
            'expires' => FrozenTime::now()->addHours(24)
        ];
        
        $entity = $this->TemporaryRegistrationsTable->newEntity($data);
        $errors = $entity->getErrors();
        $this->assertEmpty($errors);
    }

    /**
     * Test validation with invalid email
     *
     * @return void
     */
    public function testValidationInvalidEmail(): void
    {
        $data = [
            'email' => 'invalid-email',
            'verification_token' => 'unique_token_123',
            'is_verified' => false,
            'expires' => FrozenTime::now()->addHours(24)
        ];
        
        $entity = $this->TemporaryRegistrationsTable->newEntity($data);
        $errors = $entity->getErrors();
        $this->assertArrayHasKey('email', $errors);
    }

    /**
     * Test createTemporaryRegistration method
     *
     * @return void
     */
    public function testCreateTemporaryRegistration(): void
    {
        $data = [
            'email' => '<EMAIL>',
            'password' => 'secure_password_123',
            'profile_data' => json_encode([
                'last_name' => '山田',
                'first_name' => '太郎'
            ]),
            'survey_data' => json_encode([
                'year' => 2025,
                'child_sex' => 'male'
            ]),
            'product_ids' => json_encode([1, 2, 3])
        ];
        
        $result = $this->TemporaryRegistrationsTable->createTemporaryRegistration($data);
        
        $this->assertNotNull($result);
        $this->assertEquals('<EMAIL>', $result->email);
        $this->assertEquals('secure_password_123', $result->password);
        $this->assertNotEquals('<EMAIL>', $result->mask_email);
        $this->assertFalse($result->is_verified);
        $this->assertNotEmpty($result->verification_token);
        $this->assertGreaterThan(FrozenTime::now(), $result->expires);
    }

    /**
     * Test createTemporaryRegistration without password
     *
     * @return void
     */
    public function testCreateTemporaryRegistrationWithoutPassword(): void
    {
        $data = [
            'email' => '<EMAIL>',
            'profile_data' => json_encode(['name' => 'test']),
            'survey_data' => json_encode(['answer' => 'yes']),
            'product_ids' => json_encode([1])
        ];
        
        $result = $this->TemporaryRegistrationsTable->createTemporaryRegistration($data);
        
        $this->assertNotNull($result);
        $this->assertNull($result->mask_password);
    }

    /**
     * Test findByVerificationToken method
     *
     * @return void
     */
    public function testFindByVerificationToken(): void
    {
        // 有効なトークンで検索
        $result = $this->TemporaryRegistrationsTable->findByVerificationToken('test_token_123456789abcdef');
        $this->assertNotNull($result);
        $this->assertEquals('<EMAIL>', $result->email);
        
        // 無効なトークンで検索
        $result = $this->TemporaryRegistrationsTable->findByVerificationToken('invalid_token');
        $this->assertNull($result);
        
        // 認証済みのトークンで検索（結果なし）
        $result = $this->TemporaryRegistrationsTable->findByVerificationToken('test_token_fedcba987654321');
        $this->assertNull($result);
    }

    /**
     * Test findValidByEmail method
     *
     * @return void
     */
    public function testFindValidByEmail(): void
    {
        // 有効なメールアドレスで検索
        $result = $this->TemporaryRegistrationsTable->findValidByEmail('<EMAIL>');
        $this->assertNotNull($result);
        $this->assertEquals('<EMAIL>', $result->email);
        
        // 認証済みのメールアドレスで検索（結果なし）
        $result = $this->TemporaryRegistrationsTable->findValidByEmail('<EMAIL>');
        $this->assertNull($result);
        
        // 存在しないメールアドレスで検索
        $result = $this->TemporaryRegistrationsTable->findValidByEmail('<EMAIL>');
        $this->assertNull($result);
    }

    /**
     * Test cleanupExpiredRegistrations method
     *
     * @return void
     */
    public function testCleanupExpiredRegistrations(): void
    {
        // 期限切れデータのクリーンアップ実行
        $count = $this->TemporaryRegistrationsTable->cleanupExpiredRegistrations();
        
        // 1件の期限切れデータがクリーンアップされることを確認
        $this->assertEquals(1, $count);
        
        // クリーンアップ後のデータを確認
        $expiredRecord = $this->TemporaryRegistrationsTable->get(4);
        $this->assertNull($expiredRecord->mask_password);
        $this->assertNull($expiredRecord->email);
        $this->assertNull($expiredRecord->profile_data);
        $this->assertNull($expiredRecord->survey_data);
    }

    /**
     * Test invalidateOldRegistrations method
     *
     * @return void
     */
    public function testInvalidateOldRegistrations(): void
    {
        
        // 古い仮登録を無効化
        $count = $this->TemporaryRegistrationsTable->invalidateOldRegistrations('<EMAIL>');
        // 新しい仮登録を作成
        $newRegistration = $this->TemporaryRegistrationsTable->createTemporaryRegistration([
            'email' => '<EMAIL>',
            'password' => 'new_password',
            'profile_data' => json_encode(['name' => 'new']),
            'survey_data' => json_encode(['answer' => 'new']),
            'product_ids' => json_encode([1])
        ]);
        
        // 1件の古い仮登録が無効化されることを確認
        $this->assertEquals(1, $count);
        
        // 古い仮登録が無効化されていることを確認
        $oldRecord = $this->TemporaryRegistrationsTable->get(1);
        $this->assertTrue($oldRecord->is_verified);
    }

    /**
     * Test generateVerificationToken method
     *
     * @return void
     */
    public function testGenerateVerificationToken(): void
    {
        $token1 = $this->TemporaryRegistrationsTable->generateVerificationToken();
        $token2 = $this->TemporaryRegistrationsTable->generateVerificationToken();
        
        // トークンが生成されることを確認
        $this->assertNotEmpty($token1);
        $this->assertNotEmpty($token2);
        
        // 異なるトークンが生成されることを確認
        $this->assertNotEquals($token1, $token2);
        
        // トークンの長さを確認（64文字のhex文字列）
        $this->assertEquals(64, strlen($token1));
        $this->assertEquals(64, strlen($token2));
    }

    /**
     * Test findValid finder
     *
     * @return void
     */
    public function testFindValid(): void
    {
        $results = $this->TemporaryRegistrationsTable->find('valid')->toArray();
        
        // 有効な仮登録のみが取得されることを確認
        $this->assertCount(1, $results);
        $this->assertEquals('<EMAIL>', $results[0]->email);
        $this->assertFalse($results[0]->is_verified);
        $this->assertGreaterThan(FrozenTime::now(), $results[0]->expires);
    }
}
