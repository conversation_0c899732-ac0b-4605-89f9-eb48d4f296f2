<?php
declare(strict_types=1);

namespace App\Model\Entity;

use Cake\ORM\Entity;

/**
 * UserSurvey Entity
 *
 * @property int $id
 * @property int $general_user_id
 * @property int $year
 * @property int|null $child_sex
 * @property int|null $budget
 * @property bool|null $question_1_1
 * @property bool|null $question_1_2
 * @property bool|null $question_1_3
 * @property bool|null $question_1_4
 * @property bool|null $question_2_1
 * @property bool|null $question_2_2
 * @property bool|null $question_2_3
 * @property bool|null $question_2_4
 * @property bool|null $question_2_5
 * @property \Cake\I18n\FrozenTime $created
 * @property \Cake\I18n\FrozenTime $modified
 *
 * @property \App\Model\Entity\GeneralUser $general_user
 */
class UserSurvey extends Entity
{
    /**
     * Fields that can be mass assigned using newEntity() or patchEntity().
     *
     * Note that when '*' is set to true, this allows all unspecified fields to
     * be mass assigned. For security purposes, it is advised to set '*' to false
     * (or remove it), and explicitly make individual fields accessible as needed.
     *
     * @var array<string, bool>
     */
    protected $_accessible = [
        'general_user_id' => true,
        'year' => true,
        'child_sex' => true,
        'budget' => true,
        'question_1_1' => true,
        'question_1_2' => true,
        'question_1_3' => true,
        'question_1_4' => true,
        'question_2_1' => true,
        'question_2_2' => true,
        'question_2_3' => true,
        'question_2_4' => true,
        'question_2_5' => true,
        'created' => true,
        'modified' => true,
        'general_user' => true,
    ];
}