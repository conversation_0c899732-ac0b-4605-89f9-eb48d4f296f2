<?php
declare(strict_types=1);

namespace App\Test\TestCase\Service;

use App\Enums\EntityFields\EAccessToken;
use App\Kuroko\Entity\IKurokoEntity;
use App\Model\Entity\GeneralUser;
use App\Model\Entity\TemporaryRegistration;
use App\Model\Entity\UserProfile;
use App\Model\Entity\UserSurvey;
use App\Service\SchoolBagOrdersService;
use App\Service\UserRegistrationService;
use Cake\TestSuite\TestCase;
use Exception;

/**
 * SchoolBagOrdersServiceのテスト用拡張クラス
 */
class TestableSchoolBagOrdersService extends SchoolBagOrdersService
{
    private $userRegistrationServiceMock = null;
    private $processOrderResult = true;
    private $processOrderCalled = false;

    public function setUserRegistrationServiceMock($mock): void
    {
        $this->userRegistrationServiceMock = $mock;
    }

    public function setProcessOrderResult(bool $result): void
    {
        $this->processOrderResult = $result;
    }

    public function wasProcessOrderCalled(): bool
    {
        return $this->processOrderCalled;
    }

    protected function createUserRegistrationService(): UserRegistrationService
    {
        return $this->userRegistrationServiceMock ?? parent::createUserRegistrationService();
    }

    public function processOrder(GeneralUser|\App\Kuroko\Entity\Member $user, array $arrayData): bool
    {
        $this->processOrderCalled = true;
        return $this->processOrderResult;
    }
}

/**
 * SchoolBagOrdersService Test Case
 */
class SchoolBagOrdersServiceTest extends TestCase
{
    /**
     * Test fixtures
     */
    protected $fixtures = [
        'app.GeneralUsers',
        'app.TemporaryRegistrations',
        'app.UserProfiles',
        'app.UserSurveys',
        // 必要に応じて他のフィクスチャを追加
    ];

    private TestableSchoolBagOrdersService $service;

    public function setUp(): void
    {
        parent::setUp();
        $this->service = new TestableSchoolBagOrdersService();
    }

    public function tearDown(): void
    {
        unset($this->service);
        parent::tearDown();
    }

    /**
     * Test add method - 成功ケース（モック使用）
     *
     * @return void
     * @uses \App\Service\SchoolBagOrdersService::add()
     */
    public function testAddSuccess(): void
    {
        // 成功時のユーザーオブジェクトを準備
        $user = new GeneralUser([
            'id' => 1,
            'email' => '<EMAIL>',
        ]);

        $tempRegistration = new TemporaryRegistration([
            'email' => '<EMAIL>',
            'password' => 'SecurePassword123!',
            'profile_data' => json_encode([
                'last_name' => '山田',
                'first_name' => '太郎',
                'last_name_kana' => 'ヤマダ',
                'first_name_kana' => 'タロウ',
                'zip_code' => '123-4567',
                'prefecture_code' => '13',
                'address1' => '東京都渋谷区',
                'address2' => '１番地',
                'tel' => '03-1234-5678'
            ]),
            'survey_data' => json_encode([
                'year' => 2025,
                'child_sex' => 1,
                'budget' => 1
            ]),
            'product_ids' => json_encode([1, 2, 3]) // JSON文字列として設定
        ]);

        $userProfile = new UserProfile([
            'general_user_id' => 1,
            'decrypted_last_name' => '山田'
        ]);

        $userSurvey = new UserSurvey([
            'general_user_id' => 1,
        ]);

        $registrationData = [
            'user' => $user,
            'temp_registration' => $tempRegistration,
            'profile' => $userProfile,
            'survey' => $userSurvey
        ];

        // UserRegistrationServiceをモック化
        $userRegistrationServiceMock = $this->createMock(UserRegistrationService::class);
        $userRegistrationServiceMock->expects($this->once())
            ->method('completeRegistration')
            ->with('test_token_123456789abcdef')
            ->willReturn($registrationData);

        // テスト用サービスを設定
        $testService = new TestableSchoolBagOrdersService();
        $testService->setUserRegistrationServiceMock($userRegistrationServiceMock);
        $testService->setProcessOrderResult(true);

        // テスト実行
        $data = [
            EAccessToken::ACCESS_TOKEN->value => 'test_token_123456789abcdef'
        ];

        $result = $testService->add($data);

        // アサーション
        $this->assertInstanceOf(GeneralUser::class, $result);
        $this->assertEquals(1, $result->id);
        $this->assertEquals('<EMAIL>', $result->email);
        $this->assertTrue($testService->wasProcessOrderCalled());
    }

    /**
     * Test add method - ユーザー登録失敗ケース
     *
     * @return void
     * @uses \App\Service\SchoolBagOrdersService::add()
     */
    public function testAddUserRegistrationFailure(): void
    {
        // UserRegistrationServiceのモックを作成
        $userRegistrationServiceMock = $this->createMock(UserRegistrationService::class);

        // ユーザー登録が失敗するように設定
        $userRegistrationServiceMock->expects($this->once())
            ->method('completeRegistration')
            ->with('invalid_token')
            ->willReturn(null);

        // テスト用サービスを設定
        $testService = new TestableSchoolBagOrdersService();
        $testService->setUserRegistrationServiceMock($userRegistrationServiceMock);

        // テスト実行
        $data = [
            EAccessToken::ACCESS_TOKEN->value => 'invalid_token'
        ];

        $result = $testService->add($data);

        // アサーション
        $this->assertNull($result);
        $this->assertArrayHasKey('_system', $testService->getErrors());
        $this->assertEquals('ユーザー登録に失敗しました', $testService->getErrors()['_system']);
        $this->assertFalse($testService->wasProcessOrderCalled());
    }

    /**
     * Test add method - 注文処理失敗ケース
     *
     * @return void
     * @uses \App\Service\SchoolBagOrdersService::add()
     */
    public function testAddOrderProcessingFailure(): void
    {
        // 成功時のユーザーオブジェクトを準備
        $user = new GeneralUser([
            'id' => 1,
            'email' => '<EMAIL>',
        ]);

        $tempRegistration = new TemporaryRegistration([
            'email' => '<EMAIL>',
            'product_ids' => json_encode([1, 2, 3]) // JSON文字列として設定
        ]);

        $userProfile = new UserProfile([
            'general_user_id' => 1,
        ]);

        $userSurvey = new UserSurvey([
            'general_user_id' => 1,
        ]);

        $registrationData = [
            'user' => $user,
            'temp_registration' => $tempRegistration,
            'profile' => $userProfile,
            'survey' => $userSurvey
        ];

        // UserRegistrationServiceのモックを作成
        $userRegistrationServiceMock = $this->createMock(UserRegistrationService::class);
        $userRegistrationServiceMock->expects($this->once())
            ->method('completeRegistration')
            ->with('order_fail_token')
            ->willReturn($registrationData);

        // テスト用サービスを設定
        $testService = new TestableSchoolBagOrdersService();
        $testService->setUserRegistrationServiceMock($userRegistrationServiceMock);
        $testService->setProcessOrderResult(false); // 注文処理を失敗させる

        // テスト実行
        $data = [
            EAccessToken::ACCESS_TOKEN->value => 'order_fail_token'
        ];

        $result = $testService->add($data);

        // アサーション
        $this->assertNull($result);
        $this->assertArrayHasKey('_system', $testService->getErrors());
        $this->assertEquals('注文処理に失敗しました', $testService->getErrors()['_system']);
        $this->assertTrue($testService->wasProcessOrderCalled());
    }


}