<?php

namespace <PERSON>roko\ApiModel;

use App\Enums\EntityFields\EClientOrderForm;
use App\Kuroko\ApiModel\KurokoApiDynamic\ECOrders;
use App\Kuroko\Entity\AccessToken;
use App\Kuroko\Entity\Member;
use App\Service\RandselOrdersService;
use App\Test\TestCase\Controller\ApiTestCase;

class ECOrdersTest extends ApiTestCase
{
    // php ./vendor/phpunit/phpunit/phpunit --filter "testECOrdersPurchase" ./tests/TestCase/Kuroko/ApiModel/ECOrdersTest.php
    public function testECOrdersPurchase()
    {
        $accessToken = $this->getTestToken();
        $orderProducts = [
            'ec_payment_id' => 58,
            'order_products' => [
                [
                    'product_id' => 41201,
                    'quantity' => 1
                ],
                [
                    'product_id' => 41202,
                    'quantity' => 1
                ]
            ]
        ];
        $ecorders = new ECOrders();
        $response = $ecorders->purchase($accessToken, $orderProducts);

        debug(json_encode($response));
        debug((new RandselOrdersService())->getRandselOrders());
        $this->assertTrue(true);
    }

    // php ./vendor/phpunit/phpunit/phpunit --filter "testECOrderList" ./tests/TestCase/Kuroko/ApiModel/ECOrdersTest.php
    public function testECOrderList()
    {
        $accessToken = $this->getTestToken();
        $ecorders = new ECOrders();
        $response = $ecorders->list($accessToken);

        debug(json_encode($response));
        $this->assertTrue(true);
    }

    // php ./vendor/phpunit/phpunit/phpunit --filter "testECOrderAllList" ./tests/TestCase/Kuroko/ApiModel/ECOrdersTest.php
    public function testECOrderAllList()
    {
        $member = (new Member([
            "id" => 8,
            "group_ids" => ["104"],
        ]))->setAccessToken(new AccessToken([
            'access_token' => [
                'value' => "NTEwYjkwM2JlMGVhZTEyYjdiNTk2NGU4NGUwMWRkNmUxODVkODI5MTVmNWZkYTIwM2NlODBlMjUzZGZlMTQ0MeZWz/8w8wCe1GuWURYYSpiuouOTjhOXMwfgXyihsqr7RKSqOzWrpBH5N4l80t97CdROD0adoN4gG5MTzRM1mjMtXnZcelefIWdxR47P/3hVjTMzL+fAeFW36rKTvvo8pg=="
            ]
        ]))//        ]))->setAccessToken($this->getTestToken())
        ;

        $ecorders = new ECOrders();
        $query = [
            EClientOrderForm::FROM->value => "2024-09-19",
            EClientOrderForm::TO->value => "2024-09-20",
            EClientOrderForm::PRODUCT_ID->value => "41207",
        ];
        $response = $ecorders->listForMaker($member, $query);

        debug(json_encode($response));
        $this->assertTrue(true);
    }
}
