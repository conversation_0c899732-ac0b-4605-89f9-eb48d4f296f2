import { Model } from "~/src/models/Model";
import moment from "moment";
import { Member } from "~/src/models/entry/Member";
import UserDetailsClient from "~/src/lib/http/coverme-front/UserDetailsClient";

export default class UserDetails extends Model {
    public index(): Promise<Member | null> {
        const config = this.config;
        return new Promise((resolve) => {
            UserDetailsClient.create(config)
                .index<TMemberResponse>()
                .then(({ member }) => resolve(Member.create(member)))
                .catch(() => resolve(null));
        });
    }

    /**
     *
     * @param {*} data
     */
    public put(data: TMemberUpdate): Promise<TApiResponse | TResponseSuccess> {
        const config = this.config;
        const id = moment().format("YYYYMMDDHHmmss");
        return new Promise((resolve, reject) => {
            UserDetailsClient.create(config)
                .put<TMemberUpdate, TApiResponse | TResponseSuccess>(id, data)
                .then((response) => resolve(response))
                .catch((error) => reject({ errors: [error.message] }));
        });
    }
}
