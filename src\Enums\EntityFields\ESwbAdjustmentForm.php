<?php

namespace App\Enums\EntityFields;

enum ESwbAdjustmentForm: string
{
    case ID = 'id';
    case BILLING_YEAR_MONTH = 'billing_year_month';
    case MAKER_ID = 'maker_id';
    case PRODUCT_ID = 'product_id';
    case ADJUSTMENT_UNIT_PRICE = 'adjustment_unit_price';
    case ADJUSTMENT_QUANTITY = 'adjustment_quantity';
    case ADJUSTMENT_NOTE = 'adjustment_note';
    case STATUS = 'status';
    case CONFIRMED_BY = 'confirmed_by';
    case CONFIRMED = 'confirmed';
    case CREATED_BY = 'created_by';

    /**
     * @return string
     */
    public function description(): string
    {
        return match ($this) {
            self::ID => 'ID',
            self::BILLING_YEAR_MONTH => '請求年月',
            self::MAKER_ID => 'メーカーID',
            self::PRODUCT_ID => '商品ID',
            self::ADJUSTMENT_UNIT_PRICE => '調整単価',
            self::ADJUSTMENT_QUANTITY => '調整数量',
            self::ADJUSTMENT_NOTE => '備考内容',
            self::STATUS => '状態(0: 未確定, 1: 確定, 2: 確定後変更あり)',
            self::CONFIRMED_BY => '確定者ID',
            self::CONFIRMED => '確定日時',
            self::CREATED_BY => '作成者ID',
        };
    }
}
