<script setup lang="ts">
import { SwbMyBridge } from "~~/src/models/bridge/SwbMyBridge";

const props = defineProps<{
    bridge: SwbMyBridge;
}>();

const swbMonthlyInvoices = props.bridge.swb_monthly_invoices;

const totalPrice = swbMonthlyInvoices.reduce(
    (sum, invoice) => sum + invoice.total_price,
    0,
);
</script>

<template>
    <v-container fluid class="report-summary-wrapper">
        <v-row class="pt-5 pl-3 pb-0">サマリー</v-row>
        <v-row justify="start" class="summary-text">
            <v-col cols="3" class="pt-1 text-end">
                請求合計額：{{ totalPrice.toLocaleString() }}円
            </v-col>
        </v-row>
    </v-container>
</template>

<style scoped>
.report-summary-wrapper {
    position: inherit;
    margin-top: 40px;
    padding: 0 20px;
    height: 80px;
    border: #e4e4e4 solid 2px;
    color: #575757;
}
.summary-text {
    font-size: 15px;
}
</style>
