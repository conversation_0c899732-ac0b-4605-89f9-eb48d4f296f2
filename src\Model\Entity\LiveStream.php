<?php
declare(strict_types=1);

namespace App\Model\Entity;

use Cake\I18n\FrozenTime;
use Cake\ORM\Entity;

/**
 * ライブ配信エンティティ
 * 
 * @property int $id
 * @property string $title
 * @property string|null $description
 * @property \Cake\I18n\FrozenTime $start_datetime
 * @property \Cake\I18n\FrozenTime $end_datetime
 * @property string|null $platform
 * @property string|null $stream_url
 * @property \Cake\I18n\FrozenTime|null $deleted
 * @property \Cake\I18n\FrozenTime $created
 * @property \Cake\I18n\FrozenTime $modified
 * @property \App\Model\Entity\LiveStreamMaker[] $live_stream_makers
 */
class LiveStream extends Entity
{
    protected $_accessible = [
        'title' => true,
        'description' => true,
        'start_datetime' => true,
        'end_datetime' => true,
        'platform' => true,
        'stream_url' => true,
        'deleted' => true,
        'created' => true,
        'modified' => true,
        'live_stream_makers' => true,
    ];

    protected $_hidden = [];

    /**
     * 現在配信中かどうかを判定
     */
    public function isCurrentlyLive(): bool
    {
        if ($this->isDeleted()) {
            return false;
        }
        
        $now = FrozenTime::now();
        return $now >= $this->start_datetime && $now <= $this->end_datetime;
    }

    /**
     * 配信予定かどうかを判定
     */
    public function isUpcoming(): bool
    {
        if ($this->isDeleted()) {
            return false;
        }
        
        return FrozenTime::now() < $this->start_datetime;
    }

    /**
     * 配信終了かどうかを判定
     */
    public function isFinished(): bool
    {
        return FrozenTime::now() > $this->end_datetime;
    }

    /**
     * 配信期間の文字列表現を取得
     */
    public function getPeriodString(): string
    {
        $start = $this->start_datetime;
        $end = $this->end_datetime;
        
        if ($start->format('Y-m-d') === $end->format('Y-m-d')) {
            // 同日配信
            return $start->format('Y年m月d日') . ' ' . 
                   $start->format('H:i') . '〜' . $end->format('H:i');
        } else {
            // 複数日配信
            return $start->format('Y年m月d日 H:i') . ' 〜 ' . 
                   $end->format('Y年m月d日 H:i');
        }
    }

    /**
     * 配信状況を取得
     */
    public function getStatus(): string
    {
        if ($this->isDeleted()) {
            return '中止';
        }
        
        if ($this->isUpcoming()) {
            return '配信予定';
        }
        
        if ($this->isCurrentlyLive()) {
            return '配信中';
        }
        
        if ($this->isFinished()) {
            return '終了';
        }
        
        return '不明';
    }

    /**
     * 配信URLが設定されているかどうかを判定
     */
    public function hasStreamUrl(): bool
    {
        return !empty($this->stream_url);
    }

    /**
     * 論理削除されているかどうかを判定
     */
    public function isDeleted(): bool
    {
        return $this->deleted !== null;
    }

    /**
     * アクティブなライブ配信かどうかを判定
     */
    public function isActive(): bool
    {
        return !$this->isDeleted();
    }
}
