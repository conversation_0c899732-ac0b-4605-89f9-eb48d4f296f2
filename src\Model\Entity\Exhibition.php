<?php
declare(strict_types=1);

namespace App\Model\Entity;

use Cake\I18n\FrozenTime;
use Cake\ORM\Entity;

/**
 * 展示会エンティティ
 * 
 * @property int $id
 * @property string $title
 * @property string|null $description
 * @property \Cake\I18n\FrozenTime $start_datetime
 * @property \Cake\I18n\FrozenTime $end_datetime
 * @property string|null $venue_name
 * @property string|null $address
 * @property string|null $access_info
 * @property int|null $capacity
 * @property bool $requires_reservation
 * @property string|null $reservation_url
 * @property \Cake\I18n\FrozenTime|null $deleted
 * @property \Cake\I18n\FrozenTime $created
 * @property \Cake\I18n\FrozenTime $modified
 * @property \App\Model\Entity\ExhibitionMaker[] $exhibition_makers
 */
class Exhibition extends Entity
{
    protected $_accessible = [
        'title' => true,
        'description' => true,
        'start_datetime' => true,
        'end_datetime' => true,
        'venue_name' => true,
        'address' => true,
        'access_info' => true,
        'capacity' => true,
        'requires_reservation' => true,
        'reservation_url' => true,
        'deleted' => true,
        'created' => true,
        'modified' => true,
        'exhibition_makers' => true,
    ];

    protected $_hidden = [];

    /**
     * 現在開催中かどうかを判定
     */
    public function isCurrentlyRunning(): bool
    {
        if ($this->isDeleted()) {
            return false;
        }
        
        $now = FrozenTime::now();
        return $now >= $this->start_datetime && $now <= $this->end_datetime;
    }

    /**
     * 開催予定かどうかを判定
     */
    public function isUpcoming(): bool
    {
        if ($this->isDeleted()) {
            return false;
        }
        
        return FrozenTime::now() < $this->start_datetime;
    }

    /**
     * 開催終了かどうかを判定
     */
    public function isFinished(): bool
    {
        return FrozenTime::now() > $this->end_datetime;
    }

    /**
     * 開催期間の文字列表現を取得
     */
    public function getPeriodString(): string
    {
        $start = $this->start_datetime;
        $end = $this->end_datetime;
        
        if ($start->format('Y-m-d') === $end->format('Y-m-d')) {
            // 同日開催
            return $start->format('Y年m月d日') . ' ' . 
                   $start->format('H:i') . '〜' . $end->format('H:i');
        } else {
            // 複数日開催
            return $start->format('Y年m月d日 H:i') . ' 〜 ' . 
                   $end->format('Y年m月d日 H:i');
        }
    }

    /**
     * 開催状況を取得
     */
    public function getStatus(): string
    {
        if ($this->isDeleted()) {
            return '中止';
        }
        
        if ($this->isUpcoming()) {
            return '開催予定';
        }
        
        if ($this->isCurrentlyRunning()) {
            return '開催中';
        }
        
        if ($this->isFinished()) {
            return '終了';
        }
        
        return '不明';
    }

    /**
     * 予約が必要かどうかを判定
     */
    public function requiresReservation(): bool
    {
        return $this->requires_reservation;
    }

    /**
     * 定員が設定されているかどうかを判定
     */
    public function hasCapacity(): bool
    {
        return $this->capacity !== null && $this->capacity > 0;
    }

    /**
     * 論理削除されているかどうかを判定
     */
    public function isDeleted(): bool
    {
        return $this->deleted !== null;
    }

    /**
     * アクティブな展示会かどうかを判定
     */
    public function isActive(): bool
    {
        return !$this->isDeleted();
    }
}
