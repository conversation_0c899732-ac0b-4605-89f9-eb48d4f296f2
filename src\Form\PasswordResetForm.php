<?php
declare(strict_types=1);

namespace App\Form;

use Cake\Form\Form;
use Cake\Form\Schema;
use Cake\Validation\Validator;

/**
 * パスワードリセットフォーム
 */
class PasswordResetForm extends Form
{
    /**
     * スキーマの定義
     */
    protected function _buildSchema(Schema $schema): Schema
    {
        return $schema
            ->addField('token', 'string')
            ->addField('password', 'string')
            ->addField('password_confirm', 'string');
    }

    /**
     * バリデーションルールの定義
     */
    public function validationDefault(Validator $validator): Validator
    {
        $validator
            ->scalar('token')
            ->requirePresence('token', 'create', 'リセットトークンは必須です')
            ->notEmptyString('token', 'リセットトークンは必須です');

        $validator
            ->scalar('password')
            ->requirePresence('password', 'create', 'パスワードは必須です')
            ->notEmptyString('password', 'パスワードは必須です')
            ->minLength('password', 8, 'パスワードは8文字以上で入力してください')
            ->add('password', 'custom', [
                'rule' => [$this, 'validatePasswordStrength'],
                'message' => 'パスワードには英字と数字を含めてください'
            ]);

        $validator
            ->scalar('password_confirm')
            ->requirePresence('password_confirm', 'create', 'パスワード確認は必須です')
            ->notEmptyString('password_confirm', 'パスワード確認は必須です')
            ->add('password_confirm', 'custom', [
                'rule' => [$this, 'validatePasswordMatch'],
                'message' => 'パスワードが一致しません'
            ]);

        return $validator;
    }

    /**
     * パスワード強度のカスタムバリデーション
     */
    public function validatePasswordStrength($value, array $context): bool
    {
        // 英字を含むかチェック
        if (!preg_match('/[A-Za-z]/', $value)) {
            return false;
        }

        // 数字を含むかチェック
        if (!preg_match('/[0-9]/', $value)) {
            return false;
        }

        return true;
    }

    /**
     * パスワード一致のカスタムバリデーション
     */
    public function validatePasswordMatch($value, array $context): bool
    {
        return isset($context['data']['password']) && $value === $context['data']['password'];
    }

    /**
     * フォーム実行処理（実際の処理はサービスクラスで行う）
     */
    protected function _execute(array $data): bool
    {
        // 実際の処理はコントローラーでサービスクラスを呼び出して行う
        return true;
    }
}
