import { Csv } from "~/src/lib/Csv";
import { MonthlyInvoice } from "~/src/models/entry/MonthlyInvoice";
import { SwbMyBridge } from "~/src/models/bridge/SwbMyBridge";
import { TAX_RATES } from "~/src/config";
import moment from "moment";

export class InvoiceRecords {
    static downloadRecordCsv(
        record: MonthlyInvoice[],
        bridge: SwbMyBridge,
    ): void {
        Csv.download(
            this._convertRecords(record, bridge),
            this._generateCsvFilename(),
            true,
        );
    }

    private static _convertRecords(
        records: MonthlyInvoice[],
        bridge: SwbMyBridge,
    ): Record<string, any>[] {
        return records.reduce(
            (acc: Record<string, any>[], record: MonthlyInvoice) => {
                const csvObjData = this._mapRecordToCsv(record, bridge);

                return acc.concat(csvObjData);
            },
            [],
        );
    }

    private static _mapRecordToCsv(
        record: MonthlyInvoice,
        bridge: SwbMyBridge,
    ): Record<string, any> {
        const maker = bridge.makers.find(
            (maker) => maker.maker_id === record.maker_id,
        );
        const makerExt = bridge.getMakerExtraInfo(record.maker_id);
        const billingDate = this._calculateBillingDate(
            record.status_modified_year_month,
            makerExt ? makerExt.billing_cycle : null,
        );

        // 税率
        const taxRate = (
            TAX_RATES.find((taxRate) =>
                moment(
                    `${record.status_modified_year_month}-01`,
                    "YYYY-MM-DD",
                ).isSameOrAfter(moment(taxRate.startDate, "YYYY-MM-DD")),
            ) || { rate: 0.05 }
        ).rate;

        const tax = Math.floor(record.total_price * taxRate);

        return {
            計上月: `カバーミー${moment(`${record.status_modified_year_month}-01`, "YYYY-MM-DD").format("YY年MM月")}分`,
            広告主名: maker ? maker.maker_name : "",
            商材: "カバーミー＿カタログ",
            税抜: record.total_price,
            消費税: tax,
            税込: record.total_price + tax,
            入金予定日: billingDate,
            取引先コード: makerExt ? makerExt.customer_code : "",
            取引先名: makerExt ? makerExt.customer_name : "",
        };
    }

    private static _generateCsvFilename(): string {
        return `invoice_report_${moment().format("YYYYMMDDHHmmss")}.csv`;
    }

    static _calculateBillingDate(
        yearMonth: string,
        billingCycleType: number | null,
    ): string {
        if (!yearMonth || billingCycleType === null) {
            return "";
        }
        const baseDate = moment(`${yearMonth}-01`, "YYYY-MM-DD");

        if (billingCycleType === 0) {
            return baseDate.add(1, "month").endOf("month").format("YYYYMMDD");
        } else if (billingCycleType === 1) {
            return baseDate.add(2, "month").set("date", 10).format("YYYYMMDD");
        } else if (billingCycleType === 2) {
            return baseDate.add(2, "month").set("date", 20).format("YYYYMMDD");
        }

        return "";
    }
}
