import { Model } from "~~/src/models/Model";
import moment from "moment";
import UserPasswordRemindersClient from "~/src/lib/http/coverme-front/UserPasswordRemindersClient";

export default class UserPasswordReminders extends Model {
    // public view() {}

    /**
     *
     * @param {{email:"", password:""} } data
     */
    public add(data: TPasswordReminders): Promise<boolean> {
        const config = this.config;
        return new Promise((resolve) => {
            UserPasswordRemindersClient.create(config)
                .post<TPasswordReminders, TResponseSuccess>(data)
                //@todo レスポンスをオブジェクトクラスにする、レスポンス項目は検討が必要
                .then(({ success }) => resolve(!!success))
                .catch(() => resolve(false));
        });
    }

    /**
     *
     * @param {*} data
     */
    public put(data: TPasswordReminders): Promise<boolean> {
        const config = this.config;
        const id = moment().format("YYYYMMDDHHmmss");
        return new Promise((resolve) => {
            UserPasswordRemindersClient.create(config)
                .put<TPasswordReminders, TResponseSuccess>(id, data)
                .then(({ success }) => resolve(!!success))
                .catch(() => resolve(false));
        });
    }
}
