<?php

namespace App\Test\TestCase\Controller\Front;

use App\Enums\EntityFields\ELogin;
use App\Test\TestCase\Controller\ApiTestCase;
use Cake\Utility\Hash;

class UserAuthenticationsControllerTest extends ApiTestCase
{
    /**
     * Test add method
     *
     * @return void
     * @uses \App\Controller\Front\UserAuthenticationsController::add()
     * php ./vendor/phpunit/phpunit/phpunit --filter "testAdd" ./tests/TestCase/Controller/Front/UserAuthenticationsControllerTest.php
     */
    public function testAdd(): void
    {
        $this->setFrontAuthorized();
        $this->post('/front/user-authentications.json', [
            ELogin::LOGIN_EMAIL->value => "<EMAIL>",
            ELogin::LOGIN_PWD->value => "1234Asdf",
        ]);
        $response = $this->bodyToArray($this->_response->getBody());
        debug($response);
        $this->assertArrayNotHasKey("errors", $response);
        $this->assertResponseOk(print_r(Hash::flatten($response), true));

        $this->setFrontAuthorized();
        $this->post('/front/user-authentications.json', [
            ELogin::LOGIN_EMAIL->value => "<EMAIL>",
            ELogin::LOGIN_PWD->value => "pass0000",
        ]);
        $response = $this->bodyToArray($this->_response->getBody());
        debug($response);
        $this->assertArrayHasKey("errors", $response);
        $this->assertResponseOk(print_r(Hash::flatten($response), true));
    }

    /**
     * Test delete method
     *
     * @return void
     * @uses \App\Controller\Front\UserAuthenticationsController::delete()
     * php ./vendor/phpunit/phpunit/phpunit --filter "testDelete" ./tests/TestCase/Controller/Front/UserAuthenticationsControllerTest.php
     */
    public function testDelete(): void
    {
        $this->setFrontAuthorized(true);
        $this->delete('/front/user-authentications/1.json');
        $response = $this->bodyToArray($this->_response->getBody());
        debug($response);
        $this->assertResponseOk(print_r(Hash::flatten($response), true));
    }
}
