<?php

namespace App\Controller\Front;

use App\Controller\FrontController;
use App\Form\DisableParametersForm;
use App\Form\Front\UserChangePasswordsEditForm;
use App\Kuroko\Entity\Member;
use App\Service\Password\IntegratedChangePasswordsService;
use Cake\Event\Event;
use Cake\Log\Log;

class UserChangePasswordsController extends FrontController
{

    /**
     * @param $id int dummy
     * @return void
     */
    public function edit($id = null): void
    {
        $this->_onSuccess(function (Event $event) {
            $this->_setSuccessResponse([
                'success' => true,
            ]);
        });
        $this->_onError(function (Event $event, array $errors) {
            Log::debug(print_r($errors, true));
        });
        $this->_edit($id, (new IntegratedChangePasswordsService())
            ->setIdentity($this->Authentication->getIdentity())
            ->setForm(
                new UserChangePasswordsEditForm()
            ));
    }
}
