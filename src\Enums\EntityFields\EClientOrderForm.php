<?php

namespace App\Enums\EntityFields;

enum EClientOrderForm: string
{
    case FROM = 'from';
    case TO = 'to';
    case PRODUCT_ID = 'product_id';
    case STATUS = 'status';
    case SEARCH_DATE_TYPE = 'searchDateType';
    /**
     * @return string
     */
    public function description(): string
    {
        return match ($this) {
            self::FROM => 'from',
            self::TO => 'to',
            self::PRODUCT_ID => 'product_id',
            self::STATUS => 'status',
            self::SEARCH_DATE_TYPE => 'searchDateType',
        };
    }
}
