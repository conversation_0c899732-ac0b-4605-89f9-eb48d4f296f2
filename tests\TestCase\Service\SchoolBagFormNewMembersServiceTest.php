<?php
declare(strict_types=1);

namespace App\Test\TestCase\Service;

use App\Enums\EntityFields\ESchoolBagForm;
use App\Kuroko\Entity\ValidationResult;
use App\Service\SchoolBagFormNewMembersService;
use Cake\TestSuite\TestCase;
use Cake\ORM\TableRegistry;

/**
 * SchoolBagFormNewMembersService Test Case
 */
class SchoolBagFormNewMembersServiceTest extends TestCase
{
    /**
     * Test fixtures
     */
    protected $fixtures = [
        'app.GeneralUsers',
    ];

    private SchoolBagFormNewMembersService $service;

    public function setUp(): void
    {
        parent::setUp();
        $this->service = new SchoolBagFormNewMembersService();
    }

    public function tearDown(): void
    {
        unset($this->service);
        parent::tearDown();
    }

    /**
     * Test edit method - 成功ケース
     *
     * @return void
     * @uses \App\Service\SchoolBagFormNewMembersService::edit()
     */
    public function testEditSuccess(): void
    {
        $data = [
            ESchoolBagForm::EMAIL->value => '<EMAIL>',
        ];

        $result = $this->service->edit('1', $data);

        $this->assertInstanceOf(ValidationResult::class, $result);
        $this->assertTrue($this->service->isValid());
        
        $jsonData = $result->getJsonData();
        debug($jsonData);
        $this->assertEquals("", $jsonData['id']);
        $this->assertEquals(["入力チェックしました"], $jsonData['messages']);
        $this->assertEquals([], $jsonData['errors']);
    }

    /**
     * Test edit method - メールアドレス重複エラー
     *
     * @return void
     * @uses \App\Service\SchoolBagFormNewMembersService::edit()
     */
    public function testEditEmailDuplicate(): void
    {
        // 既存ユーザーを作成
        $generalUsersTable = TableRegistry::getTableLocator()->get('GeneralUsers');
        $existingUser = $generalUsersTable->newEntity([
            'email' => '<EMAIL>',
            'password' => 'password123',
        ]);
        $generalUsersTable->save($existingUser);

        $data = [
            ESchoolBagForm::EMAIL->value => '<EMAIL>',
        ];

        $result = $this->service->edit('1', $data);

        $this->assertNull($result);
        $this->assertFalse($this->service->isValid());
        
        $errors = $this->service->getErrors();
        debug($errors);
        $this->assertArrayHasKey('email', $errors);
        $this->assertEquals('このメールアドレスは既に登録されています', $errors['email']);
    }

    /**
     * Test edit method - メールアドレス未入力エラー
     *
     * @return void
     * @uses \App\Service\SchoolBagFormNewMembersService::edit()
     */
    public function testEditEmailEmpty(): void
    {
        $data = [
            // メールアドレスなし
        ];

        $result = $this->service->edit('1', $data);

        $this->assertNull($result);
        $this->assertFalse($this->service->isValid());
        
        $errors = $this->service->getErrors();
        debug($errors);
        $this->assertArrayHasKey('email', $errors);
        $this->assertEquals('メールアドレスは必須です', $errors['email']);
    }

    /**
     * Test edit method - 空のメールアドレス
     *
     * @return void
     * @uses \App\Service\SchoolBagFormNewMembersService::edit()
     */
    public function testEditEmailEmptyString(): void
    {
        $data = [
            ESchoolBagForm::EMAIL->value => '',
        ];

        $result = $this->service->edit('1', $data);

        $this->assertNull($result);
        $this->assertFalse($this->service->isValid());
        
        $errors = $this->service->getErrors();
        debug($errors);
        $this->assertArrayHasKey('email', $errors);
        $this->assertEquals('メールアドレスは必須です', $errors['email']);
    }
}
