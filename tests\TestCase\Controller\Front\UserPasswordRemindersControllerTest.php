<?php

namespace Controller\Front;

use App\Test\TestCase\Controller\ApiTestCase;
use Cake\Utility\Hash;

class UserPasswordRemindersControllerTest extends ApiTestCase
{

    /**
     * Test add method
     *
     * @return void
     * @uses \App\Controller\Front\OrdersController::add()
     * php ./vendor/phpunit/phpunit/phpunit --filter "testAdd" ./tests/TestCase/Controller/Front/UserPasswordRemindersControllerTest.php
     */
    public function testAdd()
    {
        $data = [
            "email" => "<EMAIL>",
        ];

        $this->setFrontAuthorized();
        $this->post('/front/user-password-reminders.json', $data);
        $response = $this->bodyToArray($this->_response->getBody());
        debug($response);
        $this->assertResponseOk(print_r(Hash::flatten($response), true));
    }

    /**
     * Test add method
     *
     * @return void
     * @uses \App\Controller\Front\OrdersController::edit()
     * php ./vendor/phpunit/phpunit/phpunit --filter "testEdit" ./tests/TestCase/Controller/Front/UserPasswordRemindersControllerTest.php
     */
    public function testEdit()
    {
        $data = [
            "token" => "b76a43aac4e63c37f77bd93e9268e982f0f5b9a7",
            "temp_pwd" => "5RtTjygxkV",
            "login_pwd" => "<EMAIL>"
        ];

        $this->setFrontAuthorized();
        $this->put('/front/user-password-reminders/1.json', $data);
        $response = $this->bodyToArray($this->_response->getBody());
        debug($response);
        $this->assertResponseOk(print_r(Hash::flatten($response), true));
    }
}
