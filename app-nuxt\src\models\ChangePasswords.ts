import { Model } from "~/src/models/Model";
import CovermeFrontApiClient from "~/src/lib/http/CovermeFrontApiClient";
import moment from "moment";

export abstract class ChangePasswords extends Model {
    protected abstract getClient(): CovermeFrontApiClient;
    /**
     *
     * @param {*} data
     */
    public put(data: TChangePassword): Promise<TApiResponse | null> {
        const id = moment().format("YYYYMMDDHHmmss");
        return new Promise((resolve) => {
            this.getClient()
                // .create(config)
                .put<TChangePassword, TApiResponse>(id, data)
                .then((response) => resolve(response))
                .catch(() => resolve(null));
        });
    }
}
