import { Model } from "~~/src/models/Model";
import { MonthlyInvoice } from "~~/src/models/entry/MonthlyInvoice";
import SwbMonthlyInvoicesClient from "~/src/lib/http/coverme-front/SwbMonthlyInvoicesClient";
import SwbInvoicePdfApiClient from "~/src/lib/http/coverme-front/SwbInvoicePdfApiClient";

export default class SwbInvoices extends Model {
    public getMonthlyInvoices(
        params?: Record<string, string | number | string[] | number[]>,
    ): Promise<MonthlyInvoice[]> {
        const config = this.config;
        return new Promise((resolve) => {
            SwbMonthlyInvoicesClient.create(config)
                .index<TResponseMonthlyInvoices>(params)
                .then(({ monthly_invoices }) => {
                    resolve(MonthlyInvoice.creates(monthly_invoices));
                })
                .catch((e) => console.error(e));
        });
    }

    public downloadInvoicePdf(params?: TMonthlyInvoicesPdf): Promise<Blob> {
        const config = this.config;
        // await SwbInvoicePdfApiClient.create(config).downloadInvoicePdf(item);
        return new Promise((resolve) => {
            SwbInvoicePdfApiClient.create(config)
                .downloadPdf<TMonthlyInvoicesPdf, Blob>(params)
                .then((response) => {
                    resolve(response as Blob);
                })
                .catch((e) => console.error(e));
        });
    }
}
