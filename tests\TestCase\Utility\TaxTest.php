<?php

use App\Utility\Tax;
use Cake\TestSuite\TestCase;
use Cake\I18n\FrozenDate;

class TaxTest extends TestCase
{
    // php ./vendor/phpunit/phpunit/phpunit --filter "testGetTaxRate_MatchesStartDate" ./tests/TestCase/Utility/TaxTest.php
    public function testGetTaxRate_MatchesStartDate()
    {
        $dateString = '2019-01-01';
        $expectedRate = 0.08;
        $this->assertEquals($expectedRate, Tax::getTaxRate($dateString));

        $dateString = '2019-10-01';
        $expectedRate = 0.1;
        $this->assertEquals($expectedRate, Tax::getTaxRate($dateString));
    }
}
