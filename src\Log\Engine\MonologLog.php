<?php

declare(strict_types=1);

namespace App\Log\Engine;

use Cake\Log\Engine\BaseLog;
use Monolog\Logger;
use Monolog\Level;
use Psr\Log\LogLevel;

class MonologLog extends BaseLog
{
    protected Logger $_logger;

    public function __construct(array $config = [])
    {
        parent::__construct($config);

        $name = $config['name'] ?? 'app'; // ログチャンネル名を設定 (config から取得、デフォルトは 'app')
        $this->_logger = new Logger($name);

        if (isset($config['handler'])) {
            $this->_logger->pushHandler($config['handler']);
        }
    }

    // public function log($level, string|\Stringable $message, array $context = []);
    public function log($level, string|\Stringable  $message, array $context = []): void
    {
        $monologLevel = $this->getMonologLevel($level);
        $this->_logger->log($monologLevel, $message, $context);
    }

    protected function getMonologLevel(string $level): Level
    {
        return match ($level) {
            LogLevel::EMERGENCY => Level::Emergency,
            LogLevel::ALERT => Level::Alert,
            LogLevel::CRITICAL => Level::Critical,
            LogLevel::ERROR => Level::Error,
            LogLevel::WARNING => Level::Warning,
            LogLevel::NOTICE => Level::Notice,
            LogLevel::INFO => Level::Info,
            LogLevel::DEBUG => Level::Debug,
            default => Level::Debug,
        };
    }
}
