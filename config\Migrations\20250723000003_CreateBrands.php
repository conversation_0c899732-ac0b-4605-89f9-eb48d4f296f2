<?php
declare(strict_types=1);

use Migrations\AbstractMigration;

class CreateBrands extends AbstractMigration
{
    /**
     * Up Method.
     *
     * More information on this method is available here:
     * https://book.cakephp.org/phinx/0/en/migrations.html#the-up-method
     * @return void
     */
    public function up(): void
    {
        $this->table('brands')
            ->addColumn('maker_id', 'integer', [
                'comment' => 'メーカーID',
                'default' => null,
                'limit' => null,
                'null' => false,
            ])
            ->addColumn('name', 'string', [
                'comment' => 'ブランド名',
                'default' => null,
                'limit' => 255,
                'null' => false,
            ])
            ->addColumn('description', 'text', [
                'comment' => 'ブランド説明',
                'default' => null,
                'limit' => null,
                'null' => true,
            ])
            ->addColumn('logo_url', 'string', [
                'comment' => 'ブランドロゴURL',
                'default' => null,
                'limit' => 500,
                'null' => true,
            ])
            ->addColumn('brand_image_url', 'string', [
                'comment' => 'ブランド画像URL',
                'default' => null,
                'limit' => 500,
                'null' => true,
            ])
            ->addColumn('brand_features_html', 'text', [
                'comment' => 'ブランド特徴（HTML形式）',
                'default' => null,
                'limit' => null,
                'null' => true,
            ])
            ->addColumn('other_features_html', 'text', [
                'comment' => 'その他機能（HTML形式）',
                'default' => null,
                'limit' => null,
                'null' => true,
            ])
            ->addColumn('established_year', 'integer', [
                'comment' => '設立年',
                'default' => null,
                'limit' => null,
                'null' => true,
            ])
            ->addColumn('target_age_min', 'integer', [
                'comment' => '対象年齢（最小）',
                'default' => null,
                'limit' => null,
                'null' => true,
            ])
            ->addColumn('target_age_max', 'integer', [
                'comment' => '対象年齢（最大）',
                'default' => null,
                'limit' => null,
                'null' => true,
            ])
            ->addColumn('target_gender', 'tinyinteger', [
                'comment' => '対象性別（1:男子, 2:女子, 3:男女共用）',
                'default' => null,
                'limit' => null,
                'null' => true,
            ])
            ->addColumn('price_range_min', 'integer', [
                'comment' => '価格帯（最小）',
                'default' => null,
                'limit' => null,
                'null' => true,
            ])
            ->addColumn('price_range_max', 'integer', [
                'comment' => '価格帯（最大）',
                'default' => null,
                'limit' => null,
                'null' => true,
            ])
            ->addColumn('feature_tags', 'text', [
                'comment' => '特徴タグ（カンマ区切り）',
                'default' => null,
                'limit' => null,
                'null' => true,
            ])
            ->addColumn('website_url', 'string', [
                'comment' => '公式ウェブサイトURL',
                'default' => null,
                'limit' => 500,
                'null' => true,
            ])
            ->addColumn('is_premium', 'boolean', [
                'comment' => 'プレミアムブランドフラグ',
                'default' => false,
                'limit' => null,
                'null' => false,
            ])
            ->addColumn('deleted', 'datetime', [
                'comment' => '削除日時(論理削除)',
                'default' => null,
                'limit' => null,
                'null' => true,
            ])
            ->addColumn('created', 'datetime', [
                'comment' => '作成日時',
                'default' => 'CURRENT_TIMESTAMP',
                'limit' => null,
                'null' => false,
            ])
            ->addColumn('modified', 'datetime', [
                'comment' => '更新日時',
                'default' => 'CURRENT_TIMESTAMP',
                'limit' => null,
                'null' => false,
                'update' => 'CURRENT_TIMESTAMP',
            ])
            ->addIndex(
                [
                    'maker_id',
                ],
                [
                    'name' => 'idx_brands_maker_id',
                ]
            )
            ->addIndex(
                [
                    'name',
                ],
                [
                    'name' => 'idx_brands_name',
                ]
            )
            ->addIndex(
                [
                    'target_gender',
                ],
                [
                    'name' => 'idx_brands_target_gender',
                ]
            )
            ->addIndex(
                [
                    'is_premium',
                ],
                [
                    'name' => 'idx_brands_is_premium',
                ]
            )
            ->addIndex(
                [
                    'deleted',
                ],
                [
                    'name' => 'idx_brands_deleted',
                ]
            )
            ->addIndex(
                [
                    'created',
                ],
                [
                    'name' => 'idx_brands_created',
                ]
            )
            ->addForeignKey(
                'maker_id',
                'makers',
                'id',
                [
                    'delete' => 'CASCADE',
                    'update' => 'CASCADE',
                    'constraint' => 'fk_brands_maker_id'
                ]
            )
            ->create();
    }

    /**
     * Down Method.
     *
     * More information on this method is available here:
     * https://book.cakephp.org/phinx/0/en/migrations.html#the-down-method
     * @return void
     */
    public function down(): void
    {
        $this->table('brands')->drop()->save();
    }
}
