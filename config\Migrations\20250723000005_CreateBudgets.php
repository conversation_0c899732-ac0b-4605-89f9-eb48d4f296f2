<?php
declare(strict_types=1);

use Migrations\AbstractMigration;

class CreateBudgets extends AbstractMigration
{
    /**
     * Up Method.
     *
     * More information on this method is available here:
     * https://book.cakephp.org/phinx/0/en/migrations.html#the-up-method
     * @return void
     */
    public function up(): void
    {
        $this->table('budgets')
            ->addColumn('product_id', 'integer', [
                'comment' => '商品ID',
                'default' => null,
                'limit' => null,
                'null' => false,
            ])
            ->addColumn('type', 'integer', [
                'comment' => 'カタログタイプ(1:紙, 2:デジタル)',
                'default' => null,
                'limit' => null,
                'null' => false,
            ])
            ->addColumn('price', 'integer', [
                'comment' => '単価',
                'default' => null,
                'limit' => null,
                'null' => false,
            ])
            ->addColumn('budget_quantity', 'integer', [
                'comment' => '予算数量',
                'default' => null,
                'limit' => null,
                'null' => false,
            ])
            ->addColumn('is_active', 'boolean', [
                'comment' => '有効フラグ（true: 有効, false: 無効）',
                'default' => true,
                'limit' => null,
                'null' => false,
            ])
            ->addColumn('start_date', 'datetime', [
                'comment' => '予算開始日',
                'default' => null,
                'limit' => null,
                'null' => false,
            ])
            ->addColumn('end_date', 'datetime', [
                'comment' => '予算終了日',
                'default' => null,
                'limit' => null,
                'null' => false,
            ])
            ->addColumn('priority', 'integer', [
                'comment' => '優先順位',
                'default' => 0,
                'limit' => null,
                'null' => false,
            ])
            ->addColumn('created', 'datetime', [
                'comment' => '作成日時',
                'default' => 'CURRENT_TIMESTAMP',
                'limit' => null,
                'null' => false,
            ])
            ->addColumn('modified', 'datetime', [
                'comment' => '更新日時',
                'default' => 'CURRENT_TIMESTAMP',
                'limit' => null,
                'null' => false,
                'update' => 'CURRENT_TIMESTAMP',
            ])
            ->addIndex(
                [
                    'product_id',
                ],
                [
                    'name' => 'idx_budgets_product_id',
                ]
            )
            ->addIndex(
                [
                    'type',
                ],
                [
                    'name' => 'idx_budgets_type',
                ]
            )
            ->addIndex(
                [
                    'is_active',
                ],
                [
                    'name' => 'idx_budgets_is_active',
                ]
            )
            ->addIndex(
                [
                    'start_date',
                ],
                [
                    'name' => 'idx_budgets_start_date',
                ]
            )
            ->addIndex(
                [
                    'end_date',
                ],
                [
                    'name' => 'idx_budgets_end_date',
                ]
            )
            ->addIndex(
                [
                    'priority',
                ],
                [
                    'name' => 'idx_budgets_priority',
                ]
            )
            ->addIndex(
                [
                    'product_id',
                    'type',
                    'is_active',
                ],
                [
                    'name' => 'idx_budgets_product_type_active',
                ]
            )
            ->addIndex(
                [
                    'created',
                ],
                [
                    'name' => 'idx_budgets_created',
                ]
            )
            ->addForeignKey(
                'product_id',
                'products',
                'id',
                [
                    'delete' => 'CASCADE',
                    'update' => 'CASCADE',
                    'constraint' => 'fk_budgets_product_id'
                ]
            )
            ->create();
    }

    /**
     * Down Method.
     *
     * More information on this method is available here:
     * https://book.cakephp.org/phinx/0/en/migrations.html#the-down-method
     * @return void
     */
    public function down(): void
    {
        $this->table('budgets')->drop()->save();
    }
}
