<?php

namespace App\Enums\EntityFields;

enum ESchoolBagForm: string
{
    case EMAIL = 'email';
    case LOGIN_PWD = 'login_pwd';
    case LOGIN_ID = 'login_id';
    case NAME1 = 'name1';
    case NAME2 = 'name2';
    case NAME1_HURIGANA = 'name1_hurigana';
    case NAME2_HURIGANA = 'name2_hurigana';
    case TEL = 'tel';
    case ZIP_CODE = 'zip_code';
    case TDFK_CD = 'tdfk_cd';
    case ADDRESS1 = 'address1';
    case ADDRESS2 = 'address2';
    case ADDRESS3 = 'address3';
    case BODY = 'body';
    case EXT_01 = 'ext_01';
    case EMAIL_SEND_NG_FLG = 'email_send_ng_flg';

    /**
     * @return string
     */
    public function description(): string
    {
        return match ($this) {
            self::EMAIL => 'email',
            self::LOGIN_PWD => 'パスワード',
            self::LOGIN_ID => 'ログインID',
            self::NAME1 => '姓',
            self::NAME2 => '名',
            self::NAME1_HURIGANA => 'セイ',
            self::NAME2_HURIGANA => 'メイ',
            self::TEL => '電話番号',
            self::ZIP_CODE => '郵便番号',
            self::TDFK_CD => '都道府県',
            self::ADDRESS1 => '住所1',
            self::ADDRESS2 => '住所2',
            self::ADDRESS3 => '住所3',
            self::BODY => '質問回答',
            self::EXT_01 => '注文情報',
            self::EMAIL_SEND_NG_FLG => 'メルマガ拒否フラグ',
        };
    }
}
