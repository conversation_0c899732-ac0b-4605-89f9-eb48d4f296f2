<?php

namespace App\Enums;

use Cake\Utility\Text;

/**
 * バリデーション文言定義
 */
enum EValidationErrorMessage: string
{
    case REQUIRE_PRESENCE = ':fieldは必須です';
    case NOT_EMPTY_STRING = ':fieldに入力してください';
    case MAX_LENGTH = ':fieldは:max文字以内で入力してください';
    case EXTRA_FIELD = 'フィールドは許可されていません';

    case INTEGER = ':fieldは整数で入力してください';
    case NON_NEGATIVE_INTEGER = ':fieldは正数で入力してください';
    case NUMERIC = ':fieldは数値で入力してください';
    case IN_LIST = ':fieldの値が正しくありません';
    case GREATER_THAN = ':fieldは:valueより大きい値で入力してください';
    case LESS_THAN_OR_EQUAL = ':fieldは:value以下の値で入力してください';
    case GREATER_THAN_OR_EQUAL = ':fieldは:value以上の値で入力してください';
    case IS_ARRAY = ':fieldは配列で指定してください';
    case NOT_EMPTY_ARRAY = ':fieldは1つ以上必要です';
    case SCALAR = ':fieldが正しくありません';
    case BOOLEAN = ':fieldはBooleanで指定してください';
    case UNIQUE = ':fieldはすでに使われています';
    case EXISTS_IN = ':fieldが存在していません';

    case DATE = ':fieldの日付形式が正しくありません';
    case UUID = ':fieldのUUID形式が正しくありません';
    case MD5 = ':fieldのmd5形式が正しくありません';

    case EMAIL = ':fieldの形式が正しくありません';

    case JSON_ERROR_NONE = ':fieldのJSON形式が正しくありません';

    /**
     * メッセージ置換処理
     * @param array $params
     * @return string
     */
    public function format(array $params = []): string
    {
        return Text::insert($this->value, $params);
    }
}
