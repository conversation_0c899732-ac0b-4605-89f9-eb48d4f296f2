<?php

namespace App\Mailer;

use App\Kuroko\Entity\AccessToken;
use Cake\Core\Configure;
use Cake\Core\InstanceConfigTrait;
use Cake\Utility\Hash;

class FrontLink
{
    use InstanceConfigTrait;
    private static ?FrontLink $instance = null;

    protected array $_defaultConfig = [];

    private function __construct()
    {
        $this->setConfig(Configure::read("FrontLink"));
    }

    private static function getInstance(): static
    {
        if (is_null(static::$instance)) {
            static::$instance = new static();
        }
        return static::$instance;
    }

    public static function getFormalRegistLink(AccessToken|string $accessToken): string
    {
        $token = is_string($accessToken) ? $accessToken : $accessToken->encryptToken();
        $queryString = http_build_query([
            static::getInstance()->getConfig('member.links.formalRegist.queryKeys.accessToken') => $token,
        ]);
        $url = static::getInstance()->getConfig('member.links.formalRegist.url') . '?' . $queryString;
        return $url;
    }

    public static function getPasswordResetLink(string $token): string
    {
        $queryString = http_build_query([
            static::getInstance()->getConfig('member.links.passwordReset.queryKeys.tmpToken') => $token,
        ]);
        $url = static::getInstance()->getConfig('member.links.passwordReset.url') . '?' . $queryString;
        return $url;
    }

    public static function getKurocoPasswordResetLink(array $tmpData): string
    {
        $queryString = http_build_query([
            static::getInstance()->getConfig('member.links.passwordReset.queryKeys.tmpToken') => Hash::get($tmpData, "token"),
            static::getInstance()->getConfig('member.links.passwordReset.queryKeys.tmpPassword') => Hash::get($tmpData, "temp_pwd"),
        ]);
        $url = static::getInstance()->getConfig('member.links.passwordReset.url') . '?' . $queryString;
        return $url;
    }

    public static function getMyLink(): string
    {
        $url = static::getInstance()->getConfig('member.links.my.url');
        return $url;
    }

    // public static function getLoginLink(): string
    // {
    //     $url = static::getInstance()->getConfig('member.links.login.url');
    //     return $url;
    // }
}
