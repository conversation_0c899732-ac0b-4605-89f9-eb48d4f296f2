<?php

declare(strict_types=1);

namespace App\Controller\NuxtBuild;

use App\Controller\NuxtBuildController;
use App\Form\DisableParametersForm;
use App\Service\AllProductsService;
use Cake\Event\Event;
use Cake\Log\Log;

/**
 * AllProducts Controller
 */
class AllProductsController extends NuxtBuildController
{
    /**
     * Index method
     */
    public function index(): void
    {
        $this->_onSuccess(function (Event $event, array $products) {
            $this->_setSuccessResponse([
                'products' => $products
            ]);
        });
        $this->_onError(function (Event $event, array $errors) {
            Log::debug(print_r($errors, true));
        });

        $this->_index((new AllProductsService())->setForm(
            new DisableParametersForm()
        ));
    }
}
