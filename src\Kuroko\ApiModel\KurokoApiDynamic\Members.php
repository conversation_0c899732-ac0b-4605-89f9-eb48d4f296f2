<?php

namespace App\<PERSON>roko\ApiModel\KurokoApiDynamic;

use App\Kuroko\ApiModel\KurokoApiDynamicModel;
use App\Kuroko\Entity\AccessToken;
use App\Kuroko\Entity\Member;
use App\Kuroko\Entity\InquiryDetail;
use Cake\Log\Log;
use Cake\Utility\Hash;

class Members extends KurokoApiDynamicModel
{

    /**
     * @param AccessToken $accessToken
     * @param bool $isByLogin
     * @return Member|null
     */
    public function me(AccessToken $accessToken, bool $isByLogin = false): ?Member
    {
        $token = $isByLogin ? $accessToken->encryptToken() : $accessToken->getAccessToken();
        $this->setToken($token);
        $response = $this->get($this->getEndPoint("memberMe"));
        if ($response->isSuccess() && $body = $response->getStringBody()) {
            if ($data = json_decode($body, true)) {
                if (Hash::check($data, "details")) {
                    return (new Member($data))->isIdentityOn();
                }
            }
        }
        Log::debug(__METHOD__ . " response: " . $response->getStringBody());
        return null;
    }

    /**
     * @param Member $member
     * @param array $data
     * @return Member|null
     */
    public function update(Member $member, array $data): ?Member
    {
        $this->setToken($member->getAccessToken()->getAccessToken());
        $response = $this->post($this->getEndPoint("memberUpdate"), json_encode($data));
        //        $response = $this->post($this->getEndPointWithId("memberUpdate", $member->getId()), $data);
        if (($response->isSuccess() || $response->getStatusCode() == 400) && $body = $response->getStringBody()) {
            if ($data = json_decode($body, true)) {
                if (empty(Hash::get($data, "errors", []))) {
                    return $this->me($member->getAccessToken());
                } else {
                    return new Member($data);
                }
            }
        }
        Log::debug(__METHOD__ . " request data: " . json_encode($data));
        Log::debug(__METHOD__ . " response: " . $response->getStringBody());
        return null;
    }

    /**
     * 仮会員登録
     * @param array $data
     * @return Member|null
     */
    public function regist(array $data): ?Member
    {
        // 認証していないので、システムユーザで強制的に実行
        $this->_setSysUserToken();
        $response = $this->post($this->getEndPoint("memberRegist"), json_encode($data));

        if ($response->isSuccess() && $body = $response->getStringBody()) {
            if ($data = json_decode($body, true)) {
                if (Hash::check($data, "id")) {
                    return new Member($data);
                }
            }
        }
        Log::debug(__METHOD__ . " request data: " . json_encode($data));
        Log::debug(__METHOD__ . " response: " . $response->getStringBody());
        // debug($response->getStringBody());
        return null;
    }

    public function updateStatusAndGetFormInfo(AccessToken $accessToken, string $status): ?InquiryDetail
    {
        $this->setToken($accessToken->getAccessToken());
        $response = $this->post($this->getEndPoint("updateStatusAndGetFormInfo"), json_encode(["status" => $status]));

        if ($response->isSuccess() && $body = $response->getStringBody()) {
            if ($data = json_decode($body, true)) {
                if (empty(Hash::get($data, "errors", [])) && Hash::check($data, "details.inquiry_bn_id")) {

                    return new InquiryDetail($data);
                }
            }
        }
        Log::debug(__METHOD__ . " response: " . $response->getStringBody());
        // debug($response->getStringBody());
        return null;
    }

    /**
     * 仮会員登録時、バリデーションチェックのみ行う
     * @param array $data
     * @return Member|null
     */
    public function registValidateOnly(array $data): ?Member
    {
        // 認証していないので、システムユーザで強制的に実行
        $this->_setSysUserToken();
        $response = $this->post($this->getEndPoint("memberRegistValidateOnly"), json_encode($data));

        if (($response->isSuccess() || $response->getStatusCode() == 400) && $body = $response->getStringBody()) {
            if ($data = json_decode($body, true)) {
                return new Member($data);
            }
        }
        Log::debug(__METHOD__ . " request data: " . json_encode($data));
        Log::debug(__METHOD__ . " response: " . $response->getStringBody());
        return null;
    }

    /**
     * MakerのIDを指定して、Memberのリストを取得する。
     * @param array $ids MakerのIDの配列
     * @return Member[] Memberのインスタンスの配列
     */
    public function listForMakerByIds(array $ids = []): array
    {
        // 認証していないので、システムユーザで強制的に実行
        $this->_setSysUserToken();
        $response = $this->get($this->getEndPoint("memberList"), [
            "id" => $ids
        ]);
        if ($response->isSuccess() && $body = $response->getStringBody()) {
            if ($data = json_decode($body, true)) {
                if (empty(Hash::get($data, "errors", [])) && Hash::check($data, "list")) {
                    return Member::creates(Hash::get($data, "list"));
                } else {
                    Log::error(__METHOD__ . " response: " . $response->getStringBody());
                }
            }
        }
        Log::debug(__METHOD__ . " request data: " . json_encode($data));
        Log::debug(__METHOD__ . " response: " . $response->getStringBody());
        return [];
    }

    /**
     * SWBメンバー一覧を取得する。（Administrator,SWBのみ）
     * @param Member $member ログイン中のメンバー
     * @param array $ids 指定するメンバーIDの配列
     * @return Member[] Memberのインスタンスの配列
     */
    public function swbList(Member $member, array $ids = []): array
    {
        $this->setToken($member->getAccessToken()->getAccessToken());
        $response = $this->get($this->getEndPoint("memberSwbList"), [
            "member_id" => $ids
        ]);
        if ($response->isSuccess() && $body = $response->getStringBody()) {
            if ($data = json_decode($body, true)) {
                if (empty(Hash::get($data, "errors", [])) && Hash::check($data, "list")) {
                    return Member::creates(Hash::get($data, "list"));
                } else {
                    Log::error(__METHOD__ . " response: " . $response->getStringBody());
                }
            }
        }
        Log::debug(__METHOD__ . " request data: " . json_encode($data));
        Log::debug(__METHOD__ . " response: " . $response->getStringBody());
        return [];
    }
}
