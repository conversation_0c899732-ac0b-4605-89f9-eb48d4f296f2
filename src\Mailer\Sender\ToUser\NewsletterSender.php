<?php

namespace App\Mailer\Sender\ToUser;

use App\Mailer\Sender\IToUserSender;
use App\Mailer\Sender\SenderTrait;
use App\Mailer\Sender\ToUserSenderTrait;

class NewsletterSender implements IToUserSender
{
    use SenderTrait;
    use ToUserSenderTrait;

    protected string $_subject;
    public function __construct(string $toEmail, string $toName, string $subject = "")
    {
        $this->_toEmail = $toEmail;
        $this->_toName = $toName;
        $this->_subject = $subject;
    }

    public function getSubject(): string
    {
        if (!empty($this->_subject)) {
            return $this->_subject;
        }
        return "【新着カタログのお知らせ】新たに４メーカーのランドセルカタログが追加！";
    }

    public function getTemplate(): string
    {
        return "to_user/NewsletterSender";
    }
}
