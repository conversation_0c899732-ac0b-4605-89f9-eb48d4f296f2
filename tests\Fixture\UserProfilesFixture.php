<?php
declare(strict_types=1);

namespace App\Test\Fixture;

use Cake\TestSuite\Fixture\TestFixture;
use App\Utility\Encryptor;

/**
 * UserProfilesFixture
 */
class UserProfilesFixture extends TestFixture
{
    /**
     * Table name
     */
    public $table = 'user_profiles';

    /**
     * Fields
     */
    public $fields = [
        'id' => ['type' => 'integer', 'length' => null, 'unsigned' => false, 'null' => false, 'default' => null, 'comment' => '', 'autoIncrement' => true, 'precision' => null],
        'general_user_id' => ['type' => 'integer', 'length' => null, 'unsigned' => false, 'null' => false, 'default' => null, 'comment' => '一般ユーザーID', 'precision' => null],
        'last_name' => ['type' => 'text', 'length' => null, 'null' => false, 'default' => null, 'collate' => 'utf8mb4_unicode_ci', 'comment' => '姓（暗号化）', 'precision' => null],
        'first_name' => ['type' => 'text', 'length' => null, 'null' => false, 'default' => null, 'collate' => 'utf8mb4_unicode_ci', 'comment' => '名（暗号化）', 'precision' => null],
        'last_name_kana' => ['type' => 'text', 'length' => null, 'null' => false, 'default' => null, 'collate' => 'utf8mb4_unicode_ci', 'comment' => '姓カナ（暗号化）', 'precision' => null],
        'first_name_kana' => ['type' => 'text', 'length' => null, 'null' => false, 'default' => null, 'collate' => 'utf8mb4_unicode_ci', 'comment' => '名カナ（暗号化）', 'precision' => null],
        'zip_code' => ['type' => 'text', 'length' => null, 'null' => false, 'default' => null, 'collate' => 'utf8mb4_unicode_ci', 'comment' => '郵便番号（暗号化）', 'precision' => null],
        'prefecture_code' => ['type' => 'text', 'length' => null, 'null' => false, 'default' => null, 'collate' => 'utf8mb4_unicode_ci', 'comment' => '都道府県コード（暗号化）', 'precision' => null],
        'address1' => ['type' => 'text', 'length' => null, 'null' => false, 'default' => null, 'collate' => 'utf8mb4_unicode_ci', 'comment' => '住所1（暗号化）', 'precision' => null],
        'address2' => ['type' => 'text', 'length' => null, 'null' => true, 'default' => null, 'collate' => 'utf8mb4_unicode_ci', 'comment' => '住所2（暗号化）', 'precision' => null],
        'address3' => ['type' => 'text', 'length' => null, 'null' => true, 'default' => null, 'collate' => 'utf8mb4_unicode_ci', 'comment' => '住所3（暗号化）', 'precision' => null],
        'tel' => ['type' => 'text', 'length' => null, 'null' => false, 'default' => null, 'collate' => 'utf8mb4_unicode_ci', 'comment' => '電話番号（暗号化）', 'precision' => null],
        'email_send_ng_flg' => ['type' => 'boolean', 'length' => null, 'null' => false, 'default' => false, 'comment' => 'メール送信拒否フラグ（0: 送信可, 1: 送信拒否）', 'precision' => null],
        'notes' => ['type' => 'text', 'length' => null, 'null' => true, 'default' => null, 'collate' => 'utf8mb4_unicode_ci', 'comment' => '備考（暗号化）', 'precision' => null],
        'created' => ['type' => 'datetime', 'length' => null, 'precision' => null, 'null' => false, 'default' => 'CURRENT_TIMESTAMP', 'comment' => '作成日時'],
        'modified' => ['type' => 'datetime', 'length' => null, 'precision' => null, 'null' => false, 'default' => 'CURRENT_TIMESTAMP', 'comment' => '更新日時'],
        '_indexes' => [
            'idx_user_profiles_created' => ['type' => 'index', 'columns' => ['created'], 'length' => []],
        ],
        '_constraints' => [
            'primary' => ['type' => 'primary', 'columns' => ['id'], 'length' => []],
            'uk_user_profiles_general_user_id' => ['type' => 'unique', 'columns' => ['general_user_id'], 'length' => []],
        ],
        '_options' => [
            'engine' => 'InnoDB',
            'collation' => 'utf8mb4_unicode_ci'
        ],
    ];

    /**
     * Init method
     */
    public function init(): void
    {
        $this->records = [
            [
                'id' => 1,
                'general_user_id' => 1,
                'last_name' => Encryptor::encrypt('山田'), // テスト時に暗号化される
                'first_name' => Encryptor::encrypt('太郎'),
                'last_name_kana' => Encryptor::encrypt('ヤマダ'),
                'first_name_kana' => Encryptor::encrypt('タロウ'),
                'zip_code' => Encryptor::encrypt('123-4567'),
                'prefecture_code' => Encryptor::encrypt('13'),
                'address1' => Encryptor::encrypt('東京都渋谷区神宮前1-1-1'),
                'address2' => Encryptor::encrypt('テストマンション101'),
                'address3' => null,
                'tel' => Encryptor::encrypt('03-1234-5678'),
                'email_send_ng_flg' => false,
                'notes' => Encryptor::encrypt('テストユーザー1の備考'),
                'created' => '2024-01-01 10:00:00',
                'modified' => '2024-01-01 10:00:00',
            ],
            [
                'id' => 2,
                'general_user_id' => 3,
                'last_name' => Encryptor::encrypt('佐藤'),
                'first_name' => Encryptor::encrypt('花子'),
                'last_name_kana' => Encryptor::encrypt('サトウ'),
                'first_name_kana' => Encryptor::encrypt('ハナコ'),
                'zip_code' => Encryptor::encrypt('567-8901'),
                'prefecture_code' => Encryptor::encrypt('27'),
                'address1' => Encryptor::encrypt('大阪府大阪市中央区難波2-2-2'),
                'address2' => Encryptor::encrypt('難波ビル'),
                'address3' => null,
                'tel' => Encryptor::encrypt('06-9876-5432'),
                'email_send_ng_flg' => true,
                'notes' => null,
                'created' => '2024-01-03 12:00:00',
                'modified' => '2024-01-03 12:00:00',
            ],
        ];
        parent::init();
    }
}
