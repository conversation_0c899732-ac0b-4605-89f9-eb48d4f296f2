<?php

namespace App\Kuroko\ApiModel;

use App\Kuroko\Http\Client\KurokoApiClient;
use Cake\Core\Configure;
use Cake\Core\InstanceConfigTrait;
use Cake\Http\Client\Response;

abstract class KurokoApiModel
{
    use InstanceConfigTrait;
    const CNT = "cnt";

    protected array $_defaultConfig = [];
    private KurokoApiClient $_connection;

    public function __construct(array $config = [])
    {
        $this->setConfig(array_merge(Configure::read("Kuroko.api"), $config));
        $connectionClass = $this->getConfig("connection");
        $this->_connection = new $connectionClass($this->getConfig("client"));
    }

    protected function getConnection(): KurokoApiClient
    {
        return $this->_connection;
    }

    public function setToken(string $token): static
    {
        $this->getConnection()->setToken($token);
        return $this;
    }


    protected function get($url, array $data = [], array $options = []): Response
    {
        return $this->getConnection()->get($url, $data, $options);
    }

    public function post(string $url, $data = [], array $options = []): Response
    {
        return $this->getConnection()->post($url, $data, $options);
    }

    protected function getEndPoint(string $endpoint)
    {
        return $this->getConfig("client.endPoint." . $endpoint);
    }
}
