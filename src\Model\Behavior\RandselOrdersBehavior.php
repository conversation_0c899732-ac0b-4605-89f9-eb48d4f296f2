<?php

declare(strict_types=1);

namespace App\Model\Behavior;

use App\Enums\EntityFields\EClientOrderForm;
use App\Enums\EntityFields\ESwbOrderForm;
use App\Enums\EntityFields\ERandselOrder;
use App\Enums\Options\ERandselOrderStatus;
use Cake\ORM\Behavior;
use Cake\ORM\Query;
use Cake\Utility\Hash;
use Cake\I18n\FrozenTime;
use Cake\Log\Log;

/**
 * RandselOrders behavior
 */
class RandselOrdersBehavior extends Behavior
{
    /**
     * Default configuration.
     *
     * @var array<string, mixed>
     */
    protected $_defaultConfig = [];


    use BehaviorTrait;


    /**
     * @param Query $query
     * @param array $options
     * @return Query
     */
    public function findSearch(Query $query, array $options = []): Query
    {

        foreach (
            [
                ERandselOrder::ID,
                ERandselOrder::MAKER_ID,
                ERandselOrder::MEMBER_ID,
                ERandselOrder::GENERAL_USER_ID,
                ERandselOrder::PRODUCT_ID,
                ERandselOrder::STATUS,
                ERandselOrder::APPROVAL_TYPE,
                ERandselOrder::EMAIL_SEND_NG_FLG,
            ] as $field
        ) {
            /** @var ERandselOrder $field */
            $query = $this->whereEqualIn($query, $field->value, $options);
        }

        $searchDateType = Hash::get($options, EClientOrderForm::SEARCH_DATE_TYPE->value);
        $createdFieldName = match ($searchDateType) {
            'order-date' => ERandselOrder::CREATED->value,
            'status-updated-date' => ERandselOrder::STATUS_MODIFIED->value,
            default => null,
        };
        if ($createdFieldName !== null) {
            if ($from = Hash::get($options, EClientOrderForm::FROM->value)) {
                $query->where([
                    $this->_table->getAlias() . '.' . $createdFieldName . ' >=' => $from,
                ]);
            }
            if ($to = Hash::get($options, EClientOrderForm::TO->value)) {
                $to = (new FrozenTime($to))->setTime(23, 59, 59)->i18nFormat('yyyy-MM-dd HH:mm:ss');
                $query->where([
                    $this->_table->getAlias() . '.' . $createdFieldName . ' <=' => $to,
                ]);
            }
        }

        $query->orderDesc($this->_table->getAlias() . '.' . ERandselOrder::CREATED->value);
        return $query;
    }

    /**
     * 月ごとのメーカー別（カタログ別）請求データを取得するFinder
     * @param Query $query
     * @param array $options
     * @return Query
     *
     * Example options:
     * [
     *     'maker_id' => 1,
     *     'from' => '2022-01-01',
     *     'to' => '2022-01-31',
     *     'group_by' => 'product_id',
     * ]
     */
    public function findInvoiceSearch(Query $query, array $options = []): Query
    {
        // SELECT maker_id, status_modified_year_month, sum(price) as sum
        // FROM randsel_orders
        // WHERE status = 1
        //   AND maker_id = 1
        //   AND status_modified >= '2022-01-01'
        //   AND status_modified <= '2022-01-31'
        // GROUP BY maker_id, status_modified_year_month
        // ORDER BY status_modified_year_month DESC

        // SELECT maker_id, status_modified_year_month, sum(price) as sum, product_name
        // FROM randsel_orders
        // WHERE status = 1
        //   AND maker_id = 1
        //   AND status_modified >= '2022-01-01'
        //   AND status_modified <= '2022-01-31'
        // GROUP BY maker_id, status_modified_year_month, product_id
        // ORDER BY status_modified_year_month DESC

        $query->select([
            'maker_id',
            'status_modified_year_month' => $query->func()->DATE_FORMAT(
                [
                    $this->_table->getAlias() . '.status_modified' => 'identifier',
                    "'%Y-%m'" => 'literal'
                ],
            ),
            'count' => $query->func()->count('*')
        ]);
        $query = $this->sumQuery($query, $this->_table->getAlias() . '.' . ERandselOrder::PRICE->value);

        // WHERE文
        $query = $this->whereEqualIn($query, ESwbOrderForm::MAKER_ID->value, $options);
        $query = $this->whereEqualIn($query, ERandselOrder::STATUS->value, [
            ERandselOrder::STATUS->value => ERandselOrderStatus::APPROVED->value
        ]);

        // WHERE status_modified >= ??
        if ($from = Hash::get($options, ESwbOrderForm::FROM->value)) {
            $to = (new FrozenTime($from))->modify('first day of this month')->setTime(0, 0, 0)->i18nFormat('yyyy-MM-dd HH:mm:ss');
            $query->where([
                $this->_table->getAlias() . '.' . ERandselOrder::STATUS_MODIFIED->value . ' >=' => $from,
            ]);
        }

        // WHERE status_modified <= ??
        if ($to = Hash::get($options, ESwbOrderForm::TO->value)) {
            $to = (new FrozenTime($to))->modify('last day of this month')->setTime(23, 59, 59)->i18nFormat('yyyy-MM-dd HH:mm:ss');
            $query->where([
                $this->_table->getAlias() . '.' . ERandselOrder::STATUS_MODIFIED->value . ' <=' => $to,
            ]);
        }

        // GROUP BY maker_id, status_modified_year_month
        $query->group([
            $this->_table->getAlias() . '.' . ERandselOrder::MAKER_ID->value,
            $query->func()->DATE_FORMAT([
                $this->_table->getAlias() . '.status_modified' => 'identifier',
                "'%Y-%m'" => 'literal'
            ])
        ]);

        // ORDER BY status_modified_year_month DESC
        $query->orderDesc('status_modified_year_month');

        // GROUP BY product_id (if group_by = product_id)
        if (Hash::get($options, ESwbOrderForm::GROUP_BY->value) === ERandselOrder::PRODUCT_ID->value) {
            $groupBy = Hash::get($options, ESwbOrderForm::GROUP_BY->value);
            $query->group([$this->_table->getAlias() . '.' . $groupBy]);
            $query->select([
                'product_name' => $query->func()->ANY_VALUE([
                    $this->_table->getAlias() . '.product_name' => 'identifier',
                ]),
                'product_id' => $query->func()->ANY_VALUE([
                    $this->_table->getAlias() . '.product_id' => 'identifier',
                ]),
            ]);
        }

        return $query;
    }
}
