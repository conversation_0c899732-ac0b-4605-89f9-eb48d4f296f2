<?php

namespace App\Validation\Validator;

use App\Enums\EntityFields\EClientOrderForm;
use App\Enums\EValidationErrorMessage;
use App\Validation\IValidator;
use App\Validation\ValidatorTrait;
use Cake\Validation\Validator;

class ClientOrderValidator implements IValidator
{
    use ValidatorTrait;
    static private function to(Validator $validator, array $options = []): Validator
    {
        $insertText = [
            'field' => EClientOrderForm::TO->description(),
        ];
        $field = EClientOrderForm::TO->value;
        $validator
            ->allowEmptyString($field)
            //            ->requirePresence($field, true, EValidationErrorMessage::REQUIRE_PRESENCE->format($insertText))
            ->date($field, ['ymd'], EValidationErrorMessage::DATE->format($insertText)) // Y-m-d形式のチェックを追加
//            ->notEmptyString($field, EValidationErrorMessage::NOT_EMPTY_STRING->format($insertText));
        ;
        return $validator;
    }

    static private function from(Validator $validator, array $options = []): Validator
    {
        $insertText = [
            'field' => EClientOrderForm::FROM->description(),
        ];
        $field = EClientOrderForm::FROM->value;
        $validator
            ->allowEmptyString($field)
            //            ->requirePresence($field, true, EValidationErrorMessage::REQUIRE_PRESENCE->format($insertText))
            ->date($field, ['ymd'], EValidationErrorMessage::DATE->format($insertText)) // Y-m-d形式のチェックを追加
//            ->notEmptyString($field, EValidationErrorMessage::NOT_EMPTY_STRING->format($insertText));
        ;
        return $validator;
    }

    static private function productId(Validator $validator, array $options = []): Validator
    {
        $insertText = [
            'field' => EClientOrderForm::PRODUCT_ID->description(),
        ];
        $field = EClientOrderForm::PRODUCT_ID->value;
        $validator
            ->allowEmptyString($field)
            ->nonNegativeInteger($field, EValidationErrorMessage::NON_NEGATIVE_INTEGER->format($insertText))
            //            ->requirePresence($field, true, EValidationErrorMessage::REQUIRE_PRESENCE->format($insertText))
//            ->notEmptyString($field, EValidationErrorMessage::NOT_EMPTY_STRING->format($insertText));
        ;
        return $validator;
    }

}
