<?php
declare(strict_types=1);

use Migrations\AbstractMigration;

class CreateProducts extends AbstractMigration
{
    /**
     * Up Method.
     *
     * More information on this method is available here:
     * https://book.cakephp.org/phinx/0/en/migrations.html#the-up-method
     * @return void
     */
    public function up(): void
    {
        $this->table('products')
            ->addColumn('maker_id', 'integer', [
                'comment' => 'メーカーID',
                'default' => null,
                'limit' => null,
                'null' => false,
            ])
            ->addColumn('brand_id', 'integer', [
                'comment' => 'ブランドID',
                'default' => null,
                'limit' => null,
                'null' => false,
            ])
            ->addColumn('is_display', 'boolean', [
                'comment' => '申し込みフォーム表示フラグ（true: 表示, false: 非表示）',
                'default' => false,
                'limit' => null,
                'null' => false,
            ])
            ->addColumn('year', 'integer', [
                'comment' => '年度',
                'default' => null,
                'limit' => null,
                'null' => false,
            ])
            ->addColumn('display_name', 'string', [
                'comment' => '表示商品名（ランドセルカタログ）',
                'default' => null,
                'limit' => 255,
                'null' => false,
            ])
            ->addColumn('description_html', 'text', [
                'comment' => '商品説明（HTML形式）',
                'default' => null,
                'limit' => null,
                'null' => true,
            ])
            ->addColumn('note_html', 'text', [
                'comment' => '備考（HTML形式）',
                'default' => null,
                'limit' => null,
                'null' => true,
            ])
            ->addColumn('mask_image_description', 'text', [
                'comment' => '画像に被せる説明文',
                'default' => null,
                'limit' => null,
                'null' => true,
            ])
            ->addColumn('image_url', 'string', [
                'comment' => '画像URL',
                'default' => null,
                'limit' => 500,
                'null' => true,
            ])
            ->addColumn('sort_order', 'integer', [
                'comment' => '表示順序',
                'default' => 0,
                'limit' => null,
                'null' => false,
            ])
            ->addColumn('pdf_url', 'string', [
                'comment' => 'pdfカタログURL',
                'default' => null,
                'limit' => 500,
                'null' => true,
            ])
            ->addColumn('price_range', 'text', [
                'comment' => '価格帯（例：3万円台、4万円台）',
                'default' => null,
                'limit' => null,
                'null' => true,
            ])
            ->addColumn('weight_range', 'text', [
                'comment' => '重さ帯（例：900g台、1000g台）',
                'default' => null,
                'limit' => null,
                'null' => true,
            ])
            ->addColumn('material', 'integer', [
                'comment' => '素材（1:人工皮革, 2:天然皮革, 3:合成皮革, 4:その他）',
                'default' => null,
                'limit' => null,
                'null' => true,
            ])
            ->addColumn('deleted', 'datetime', [
                'comment' => '削除日時(論理削除)',
                'default' => null,
                'limit' => null,
                'null' => true,
            ])
            ->addColumn('created', 'datetime', [
                'comment' => '作成日時',
                'default' => 'CURRENT_TIMESTAMP',
                'limit' => null,
                'null' => false,
            ])
            ->addColumn('modified', 'datetime', [
                'comment' => '更新日時',
                'default' => 'CURRENT_TIMESTAMP',
                'limit' => null,
                'null' => false,
                'update' => 'CURRENT_TIMESTAMP',
            ])
            ->addIndex(
                [
                    'maker_id',
                ],
                [
                    'name' => 'idx_products_maker_id',
                ]
            )
            ->addIndex(
                [
                    'brand_id',
                ],
                [
                    'name' => 'idx_products_brand_id',
                ]
            )
            ->addIndex(
                [
                    'is_display',
                ],
                [
                    'name' => 'idx_products_is_display',
                ]
            )
            ->addIndex(
                [
                    'year',
                ],
                [
                    'name' => 'idx_products_year',
                ]
            )
            ->addIndex(
                [
                    'sort_order',
                ],
                [
                    'name' => 'idx_products_sort_order',
                ]
            )
            ->addIndex(
                [
                    'material',
                ],
                [
                    'name' => 'idx_products_material',
                ]
            )
            ->addIndex(
                [
                    'deleted',
                ],
                [
                    'name' => 'idx_products_deleted',
                ]
            )
            ->addIndex(
                [
                    'created',
                ],
                [
                    'name' => 'idx_products_created',
                ]
            )
            ->addForeignKey(
                'maker_id',
                'makers',
                'id',
                [
                    'delete' => 'CASCADE',
                    'update' => 'CASCADE',
                    'constraint' => 'fk_products_maker_id'
                ]
            )
            ->addForeignKey(
                'brand_id',
                'brands',
                'id',
                [
                    'delete' => 'CASCADE',
                    'update' => 'CASCADE',
                    'constraint' => 'fk_products_brand_id'
                ]
            )
            ->create();
    }

    /**
     * Down Method.
     *
     * More information on this method is available here:
     * https://book.cakephp.org/phinx/0/en/migrations.html#the-down-method
     * @return void
     */
    public function down(): void
    {
        $this->table('products')->drop()->save();
    }
}
