import { Validations } from "~~/src/lib/Validations";

export default class ForgotPasswordForm {
    private _email: string;
    private _valid: boolean = false;

    constructor() {
        this._email = "";
    }

    get valid(): boolean {
        return this._valid;
    }

    set valid(value: boolean) {
        this._valid = value;
    }

    get email(): string {
        return this._email;
    }

    set email(value: string) {
        this._email = value;
    }

    get email_rules(): TValidationFunction[] {
        return [
            Validations.notEmptyString("メールアドレス"),
            Validations.email("メールアドレス"),
        ];
    }

    get data(): TPasswordReminders {
        return {
            email: this.email,
        };
    }
}
