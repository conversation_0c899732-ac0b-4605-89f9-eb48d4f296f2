<?php

namespace App\<PERSON>roko\ApiModel\KurokoApiDynamic;

use App\Enums\EntityFields\EClientOrderForm;
use App\Kuroko\ApiModel\KurokoApiDynamicModel;
use App\Kuroko\Entity\AccessToken;
use App\Kuroko\Entity\Member;
use App\Kuroko\Entity\ECOrder;
use App\Kuroko\Entity\Order;
use App\Service\RandselOrdersService;
use Cake\Log\Log;
use Cake\Utility\Hash;
use Cake\Core\Configure;
use Exception;

class ECOrders extends KurokoApiDynamicModel
{
    const TOPICS_CATEGORY_ID = "topics_category_id";
    const INST_YMDHI_FROM_PATH = "inst_ymdhi.from";
    const INST_YMDHI_TO_PATH = "inst_ymdhi.to";
    const PRODUCT_ID = "product_id";

    public function purchase(AccessToken $accessToken, array $orderProducts): ?ECOrder
    {
        Log::info(__METHOD__ . " orders: " . json_encode($orderProducts), ['scope' => ['ordersLog']]);
        $this->setToken($accessToken->getAccessToken());
        $response = $this->post($this->getEndPoint("eCOrderPurchase"), json_encode($orderProducts));

        if ($response->isSuccess() && $body = $response->getStringBody()) {
            if ($data = json_decode($body, true)) {
                if (empty(Hash::get($data, "errors", [])) && Hash::check($data, "ids")) {
                    try {
                        $ecOrder = new ECOrder($data);
                        $randselOrdersService = new RandselOrdersService();
                        //                    debug($ecOrder->getRandselOrderData());
                        if ($randselOrdersService->createRandselOrder($ecOrder->getRandselOrderData())) {
                            Log::info(__METHOD__ . " RDS 注文データ作成に成功", ['scope' => ['ordersLog']]);
                            return $ecOrder;
                        } else {
                            Log::error(__METHOD__ . " RDS 注文データ作成に失敗", ['scope' => ['ordersLog']]);
                            Log::error(__METHOD__ . " RDS 注文データ作成失敗時の ecOrder データ: " . json_encode($ecOrder->getJsonData()), ['scope' => ['ordersLog']]);
                            return $ecOrder;
                        }
                    } catch (Exception $exception) {
                        Log::error("kuroco のみ注文データ作成に成功", ['scope' => ['ordersLog']]);
                        Log::error(__METHOD__ . " kuroco のみ注文データ作成に成功時の ecOrder データ: " . json_encode($ecOrder->getJsonData()), ['scope' => ['ordersLog']]);
                        return $ecOrder;
                    }
                }
            }
        }
        Log::error(__METHOD__ . " API リクエスト失敗： " . $response->getStringBody() . " (" . $response->getStatusCode() . ")", ['scope' => ['ordersLog']]);
        return null;
    }

    /**
     * @param AccessToken $accessToken
     * @return array
     */
    public function list(AccessToken $accessToken): array
    {
        $this->setToken($accessToken->getAccessToken());

        $queryString = http_build_query([
            static::CNT => Configure::read("Kuroko.api.allListMaxCnt"),
        ]);
        $fullUrl = $this->getEndPoint("eCOrderList") . '?' . $queryString;

        $response = $this->get($fullUrl);

        if ($response->isSuccess() && $body = $response->getStringBody()) {
            if ($data = json_decode($body, true)) {
                if (empty(Hash::get($data, "errors", [])) && Hash::check($data, "list")) {
                    return Order::creates(Hash::get($data, "list"));
                }
            }
        }
        //        debug($response->getStringBody());
        return [];
    }

    /**
     * @param Member $member
     * @param array $data
     * @return array
     */
    public function listForMaker(Member $member, array $data = []): array
    {
        $this->setToken($member->getAccessToken()->getAccessToken());
        //debug($member->getAccessToken()->getAccessToken());
        //必要になる情報：開始日、終了日、プロジェクトID（array）
        //日付指定：https://swb-sbpg.g.kuroco.app/rcms-api/5/ec/order/all-list?cnt=1000&inst_ymdhi[from]=2024-07-01 00:00&inst_ymdhi[to]=2024-07-03 23:59
        //商材指定：https://swb-sbpg.g.kuroco.app/rcms-api/5/ec/order/all-list?cnt=1000&product_id[]=41207,&product_id[]=11111

        $query = [
            static::TOPICS_CATEGORY_ID => $member->getMakerId(),
            static::CNT => Configure::read("Kuroko.api.allListMaxCnt"),
        ];

        if ($form = Hash::get($data, EClientOrderForm::FROM->value)) {
            $query = Hash::insert($query, static::INST_YMDHI_FROM_PATH, $form);
        }
        if ($to = Hash::get($data, EClientOrderForm::TO->value)) {
            $query = Hash::insert($query, static::INST_YMDHI_TO_PATH, $to);
        }
        if ($productId = Hash::get($data, EClientOrderForm::PRODUCT_ID->value)) {
            $query = Hash::insert($query, static::PRODUCT_ID, [
                $productId
            ]);
        }
        $queryString = http_build_query($query);
        $fullUrl = $this->getEndPoint("eCOrderAllList") . '?' . $queryString;
        //        debug($fullUrl);

        Log::debug($fullUrl);
        $response = $this->get($fullUrl);

        if ($response->isSuccess() && $body = $response->getStringBody()) {
            if ($data = json_decode($body, true)) {
                if (empty(Hash::get($data, "errors", [])) && Hash::check($data, "list")) {
                    return Order::creates(Hash::get($data, "list"));
                } else {
                    Log::error($response->getStringBody());
                }
            }
        }
        return [];
    }

    /**
     * @param array $data
     * @return array
     */
    public function listForMigration(array $data = []): array
    {
        $this->_setSysUserToken();
        //debug($member->getAccessToken()->getAccessToken());
        //必要になる情報：開始日、終了日、プロジェクトID（array）
        //日付指定：https://swb-sbpg.g.kuroco.app/rcms-api/5/ec/order/all-list?cnt=1000&inst_ymdhi[from]=2024-07-01 00:00&inst_ymdhi[to]=2024-07-03 23:59
        //商材指定：https://swb-sbpg.g.kuroco.app/rcms-api/5/ec/order/all-list?cnt=1000&product_id[]=41207,&product_id[]=11111

        $query = [
            // static::TOPICS_CATEGORY_ID => $member->getMakerId(),
            // static::CNT => Configure::read("Kuroko.api.allListMaxCnt"),
        ];

        if ($form = Hash::get($data, EClientOrderForm::FROM->value)) {
            $query = Hash::insert($query, static::INST_YMDHI_FROM_PATH, $form);
        }
        if ($to = Hash::get($data, EClientOrderForm::TO->value)) {
            $query = Hash::insert($query, static::INST_YMDHI_TO_PATH, $to);
        }
        if ($productId = Hash::get($data, EClientOrderForm::PRODUCT_ID->value)) {
            $query = Hash::insert($query, static::PRODUCT_ID, [
                (int)$productId
            ]);
        }
        if ($ecOrderId = Hash::get($data, "ec_order_id")) {
            $query = Hash::insert($query, "ec_order_id", (int)$ecOrderId);
        }
        if ($pageID = Hash::get($data, "pageID", 1)) {
            $query = Hash::insert($query, "pageID", $pageID);
        }
        $queryString = http_build_query($query);
        $fullUrl = $this->getEndPoint("eCOrderAllList") . '?' . $queryString;

        Log::debug($fullUrl);
        $response = $this->get($fullUrl);
        if ($response->isSuccess() && $body = $response->getStringBody()) {
            if ($data = json_decode($body, true)) {
                if (empty(Hash::get($data, "errors", [])) && Hash::check($data, "list")) {
                    return ["list" => ECOrder::creates(Hash::get($data, "list")), "totalPageCnt" => (int)Hash::get($data, "pageInfo.totalPageCnt")];
                } else {
                    Log::error($response->getStringBody());
                }
            }
        }
        return [];
    }
}
