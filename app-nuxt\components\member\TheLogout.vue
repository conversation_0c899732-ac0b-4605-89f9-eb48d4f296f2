<script setup lang="ts">
import { ref } from "vue";
import { useRuntimeConfig } from "#app";
import MemberLogins from "~/src/models/MemberLogins";
import { useAuthStore } from "~/src/stores/auth";

const isLoading = ref(false);
const isLoggedOut = ref(false);
const config = useRuntimeConfig();
const authStore = useAuthStore();

// const logout = (): void => {
isLoading.value = true;
MemberLogins.create(config)
    .delete()
    .then((success) => {
        console.log(success);
        if (success) {
            authStore.clearAuth();
            isLoggedOut.value = true;
        } else {
            console.log("Logout error");
        }
        isLoading.value = false;
    });
// };
</script>

<template>
    <div>
        <div class="test">ログアウト</div>
        <template v-if="isLoading">
            <v-progress-circular indeterminate></v-progress-circular>
        </template>
        <template v-else>
            <template v-if="isLoggedOut">
                <v-container> ログアウトに成功しました </v-container>
            </template>
            <template v-else>
                <v-container class="test">
                    ログアウトに失敗しました！！！
                </v-container>
            </template>
        </template>
    </div>
</template>

<style scoped lang="scss">
.test {
    color: $error-color;
}
</style>
