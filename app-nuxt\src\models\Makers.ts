import { Model } from "~~/src/models/Model";
import { Maker } from "~~/src/models/entry/Maker";
import MakersClient from "~/src/lib/http/coverme-front/MakersClient";

export default class Makers extends Model {
    public getList(params?: Record<string, string | number>): Promise<Maker[]> {
        const config = this.config;
        return new Promise((resolve) => {
            // const kurokoEcProductClient = KurokoEcProductClient.create(config);
            MakersClient.create(config)
                .index<TResponseMakers>(params)
                .then(({ makers }) => {
                    resolve(Maker.creates(makers));
                })
                .catch((e) => console.error(e));
        });
    }
}
