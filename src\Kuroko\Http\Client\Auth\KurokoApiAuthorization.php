<?php

namespace App\Kuroko\Http\Client\Auth;

use Cake\Http\Client\Request;
use Cake\Log\Log;
use Cake\Utility\Hash;
use App\Utility\Encryptor;
use Psr\Http\Message\RequestInterface;

class KurokoApiAuthorization
{
    /**
     * KurokoAPI認証
     * Add Authorization header to the request.
     *
     * @param Request $request Request instance.
     * @param array $credentials Credentials.
     * @return Request&RequestInterface The updated request.
     */
    public function authentication(Request $request, array $credentials): Request
    {
//        Log::error("credentials : " . print_r($credentials, true));
        $decryptToken = Encryptor::decrypt(Hash::get($credentials, 'token'));
//        Log::error("credentials decryptToken : " . print_r($decryptToken, true));
        return $request->withHeader('X-RCMS-API-ACCESS-TOKEN', $decryptToken);
    }
}
