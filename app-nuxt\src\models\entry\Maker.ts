export class Maker {
    private readonly _maker_id: number;
    private readonly _maker_name: string;
    private readonly _ext: TMakerExt;
    constructor(data: TMaker) {
        this._maker_id = Number(data.maker_id);
        this._maker_name = data.maker_name;
        this._maker_name = data.maker_name;
        this._ext = data.ext;
    }

    static creates(makers: TMaker[]): Maker[] {
        return makers.map((maker: TMaker) => Maker.create(maker));
    }

    static create(maker: TMaker): Maker {
        return new Maker(maker);
    }

    get data(): TMaker {
        return {
            maker_id: this.maker_id,
            maker_name: this.maker_name,
            ext: this.ext,
        };
    }

    get maker_id(): number {
        return this._maker_id;
    }

    get maker_name(): string {
        return this._maker_name;
    }

    get ext(): TMakerExt {
        return this._ext;
    }
}
