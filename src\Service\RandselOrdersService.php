<?php

namespace App\Service;

use App\Enums\EntityFields\ERandselOrder;
use App\Enums\EntityFields\ESwbOrderForm;
use App\Enums\Options\ERandselOrderApprovalType;
use App\Enums\Options\ERandselOrderStatus;
use App\Kuroko\Entity\Member;
use App\Model\Entity\RandselOrder;
use App\Model\Table\RandselOrdersTable;
use Cake\Log\Log;
use Cake\Utility\Hash;
use Exception;

class RandselOrdersService implements IService
{
    use ServiceTrait;

    protected array $_defaultConfig = [
        "defaultModel" => RandselOrdersTable::class,
    ];


    public function initialize(): void {}


    /**
     * @param array $approvalData
     * @return bool
     *
     * @example
     *  (new RandselOrdersService())->setIdentity($this->getIdentity())->csvApproval([["id" => 0,"status" => 1]])
     */
    public function csvApproval(array $approvalData, bool $validateOnly = true): bool
    {
        return $this->processApproval($approvalData, ERandselOrderApprovalType::CSV, $validateOnly);
    }

    /**
     * @param array $approvalData
     * @return bool
     *
     * @example
     *  (new RandselOrdersService())->setIdentity($this->getIdentity())->screenApproval([["id" => 0,"status" => 1]])
     */
    public function screenApproval(array $approvalData): bool
    {
        return $this->processApproval($approvalData, ERandselOrderApprovalType::SCREEN);
    }

    private function processApproval(array $approvalData, ERandselOrderApprovalType $approvalType, bool $validateOnly = false): bool
    {
        /** @var Member $member */
        $member = $this->getIdentity();
        $makerId = $member->getMakerId();
        $errors = [];
        $self = $this;
        try {
            $success = $this->getDefaultModel()->getConnection()->transactional(
                function () use ($makerId, $approvalData, $approvalType, $errors, $validateOnly, $self): bool {
                    $failedIds = [];
                    $conditionTargets = [
                        ERandselOrder::ID,
                        //            ERandselOrder::MAKER_ID,
                        //            ERandselOrder::PRODUCT_ID,
                    ];
                    foreach ($approvalData as $approvalDatum) {
                        $condition = [];
                        foreach ($conditionTargets as $conditionTarget) {
                            /** @var ERandselOrder $conditionTarget */
                            if ($value = Hash::get($approvalDatum, $conditionTarget->value)) {
                                $condition = Hash::insert($condition, $conditionTarget->value, $value);
                            } else {
                                throw new Exception("Condition Error:" . json_encode($conditionTarget));
                            }
                        }
                        $condition = Hash::insert($condition, ERandselOrder::MAKER_ID->value, $makerId);
                        $condition = Hash::insert($condition, ERandselOrder::STATUS->value, ERandselOrderStatus::PENDING->value);
                        /** @var RandselOrder $entity */
                        $entity = $this->search($condition)->first();
                        if (empty($entity)) {
                            $errors[] = "注文ID: " . Hash::get($approvalDatum, ERandselOrder::ID->value) . " が見つかりませんまたは承認/否認済みです";
                            $failedIds[] = Hash::get($approvalDatum, ERandselOrder::ID->value);
                            Log::error("NotFound entity: " . json_encode($condition));
                            // throw new Exception("NotFound entity: " . json_encode($condition));
                        } elseif (!$this->save($entity->changeStatus(
                            Hash::get($approvalDatum, ERandselOrder::STATUS->value),
                            $approvalType
                        ))) {
                            $errors[] = "注文ID: " . Hash::get($approvalDatum, ERandselOrder::ID->value) . " が保存失敗";
                            Log::error("保存失敗  : " . $entity->getErrors());
                            // throw new Exception("保存失敗  : " . $entity->getErrors());
                        }
                    }
                    $self->setErrors($errors);
                    if ($validateOnly) {
                        $self->setErrors([
                            'failed_ids' => $failedIds,
                        ]);
                        return false;
                    }
                    return empty($errors);
                }
            );
            Log::debug("承認終了");
            return $success;
        } catch (Exception $e) {
            $this->setErrors([
                '_error' => $e->getMessage(),
            ]);
        }
        return false;
    }


    /**
     * 一覧の返却、条件を指定する
     * @param array $conditions
     * @return array
     */
    public function getRandselOrders(array $conditions = []): array
    {
        // $result = [];
        /** @var RandselOrder[] $orders */
        $orders = $this->search($conditions)->contain(['Products'])->all()->toArray();
        // foreach ($orders as $order) {
        //     $result[] = $order->responseData();
        // }
        // return $result;
        return $orders;
    }

    /**
     * 請求書用データを返却
     * @param array $conditions
     * @return array
     */
    public function getMonthlyInvoices(array $conditions = []): array
    {
        $result = [];
        /** @var RandselOrder[] $orders */
        $orders = $this->invoiceSearch($conditions)->toArray();
        foreach ($orders as $order) {
            $invoice = [];
            $invoice['maker_id'] = $order->maker_id;
            $invoice['total_price'] = $order->sum;
            $invoice['status_modified_year_month'] = $order->status_modified_year_month;
            $invoice['count'] = $order->count;

            if (Hash::get($conditions, ESwbOrderForm::GROUP_BY->value) === ERandselOrder::PRODUCT_ID->value) {
                $invoice['product_name'] = $order->product_name;
                $invoice['product_id'] = $order->product_id;
            }
            $result[] = $invoice;
        }
        return $result;
    }

    /**
     * @param array $data
     * @return RandselOrder|null
     */
    public function createRandselOrder(array $data = []): ?RandselOrder
    {
        $encrypted = RandselOrder::dataEncrypt($data);
        /** @var RandselOrder $entity */
        $entity = $this->getDefaultModel()->newEntity($encrypted, [
            //            'validate' => 'add',
        ]);
        $entity->id = Hash::get($data, ERandselOrder::ID->value);
        if ($errors = $entity->getErrors()) {
            //            debug($errors);
            $this->setErrors($errors);
            return null;
        }
        if (!$this->save($entity)) {
            Log::error("保存に失敗");
            $this->setErrors([
                '_errors' => "save error",
            ]);
            return null;
        }
        /** @var RandselOrder $entity */
        $entity = $this->search([
            ERandselOrder::ID->value => $entity->id,
        ])->first();
        return $entity;
    }
}
