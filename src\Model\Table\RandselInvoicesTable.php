<?php

declare(strict_types=1);

namespace App\Model\Table;

use Cake\ORM\Query;
use Cake\ORM\RulesChecker;
use Cake\ORM\Table;
use Cake\Validation\Validator;

/**
 * RandselInvoices Model
 *
 * @method \App\Model\Entity\RandselInvoice newEmptyEntity()
 * @method \App\Model\Entity\RandselInvoice newEntity(array $data, array $options = [])
 * @method \App\Model\Entity\RandselInvoice[] newEntities(array $data, array $options = [])
 * @method \App\Model\Entity\RandselInvoice get($primaryKey, $options = [])
 * @method \App\Model\Entity\RandselInvoice findOrCreate($search, ?callable $callback = null, $options = [])
 * @method \App\Model\Entity\RandselInvoice patchEntity(\Cake\Datasource\EntityInterface $entity, array $data, array $options = [])
 * @method \App\Model\Entity\RandselInvoice[] patchEntities(iterable $entities, array $data, array $options = [])
 * @method \App\Model\Entity\RandselInvoice|false save(\Cake\Datasource\EntityInterface $entity, $options = [])
 * @method \App\Model\Entity\RandselInvoice saveOrFail(\Cake\Datasource\EntityInterface $entity, $options = [])
 * @method \App\Model\Entity\RandselInvoice[]|\Cake\Datasource\ResultSetInterface|false saveMany(iterable $entities, $options = [])
 * @method \App\Model\Entity\RandselInvoice[]|\Cake\Datasource\ResultSetInterface saveManyOrFail(iterable $entities, $options = [])
 * @method \App\Model\Entity\RandselInvoice[]|\Cake\Datasource\ResultSetInterface|false deleteMany(iterable $entities, $options = [])
 * @method \App\Model\Entity\RandselInvoice[]|\Cake\Datasource\ResultSetInterface deleteManyOrFail(iterable $entities, $options = [])
 *
 * @mixin \Cake\ORM\Behavior\TimestampBehavior
 */
class RandselInvoicesTable extends Table
{
    /**
     * Initialize method
     *
     * @param array $config The configuration for the Table.
     * @return void
     */
    public function initialize(array $config): void
    {
        parent::initialize($config);

        $this->setTable('randsel_invoices');
        $this->setDisplayField('billing_year_month');
        $this->setPrimaryKey('id');

        $this->addBehavior('Timestamp');
    }

    /**
     * Default validation rules.
     *
     * @param \Cake\Validation\Validator $validator Validator instance.
     * @return \Cake\Validation\Validator
     */
    public function validationDefault(Validator $validator): Validator
    {
        $validator
            ->integer('maker_id')
            ->requirePresence('maker_id', 'create')
            ->notEmptyString('maker_id');

        $validator
            ->integer('product_id')
            ->requirePresence('product_id', 'create')
            ->notEmptyString('product_id');

        $validator
            ->scalar('billing_year_month')
            ->maxLength('billing_year_month', 7)
            ->requirePresence('billing_year_month', 'create')
            ->notEmptyString('billing_year_month');

        $validator
            ->integer('total_amount')
            ->notEmptyString('total_amount');

        $validator
            ->integer('total_quantity')
            ->notEmptyString('total_quantity');

        $validator
            ->integer('adjustment_amount')
            ->notEmptyString('adjustment_amount');

        $validator
            ->integer('adjustment_quantity')
            ->notEmptyString('adjustment_quantity');

        $validator
            ->scalar('adjustment_note')
            ->allowEmptyString('adjustment_note');

        $validator
            ->integer('invoice_amount')
            ->notEmptyString('invoice_amount');

        return $validator;
    }

    /**
     * Returns a rules checker object that will be used for validating
     * application integrity.
     *
     * @param \Cake\ORM\RulesChecker $rules The rules object to be modified.
     * @return \Cake\ORM\RulesChecker
     */
    public function buildRules(RulesChecker $rules): RulesChecker
    {
        $rules->add($rules->isUnique(['maker_id', 'billing_year_month', 'product_id']), ['errorField' => 'maker_id']);

        return $rules;
    }
}
