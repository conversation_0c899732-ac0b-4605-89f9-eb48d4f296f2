<?php

namespace App\Validation\Validator;

use App\Enums\EntityFields\EMember;
use App\Enums\EValidationErrorMessage;
use App\Validation\IValidator;
use App\Validation\ValidatorTrait;
use Cake\Validation\Validator;

class MemberValidator implements IValidator
{
    use ValidatorTrait;

    static public function validateName(string $field, Validator $validator, array $insertText): Validator
    {
        $validator
            ->maxLength($field, 30, EValidationErrorMessage::MAX_LENGTH->format(
                $insertText + [
                    'max' => 100
                ]
            ))
            ->requirePresence($field, true, EValidationErrorMessage::REQUIRE_PRESENCE->format($insertText))
            ->notEmptyString($field, EValidationErrorMessage::NOT_EMPTY_STRING->format($insertText));
        return $validator;
    }

    static private function name1(Validator $validator, array $options = []): Validator
    {
        $insertText = [
            'field' => EMember::NAME1->description(),
        ];
        $field = EMember::NAME1->value;
        return static::validateName($field, $validator, $insertText);
    }

    static private function name2(Validator $validator, array $options = []): Validator
    {
        $insertText = [
            'field' => EMember::NAME2->description(),
        ];
        $field = EMember::NAME2->value;
        return static::validateName($field, $validator, $insertText);
    }
}
