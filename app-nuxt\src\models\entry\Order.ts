import OrderDetail from "~/src/models/entry/OrderDetail";
import {
    PREFECTURE_LIST,
    APPROVAL_STATUS_LIST,
    getListLabelByValue,
} from "~~/src/list";
import moment from "moment";

export default class Order {
    private readonly _order_nm01: string;
    private readonly _order_nm02: string;
    private readonly _order_nm01_kana: string;
    private readonly _order_nm02_kana: string;
    private readonly _billing_date: string;
    private readonly _tdfk_cd: string;
    private readonly _approval_status: string;
    private readonly _payment_total: number;
    private readonly _ec_order_id: number;
    private readonly _member_id: number;
    private readonly _order_email: string;
    private readonly _order_zip: string;
    private readonly _order_addr01: string;
    private readonly _order_addr02: string;
    private readonly _order_addr03: string;
    private readonly _order_tel: string;
    private readonly _send_email_ng_flag: boolean;
    private readonly _inquiry_body: string;

    private readonly _order_details: OrderDetail[];

    constructor(order: TOrder) {
        this._order_nm01 = order.order_nm01;
        this._order_nm02 = order.order_nm02;
        this._order_nm01_kana = order.order_nm01_kana;
        this._order_nm02_kana = order.order_nm02_kana;
        this._billing_date = order.billing_date;
        this._tdfk_cd = order.tdfk_cd;
        this._approval_status = order.approval_status as string;
        this._payment_total = order.payment_total;
        this._ec_order_id = order.ec_order_id;
        this._member_id = order.member_id;
        this._order_email = order.order_email;
        this._order_zip = order.order_zip;
        this._order_addr01 = order.order_addr01;
        this._order_addr02 = order.order_addr02;
        this._order_addr03 = order.order_addr03;
        this._order_tel = order.order_tel;
        this._send_email_ng_flag = order.send_email_ng_flag;
        this._inquiry_body = order.inquiry_body;

        this._order_details = OrderDetail.creates(order.order_details);
    }

    get order_details(): OrderDetail[] {
        return this._order_details;
    }

    /**
     * 1個しかない想定の時
     */
    get order_detail(): OrderDetail {
        return this.order_details[0];
    }

    get order_nm01(): string {
        return this._order_nm01;
    }

    get order_nm02(): string {
        return this._order_nm02;
    }

    get order_nm01_kana(): string {
        return this._order_nm01_kana;
    }

    get order_nm02_kana(): string {
        return this._order_nm02_kana;
    }

    get name(): string {
        return `${this._order_nm01} ${this._order_nm02}`;
    }

    get billing_date(): string {
        return moment(this._billing_date).format("YYYY-MM-DD HH:mm");
    }

    get tdfk_cd(): string {
        return this._tdfk_cd;
    }

    get approval_status(): string {
        return this._approval_status;
    }

    get display_tdfk_cd(): string {
        return getListLabelByValue(this._tdfk_cd, PREFECTURE_LIST);
    }

    get display_approval_status(): string {
        return getListLabelByValue(this._approval_status, APPROVAL_STATUS_LIST);
    }

    get payment_total(): number {
        return this._payment_total;
    }

    get ec_order_id(): number {
        return this._ec_order_id;
    }

    get member_id(): number {
        return this._member_id;
    }

    get order_email(): string {
        return this._order_email;
    }

    get order_zip(): string {
        return this._order_zip;
    }

    get order_addr01(): string {
        return this._order_addr01;
    }

    get order_addr02(): string {
        return this._order_addr02;
    }

    get order_addr03(): string {
        return this._order_addr03;
    }

    get order_tel(): string {
        return this._order_tel;
    }

    get send_email_ng_flag(): boolean {
        return this._send_email_ng_flag;
    }

    get inquiry_body(): string {
        return this._inquiry_body;
    }

    static creates(orders: TOrder[]): Order[] {
        return orders.map((order: TOrder) => Order.create(order));
    }

    static create(order: TOrder): Order {
        return new Order(order);
    }
}
