<script setup lang="ts">
import { ref, onMounted } from "vue";
import { useRuntimeConfig } from "#app";
import ClientDetails from "~/src/models/ClientDetails";
import type { Product } from "~/src/models/entry/Product";
import type { Member } from "~/src/models/entry/Member";
import { ClientMyBridge } from "~~/src/models/bridge/ClientMyBridge";
import TheLoading from "~/components/parts/TheLoading.vue";
import TheNotLinkedProducts from "~/components/client/template/error/TheNotLinkedProducts.vue";
import TheSessionTimeOut from "~/components/client/template/error/TheSessionTimeOut.vue";
import TheMemberChangePassword from "~/components/my/member/TheMemberChangePassword.vue";
import TheOrderDetail from "~/components/client/tab-content/TheOrderDetail.vue";
import TheTabSelector from "~/components/TheTabSelector.vue";
import TheReport from "~/components/client/tab-content/TheReport.vue";

const props = defineProps<{
    products: Array<Product>;
}>();

const config = useRuntimeConfig();
const bridge = ref<ClientMyBridge>(new ClientMyBridge(config, props.products));
onMounted(async () => {
    ClientDetails.create(config)
        .index()
        .then((m) => {
            if (m) bridge.value.member = m;
        });
});

// タブ関連
const Tabs = {
    Detail: { key: "detail", label: "詳細" },
    Report: { key: "report", label: "レポート" },
} as const;
const activeTab = ref<(typeof Tabs)[keyof typeof Tabs]["key"]>(Tabs.Detail.key);
const setActiveTab = (
    tabKey: (typeof Tabs)[keyof typeof Tabs]["key"],
): void => {
    activeTab.value = tabKey;
};
const role = "client";

const currentComponent = ref("");
const showComponent = (component: string): void => {
    currentComponent.value = component;
};
const gotoMy = (): void => {
    currentComponent.value = "";
};
</script>

<template>
    <v-container class="client-my-wrapper">
        <v-row
            v-if="currentComponent !== 'ComponentA'"
            class="flex-row-reverse"
        >
            <v-btn
                flat
                color="grey-lighten-4"
                @click="showComponent('ComponentA')"
            >
                パスワード変更
            </v-btn>
        </v-row>
        <template v-if="currentComponent === 'ComponentA'">
            <the-member-change-password
                :member="bridge.member as Member"
                @my="gotoMy()"
            />
        </template>
        <template v-else>
            <the-tab-selector
                :tabs="Tabs"
                :active-tab="activeTab"
                :user-role="role"
                @update:active-tab="setActiveTab"
            />
            <div v-if="bridge.is_product_loaded">
                <template v-if="bridge.is_success">
                    <the-order-detail
                        v-show="activeTab === Tabs.Detail.key"
                        :bridge="bridge"
                    />
                    <the-report
                        v-show="activeTab === Tabs.Report.key"
                        :bridge="bridge"
                    />
                </template>
                <template v-else>
                    <the-not-linked-products
                        v-if="!bridge.my_products.length"
                    />
                    <the-session-time-out v-else />
                </template>
            </div>
            <the-loading v-else color="clientPrimary">
                情報を読み込んでいます、しばらくお待ちください
            </the-loading>
        </template>
    </v-container>
</template>

<style scoped></style>
