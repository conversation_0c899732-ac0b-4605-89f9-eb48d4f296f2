# For more information on configuration, see:
#   * Official English Documentation: http://nginx.org/en/docs/
#   * Official Russian Documentation: http://nginx.org/ru/docs/

user nginx;
worker_processes auto;
error_log /var/log/nginx/error.log notice;
pid /run/nginx.pid;

# Load dynamic modules. See /usr/share/doc/nginx/README.dynamic.
include /usr/share/nginx/modules/*.conf;

events {
    worker_connections 1024;
}

http {
    log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
                      '$status $body_bytes_sent "$http_referer" '
                      '"$http_user_agent" "$http_x_forwarded_for"';

    access_log  /var/log/nginx/access.log  main;

    sendfile            on;
    tcp_nopush          on;
    keepalive_timeout   65;
    types_hash_max_size 4096;
    server_tokens off;

    include             /etc/nginx/mime.types;
    default_type        application/octet-stream;

    # Load modular configuration files from the /etc/nginx/conf.d directory.
    # See http://nginx.org/en/docs/ngx_core_module.html#include
    # for more information.
    include /etc/nginx/conf.d/*.conf;

    server {
        server_name  dev.coverme.jp;
        root         /usr/share/nginx/html/wordpress;

        client_max_body_size 20M;

        # Load configuration files for the default server block.
        include /etc/nginx/default.d/*.conf;

        error_page 400 403 404 /40x.html;
        location = /40x.html {
            root /usr/share/nginx/html/wordpress;
        }

        location / {
            # スラッグ、パーマリンク対応
            try_files $uri $uri/ /index.php?$args;
        }

        listen [::]:443 ssl ipv6only=on; # managed by Certbot
        listen 443 ssl; # managed by Certbot
        ssl_certificate /etc/letsencrypt/live/dev.coverme.jp/fullchain.pem; # managed by Certbot
        ssl_certificate_key /etc/letsencrypt/live/dev.coverme.jp/privkey.pem; # managed by Certbot
        include /etc/letsencrypt/options-ssl-nginx.conf; # managed by Certbot
        ssl_dhparam /etc/letsencrypt/ssl-dhparams.pem; # managed by Certbot

    }


    server {
        if ($host = dev.coverme.jp) {
            return 301 https://$host$request_uri;
        } # managed by Certbot


        listen       80;
        listen       [::]:80;
        server_name  dev.coverme.jp;
        return 404; # managed by Certbot
    }
}
