<?php

namespace App\Kuroko\Entity;

use Cake\Utility\Hash;

class Order implements IKurokoEntity
{
    use KurokoEntityTrait;

    private ?string $_inquiryBody = null;
    private ?string $_order_nm01_kana = null;
    private ?string $_order_nm02_kana = null;
    private ?bool $_send_email_flag = null;

    /**
     * @param array $data
     * @return array
     */
    public function initialize(array $data): array
    {

        return Hash::insert(
            $data,
            "order_details",
            OrderDetail::creates(Hash::get($data, "order_details", []))
        );
    }

    public function getMemberId()
    {
        return $this->get("member_id");
    }

    public function getOrderDetails(): array
    {
        return $this->get("order_details");
    }

    public function getOrderNm01()
    {
        return $this->get("order_nm01");
    }

    public function getOrderNm02()
    {
        return $this->get("order_nm02");
    }

    public function getMemo01()
    {
        return $this->get("memo01");
    }

    public function getOrderId()
    {
        return $this->get("ec_order_id");
    }

    public function getApprovalStatus()
    {
        return $this->get("generic_payment_status.key");
    }

    public function getTdfkCd()
    {
        $pref = $this->get("order_pref");
        return sprintf('%02d', $pref);
    }

    public function getPaymentTotal()
    {
        return $this->get("payment_total");
    }

    public function getInstYmdhi()
    {
        return $this->get("inst_ymdhi");
    }

    public function getOrderEmail()
    {
        return $this->get("order_email");
    }

    public function getOrderZip()
    {
        return $this->get("order_zip");
    }

    public function getOrderAddr01()
    {
        return $this->get("order_addr01");
    }

    public function getOrderAddr02()
    {
        return $this->get("order_addr02");
    }

    public function getOrderAddr03()
    {
        return $this->get("order_addr03");
    }

    public function getOrderTel()
    {
        return $this->get("order_tel");
    }

    public function setInquiryBody(string $body): static
    {
        $this->_inquiryBody = $body;
        return $this;
    }

    public function setOrderName01Kana(string $value): static
    {
        $this->_order_nm01_kana = $value;
        return $this;
    }

    public function setOrderName02Kana(string $value): static
    {
        $this->_order_nm02_kana = $value;
        return $this;
    }

    public function setSendEmailFlag(bool | int $value): static
    {
        $this->_send_email_flag = (bool)$value;
        return $this;
    }

    public function getInquiryBody(): ?string
    {
        return $this->_inquiryBody;
    }

    public function getOrderName01Kana(): ?string
    {
        return $this->_order_nm01_kana;
    }

    public function getOrderName02Kana(): ?string
    {
        return $this->_order_nm02_kana;
    }

    public function getSendEmailFlag(): ?bool
    {
        return $this->_send_email_flag;
    }

    public function getJsonData(): array
    {
        $data = [
            "ec_order_id" => $this->getOrderId(),
            "member_id" => $this->getMemberId(),
            "order_nm01" => $this->getOrderNm01(),
            "order_nm02" => $this->getOrderNm02(),
            "approval_status" => $this->getApprovalStatus(),
            "tdfk_cd" => $this->getTdfkCd(),
            "billing_date" => $this->getInstYmdhi(),
            "payment_total" => $this->getPaymentTotal(),
            "memo01" => $this->getMemo01(),
            "order_details" => $this->getOrderDetails(),
            "order_email" => $this->getOrderEmail(),
            "order_zip" => $this->getOrderZip(),
            "order_addr01" => $this->getOrderAddr01(),
            "order_addr02" => $this->getOrderAddr02(),
            "order_addr03" => $this->getOrderAddr03(),
            "order_tel" => $this->getOrderTel(),
            "order_nm01_kana" => $this->getOrderName01Kana(),
            "order_nm02_kana" => $this->getOrderName02Kana(),
            "send_email_ng_flag" => $this->getSendEmailFlag(),
        ];
        if ($body = $this->getInquiryBody()) {
            $data = Hash::insert($data, 'inquiry_body', $body);
        }

        return $data;
    }
}
