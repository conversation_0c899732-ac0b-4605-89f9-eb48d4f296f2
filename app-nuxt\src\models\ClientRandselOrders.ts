import moment from "moment";
import { BaseRandselOrders } from "~/src/models/base/BaseRandselOrders";
import ClientScreenRandselOrdersClient from "~/src/lib/http/coverme-front/ClientScreenRandselOrdersClient";
import ClientCsvRandselOrdersClient from "~/src/lib/http/coverme-front/ClientCsvRandselOrdersClient";
import ClientRandselOrdersClient from "~/src/lib/http/coverme-front/ClientRandselOrdersClient";

export class ClientRandselOrders extends BaseRandselOrders {
    protected client = ClientRandselOrdersClient;
    // public index(
    //     params?: Record<string, string | number | string[] | number[]>,
    // ): Promise<RandselOrder[]> {
    //     const config = this.config;
    //     return new Promise((resolve, reject) => {
    //         ClientRandselOrdersClient.create(config)
    //             .index<TRandselOrdersResponse>(params)
    //             .then(({ randsel_orders }) =>
    //                 resolve(RandselOrder.creates(randsel_orders)),
    //             )
    //             .catch((error) => reject(new Error(error)));
    //     });
    // }

    public putScreenRandselOrders(
        data: TUpdateStatusOrder,
    ): Promise<TResponseSuccess | null> {
        const id = moment().format("YYYYMMDDHHmmss");
        const config = this.config;
        return new Promise((resolve) => {
            ClientScreenRandselOrdersClient.create(config)
                .put<TUpdateStatusOrder, TResponseSuccess>(id, data)
                .then((response) => resolve(response))
                .catch(() => resolve(null));
        });
    }

    public putCsvRandselOrders(
        data: TUpdateStatusOrder,
    ): Promise<TResponseSuccess | TResponseCsvRandselOrdersError | null> {
        const id = moment().format("YYYYMMDDHHmmss");
        const config = this.config;
        return new Promise((resolve) => {
            ClientCsvRandselOrdersClient.create(config)
                .put<TUpdateStatusOrder, TResponseSuccess>(id, data)
                .then((response) => resolve(response))
                .catch(() => resolve(null));
        });
    }
}
