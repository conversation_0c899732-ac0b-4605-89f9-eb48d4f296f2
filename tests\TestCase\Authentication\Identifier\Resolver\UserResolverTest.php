<?php
declare(strict_types=1);

namespace App\Test\TestCase\Authentication\Identifier\Resolver;

use App\Authentication\Identifier\Resolver\UserResolver;
use App\Enums\EntityFields\ELogin;
use App\Kuroko\Entity\AccessToken;
use App\Kuroko\Entity\Member;
use App\Model\Entity\UserToken;
use App\Service\AuthenticationService;
use App\Test\TestCase\AppTestCase;
use Cake\I18n\FrozenTime;
use Cake\ORM\TableRegistry;

/**
 * UserResolver Test Case
 */
class UserResolverTest extends AppTestCase
{
    /**
     * Test fixtures
     */
    protected $fixtures = [
        'app.GeneralUsers',
        'app.UserTokens',
        'app.UserProfiles',
        'app.UserSurveys',
        'app.SwbUsers',
        'app.MakerUsers',
    ];

    /**
     * Test subject
     */
    protected $UserResolver;

    /**
     * setUp method
     */
    public function setUp(): void
    {
        parent::setUp();
        $this->UserResolver = new UserResolver();
    }

    /**
     * tearDown method
     */
    public function tearDown(): void
    {
        unset($this->UserResolver);
        parent::tearDown();
    }

    /**
     * 新システム一般ユーザーの認証テスト
     */
    public function testFindNewSystemGeneralUser(): void
    {
        // テスト用の新システムユーザーはfixtureで作成済み
        // 認証条件を設定
        $conditions = [
            ELogin::LOGIN_EMAIL->value => '<EMAIL>',
            ELogin::LOGIN_PWD->value => 'password1234', // fixtureのパスワードに合わせる
            ELogin::TYPE->value => AuthenticationService::USER_TYPE_GENERAL,
        ];

        // 認証実行
        $result = $this->UserResolver->find($conditions);

        // 結果検証
        $this->assertInstanceOf(Member::class, $result);
        $this->assertEquals('<EMAIL>', $result->getEmail());
        $this->assertNotNull($result->getAccessToken());
        $this->assertTrue($result->isIdentity());
        $this->assertTrue($result->get('details.is_new_system_user'));
    }

    /**
     * 新システムSWBユーザーの認証テスト
     */
    public function testFindNewSystemSwbUser(): void
    {
        // 認証条件を設定
        $conditions = [
            ELogin::LOGIN_EMAIL->value => '<EMAIL>',
            ELogin::LOGIN_PWD->value => 'password1234', // fixtureのパスワードに合わせる
            ELogin::TYPE->value => AuthenticationService::USER_TYPE_SWB,
        ];

        // 認証実行
        $result = $this->UserResolver->find($conditions);

        debug($result);

        // 結果検証
        $this->assertInstanceOf(Member::class, $result);
        $this->assertEquals('<EMAIL>', $result->getEmail());
        $this->assertNotNull($result->getAccessToken());
        $this->assertTrue($result->isIdentity());
        $this->assertTrue($result->get('details.is_new_system_user'));
    }

    /**
     * 新システムメーカーユーザーの認証テスト
     */
    public function testFindNewSystemMakerUser(): void
    {
        // 認証条件を設定
        $conditions = [
            ELogin::LOGIN_EMAIL->value => '<EMAIL>',
            ELogin::LOGIN_PWD->value => 'password1234', // fixtureのパスワードに合わせる
            ELogin::TYPE->value => AuthenticationService::USER_TYPE_MAKER,
        ];

        // 認証実行
        $result = $this->UserResolver->find($conditions);

        debug($result);

        // 結果検証
        $this->assertInstanceOf(Member::class, $result);
        $this->assertEquals('<EMAIL>', $result->getEmail());
        $this->assertNotNull($result->getAccessToken());
        $this->assertTrue($result->isIdentity());
        $this->assertTrue($result->get('details.is_new_system_user'));
    }

    /**
     * Kurocoユーザーの認証テスト（パスワードがnull）
     */
    public function testFindKurocoUser(): void
    {
        // 認証条件を設定
        $conditions = [
            ELogin::LOGIN_EMAIL->value => '<EMAIL>',
            ELogin::LOGIN_PWD->value => 'anypassword',
            ELogin::TYPE->value => AuthenticationService::USER_TYPE_GENERAL,
        ];

        // 認証実行（新システム認証は失敗し、Kuroco認証にフォールバック）
        $result = $this->UserResolver->find($conditions);

        // Kuroco認証のモック(src\Kuroko\Http\Client\Mock\dynamic-member-me.json)があるので、認証成功
        $this->assertInstanceOf(Member::class, $result);
        $this->assertEquals('<EMAIL>', $result->getEmail());
        $this->assertNotNull($result->getAccessToken());
        $this->assertTrue($result->isIdentity());
        $this->assertFalse($result->get('details.is_new_system_user') ?? false);
    }

    /**
     * 存在しないユーザーの認証テスト
     */
    public function testFindNonExistentUser(): void
    {
        // 認証条件を設定（新システム認証を明示的に指定）
        $conditions = [
            ELogin::LOGIN_EMAIL->value => '<EMAIL>',
            ELogin::LOGIN_PWD->value => 'password123',
            ELogin::TYPE->value => AuthenticationService::USER_TYPE_GENERAL,
        ];

        // 認証実行
        $result = $this->UserResolver->find($conditions);

        // 結果検証（新システム認証失敗後、Kuroco認証にフォールバックする）
        // モック環境では、Kuroco認証が成功する場合があるため、
        // 新システムユーザーでないことを確認
        if ($result) {
            $this->assertFalse($result->get('details.is_new_system_user') ?? false);
        } else {
            $this->assertNull($result);
        }
    }

    /**
     * 新システムトークン認証テスト
     */
    public function testFindByNewSystemToken(): void
    {
        // テスト用の新システムユーザーはfixtureで作成済み
        // APIアクセストークンを作成
        $userTokensTable = TableRegistry::getTableLocator()->get('UserTokens');
        $tokenEntity = $userTokensTable->createApiAccessToken(3, 24);   // fixtureのuser_id=3
        $this->assertNotEmpty($tokenEntity);

        // トークンを暗号化
        $encryptedToken = (new AccessToken([
            'access_token' => ['value' => $tokenEntity->token]
        ]))->encryptToken();

        // トークン認証条件を設定
        $conditions = [
            ELogin::TOKEN->value => $encryptedToken,
        ];

        // 認証実行
        $result = $this->UserResolver->find($conditions);

        debug($result);

        // 結果検証
        $this->assertInstanceOf(Member::class, $result);
        $this->assertEquals('<EMAIL>', $result->getEmail());
        $this->assertNotNull($result->getAccessToken());
    }

    /**
     * Kurocoトークン認証テスト
     */
    public function testFindByKurocoToken(): void
    {
        // トークンを暗号化
        $encryptedToken = (new AccessToken([
            // 'access_token' => ['value' => $tokenEntity->token]
            'access_token' => ['value' => '126058a835be5b4e9d4d34ba0baa4803e174c750d265d4d07ebc377cc94a0340']
        ]))->encryptToken();

        // トークン認証条件を設定
        $conditions = [
            ELogin::TOKEN->value => $encryptedToken,
        ];

        // 認証実行
        $result = $this->UserResolver->find($conditions);

        debug($result);

        // 結果検証
        $this->assertInstanceOf(Member::class, $result);
        $this->assertEquals('<EMAIL>', $result->getEmail());
        $this->assertNotNull($result->getAccessToken());
    }

    /**
     * 無効なトークン認証テスト
     */
    public function testFindByInvalidToken(): void
    {
        // 無効なトークンで認証条件を設定
        $conditions = [
            ELogin::TOKEN->value => 'invalid_token',
        ];

        // 認証実行
        $result = $this->UserResolver->find($conditions);

        // 結果検証
        $this->assertNull($result);
    }

    /**
     * 期限切れトークン認証テスト
     */
    public function testFindByExpiredToken(): void
    {
        // テスト用の新システムユーザーを作成
        $generalUsersTable = TableRegistry::getTableLocator()->get('GeneralUsers');
        $user = $generalUsersTable->newEntity([
            'email' => '<EMAIL>',
            'password' => 'password',
        ]);
        $savedUser = $generalUsersTable->save($user);
        $this->assertNotEmpty($savedUser);

        // 期限切れのAPIアクセストークンを作成
        $userTokensTable = TableRegistry::getTableLocator()->get('UserTokens');
        $tokenEntity = $userTokensTable->newEntity([
            'general_user_id' => $savedUser->id,
            'token' => $userTokensTable->generateToken(),
            'type' => UserToken::TYPE_API_ACCESS,
            'expires' => FrozenTime::now()->subHours(1) // 1時間前に期限切れ
        ]);
        $savedToken = $userTokensTable->save($tokenEntity);
        $this->assertNotEmpty($savedToken);

        // トークンを暗号化
        $encryptedToken = (new AccessToken([
            'access_token' => ['value' => $savedToken->token]
        ]))->encryptToken();

        // トークン認証条件を設定
        $conditions = [
            ELogin::TOKEN->value => $encryptedToken,
        ];

        // 認証実行
        $result = $this->UserResolver->find($conditions);

        // 結果検証（期限切れトークンのため新システム認証失敗、Kuroco認証にフォールバック）
        // モック環境では、Kuroco認証が成功する場合があるため、
        // 新システムユーザーでないことを確認
        if ($result) {
            $this->assertFalse($result->get('details.is_new_system_user') ?? false);
        } else {
            $this->assertNull($result);
        }
    }

    /**
     * 空の認証情報テスト
     */
    public function testFindWithEmptyCredentials(): void
    {
        // 空の認証条件
        $conditions = [
            ELogin::LOGIN_EMAIL->value => '',
            ELogin::LOGIN_PWD->value => '',
        ];

        // 認証実行
        $result = $this->UserResolver->find($conditions);

        // 結果検証
        $this->assertNull($result);
    }
}
