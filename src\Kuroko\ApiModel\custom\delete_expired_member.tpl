{* カスタムメンバーフィルターを設定 *}
{assign_array var='method_params'         values=''}
{assign var='method_params.custom_search_id' value=1}
{* 対象メンバーリストの全量を設定 *}
{assign_array var='request_params'        values=''}
{assign       var='request_params.cnt'    value=100000}

{*対象メンバーの取得*}
{api_method
    var='response'
    model='Member'
    method='list'
    method_params=$method_params
    request_params=$request_params
    version='1'
}
{* {logger msg1="対象メンバーの取得" msg2=$response msg3=$status} *}

{*対象メンバーの削除*}
{foreach from=$response.list item=n}
    {assign var='target' value="/rcms-api/5/member/delete-expired-member/`$n.member_id`"}
    {api_internal
        var='response'
        status_var='status'
        endpoint=$target
        method='POST'
        member_id='8'
    }
    {logger msg1="`$n.name1`(member_id=`$n.member_id`)を削除します。" msg2=$response msg3=$status msg4=$n.email}

    {* アンケートデータ取得 *}
    {assign_array var='inquiry_message_method_params'         values=''}
    {assign var='inquiry_message_method_params.inquiry_bn_id' value=$n.pre_form_id}

    {api_method
        var='inquiry_message_response'
        model='InquiryMessage'
        method='details'
        method_params=$inquiry_message_method_params
        version='1'
    }

    {* アンケートデータの削除 *}
    {assign var='target' value="/rcms-api/5/inquiry/delete-expired-inquiry-message/`$n.pre_form_id`"}
    {api_internal
        var='response'
        status_var='status'
        endpoint=$target
        method='POST'
        member_id='8'
    }
    {logger msg1="`$n.name1`(pre_form_id=`$n.pre_form_id`)のフォーム回答を削除します。" msg2=$response msg3=$status msg4=$inquiry_message_response.details.ext_01}
{/foreach}