<?php

namespace App\Validation\Validator;

use App\Enums\EntityFields\EUserChangePassword;
use App\Enums\EValidationErrorMessage;
use App\Validation\IValidator;
use App\Validation\ValidatorTrait;
use Cake\Validation\Validator;

class UserChangePasswordFormValidator implements IValidator
{
    use ValidatorTrait;

    static public function validateEmail(string $field, Validator $validator, array $insertText): Validator
    {
        $validator
            ->maxLength($field, 50, EValidationErrorMessage::MAX_LENGTH->format(
                $insertText + [
                    'max' => 50
                ]
            ))
            ->requirePresence($field, true, EValidationErrorMessage::REQUIRE_PRESENCE->format($insertText))
            ->notEmptyString($field, EValidationErrorMessage::NOT_EMPTY_STRING->format($insertText));
        return $validator;
    }

    static public function validatePassword(string $field, Validator $validator, array $insertText): Validator
    {
        $validator
            ->requirePresence($field, true, EValidationErrorMessage::REQUIRE_PRESENCE->format($insertText))
            ->notEmptyString($field, EValidationErrorMessage::NOT_EMPTY_STRING->format($insertText));
        return $validator;
    }


    static private function loginId(Validator $validator, array $options = []): Validator
    {
        $insertText = [
            'field' => EUserChangePassword::LOGIN_ID->description(),
        ];
        $field = EUserChangePassword::LOGIN_ID->value;
        return static::validateEmail($field, $validator, $insertText);
    }

    static private function currentPassword(Validator $validator, array $options = []): Validator
    {
        $insertText = [
            'field' => EUserChangePassword::CURRENT_PASSWORD->description(),
        ];
        $field = EUserChangePassword::CURRENT_PASSWORD->value;
        return static::validatePassword($field, $validator, $insertText);
    }

    static private function newPassword(Validator $validator, array $options = []): Validator
    {
        $insertText = [
            'field' => EUserChangePassword::NEW_PASSWORD->description(),
        ];
        $field = EUserChangePassword::NEW_PASSWORD->value;
        return static::validatePassword($field, $validator, $insertText);
    }
}
