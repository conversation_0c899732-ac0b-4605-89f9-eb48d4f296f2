<?php

namespace App\Controller\Front;

use App\Controller\FrontController;
use App\Service\Password\IntegratedPasswordRemindersService;
use App\Form\Front\UserPasswordRemindersAddForm;
use App\Form\Front\UserPasswordRemindersEditForm;
use Cake\Event\Event;
use Cake\Event\EventInterface;
use Cake\Log\Log;

class UserPasswordRemindersController extends FrontController
{

    public function beforeFilter(EventInterface $event): void
    {
        parent::beforeFilter($event);
        $this->Authentication->addUnauthenticatedActions(['add', 'edit']);
    }

    public function add(): void
    {
        $this->_onSuccess(function (Event $event) {
            $this->_setSuccessResponse([
                'success' => true,
            ]);
        });
        $this->_onError(function (Event $event, array $errors) {
            Log::debug(print_r($errors, true));
        });
        $this->_add((new IntegratedPasswordRemindersService())
            ->setForm(
                new UserPasswordRemindersAddForm()
            ));
    }

    public function edit($id = null): void
    {
        $this->_onSuccess(function (Event $event) {
            $this->_setSuccessResponse([
                'success' => true,
            ]);
        });
        $this->_onError(function (Event $event, array $errors) {
            Log::debug(print_r($errors, true));
        });
        $this->_edit(
            $id,
            (new IntegratedPasswordRemindersService())
                ->setForm(
                    new UserPasswordRemindersEditForm()
                )
        );
    }
}
