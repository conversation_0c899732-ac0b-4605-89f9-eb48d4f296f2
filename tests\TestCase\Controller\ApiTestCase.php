<?php

namespace App\Test\TestCase\Controller;

use App\Kuroko\Entity\AccessToken;
use App\Test\TestCase\AppTestCase;
use App\Utility\Encryptor;
use Cake\Core\Configure;
use Cake\Utility\Hash;
use Psr\Http\Message\StreamInterface;

abstract class ApiTestCase extends AppTestCase
{
//    use IntegrationTestTrait;
//
//    /**
//     * Fixtures
//     *
//     * @var array<string>
//     */
//    protected $fixtures = [];

    /**
     * @param StreamInterface $body
     * @return array
     */
    public function bodyToArray(StreamInterface $body): array
    {
        $string = (string)$body;
        return $string === 'null' ? [] : json_decode((string)$body, true);
    }

    public function setNuxtBuildAuthorized(): void
    {
        $this->configRequest([
            'headers' => [
                'Accept' => 'application/json',
                'CM-TOKEN' => Configure::read("Api.nuxtBuild.token"),
                //                'Authorization' => 'Bearer ' . Configure::read("Api.nuxtBuild.token"),
            ]
        ]);
    }

    public function setFrontAuthorized(bool $withUserAuth = false): void
    {
        $headers = [
            'Accept' => 'application/json',
            'CM-TOKEN' => Configure::read("Api.front.token"),
        ];
        if ($withUserAuth) {
            $headers = Hash::insert(
                $headers,
                'Authorization-coverme',
                'Bearer ' . Configure::read("Kuroko.api.token.dynamic.sysUser")
            );
        }
        $this->configRequest([
            'headers' => $headers,
        ]);
    }


    protected function getTestToken(): AccessToken
    {
        return new AccessToken([
            'access_token' => [
                'value' => Encryptor::encrypt('test_token'),
            ]
        ]);
    }
}
