<?php

namespace App\Kuroko\Entity;

/**
 * バリデーション結果エンティティ
 * 
 * メールアドレス重複チェックなどのバリデーション結果を返すためのエンティティ
 */
class ValidationResult implements IKurokoEntity
{
    use KurokoEntityTrait;

    /**
     * 返却データのマッピング
     * @return array
     */
    public function getJsonData(): array
    {
        return [
            "id" => "",
            "messages" => ["入力チェックしました"],
            "errors" => []
        ];
    }

    /**
     * @param array $data
     * @return array
     */
    public function initialize(array $data): array
    {
        return $data;
    }
}
