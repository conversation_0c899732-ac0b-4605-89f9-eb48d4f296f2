<?php

use App\Config\DotEnvConfig;
use Cake\Cache\Engine\FileEngine;

$withPrefix = fn(string $name) => (isCli() ? "cli-" : "") . $name;

return [
    /*
   * Configure the cache adapters.
   */
    'Cache' => [
        'default' => [
            'className' => FileEngine::class,
            'path' => CACHE,
            'url' => DotEnvConfig::read('CACHE_DEFAULT_URL', null),
        ],

        /*
         * Configure the cache used for general framework caching.
         * Translation cache files are stored with this configuration.
         * Duration will be set to '+2 minutes' in bootstrap.php when debug = true
         * If you set 'className' => 'Null' core cache will be disabled.
         */
        '_cake_core_' => [
            'className' => FileEngine::class,
            'prefix' => $withPrefix('myapp_cake_core_'),
            'path' => CACHE . 'persistent' . DS,
            'serialize' => true,
            'duration' => '+1 years',
            'url' => DotEnvConfig::read('CACHE_CAKECORE_URL', null),
        ],

        /*
         * Configure the cache for model and datasource caches. This cache
         * configuration is used to store schema descriptions, and table listings
         * in connections.
         * Duration will be set to '+2 minutes' in bootstrap.php when debug = true
         */
        '_cake_model_' => [
            'className' => FileEngine::class,
            'prefix' => $withPrefix('myapp_cake_model_'),
            'path' => CACHE . 'models' . DS,
            'serialize' => true,
            'duration' => '+1 years',
            'url' => DotEnvConfig::read('CACHE_CAKEMODEL_URL', null),
        ],

        /*
         * Configure the cache for routes. The cached routes collection is built the
         * first time the routes are processed through `config/routes.php`.
         * Duration will be set to '+2 seconds' in bootstrap.php when debug = true
         */
        '_cake_routes_' => [
            'className' => FileEngine::class,
            'prefix' => $withPrefix('myapp_cake_routes_'),
            'path' => CACHE,
            'serialize' => true,
            'duration' => '+1 years',
            'url' => DotEnvConfig::read('CACHE_CAKEROUTES_URL', null),
        ],
    ],
];
