<?php

namespace App\Kuroko\ApiModel\KurokoApiPrivateStatic;

use App\Kuroko\ApiModel\KurokoApiPrivateStaticModel;
use App\Kuroko\Entity\Product;
use Cake\Utility\Hash;
use Cake\Core\Configure;
use Cake\Log\Log;

class Products extends KurokoApiPrivateStaticModel
{
    /**
     * 商品リスト取得
     *
     * @return \App\Kuroko\Entity\Product[]
     */
    public function getProductList(): array
    {
        $products = [];
        $queryString = http_build_query([
            static::CNT => Configure::read("Kuroko.api.allListMaxCnt"),
        ]);
        $fullUrl = $this->getEndPoint("products") . '?' . $queryString;

        $response = $this->get($fullUrl);
        if ($response->isSuccess() && $body = $response->getStringBody()) {
            if ($data = json_decode($body, true)) {
                if (Hash::check($data, "list")) {
                    foreach (Hash::get($data, "list") as $value) {
                        $products[] = new Product($value);
                    }
                }
            }
        }

        if (empty($products)) {
            Log::debug(__METHOD__ . " request data: " . json_encode($data));
            Log::debug(__METHOD__ . " response: " . $response->getStringBody());
        }
        return $products;
    }

    /**
     * 全商品リスト取得（非公開商品も含む）
     *
     * @return \App\Kuroko\Entity\Product[]
     */
    public function getAllProductList(): array
    {
        $products = [];
        $queryString = http_build_query([
            static::CNT => Configure::read("Kuroko.api.allListMaxCnt"),
        ]);
        $fullUrl = $this->getEndPoint("productAllList") . '?' . $queryString;

        $response = $this->get($fullUrl);
        if ($response->isSuccess() && $body = $response->getStringBody()) {
            if ($data = json_decode($body, true)) {
                if (Hash::check($data, "list")) {
                    foreach (Hash::get($data, "list") as $value) {
                        $products[] = new Product($value);
                    }
                }
            }
        }

        if (empty($products)) {
            Log::debug(__METHOD__ . " request data: " . json_encode($data));
            Log::debug(__METHOD__ . " response: " . $response->getStringBody());
        }
        return $products;
    }
}
