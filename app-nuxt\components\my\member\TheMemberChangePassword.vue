<script setup lang="ts">
import { reactive, ref } from "vue";
import UserChangePasswords from "~/src/models/UserChangePasswords";
import ClientChangePasswords from "~/src/models/ClientChangePasswords";
import { useRuntimeConfig, useRoute } from "#app";
import ChangePasswordForm from "~/src/form/ChangePasswordForm";
import { Member } from "~/src/models/entry/Member";

const props = defineProps({
    member: {
        type: Member,
        required: true,
    },
});
const isClient = useRoute().path.startsWith("/client/");
const isSwb = useRoute().path.startsWith("/swb/");

const config = useRuntimeConfig();
const emit = defineEmits(["my"]);

const changePasswordForm = reactive(new ChangePasswordForm(props.member));
const isLoading = ref(false);
const errorMessage = ref("");
const toComplete = (): void => {
    if (!changePasswordForm.valid) {
        return;
    }
    isLoading.value = true;
    const changePasswordModel = isClient
        ? ClientChangePasswords
        : UserChangePasswords;

    changePasswordModel
        .create(config)
        .put(changePasswordForm.data)
        .then((response) => {
            console.log(response);
            if (!response?.errors) {
                emit("my");
            } else {
                console.log(response.errors[0].message);
                errorMessage.value = response.errors[0].message;
            }

            isLoading.value = false;
        });
};

const gotoMy = (): void => {
    emit("my");
};
</script>

<template>
    <p class="text-h6 font-weight-bold mb-8">パスワード変更</p>
    <template v-if="isLoading">Loading...</template>
    <template v-else>
        <v-form v-model="changePasswordForm.valid">
            <v-container>
                <v-row v-if="errorMessage !== ''" class="pb-4">
                    <v-alert type="error">
                        {{ errorMessage }}
                    </v-alert>
                </v-row>
                <v-row>
                    <v-col cols="12" md="12" class="py-0">
                        <v-label>現在のパスワード</v-label>
                    </v-col>
                </v-row>
                <v-row>
                    <v-col cols="12" md="6">
                        <v-text-field
                            v-model="changePasswordForm.current_password"
                            density="comfortable"
                            :rules="changePasswordForm.password_rules"
                            type="password"
                            required
                        ></v-text-field>
                    </v-col>
                </v-row>
                <v-row>
                    <v-col cols="12" md="12" class="py-0">
                        <v-label>新しいパスワード</v-label>
                    </v-col>
                </v-row>
                <v-row>
                    <v-col cols="12" md="6">
                        <v-text-field
                            v-model="changePasswordForm.new_password"
                            density="comfortable"
                            :rules="changePasswordForm.password_rules"
                            type="password"
                            required
                        ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="6">
                        <v-text-field
                            v-model="
                                changePasswordForm.new_password_confirmation
                            "
                            density="comfortable"
                            :rules="
                                changePasswordForm.password_confirmation_rules
                            "
                            type="password"
                            :label="`確認用`"
                            required
                        ></v-text-field>
                    </v-col>
                </v-row>
                <v-row justify="center">
                    <v-col cols="12" md="3">
                        <v-btn
                            block
                            color="grey-lighten-4"
                            rounded="xl"
                            size="large"
                            @click="gotoMy"
                        >
                            戻る
                        </v-btn>
                    </v-col>
                    <v-col cols="12" md="3">
                        <v-btn
                            block
                            :disabled="!changePasswordForm.valid"
                            :color="isClient ? 'clientPrimary' : 'primary'"
                            rounded="xl"
                            size="large"
                            @click="toComplete"
                        >
                            更新
                        </v-btn>
                    </v-col>
                </v-row>
            </v-container>
        </v-form>
    </template>
</template>

<style scoped></style>
