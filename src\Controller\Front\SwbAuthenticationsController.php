<?php

namespace App\Controller\Front;

use App\Controller\FrontController;
use App\Form\DisableParametersForm;
use App\Form\Front\ClientAuthenticationsAddForm;
use App\Kuroko\Entity\Member;
use App\Service\Swb\SwbAuthenticationsService;
use Cake\Event\Event;
use Cake\Event\EventInterface;
use Cake\Log\Log;

class SwbAuthenticationsController extends FrontController
{


    public function beforeFilter(EventInterface $event): void
    {
        parent::beforeFilter($event);
        $this->Authentication->addUnauthenticatedActions(['add']);
    }


    /**
     * ログイン
     * @return void
     */
    public function add(): void
    {
        $this->_onSuccess(function (Event $event, Member $member) {
            // 成功時にはトークンとユーザ情報を返却する
            $this->_setSuccessResponse([
                'member' => $member,
                'access_token' => $member->getAccessToken()->encryptToken(),
            ]);
        });
        $this->_onError(function (Event $event, array $errors) {
            Log::debug(print_r($errors, true));
        });
        $this->_add(
            (new SwbAuthenticationsService())
                ->setAuthenticationComponent($this->Authentication)
                ->setForm(
                    new ClientAuthenticationsAddForm()
                )
        );
    }

    /**
     * ログアウト
     * @param $id
     * @return void
     */
    public function delete($id = null): void
    {
        $this->_onSuccess(function (Event $event, bool $success) {
            // 成功時にログアウト成功を返却する
            $this->_setSuccessResponse([
                'success' => $success
            ]);
        });
        $this->_onError(function (Event $event, array $errors) {
            Log::debug(print_r($errors, true));
        });
        $this->_delete(
            $id,
            (new SwbAuthenticationsService())
                ->setIdentity($this->Authentication->getIdentity())
                ->setForm(
                    new DisableParametersForm()
                )
        );
    }
}
