<?php

namespace App\Service;

use App\Kuroko\ApiModel\KurokoApiDynamic\Members;
use App\Kuroko\Entity\AccessToken;
use App\Kuroko\Entity\Member;
use App\Kuroko\Entity\InquiryDetail;

class MembersService implements IService
{
    use ServiceTrait;

    protected array $_defaultConfig = [];

    public function initialize(): void
    {
    }

    public function preFormTmpMember(array $data): ?Member
    {
        $members = new Members();
        return $members->regist($data);
    }

    public function getMeByAccessToken(AccessToken $accessToken): ?Member
    {
        $members = new Members();
        return $members->me($accessToken);
    }

    public function getMeByLogin(AccessToken $accessToken): ?Member
    {
        $members = new Members();
        return $members->me($accessToken, true);
    }

    public function updateStatusAndGetFormInfo(AccessToken $accessToken): ?InquiryDetail
    {
        $members = new Members();
        return $members->updateStatusAndGetFormInfo($accessToken, Member::STATUS_VERIFIED);
    }

    public function registValidateOnly(array $data): ?Member
    {
        $members = new Members();
        return $members->registValidateOnly($data);
    }

    public function getSwbList(Member $member, array $ids = []): ?array
    {
        $members = new Members();
        return $members->swbList($member, $ids);
    }
}
