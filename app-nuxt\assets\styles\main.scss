@import "utils/variables";
@import "@fontsource/noto-sans-jp/400.css";
@import "@fontsource/noto-sans-jp/700.css";
// .v-application {
//     [class*='text-'] { font-family: 'Noto Sans JP', sans-serif !important; }
//     font-family: 'Noto Sans JP', sans-serif !important;
// }

// * {
//     color: $primary-color;
// }
// .coverme-header .v-toolbar__content {
//     width: fit-content;
//     max-width: 1200px;
//     margin: auto;
// }
// .coverme-catalog-checkbox .v-checkbox-btn.v-selection-control {
//     justify-content: center;
// }

.coverme-catalog-checkbox .v-label {
    flex: 1 1 100% !important;
    pointer-events: none;
}

// カレンダークリック範囲拡大
.coverme-input-calendar input[type="date"]::-webkit-inner-spin-button {
    -webkit-appearance: none;
}

.coverme-input-calendar input[type="date"]::-webkit-clear-button {
    -webkit-appearance: none;
}

.coverme-input-calendar input[type="date"] {
    position: relative;
}

.coverme-input-calendar input[type="date"]::-webkit-calendar-picker-indicator {
    position: absolute;
    width: 100%;
    height: 100%;
}

.coverme-input-calendar input[type="date"]::-webkit-calendar-picker-indicator {
    opacity: 0;
    cursor: pointer;
}

.coverme-input-calendar input[type="date"]::-webkit-date-and-time-value {
    text-align: left;
}

div.v-data-table.coverme-member-orders-table .v-data-table-footer {
    display: none;
}

.coverme-input-address2 .v-messages__message {
    line-height: 14px !important;
}