<?php
declare(strict_types=1);

namespace App\Model\Entity;

use Cake\ORM\Entity;
use App\Utility\Encryptor;

/**
 * UserProfile Entity
 *
 * @property int $id
 * @property int $general_user_id
 * @property string $last_name
 * @property string $first_name
 * @property string $last_name_kana
 * @property string $first_name_kana
 * @property string $zip_code
 * @property string $prefecture_code
 * @property string $address1
 * @property string $address2
 * @property string|null $address3
 * @property string $tel
 * @property bool $email_send_ng_flg
 * @property string|null $notes
 * @property \Cake\I18n\FrozenTime $created
 * @property \Cake\I18n\FrozenTime $modified
 *
 * @property \App\Model\Entity\GeneralUser $general_user
 */
class UserProfile extends Entity
{
    /**
     * Fields that can be mass assigned using newEntity() or patchEntity().
     *
     * Note that when '*' is set to true, this allows all unspecified fields to
     * be mass assigned. For security purposes, it is advised to set '*' to false
     * (or remove it), and explicitly make individual fields accessible as needed.
     *
     * @var array<string, bool>
     */
    protected $_accessible = [
        'general_user_id' => true,
        'last_name' => true,
        'first_name' => true,
        'last_name_kana' => true,
        'first_name_kana' => true,
        'zip_code' => true,
        'prefecture_code' => true,
        'address1' => true,
        'address2' => true,
        'address3' => true,
        'tel' => true,
        'email_send_ng_flg' => true,
        'notes' => true,
        'created' => true,
        'modified' => true,
        'general_user' => true,
    ];

    protected $_virtual = [
        'decrypted_last_name',
        'decrypted_first_name',
        'decrypted_last_name_kana',
        'decrypted_first_name_kana',
        'decrypted_zip_code',
        'decrypted_prefecture_code',
        'decrypted_address1',
        'decrypted_address2',
        'decrypted_address3',
        'decrypted_tel',
        'decrypted_notes',
    ];

    // 暗号化フィールドのセッター
    protected function _setLastName($value)
    {
        return Encryptor::encrypt($value);
    }

    protected function _setFirstName($value)
    {
        return Encryptor::encrypt($value);
    }

    protected function _setLastNameKana($value)
    {
        return Encryptor::encrypt($value);
    }

    protected function _setFirstNameKana($value)
    {
        return Encryptor::encrypt($value);
    }

    protected function _setZipCode($value)
    {
        return Encryptor::encrypt($value);
    }

    protected function _setPrefectureCode($value)
    {
        return Encryptor::encrypt($value);
    }

    protected function _setAddress1($value)
    {
        return Encryptor::encrypt($value);
    }

    protected function _setAddress2($value)
    {
        return Encryptor::encrypt($value);
    }

    protected function _setAddress3($value)
    {
        return $value !== null ? Encryptor::encrypt($value) : null;
    }

    protected function _setTel($value)
    {
        return Encryptor::encrypt($value);
    }

    protected function _setNotes($value)
    {
        return $value !== null ? Encryptor::encrypt($value) : null;
    }

    // 暗号化フィールドのゲッター
    protected function _getDecryptedLastName()
    {
        $value = $this->last_name;
        return Encryptor::decrypt($value);
    }

    protected function _getDecryptedFirstName()
    {
        $value = $this->first_name;
        return Encryptor::decrypt($value);
    }

    protected function _getDecryptedLastNameKana()
    {
        $value = $this->last_name_kana;
        return Encryptor::decrypt($value);
    }

    protected function _getDecryptedFirstNameKana()
    {
        $value = $this->first_name_kana;
        return Encryptor::decrypt($value);
    }

    protected function _getDecryptedZipCode()
    {
        $value = $this->zip_code;
        return Encryptor::decrypt($value);
    }

    protected function _getDecryptedPrefectureCode()
    {
        $value = $this->prefecture_code;
        return Encryptor::decrypt($value);
    }

    protected function _getDecryptedAddress1()
    {
        $value = $this->address1;
        return Encryptor::decrypt($value);
    }

    protected function _getDecryptedAddress2()
    {
        $value = $this->address2;
        return Encryptor::decrypt($value);
    }

    protected function _getDecryptedAddress3()
    {
        $value = $this->address3;
        return $value !== null ? Encryptor::decrypt($value) : null;
    }

    protected function _getDecryptedTel()
    {
        $value = $this->tel;
        return Encryptor::decrypt($value);
    }

    protected function _getDecryptedNotes()
    {
        $value = $this->notes;
        return $value !== null ? Encryptor::decrypt($value) : null;
    }
}
