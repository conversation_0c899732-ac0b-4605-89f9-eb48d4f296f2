<?php

namespace App\Service\Traits;

use App\Kuroko\Entity\Member;
use App\Mailer\AppMailer;
use App\Mailer\Sender\ToUser\OrderCompleteSender;
use Cake\Utility\Hash;
use Cake\Log\Log;

/**
 * 通常注文処理機能を提供するTrait
 */
trait StandardOrderTrait
{
    use OrderTrait;
    
    /**
     * 商品IDを取得
     * 
     * @param array $productData 商品データ
     * @return array 商品ID配列
     */
    protected function getProductIds(array $productData): array
    {
        return $productData ?? [];
    }
    
    /**
     * ユーザーIDを取得
     * 
     * @param Member $user ユーザー情報
     * @return int ユーザーID
     */
    protected function getUserId(Member $user): int
    {
        return $user->getId();
    }

    /**
     * 注文完了メールを送信
     * 
     * @param Member $user ユーザー情報
     * @return void
     */
    protected function sendCompletionEmail(Member $user, array $randselOrders): void
    {
        if (!$this->randselOrdersTable) {
            $this->initializeOrder();
        }

        Log::debug(__METHOD__ . " randselOrders: " . json_encode($randselOrders));
        Log::debug(__METHOD__ . " randselOrders ids: " . json_encode(array_column($randselOrders, 'id')));

        // 注文情報を取得
        $oldOrders = $this->randselOrdersTable->find()
            ->where(['general_user_id' => $user->getId(), 'id not in' => array_column($randselOrders, 'id')])
            ->toArray();
        
        AppMailer::sendToUser(
            (new OrderCompleteSender(
                $user->getEmail(),
                $user->get('details.name1')
            ))->setViewVars(['newOrders' => $randselOrders, 'oldOrders' => $oldOrders,])
        );
        
        Log::info("Order completion email sent to: {$user->getEmail()}");
    }
}