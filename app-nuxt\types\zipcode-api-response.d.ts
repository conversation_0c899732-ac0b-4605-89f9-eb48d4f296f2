type TZipCodeApiResponse = {
    prefCode: string;
    cityCode: string;
    postcode: string;
    oldPostcode: string;
    hiragana: {
        pref: string;
        city: string;
        town: string;
        allAddress: string;
    };
    halfWidthKana: {
        pref: string;
        city: string;
        town: string;
        allAddress: string;
    };
    fullWidthKana: {
        pref: string;
        city: string;
        town: string;
        allAddress: string;
    };
    generalPostcode: boolean;
    officePostcode: boolean;
    location: {
        latitude: number;
        longitude: number;
    };
    pref: string;
    city: string;
    town: string;
    allAddress: string;
};
