<script setup lang="ts">
import { SwbMyBridge } from "~~/src/models/bridge/SwbMyBridge";
import TheSwbInvoiceSearchForm from "~/components/swb/form/TheSwbInvoiceSearchForm.vue";
import TheInvoiceSummary from "~/components/swb/tab-content/invoice-detail/TheInvoiceSummary.vue";
import TheMonthlyInvoices from "~/components/swb/tab-content/invoice-detail/TheMonthlyInvoices.vue";
import TheLoading from "~/components/parts/TheLoading.vue";

defineProps<{
    bridge: SwbMyBridge;
}>();
</script>

<template>
    <div>
        <the-swb-invoice-search-form :bridge="bridge" />
        <div v-if="bridge.is_invoice_loaded">
            <the-invoice-summary :bridge="bridge" />
            <the-monthly-invoices :bridge="bridge" />
        </div>
        <v-row v-else justify="center" class="mt-10">
            <the-loading color="swbPrimary">
                しばらくお待ちください。
            </the-loading>
        </v-row>
    </div>
</template>

<style scoped></style>
