<?php
declare(strict_types=1);

use Migrations\AbstractSeed;

/**
 * TestAll seed.
 */
class TestAllSeed extends AbstractSeed
{
    /**
     * Run Method.
     *
     * Write your database seeder using this method.
     *
     * More information on writing seeds is available here:
     * https://book.cakephp.org/migrations/3/en/index.html#creating-seeds
     *
     * @return void
     */
    public function run(): void
    {
        // Disable foreign key checks
        $this->execute('SET FOREIGN_KEY_CHECKS=0');

        // Truncate tables
        $this->table('budgets')->truncate();
        $this->table('products')->truncate();
        $this->table('brands')->truncate();
        $this->table('makers')->truncate();

        // Insert data for makers
        $makersData = [
            [
                'id' => 1,
                'name' => '盛田',
                'description' => '盛田のランドセルメーカー',
                'maker_image_url' => 'https://coverme.jp/wp-content/uploads/2025/01/morita_200.jpg',
                'billing_cycle' => 1,
            ],
            [
                'id' => 2,
                'name' => 'CHIKYU株式会社',
                'description' => '地球NASAランドセル®のメーカー',
                'maker_image_url' => 'https://coverme.jp/wp-content/uploads/2025/03/chikyu_200.png',
                'billing_cycle' => 1,
            ],
            [
                'id' => 3,
                'name' => '羅羅屋',
                'description' => 'ララちゃんランドセルのメーカー',
                'maker_image_url' => 'https://coverme.jp/wp-content/uploads/2024/10/rara_logo_200.jpg',
                'billing_cycle' => 1,
            ],
            [
                'id' => 4,
                'name' => '羽倉ランドセル',
                'description' => '羽倉ランドセルのメーカー',
                'maker_image_url' => 'https://coverme.jp/tmp/cm_hakura.jpg',
                'billing_cycle' => 1,
            ],
            [
                'id' => 5,
                'name' => '萬勇鞄',
                'description' => '萬勇鞄のメーカー',
                'maker_image_url' => 'https://coverme.jp/wp-content/uploads/2024/11/manyu_1105.png',
                'billing_cycle' => 1,
            ],
            [
                'id' => 6,
                'name' => 'ごとうじゅう',
                'description' => 'ごとうじゅうランドセルのメーカー',
                'maker_image_url' => 'https://coverme.jp/wp-content/uploads/2025/02/gotoju_200.jpg',
                'billing_cycle' => 1,
            ],
            [
                'id' => 7,
                'name' => 'コクホー',
                'description' => 'コクホーのランドセルメーカー',
                'maker_image_url' => 'http://localhost:3000/catalog/_nuxt/assets/111.jpg',
                'billing_cycle' => 1,
            ],
            [
                'id' => 8,
                'name' => '山耕株式会社',
                'description' => '恐竜ランドセルのメーカー',
                'maker_image_url' => 'https://picsum.photos/300/300',
                'billing_cycle' => 1,
            ],
            [
                'id' => 9,
                'name' => 'サンプルサンドセル',
                'description' => 'サンプルサンドセルのメーカー',
                'maker_image_url' => 'https://coverme.jp/tmp/dummy_logo.png',
                'billing_cycle' => 1,
            ],
            [
                'id' => 10,
                'name' => 'カバンのフジタ',
                'description' => 'フジタのランドセルのメーカー',
                'maker_image_url' => 'https://coverme.jp/tmp/fujita_200x200.png',
                'billing_cycle' => 1,
            ],
            [
                'id' => 11,
                'name' => '鞄工房山本',
                'description' => '鞄工房山本のメーカー',
                'maker_image_url' => 'https://coverme.jp/wp-content/uploads/2024/10/yamamoto_200.jpg',
                'billing_cycle' => 1,
            ],
            [
                'id' => 12,
                'name' => '東玉',
                'description' => '戸塚ランドセルのメーカー',
                'maker_image_url' => 'https://coverme.jp/wp-content/uploads/2024/12/totsuka_randoseru_resized.png',
                'billing_cycle' => 1,
            ],
        ];
        $this->table('makers')->insert($makersData)->save();

        // Insert data for brands
        $brandsData = [
            [
                'id' => 1,
                'maker_id' => 1,
                'name' => '盛田のランドセル',
                'description' => '盛田のランドセルブランド',
                'logo_url' => 'https://coverme.jp/wp-content/uploads/2025/01/morita_200.jpg',
                'is_premium' => false,
            ],
            [
                'id' => 2,
                'maker_id' => 2,
                'name' => '地球NASAランドセル®',
                'description' => '地球NASAランドセル®ブランド',
                'logo_url' => 'https://coverme.jp/wp-content/uploads/2025/03/chikyu_200.png',
                'is_premium' => false,
            ],
            [
                'id' => 3,
                'maker_id' => 3,
                'name' => 'ララちゃんランドセル',
                'description' => 'ララちゃんランドセルブランド',
                'logo_url' => 'https://coverme.jp/wp-content/uploads/2024/10/rara_logo_200.jpg',
                'is_premium' => false,
            ],
            [
                'id' => 4,
                'maker_id' => 4,
                'name' => '羽倉ランドセル',
                'description' => '羽倉ランドセルブランド',
                'logo_url' => 'https://coverme.jp/tmp/cm_hakura.jpg',
                'is_premium' => false,
            ],
            [
                'id' => 5,
                'maker_id' => 5,
                'name' => '萬勇鞄',
                'description' => '萬勇鞄ブランド',
                'logo_url' => 'https://coverme.jp/wp-content/uploads/2024/11/manyu_1105.png',
                'is_premium' => false,
            ],
            [
                'id' => 6,
                'maker_id' => 6,
                'name' => 'ごとうじゅうランドセル',
                'description' => 'ごとうじゅうランドセルブランド',
                'logo_url' => 'https://coverme.jp/wp-content/uploads/2025/02/gotoju_200.jpg',
                'is_premium' => false,
            ],
            [
                'id' => 7,
                'maker_id' => 7,
                'name' => 'コクホーのランドセル',
                'description' => 'コクホーのランドセルブランド',
                'logo_url' => 'http://localhost:3000/catalog/_nuxt/assets/111.jpg',
                'is_premium' => false,
            ],
            [
                'id' => 8,
                'maker_id' => 8,
                'name' => '恐竜ランドセル',
                'description' => '恐竜ランドセルブランド',
                'logo_url' => 'https://picsum.photos/300/300',
                'is_premium' => false,
            ],
            [
                'id' => 9,
                'maker_id' => 9,
                'name' => 'サンプルサンドセル',
                'description' => 'サンプルサンドセルブランド',
                'logo_url' => 'https://coverme.jp/tmp/dummy_logo.png',
                'is_premium' => false,
            ],
            [
                'id' => 10,
                'maker_id' => 10,
                'name' => 'フジタのランドセル',
                'description' => 'フジタのランドセルブランド',
                'logo_url' => 'https://coverme.jp/tmp/fujita_200x200.png',
                'is_premium' => false,
            ],
            [
                'id' => 11,
                'maker_id' => 11,
                'name' => '鞄工房山本',
                'description' => '鞄工房山本ブランド',
                'logo_url' => 'https://coverme.jp/wp-content/uploads/2024/10/yamamoto_200.jpg',
                'is_premium' => false,
            ],
            [
                'id' => 12,
                'maker_id' => 12,
                'name' => '戸塚ランドセル',
                'description' => '戸塚ランドセルブランド',
                'logo_url' => 'https://coverme.jp/wp-content/uploads/2024/12/totsuka_randoseru_resized.png',
                'is_premium' => false,
            ],
        ];
        $this->table('brands')->insert($brandsData)->save();

        // Insert data for products
        $productsData = [
            [
                'id' => 41213,
                'maker_id' => 1,
                'brand_id' => 1,
                'is_display' => true,
                'year' => 2026,
                'display_name' => '盛田のランドセル(2026年度カタログ)',
                'description_html' => 'グッドデザイン賞受賞のランドセルは通学時の体の負担を軽減する高機能設計.\r\nさらに軽さと機能性を両立したナイロンランドセル"ラッカル"が新登場！\r\n<b>盛田は創業以来、お子さまの大切な6年間に家族のように寄り添うランドセルをつくっています。</b>',
                'note_html' => '発送中',
                'image_url' => 'https://coverme.jp/wp-content/uploads/2025/01/morita_200.jpg',
                'sort_order' => 1,
            ],
            [
                'id' => 41217,
                'maker_id' => 2,
                'brand_id' => 2,
                'is_display' => true,
                'year' => 2026,
                'display_name' => '地球NASAランドセル®（2026年度カタログ）',
                'description_html' => '『地球の未来は子どもの未来』\r\n最先端テクノロジーとサステナブルな活動でお子さまにも環境にもやさしいランドセルを.\r\n地球NASAランドセル®は、肩ベルトと背あて部分にNASAで採用された衝撃吸収材テンパーフォーム®を搭載。登下校時にかかるお子さまへの負担を軽減する親子で安心の機能性ランドセルです。',
                'note_html' => '注文受付中',
                'image_url' => 'https://coverme.jp/wp-content/uploads/2025/03/chikyu_200.png',
                'sort_order' => 2,
            ],
            [
                'id' => 41206,
                'maker_id' => 3,
                'brand_id' => 3,
                'is_display' => true,
                'year' => 2026,
                'display_name' => '☆ララちゃんランドセル☆羅羅屋（2026総合カタログ）',
                'description_html' => '☆ララちゃんランドセルは、自社工場で心を込めて手作りでお届けします。業界初のオーダーメイド。今では200億通り以上の組み合わせで自分だけのランドセルをつくれます。小さな身体でも背負いやすい負担軽減『マジかるベルト』や、お洒落なデザインにも注目.\r\n【安心】６年間完全無料修理保証スタート！貸出しサービスも大好評。',
                'note_html' => '8月～発送',
                'image_url' => 'https://coverme.jp/wp-content/uploads/2024/10/rara_logo_200.jpg',
                'sort_order' => 3,
            ],
            [
                'id' => 41207,
                'maker_id' => 4,
                'brand_id' => 4,
                'is_display' => true,
                'year' => 2026,
                'display_name' => '羽倉ランドセル（2026年総合カタログ）',
                'description_html' => '【初の豊岡鞄認定ランドセル】\r\nカラーで選ぶなら「羽倉」。世界に一つ、自分だけのオーダーランドセルも作れます.\r\n６年間の途中で、色の好みが変わっても安心！「フラップ交換サービス」も実施中。',
                'note_html' => '9月～発送',
                'image_url' => 'https://coverme.jp/tmp/cm_hakura.jpg',
                'sort_order' => 4,
            ],
            [
                'id' => 41208,
                'maker_id' => 5,
                'brand_id' => 5,
                'is_display' => true,
                'year' => 2026,
                'display_name' => '萬勇鞄（2026年度カタログ）',
                'description_html' => '萬勇鞄は確かな技術で、つくり続ける【名古屋ランドセル】\r\nOnlyONEの個性に応える、手づくりランドセルをコンセプトに一人ひとりの「大好き」や「ワクワク」を叶える、デザインと100超のカラーラインナップ.\r\nランドセル探しは、お子さまの“感性”と“自分らしさ”が花開く絶好のチャンス.\r\n「自宅でランドセル体験を。」\r\n見て、背負って、体感できる。貸出サービスも行っております。',
                'note_html' => '9月～発送',
                'image_url' => 'https://coverme.jp/wp-content/uploads/2024/11/manyu_1105.png',
                'sort_order' => 5,
            ],
            [
                'id' => 41202,
                'maker_id' => 3,
                'brand_id' => 3,
                'is_display' => false,
                'year' => 2026,
                'display_name' => 'ESスチューデントデイパック',
                'description_html' => 'ワークマンワークマンワークESスチューデントデイパック(通常カタログ)説明文',
                'note_html' => '発送中',
                'image_url' => 'https://coverme.jp/wp-content/uploads/2024/08/2207294_m-300x202.jpg',
                'sort_order' => 6,
            ],
            [
                'id' => 41201,
                'maker_id' => 4,
                'brand_id' => 4,
                'is_display' => false,
                'year' => 2026,
                'display_name' => '天使の羽',
                'description_html' => 'めちゃかるいよ\r\nめちゃかるいよめちゃかるいよ',
                'note_html' => '受け付け停止',
                'image_url' => 'https://coverme.jp/wp-content/uploads/2024/07/pixta_38023617_M-300x200.jpg',
                'sort_order' => 7,
            ],
            [
                'id' => 41216,
                'maker_id' => 6,
                'brand_id' => 6,
                'is_display' => true,
                'year' => 2026,
                'display_name' => 'ごとうじゅうランドセル（2026年度カタログ）',
                'description_html' => '【職人がひとつずつ作り上げる、純国産手作りランドセル工房】\r\n\r\n2025年春に新しくスタートするブランド”ごとうじゅうランドセル”は、\r\nお子さまの身体を想ったカスタムメイドのランドセルを中心に、\r\n7シリーズ49バリエーションの多彩なデザインを展開.\r\n職人の熟練の技で丁寧に作り上げる、世界にひとつだけのランドセルをお届けします。',
                'note_html' => '発送中',
                'image_url' => 'https://coverme.jp/wp-content/uploads/2025/02/gotoju_200.jpg',
                'sort_order' => 8,
            ],
            [
                'id' => 41215,
                'maker_id' => 7,
                'brand_id' => 7,
                'is_display' => true,
                'year' => 2026,
                'display_name' => 'コクホーのランドセル（2026年度カタログ）',
                'description_html' => '「ずっと好き、がきっと見つかる。」\r\n\r\nコクホーのランドセルは幅広いラインナップと充実した機能性が特徴です.\r\nお子様にとって大切な6年間という月日を、\r\n安心して共に歩むことが出来るランドセルをお届けいたします. \r\n\r\n【新商品多数】まずはお気軽にカタログをご請求くださいませ。',
                'note_html' => '発送中',
                'image_url' => 'http://localhost:3000/catalog/_nuxt/assets/111.jpg',
                'sort_order' => 9,
            ],
            [
                'id' => 41214,
                'maker_id' => 8,
                'brand_id' => 8,
                'is_display' => true,
                'year' => 2026,
                'display_name' => '恐竜ランドセル（2026年度カタログ）',
                'description_html' => '恐竜専門店ダイナソーベースを代表する恐竜ランドセル！\r\n本格的な恐竜骨格がデザインされた恐竜を選び、ランドセルの本体カラー・背中のカラー等を選んで世界に一つだけのオリジナル恐竜ランドセルを作ろう.\r\nその他にも恐竜ランドセルシリーズがあるのでカタログを見てお気に入りを探そう！',
                'note_html' => '7月～発送',
                'image_url' => 'https://picsum.photos/300/300',
                'sort_order' => 10,
            ],
            [
                'id' => 41212,
                'maker_id' => 9,
                'brand_id' => 9,
                'is_display' => true,
                'year' => 2026,
                'display_name' => '26年度入学者様向けサンプルランドセル',
                'description_html' => '説明',
                'note_html' => '発送中',
                'image_url' => 'https://coverme.jp/tmp/dummy_logo.png',
                'sort_order' => 11,
            ],
            [
                'id' => 41209,
                'maker_id' => 10,
                'brand_id' => 10,
                'is_display' => true,
                'year' => 2026,
                'display_name' => 'フジタのランドセル（2026年度カタログ）',
                'description_html' => '安全もワクワクも！七色に光るランドセル！\r\n\r\n本格工房系ランドセル。雪に強く丈夫で、天然皮革の馴染む性質を最大限に活かした、作感性工学に基づいた背負い心地と負担軽減を考慮した数少ない貴重なランドセル。',
                'note_html' => '10月～発送',
                'image_url' => 'https://coverme.jp/tmp/fujita_200x200.png',
                'sort_order' => 12,
            ],
            [
                'id' => 41210,
                'maker_id' => 11,
                'brand_id' => 11,
                'is_display' => true,
                'year' => 2026,
                'display_name' => '鞄工房山本(2026年度カタログ)',
                'description_html' => 'ご家族にとって人生に残るランドセルを、奈良の工房からお届けします.\r\nラインナップは102種、お子さまの個性を表現できる豊富なデザインとカラーを展開.\r\n4店舗ある直営店のほか、全国各地での出張店舗やご自宅へのレンタルランドセルでもランドセルをご試着いただけます。',
                'note_html' => '10月～発送',
                'image_url' => 'https://coverme.jp/wp-content/uploads/2024/10/yamamoto_200.jpg',
                'sort_order' => 13,
            ],
            [
                'id' => 41211,
                'maker_id' => 12,
                'brand_id' => 12,
                'is_display' => true,
                'year' => 2026,
                'display_name' => '戸塚ランドセル（2026年度カタログ）',
                'description_html' => '子どもたちの笑顔やあたたかい日常からインスピレーションを得た優しいデザインのランドセル.\r\n工房系ランドセルとしては最大級の収納力「Grand Cube‐グランドキューブ」が特長。ロック錠は自動回転機能にとどまらず、子供たちの成長に合わせて身幅の調整が可能なスライド機能も搭載。6年間ずっと使える機能性とデザイン性を兼ね備えたランドセルです。',
                'note_html' => '11月～発送',
                'image_url' => 'https://coverme.jp/wp-content/uploads/2024/12/totsuka_randoseru_resized.png',
                'sort_order' => 14,
            ],
        ];
        $this->table('products')->insert($productsData)->save();

        // Insert data for budgets
        $budgetsData = [
            [
                'product_id' => 41213,
                'type' => 1,
                'price' => 350,
                'budget_quantity' => 100,
                'is_active' => true,
                'start_date' => '2025-01-01 00:00:00',
                'end_date' => '2026-12-31 23:59:59',
                'priority' => 1,
            ],
            [
                'product_id' => 41217,
                'type' => 1,
                'price' => 350,
                'budget_quantity' => 100,
                'is_active' => true,
                'start_date' => '2025-01-01 00:00:00',
                'end_date' => '2026-12-31 23:59:59',
                'priority' => 1,
            ],
            [
                'product_id' => 41206,
                'type' => 1,
                'price' => 60,
                'budget_quantity' => 100,
                'is_active' => true,
                'start_date' => '2025-01-01 00:00:00',
                'end_date' => '2026-12-31 23:59:59',
                'priority' => 1,
            ],
            [
                'product_id' => 41207,
                'type' => 1,
                'price' => 50,
                'budget_quantity' => 100,
                'is_active' => true,
                'start_date' => '2025-01-01 00:00:00',
                'end_date' => '2026-12-31 23:59:59',
                'priority' => 1,
            ],
            [
                'product_id' => 41208,
                'type' => 1,
                'price' => 350,
                'budget_quantity' => 100,
                'is_active' => true,
                'start_date' => '2025-01-01 00:00:00',
                'end_date' => '2026-12-31 23:59:59',
                'priority' => 1,
            ],
            [
                'product_id' => 41202,
                'type' => 1,
                'price' => 200,
                'budget_quantity' => 100,
                'is_active' => true,
                'start_date' => '2025-01-01 00:00:00',
                'end_date' => '2026-12-31 23:59:59',
                'priority' => 1,
            ],
            [
                'product_id' => 41201,
                'type' => 1,
                'price' => 200,
                'budget_quantity' => 100,
                'is_active' => true,
                'start_date' => '2025-01-01 00:00:00',
                'end_date' => '2026-12-31 23:59:59',
                'priority' => 1,
            ],
            [
                'product_id' => 41216,
                'type' => 1,
                'price' => 350,
                'budget_quantity' => 100,
                'is_active' => true,
                'start_date' => '2025-01-01 00:00:00',
                'end_date' => '2026-12-31 23:59:59',
                'priority' => 1,
            ],
            [
                'product_id' => 41215,
                'type' => 1,
                'price' => 350,
                'budget_quantity' => 100,
                'is_active' => true,
                'start_date' => '2025-01-01 00:00:00',
                'end_date' => '2026-12-31 23:59:59',
                'priority' => 1,
            ],
            [
                'product_id' => 41214,
                'type' => 1,
                'price' => 350,
                'budget_quantity' => 100,
                'is_active' => true,
                'start_date' => '2025-01-01 00:00:00',
                'end_date' => '2026-12-31 23:59:59',
                'priority' => 1,
            ],
            [
                'product_id' => 41212,
                'type' => 1,
                'price' => 350,
                'budget_quantity' => 100,
                'is_active' => true,
                'start_date' => '2025-01-01 00:00:00',
                'end_date' => '2026-12-31 23:59:59',
                'priority' => 1,
            ],
            [
                'product_id' => 41209,
                'type' => 1,
                'price' => 350,
                'budget_quantity' => 100,
                'is_active' => true,
                'start_date' => '2025-01-01 00:00:00',
                'end_date' => '2026-12-31 23:59:59',
                'priority' => 1,
            ],
            [
                'product_id' => 41210,
                'type' => 1,
                'price' => 350,
                'budget_quantity' => 100,
                'is_active' => true,
                'start_date' => '2025-01-01 00:00:00',
                'end_date' => '2026-12-31 23:59:59',
                'priority' => 1,
            ],
            [
                'product_id' => 41211,
                'type' => 1,
                'price' => 350,
                'budget_quantity' => 100,
                'is_active' => true,
                'start_date' => '2025-01-01 00:00:00',
                'end_date' => '2026-12-31 23:59:59',
                'priority' => 1,
            ],
        ];
        $this->table('budgets')->insert($budgetsData)->save();

        // Re-enable foreign key checks
        $this->execute('SET FOREIGN_KEY_CHECKS=1');
    }
}