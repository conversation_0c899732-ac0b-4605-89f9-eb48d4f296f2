<script setup lang="ts">
import { ClientMyBridge } from "~~/src/models/bridge/ClientMyBridge";
import TheClientOrderSearchForm from "~/components/client/form/TheClientOrderSearchForm.vue";
import TheClientOrderCsvForm from "~/components/client/form/TheClientOrderCsvForm.vue";
import TheClientOrders from "~/components/client/tab-content/order-detail/TheClientOrders.vue";

defineProps<{
    bridge: ClientMyBridge;
}>();
</script>

<template>
    <div>
        <the-client-order-csv-form :bridge="bridge" />
        <the-client-order-search-form :bridge="bridge" />
        <the-client-orders :bridge="bridge" />
    </div>
</template>

<style scoped></style>
