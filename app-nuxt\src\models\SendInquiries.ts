import KurokoFrontStaticSendInquiryClient from "~~/src/lib/http/kuroko-static/KurokoFrontStaticSendInquiryClient";
import { Model } from "~~/src/models/Model";

/**
 * @deprecated
 */
export default class SendInquiries extends Model {
    public send(data: TSchoolBagForm): Promise<boolean> {
        const config = this.config;
        const kurokoFrontStaticSendInquiryClient =
            KurokoFrontStaticSendInquiryClient.create(config);
        return new Promise((resolve) => {
            kurokoFrontStaticSendInquiryClient
                .post(data)
                .then(() => resolve(true))
                .catch(() => resolve(false));
        });
    }
}
