<?php

declare(strict_types=1);

namespace App\Test\TestCase\Controller\NuxtBuild;

use App\Test\TestCase\Controller\ApiTestCase;
use Cake\Core\Configure;
use Cake\Utility\Hash;

/**
 * App\Controller\NuxtBuild\ProductsController Test Case
 *
 * @uses \App\Controller\NuxtBuild\ProductsController
 */
class AllProductsControllerTest extends ApiTestCase
{

    /**
     * Test index method
     *
     * @return void
     * @uses \App\Controller\NuxtBuild\ProductsController::index()
     * php ./vendor/phpunit/phpunit/phpunit --filter "testIndex" ./tests/TestCase/Controller/NuxtBuild/AllProductsControllerTest.php
     */
    public function testIndex(): void
    {
        //        debug(Configure::read("Kuroko"));

        $this->setNuxtBuildAuthorized();
        $this->get('/nuxt-build/all-products.json');
        $response = $this->bodyToArray($this->_response->getBody());
        debug($response);
        $this->assertResponseOk(print_r(Hash::flatten($response), true));
    }

    /**
     * Test view method
     *
     * @return void
     * @uses \App\Controller\NuxtBuild\ProductsController::view()
     */
    public function testView(): void
    {
        $this->markTestIncomplete('Not implemented yet.');
    }

    /**
     * Test add method
     *
     * @return void
     * @uses \App\Controller\NuxtBuild\ProductsController::add()
     */
    public function testAdd(): void
    {
        $this->markTestIncomplete('Not implemented yet.');
    }

    /**
     * Test edit method
     *
     * @return void
     * @uses \App\Controller\NuxtBuild\ProductsController::edit()
     */
    public function testEdit(): void
    {
        $this->markTestIncomplete('Not implemented yet.');
    }

    /**
     * Test delete method
     *
     * @return void
     * @uses \App\Controller\NuxtBuild\ProductsController::delete()
     */
    public function testDelete(): void
    {
        $this->markTestIncomplete('Not implemented yet.');
    }
}
