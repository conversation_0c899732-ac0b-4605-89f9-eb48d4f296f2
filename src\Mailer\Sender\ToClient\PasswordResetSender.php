<?php

namespace App\Mailer\Sender\ToClient;

use App\Mailer\Sender\IToClientSender;
use App\Mailer\Sender\SenderTrait;
use App\Mailer\Sender\ToUserSenderTrait;

/**
 * メーカーユーザー用パスワードリセットメール送信者
 */
class PasswordResetSender implements IToClientSender
{
    use SenderTrait;
    use ToUserSenderTrait;

    public function getSubject(): string
    {
        return "【カバーミー】パスワード再設定のお願い";
    }

    public function getTemplate(): string
    {
        return "to_client/PasswordResetSender";
    }
}
