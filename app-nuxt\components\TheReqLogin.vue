<script setup lang="ts">
import { ref, computed, onMounted } from "vue";
import { isLogin } from "~/src/stores/auth";
import TheLogin from "~/components/TheLogin.vue";

// ローディング状態を管理するref
const isLoading = ref(true);
const isLoggedIn = computed(() => isLogin());

// 初回ロード時にisLoginの値をチェックする
onMounted(() => {
    // isLoggedIn.value = isLogin();
    isLoading.value = false;
});
</script>

<template>
    <div v-if="isLoading">
        <v-progress-circular indeterminate></v-progress-circular>
    </div>
    <div v-else>
        <div v-if="isLoggedIn"><slot></slot></div>
        <the-login v-else></the-login>
    </div>
</template>

<style scoped></style>
