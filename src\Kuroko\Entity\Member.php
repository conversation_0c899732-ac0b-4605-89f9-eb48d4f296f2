<?php

namespace App\Kuroko\Entity;

use Authentication\IdentityInterface;
use Cake\Core\Configure;
use Cake\Utility\Hash;

class Member implements IKurokoEntity, IdentityInterface
{
    use KurokoEntityTrait;

    public const STATUS_UNVERIFIED = "1"; // 未認証
    public const STATUS_VERIFIED = "2";   // 認証済み

    public const LOGIN_OK_FLG_OK = 1;   // ログイン許可

    private bool $_isIdentity = false;

    private ?AccessToken $_accessToken = null;

    public function getAccessToken(): ?AccessToken
    {
        return $this->_accessToken;
    }

    public function setAccessToken(?AccessToken $accessToken): static
    {
        $this->_accessToken = $accessToken;
        return $this;
    }


    public function isIdentity(): bool
    {
        return $this->_isIdentity;
    }

    public function isIdentityOn(): static
    {
        $this->_isIdentity = true;
        return $this;
    }

    public function getId(): ?string
    {
        if ($this->isIdentity()) {
            return $this->get("details.member_id");
        }
        return Hash::get($this->getData(), "id", Hash::get($this->getData(), "member_id"));
    }

    public function getGroupIds(): array
    {
        if ($this->isIdentity()) {
            return $this->get("details.group_ids", []);
        }
        return Hash::get($this->getData(), "group_ids", []);
    }

    public function getStatus(): string
    {
        if ($this->isIdentity()) {
            return $this->get("details.status.key", "0");
        }
        return Hash::get($this->getData(), "status.key", "0");
    }

    public function getPreFormid(): ?string
    {
        if (!$this->isIdentity()) {
            return $this->get("pre_form_id");
        }
        return Hash::get($this->getData(), "details.pre_form_id");
    }

    public function getMakerId(): ?int
    {
        return Hash::get($this->getData(), "details.maker_id");
    }

    public function getName1Kana(): ?string
    {
        if (!$this->isIdentity()) {
            return $this->get("name1_hurigana");
        }
        return Hash::get($this->getData(), "details.name1_hurigana");
    }

    public function getName2Kana(): ?string
    {
        if (!$this->isIdentity()) {
            return $this->get("name2_hurigana");
        }
        return Hash::get($this->getData(), "details.name2_hurigana");
    }

    public function getSendEmailFlag(): ?bool
    {
        if (!$this->isIdentity()) {
            return $this->get("email_send_ng_flg");
        }
        return Hash::get($this->getData(), "details.email_send_ng_flg");
    }

    public function getEmail(): ?string
    {
        if (!$this->isIdentity()) {
            return $this->get("email");
        }
        return Hash::get($this->getData(), "details.email");
    }

    public function getErrors(): array
    {
        return $this->get("errors", []);
    }

    /**
     * @return bool
     */
    public function isBelongsToClientGroup(): bool
    {
        return in_array(Configure::read("Kuroko.api.clientGroupId"), $this->getGroupIds());
    }

    /**
     * @return bool
     */
    public function isBelongsToSwbGroup(): bool
    {
        return in_array(Configure::read("Kuroko.api.swbGroupId"), $this->getGroupIds());
    }

    /**
     * @return bool
     */
    public function isStatusAuthenticated(): bool
    {
        return $this->getStatus() == static::STATUS_VERIFIED;
    }

    public function getJsonData(): array
    {
        if ($this->isIdentity()) {
            //@todo  details の中身を返却,不要項目は消すこと
            return Hash::get($this->getData(), "details", []);
            // return [Hash::get($this->getData(), "details.member_id", [])];
        }
        return [
            "id" => $this->getId(),
            "messages" => Hash::get($this->getData(), "messages", []),
            "errors" => Hash::get($this->getData(), "errors", []),
        ];
    }

    public function offsetExists(mixed $offset): bool
    {
        return false;
    }

    public function offsetGet(mixed $offset): mixed
    {
        return null;
    }

    public function offsetSet(mixed $offset, mixed $value): void {}

    public function offsetUnset(mixed $offset): void {}

    public function getIdentifier(): ?string
    {
        return $this->getId();
    }

    public function getOriginalData(): static
    {
        return $this;
    }

    /**
     * @param array $data
     * @return array
     */
    public function initialize(array $data): array
    {
        return Hash::insert(
            $data,
            "errors",
            Error::creates(Hash::get($data, "errors", []))
        );
    }
}
