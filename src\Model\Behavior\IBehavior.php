<?php

namespace App\Model\Behavior;

use Cake\ORM\Query;
use Cake\ORM\Table;

interface IBehavior
{
    /**
     * @param Query $query
     * @param array $options
     * @return Query
     */
    public function findSearch(Query $query, array $options = []): Query;

    /**
     * @param Query $query
     * @param string $field
     * @param array $options
     * @param Table|null $table
     * @return Query
     */
    public function whereEqualIn(Query $query, string $field, array $options, ?Table $table = null): Query;


    /**
     * @param Query $query
     * @param string $field
     * @param string $expression
     * @return Query
     */
    public function countQuery(Query $query, string $field, string $expression = '*'): Query;
}
