<?php

namespace App\Form\Front;

use App\Enums\EFormFieldType;
use App\Enums\EntityFields\ELogin;
use App\Form\ApiForm;
use App\Validation\Validator\LoginValidator;
use Cake\Form\Schema;
use Cake\Validation\Validator;

class UserAuthenticationsAddForm extends ApiForm
{

    public function _buildSchema(Schema $schema): Schema
    {
        $schema->addField(ELogin::LOGIN_EMAIL->value, EFormFieldType::STRING->value);
        $schema->addField(ELogin::LOGIN_PWD->value, EFormFieldType::STRING->value);
        return $schema;
    }

    public function validationDefault(Validator $validator): Validator
    {
        return LoginValidator::buildValidator($validator, [
            ELogin::LOGIN_EMAIL->value,
            ELogin::LOGIN_PWD->value,
        ]);
    }

    protected function _getAllowFieldsPath(): array
    {
        return [
            ELogin::LOGIN_EMAIL->value,
            ELogin::LOGIN_PWD->value,
        ];
    }
}
