<?php

namespace App\Service\Traits;

trait PdfImageTrait
{
    public function getPdfImageViewVars(): array
    {
        $logo = file_get_contents(WWW_ROOT . 'img' . DS . 'logo.png');
        $logoBase64 = base64_encode($logo);
        $companySeal = file_get_contents(WWW_ROOT . 'img' . DS . 'company_seal.png');
        $companySealBase64 = base64_encode($companySeal);

        return [
            'logo_base64' => $logoBase64,
            'company_seal_base64' => $companySealBase64,
        ];
    }
}
