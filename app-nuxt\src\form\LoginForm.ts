import { Validations } from "~~/src/lib/Validations";

export default class LoginForm {
    private _email: string;
    private _password: string;
    private _type: string;
    private _valid: boolean = false;

    constructor() {
        this._email = "";
        this._password = "";
        this._type = "general";
    }

    get valid(): boolean {
        return this._valid;
    }

    set valid(value: boolean) {
        this._valid = value;
    }

    get email(): string {
        return this._email;
    }

    set email(value: string) {
        this._email = value;
    }

    get email_rules(): TValidationFunction[] {
        return [
            Validations.notEmptyString("メールアドレス"),
            Validations.email("メールアドレス"),
        ];
    }

    get password(): string {
        return this._password;
    }

    set password(value: string) {
        this._password = value;
    }

    get type(): string {
        return this._type;
    }

    set type(value: string) {
        this._type = value;
    }

    get password_rules(): TValidationFunction[] {
        return [
            Validations.notEmptyString("パスワード"),
            Validations.password2("パスワード"),
        ];
    }

    get data(): TLogin {
        return {
            email: this.email,
            password: this.password,
            type: this.type,
        };
    }
}
