<?php

namespace App\Service\Traits;

use App\Model\Entity\GeneralUser;
use App\Model\Table\TemporaryRegistrationsTable;
use App\Mailer\AppMailer;
use App\Mailer\Sender\ToUser\OrderCompleteAndRegistCompletedSender;
use App\Model\Table\UserProfilesTable;
use Cake\ORM\TableRegistry;
use Cake\Utility\Hash;
use Cake\Log\Log;

/**
 * 会員登録時の注文処理機能を提供するTrait
 */
trait RegistrationOrderTrait
{
    use OrderTrait;
    
    /**
     * TemporaryRegistrationsTableインスタンス
     */
    protected ?TemporaryRegistrationsTable $temporaryRegistrationsTable = null;
    protected ?UserProfilesTable $userProfilesTable = null;
    
    /**
     * 登録時注文処理の初期化
     */
    protected function initializeRegistrationOrder(): void
    {
        $this->initializeOrder();
        // $this->temporaryRegistrationsTable = TableRegistry::getTableLocator()->get('TemporaryRegistrations');
        $this->userProfilesTable = TableRegistry::getTableLocator()->get('UserProfiles');
    }
    
    /**
     * 商品IDを取得
     * 
     * @param array $arrayData
     * @return array 商品ID配列
     */
    protected function getProductIds(array $arrayData): array
    {
        if ($tempRegistration = Hash::get($arrayData, 'temp_registration')) {
            return $arrayData['temp_registration']->product_ids;
        }
        
        return [];
    }

    /**
     * ユーザーIDを取得
     * 
     * @param GeneralUser $user ユーザー情報
     * @return int ユーザーID
     */
    protected function getUserId(GeneralUser $user): int
    {
        return $user->id;
    }
    
    /**
     * 注文完了メールを送信
     * 
     * @param GeneralUser $user ユーザー情報
     * @return void
     */
    protected function sendCompletionEmail(GeneralUser $user, array $randselOrders): void
    {
        if (!$this->randselOrdersTable || !$this->userProfilesTable) {
            $this->initializeRegistrationOrder();
        }
        
        $profileData = $this->userProfilesTable->find()
            ->where(['general_user_id' => $user->id])
            ->first();
        
        // 会員登録完了メールを送信
        AppMailer::sendToUser(
            (new OrderCompleteAndRegistCompletedSender(
                $user->email,
                $profileData->decrypted_last_name
            ))->setViewVars(['orders' => $randselOrders])
        );
        
        Log::info("Registration and order completion email sent to: {$user->email}");
    }
}