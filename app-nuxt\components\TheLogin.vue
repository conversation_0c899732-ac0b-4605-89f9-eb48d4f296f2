<script setup lang="ts">
import LoginForm from "~~/src/form/LoginForm";
import { reactive, ref, computed } from "vue";
import { useRuntimeConfig, useRoute } from "#app";
import MemberLogins from "~/src/models/MemberLogins";
import ClientLogins from "~/src/models/ClientLogins";
import SwbLogins from "~/src/models/SwbLogins";
import { useAuthStore } from "~/src/stores/auth";

const loginForm = reactive(new LoginForm());

const stepLogin = "login";
const stepComplete = "complete";

const isLoading = ref(false);
const step = ref(stepLogin);
const errorMessage = ref("");
const config = useRuntimeConfig();
const authStore = useAuthStore();

const isClient = computed(() => useRoute().meta.type === 'client');
const isSwb = computed(() => useRoute().meta.type === 'swb');

const toComplete = (): void => {
    if (!loginForm.valid) {
        return;
    }
    isLoading.value = true;
    
    loginForm.type = isClient.value ? "maker" : isSwb.value ? "swb" : "general";
    const loginModel = isSwb.value
        ? SwbLogins
        : isClient.value
          ? ClientLogins
          : MemberLogins;

    loginModel
        .create(config)
        .add(loginForm.data)
        .then((response) => {
            if ("access_token" in response) {
                authStore.setAuth(response.access_token, response.member.group_ids);
                step.value = stepComplete;
                // navigateTo("/member/account");
            } else {
                switch (response.code) {
                    case "FAILURE_CREDENTIALS_INVALID":
                        errorMessage.value = "メールアドレスまたはパスワードが間違っています";
                        break;
                    default:
                        errorMessage.value = response.message;
                }
            }
            isLoading.value = false;
        });
};
</script>

<template>
    <div>
        <div class="text-h5 font-weight-bold pa-4">ログインフォーム</div>
        <template v-if="isLoading">
            <v-progress-circular indeterminate></v-progress-circular>
        </template>
        <template v-else>
            <template v-if="step === stepLogin">
                <v-alert v-if="errorMessage" type="error" class="mt-3">
                    {{ errorMessage }}
                </v-alert>
                <v-form v-model="loginForm.valid">
                    <v-container>
                        <v-row>
                            <v-text-field
                                v-model="loginForm.email"
                                density="comfortable"
                                :rules="loginForm.email_rules"
                                label="メールアドレス"
                                type="email"
                                required
                            ></v-text-field>
                        </v-row>
                        <v-row>
                            <v-text-field
                                v-model="loginForm.password"
                                density="comfortable"
                                :rules="loginForm.password_rules"
                                label="パスワード"
                                type="password"
                                required
                            ></v-text-field>
                        </v-row>
                        <v-row>
                            <v-btn
                                :color="
                                    isSwb
                                        ? 'swbPrimary'
                                        : isClient
                                          ? 'clientPrimary'
                                          : 'primary'
                                "
                                :disabled="!loginForm.valid"
                                @click="toComplete"
                                >ログイン</v-btn
                            >
                        </v-row>
                        <v-row v-if="!(isClient || isSwb)" class="pt-4">
                            パスワードを忘れた方は
                            <NuxtLink to="/forgot-password/"> こちら </NuxtLink>
                        </v-row>
                    </v-container>
                </v-form>
            </template>
            <template v-else-if="step === stepComplete">
                <v-container> ログインに成功しました </v-container>
            </template>
        </template>
    </div>
</template>

<style scoped lang="scss">
// .client-button {
//     font-weight: bold;
//     color: white !important;
// }
</style>
