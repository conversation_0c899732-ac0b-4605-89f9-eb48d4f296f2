<?php

namespace App\Kuroko\ApiModel;

abstract class KurokoApiDynamicModel extends KurokoApiModel
{

    protected function getEndPoint(string $endpoint)
    {
        return parent::getEndPoint("dynamic." . $endpoint);
    }

    protected function getEndPointWithId(string $endpoint, string|int $id): string
    {
        return $this->getEndPoint($endpoint) . "/" . $id;
    }


    public function __construct(array $config = [])
    {
        parent::__construct($config);
    }

    protected function _setSysUserToken(): static
    {
        $this->setToken($this->getConfig("token.dynamic.sysUser"));
        return $this;
    }

}
