<?php

declare(strict_types=1);

namespace App\Command;

use Cake\Command\Command;
use Cake\Console\Arguments;
use Cake\Console\ConsoleIo;
use Cake\Console\ConsoleOptionParser;
use Cake\I18n\FrozenDate;
use Cake\Log\Log;
use Cake\Utility\Hash;
use App\Enums\EntityFields\ESwbOrderForm;
use App\Service\RandselOrdersService;
use App\Service\RandselInvoiceAdjustmentsService;
use App\Service\RandselInvoicesService;
use App\Service\AllProductsService;
use Exception;

/**
 * InvoiceCalculation command.
 */
class InvoiceCalculationCommand extends Command
{
    /**
     * Hook method for defining this command's option parser.
     *
     * @see https://book.cakephp.org/4/en/console-commands/commands.html#defining-arguments-and-options
     * @param \Cake\Console\ConsoleOptionParser $parser The parser to be defined
     * @return \Cake\Console\ConsoleOptionParser The built parser.
     */
    public function buildOptionParser(ConsoleOptionParser $parser): ConsoleOptionParser
    {
        $parser = parent::buildOptionParser($parser);

        $parser->setDescription([
            '請求金額を計算し、テーブル:randsel_invoices に登録します。※調整金額は含めません。更新は行われません。',
        ]);

        $parser->addOption('year_month', [
            'help' => '請求年月 (YYYY-MM) 例：--year_month=2022-01',
            'default' => FrozenDate::now()->subMonths(1)->format('Y-m'),
        ]);

        return $parser;
    }

    /**
     * Implement this method with your command's logic.
     *
     * @param \Cake\Console\Arguments $args The command arguments.
     * @param \Cake\Console\ConsoleIo $io The console io
     * @return int|null The exit code or null for success
     */
    public function execute(Arguments $args, ConsoleIo $io)
    {
        Log::info('請求金額計算バッチ開始');
        $options = $args->getOptions();

        $io->out("options: " . json_encode($options));

        $yearMonth = $args->getOption('year_month');
        // バリデーションチェック
        if (!preg_match('/^\d{4}-\d{2}$/', $yearMonth)) {
            $io->error('請求年月は YYYY-MM 形式で指定してください。例: 2024-02');
            Log::error('請求年月バリデーションエラー: ' . $yearMonth . ' は YYYY-MM 形式ではありません。');
            return Command::CODE_ERROR;
        }
        Log::info('請求年月: ' . $yearMonth);

        $io->out('請求年月: ' . $yearMonth . ' で請求金額計算処理を開始します。');

        $options = [
            'from' => $yearMonth . '-01',
            'to' => $yearMonth . '-01',
            'group_by' => ESwbOrderForm::PRODUCT_ID->value
        ];

        // 注文テーブルから請求金額を取得
        $invoicesByProduct = Hash::combine((new RandselOrdersService())->getMonthlyInvoices($options), '{n}.product_id', '{n}');

        $products = (new AllProductsService())->index();

        $saveCount = 0;
        $errorCount = 0;
        // 商品ごとに請求金額計算
        foreach ($products as $product) {
            $productId = $product->getJsonData()['product_id'];
            $totalAmount = 0;

            // 前月の請求金額と調整金額がなしの場合はスキップ
            if (!isset($invoicesByProduct[$productId])) {
                continue;
            }

            if ($invoice = Hash::get($invoicesByProduct, $productId)) {
                $totalAmount = $invoice['total_price'];
            }

            $randselInvoice = [
                'maker_id' => Hash::get($invoicesByProduct, $productId)['maker_id'],
                'product_id' => $productId,
                'billing_year_month' => $yearMonth,
                'total_amount' => (int) $totalAmount,
                'total_quantity' => Hash::get($invoicesByProduct, $productId)['count'] ?? 0,
                'invoice_amount' => (int) $totalAmount,
            ];

            if (!(new RandselInvoicesService())->createRandselInvoice($randselInvoice)) {
                $errorCount++;
                $io->out('請求金額登録失敗: maker_id=' . Hash::get($invoicesByProduct, $productId)['maker_id'] . ', product_id=' . $productId . ', year_month=' . $yearMonth);
                Log::error('請求金額登録失敗: maker_id=' . Hash::get($invoicesByProduct, $productId)['maker_id'] . ', product_id=' . $productId . ', year_month=' . $yearMonth);
            } else {
                $saveCount++;
            }
        }

        $io->out('請求金額計算処理完了');
        $io->out('新規登録: ' . $saveCount . '件, エラー: ' . $errorCount . '件');
        Log::info('請求金額計算バッチ完了');
        Log::info('新規登録: ' . $saveCount . '件, エラー: ' . $errorCount . '件');

        return Command::CODE_SUCCESS;
    }
}
