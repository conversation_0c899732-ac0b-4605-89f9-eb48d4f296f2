<?php
declare(strict_types=1);

namespace App\Test\TestCase\Service;

use App\Model\Entity\GeneralUser;
use App\Model\Entity\SwbUser;
use App\Model\Entity\MakerUser;
use App\Service\AuthenticationService;
use App\Test\TestCase\AppTestCase;
use Cake\ORM\TableRegistry;

/**
 * 統合認証サービステストケース
 */
class IntegratedAuthenticationServiceTest extends AppTestCase
{
    /**
     * Test fixtures
     */
    protected $fixtures = [
        'app.GeneralUsers',
        'app.SwbUsers',
        'app.MakerUsers',
        'app.UserProfiles',
    ];

    /**
     * Test subject
     */
    protected $AuthenticationService;

    /**
     * setUp method
     */
    public function setUp(): void
    {
        parent::setUp();
        $this->AuthenticationService = new AuthenticationService();
    }

    /**
     * tearDown method
     */
    public function tearDown(): void
    {
        unset($this->AuthenticationService);
        parent::tearDown();
    }

    /**
     * 新システム一般ユーザーの認証テスト
     */
    public function testAuthenticateNewSystemGeneralUser(): void
    {
        // テスト用の新システム一般ユーザーを作成
        $generalUsersTable = TableRegistry::getTableLocator()->get('GeneralUsers');
        $user = $generalUsersTable->newEntity([
            'email' => '<EMAIL>',
            'password' => 'password123', // エンティティで自動ハッシュ化
        ]);
        $savedUser = $generalUsersTable->save($user);
        $this->assertNotEmpty($savedUser);

        // 認証実行
        $result = $this->AuthenticationService->authenticate(
            '<EMAIL>',
            'password123',
            AuthenticationService::USER_TYPE_GENERAL
        );

        // 結果検証
        $this->assertInstanceOf(GeneralUser::class, $result);
        $this->assertEquals('<EMAIL>', $result->email);
        $this->assertFalse($result->isKurocoUser());
    }

    /**
     * Kuroco一般ユーザーの認証テスト（パスワードがnull）
     */
    public function testAuthenticateKurocoGeneralUser(): void
    {
        // テスト用のKuroco一般ユーザーを作成（パスワードがnull）
        $generalUsersTable = TableRegistry::getTableLocator()->get('GeneralUsers');
        $user = $generalUsersTable->newEntity([
            'email' => '<EMAIL>',
            'password' => null, // Kurocoユーザー
        ]);
        $savedUser = $generalUsersTable->save($user);
        $this->assertNotEmpty($savedUser);

        // 認証実行（新システム認証は失敗する）
        $result = $this->AuthenticationService->authenticate(
            '<EMAIL>',
            'anypassword',
            AuthenticationService::USER_TYPE_GENERAL
        );

        // 結果検証（Kurocoユーザーのため認証失敗）
        $this->assertNull($result);
    }

    /**
     * SWBユーザーの認証テスト（完全に新システム移行済み）
     */
    public function testAuthenticateSwbUser(): void
    {
        // テスト用のSWBユーザーを作成
        $swbUsersTable = TableRegistry::getTableLocator()->get('SwbUsers');
        $user = $swbUsersTable->newEntity([
            'email' => '<EMAIL>',
            'password' => 'password123',
            'authority_id' => 100,
        ]);
        $savedUser = $swbUsersTable->save($user);
        $this->assertNotEmpty($savedUser);

        // 認証実行
        $result = $this->AuthenticationService->authenticate(
            '<EMAIL>',
            'password123',
            AuthenticationService::USER_TYPE_SWB
        );

        // 結果検証
        $this->assertInstanceOf(SwbUser::class, $result);
        $this->assertEquals('<EMAIL>', $result->email);
    }

    /**
     * メーカーユーザーの認証テスト（完全に新システム移行済み）
     */
    public function testAuthenticateMakerUser(): void
    {
        // テスト用のメーカーユーザーを作成
        $makerUsersTable = TableRegistry::getTableLocator()->get('MakerUsers');
        $user = $makerUsersTable->newEntity([
            'email' => '<EMAIL>',
            'password' => 'password123',
            'maker_id' => 1,
        ]);
        $savedUser = $makerUsersTable->save($user);
        $this->assertNotEmpty($savedUser);

        // 認証実行
        $result = $this->AuthenticationService->authenticate(
            '<EMAIL>',
            'password123',
            AuthenticationService::USER_TYPE_MAKER
        );

        // 結果検証
        $this->assertInstanceOf(MakerUser::class, $result);
        $this->assertEquals('<EMAIL>', $result->email);
    }

    /**
     * 存在しないユーザーの認証テスト
     */
    public function testAuthenticateNonExistentUser(): void
    {
        // 認証実行
        $result = $this->AuthenticationService->authenticate(
            '<EMAIL>',
            'password123',
            AuthenticationService::USER_TYPE_GENERAL
        );

        // 結果検証
        $this->assertNull($result);
    }

    /**
     * 間違ったパスワードでの認証テスト
     */
    public function testAuthenticateWithWrongPassword(): void
    {
        // テスト用の新システム一般ユーザーを作成
        $generalUsersTable = TableRegistry::getTableLocator()->get('GeneralUsers');
        $user = $generalUsersTable->newEntity([
            'email' => '<EMAIL>',
            'password' => 'correctpassword',
        ]);
        $savedUser = $generalUsersTable->save($user);
        $this->assertNotEmpty($savedUser);

        // 間違ったパスワードで認証実行
        $result = $this->AuthenticationService->authenticate(
            '<EMAIL>',
            'wrongpassword',
            AuthenticationService::USER_TYPE_GENERAL
        );

        // 結果検証
        $this->assertNull($result);
    }

    /**
     * 無効なユーザータイプでの認証テスト
     */
    public function testAuthenticateWithInvalidUserType(): void
    {
        // 認証実行
        $result = $this->AuthenticationService->authenticate(
            '<EMAIL>',
            'password123',
            'invalid_type'
        );

        // 結果検証
        $this->assertNull($result);
    }

    /**
     * Kurocoユーザー判定テスト
     */
    public function testIsKurocoUser(): void
    {
        // テスト用のKurocoユーザーを作成
        $generalUsersTable = TableRegistry::getTableLocator()->get('GeneralUsers');
        $kurocoUser = $generalUsersTable->newEntity([
            'email' => '<EMAIL>',
            'password' => null, // Kurocoユーザー
        ]);
        $generalUsersTable->save($kurocoUser);

        // テスト用の新システムユーザーを作成
        $newSystemUser = $generalUsersTable->newEntity([
            'email' => '<EMAIL>',
            'password' => 'password123',
        ]);
        $generalUsersTable->save($newSystemUser);

        // Kurocoユーザー判定テスト
        $isKuroco1 = $this->AuthenticationService->isKurocoUser(
            '<EMAIL>',
            AuthenticationService::USER_TYPE_GENERAL
        );
        $this->assertTrue($isKuroco1);

        // 新システムユーザー判定テスト
        $isKuroco2 = $this->AuthenticationService->isKurocoUser(
            '<EMAIL>',
            AuthenticationService::USER_TYPE_GENERAL
        );
        $this->assertFalse($isKuroco2);

        // 存在しないユーザー判定テスト
        $isKuroco3 = $this->AuthenticationService->isKurocoUser(
            '<EMAIL>',
            AuthenticationService::USER_TYPE_GENERAL
        );
        $this->assertFalse($isKuroco3);
    }

    /**
     * パスワード妥当性チェックテスト
     */
    public function testValidatePassword(): void
    {
        // 有効なパスワード
        $errors1 = $this->AuthenticationService->validatePassword('password123');
        $this->assertEmpty($errors1);

        // 短すぎるパスワード
        $errors2 = $this->AuthenticationService->validatePassword('pass1');
        $this->assertNotEmpty($errors2);
        $this->assertContains('パスワードは8文字以上で入力してください', $errors2);

        // 英字なしパスワード
        $errors3 = $this->AuthenticationService->validatePassword('12345678');
        $this->assertNotEmpty($errors3);
        $this->assertContains('パスワードには英字を含めてください', $errors3);

        // 数字なしパスワード
        $errors4 = $this->AuthenticationService->validatePassword('password');
        $this->assertNotEmpty($errors4);
        $this->assertContains('パスワードには数字を含めてください', $errors4);
    }

    /**
     * パスワードハッシュ化テスト
     */
    public function testHashPassword(): void
    {
        $password = 'password123';
        $hashedPassword = $this->AuthenticationService->hashPassword($password);

        $this->assertNotEquals($password, $hashedPassword);
        $this->assertNotEmpty($hashedPassword);
        
        // 同じパスワードでも異なるハッシュが生成される（salt使用のため）
        $hashedPassword2 = $this->AuthenticationService->hashPassword($password);
        $this->assertNotEquals($hashedPassword, $hashedPassword2);
    }
}
