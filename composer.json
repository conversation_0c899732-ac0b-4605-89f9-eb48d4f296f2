{"name": "cakephp/app", "description": "CakePHP skeleton app", "homepage": "https://cakephp.org", "type": "project", "license": "MIT", "require": {"php": ">=8.1", "cakephp/authentication": "^2.0", "cakephp/cakephp": "^4.5.0", "cakephp/migrations": "^3.7", "cakephp/plugin-installer": "^1.3", "dompdf/dompdf": "^3.1", "friendsofcake/cakepdf": "^4.1", "mobiledetect/mobiledetectlib": "^3.74", "monolog/monolog": "^3.8", "muffin/trash": "^3.1"}, "require-dev": {"cakephp/bake": "^2.8", "cakephp/cakephp-codesniffer": "^4.5", "cakephp/debug_kit": "^4.9", "josegonzalez/dotenv": "^4.0", "phpunit/phpunit": "^9.6"}, "suggest": {"markstory/asset_compress": "An asset compression plugin which provides file concatenation and a flexible filter system for preprocessing and minification.", "dereuromark/cakephp-ide-helper": "After baking your code, this keeps your annotations in sync with the code evolving from there on for maximum IDE and PHPStan/Psalm compatibility.", "phpstan/phpstan": "PHPStan focuses on finding errors in your code without actually running it. It catches whole classes of bugs even before you write tests for the code.", "cakephp/repl": "Console tools for a REPL interface for CakePHP applications."}, "autoload": {"psr-4": {"App\\": "src/"}}, "autoload-dev": {"psr-4": {"App\\Test\\": "tests/", "Cake\\Test\\": "vendor/cakephp/cakephp/tests/"}}, "scripts": {"post-install-cmd": "App\\Console\\Installer::postInstall", "post-create-project-cmd": "App\\Console\\Installer::postInstall", "check": ["@test", "@cs-check"], "cs-check": "phpcs --colors -p", "cs-fix": "phpcbf --colors -p", "test": "phpunit --colors=always"}, "minimum-stability": "dev", "prefer-stable": true, "config": {"sort-packages": true, "allow-plugins": {"cakephp/plugin-installer": true, "dealerdirect/phpcodesniffer-composer-installer": true}}}