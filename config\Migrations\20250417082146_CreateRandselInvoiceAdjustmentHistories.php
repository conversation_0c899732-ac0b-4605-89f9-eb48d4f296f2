<?php
declare(strict_types=1);

use Migrations\AbstractMigration;

class CreateRandselInvoiceAdjustmentHistories extends AbstractMigration
{
    /**
     * Change Method.
     *
     * More information on this method is available here:
     * https://book.cakephp.org/phinx/0/en/migrations.html#the-change-method
     * @return void
     */
    public function change(): void
    {
        $table = $this->table('randsel_invoice_adjustment_histories', [
            'comment' => '調整金額履歴'
        ]);
        $table->addColumn('invoice_adjustment_id', 'integer', [
            'null' => false,
            'comment' => '調整金額ID',
            'default' => null,
            'limit' => null,
        ])
            ->addColumn('action_type', 'tinyinteger', [
                'null' => false,
                'comment' => '操作種類 (1:作成, 2:変更, 3:確定)',
                'default' => null,
                'limit' => null,
            ])
            ->addColumn('changes', 'json', [
                'null' => true,
                'comment' => '変更内容 (JSON形式)',
                'default' => null,
            ])
            ->addColumn('created_by', 'integer', [
                'null' => false,
                'comment' => '操作者ID(kuroco member_id)',
                'default' => null,
                'limit' => null,
            ])
            ->addColumn('created', 'datetime', [
                'null' => false,
                'comment' => '作成日時',
                'default' => 'CURRENT_TIMESTAMP',
                'limit' => null,
            ])
            ->addIndex(
                ['invoice_adjustment_id'],
                ['name' => 'idx_invoice_adjustment_id']
            )
            ->addForeignKey(
                'invoice_adjustment_id',
                'randsel_invoice_adjustments',
                'id',
                [
                    'delete' => 'CASCADE',
                    'update' => 'NO_ACTION',
                ]
            )
            ->create();
    }
}
