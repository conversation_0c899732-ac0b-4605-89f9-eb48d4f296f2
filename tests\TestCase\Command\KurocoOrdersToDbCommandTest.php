<?php

declare(strict_types=1);

namespace App\Test\TestCase\Command;

use App\Command\KurocoOrdersToDbCommand;
use Cake\Console\ConsoleIo;
use Cake\Console\Arguments;
use Cake\Console\TestSuite\ConsoleIntegrationTestTrait;
use Cake\Console\ConsoleOptionParser;
use App\Test\TestCase\AppTestCase;
use App\Service\RandselOrdersService;

/**
 * App\Command\KurocoOrdersToDbCommand Test Case
 *
 * @uses \App\Command\KurocoOrdersToDbCommand
 */
class KurocoOrdersToDbCommandTest extends AppTestCase
{
    use ConsoleIntegrationTestTrait;

    /**
     * setUp method
     *
     * @return void
     */
    protected function setUp(): void
    {
        parent::setUp();
        $this->useCommandRunner();
    }

    /**
     * Test that the description of the command is correct
     *
     * @return void
     */
    // php ./vendor/phpunit/phpunit/phpunit --filter "testDescriptionOutput" ./tests/TestCase/Command/KurocoOrdersToDbCommandTest.php
    public function testDescriptionOutput()
    {
        $description = [
            'Kurocoの注文情報をDBに登録します。',
            'Kurocoの注文情報について、未削除のユーザーの注文情報のみを取得します。',
            'オプションを指定しない場合は、すべての注文情報を取得します。',
            '他のオプションを指定する場合は、オプションヘルプを参照してください。',
        ];
        $description = implode("\n", $description);

        $this->exec('kuroco_orders_to_db --help');
        $this->assertOutputContains($description);
    }

    /**
     * Test buildOptionParser method
     *
     * @return void
     * @uses \App\Command\KurocoOrdersToDbCommand::buildOptionParser()
     */
    // php ./vendor/phpunit/phpunit/phpunit --filter "testBuildOptionParser" ./tests/TestCase/Command/KurocoOrdersToDbCommandTest.php
    public function testBuildOptionParser(): void
    {
        $command = new KurocoOrdersToDbCommand();
        $parser = $command->buildOptionParser(new ConsoleOptionParser());
        // Test that the parser has the correct options
        $this->assertArrayHasKey('from', $parser->options());
        $this->assertArrayHasKey('to', $parser->options());
        $this->assertArrayHasKey('product_id', $parser->options());
        $this->assertArrayHasKey('order_id', $parser->options());
    }

    // php ./vendor/phpunit/phpunit/phpunit --filter "testExecuteWithNoOptions" ./tests/TestCase/Command/KurocoOrdersToDbCommandTest.php
    public function testExecuteWithNoOptions()
    {
        $command = new KurocoOrdersToDbCommand();
        $args = new Arguments([], [], []);
        $io = new ConsoleIo();
        $result = $command->execute($args, $io);
        $this->assertNull($result);

        $service = new RandselOrdersService();
        $this->assertNotEmpty($service->getRandselOrders());
        debug($service->getRandselOrders());
    }

    // php ./vendor/phpunit/phpunit/phpunit --filter "testExecuteWithOrderId" ./tests/TestCase/Command/KurocoOrdersToDbCommandTest.php
    public function testExecuteWithOrderId()
    {
        $command = new KurocoOrdersToDbCommand();
        $args = new Arguments(
            [],
            [
                'order_id' => ['4232', '4212'],
            ],
            []
        );
        $io = new ConsoleIo();
        $result = $command->execute($args, $io);
        $this->assertNull($result);
        $service = new RandselOrdersService();
        $this->assertNotEmpty($service->getRandselOrders());
        debug($service->getRandselOrders([
            'id' => ['4232', '4212'],
        ]));
    }

    // php ./vendor/phpunit/phpunit/phpunit --filter "testExecuteWithProductId" ./tests/TestCase/Command/KurocoOrdersToDbCommandTest.php
    public function testExecuteWithProductId()
    {
        $command = new KurocoOrdersToDbCommand();
        $args = new Arguments([], ['product_id' => '41207'], []);
        $io = new ConsoleIo();
        $result = $command->execute($args, $io);
        $this->assertNull($result);
        $service = new RandselOrdersService();
        $this->assertNotEmpty($service->getRandselOrders());
        debug($service->getRandselOrders([
            'product_id' => '41207',
        ]));
    }

    // php ./vendor/phpunit/phpunit/phpunit --filter "testExecuteWithFromAndToDates" ./tests/TestCase/Command/KurocoOrdersToDbCommandTest.php
    public function testExecuteWithFromAndToDates()
    {
        $command = new KurocoOrdersToDbCommand();
        $args = new Arguments([], ['from' => '2024-11-01', 'to' => '2024-11-14'], []);
        $io = new ConsoleIo();
        $result = $command->execute($args, $io);
        $this->assertNull($result);
        $service = new RandselOrdersService();
        $this->assertNotEmpty($service->getRandselOrders());
        debug($service->getRandselOrders([
            'from' => '2024-11-01',
            'to' => '2024-11-14',
            'searchDateType' => 'order-date',
        ]));
    }
}
