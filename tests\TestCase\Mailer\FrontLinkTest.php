<?php

namespace App\Test\TestCase\Mailer;

use App\Kuroko\Entity\AccessToken;
use App\Mailer\FrontLink;
use Cake\TestSuite\TestCase;

class FrontLinkTest extends TestCase
{
    // php ./vendor/phpunit/phpunit/phpunit --filter "testGetFormalRegistLink" ./tests/TestCase/Mailer/FrontLinkTest.php
    public function testGetFormalRegistLink()
    {
        $accessTokenMock = $this->createMock(AccessToken::class);
        $accessTokenMock->method('encryptToken')
            ->willReturn('test_token');

        $expectedUrl = 'http://localhost/catalog/member/registration-success/?t=test_token';
        $result = FrontLink::getFormalRegistLink($accessTokenMock);

        $this->assertEquals($expectedUrl, $result);
    }

    // php ./vendor/phpunit/phpunit/phpunit --filter "testGetKurocoPasswordResetLink" ./tests/TestCase/Mailer/FrontLinkTest.php
    public function testGetKurocoPasswordResetLink()
    {
        $data = [
            "token" => "test_token",
            "temp_pwd" => "1234tptp",
        ];

        $expectedUrl = 'http://localhost/catalog/member/password-reset/?tt=test_token&tp=1234tptp';
        $result = FrontLink::getKurocoPasswordResetLink($data);

        $this->assertEquals($expectedUrl, $result);
    }

    // php ./vendor/phpunit/phpunit/phpunit --filter "testGetMyLink" ./tests/TestCase/Mailer/FrontLinkTest.php
    public function testGetMyLink()
    {
        $expectedUrl = 'http://localhost/catalog/member/account/';
        $result = FrontLink::getMyLink();
        $this->assertEquals($expectedUrl, $result);
    }

    // php ./vendor/phpunit/phpunit/phpunit --filter "testGetLoginLink" ./tests/TestCase/Mailer/FrontLinkTest.php
    // public function testGetLoginLink()
    // {
    //     $expectedUrl = 'http://localhost/catalog/member/login';
    //     $result = FrontLink::getLoginLink();

    //     $this->assertEquals($expectedUrl, $result);
    // }
}
