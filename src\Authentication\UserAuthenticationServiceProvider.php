<?php

namespace App\Authentication;

use Authentication\AuthenticationServiceInterface;
use Authentication\AuthenticationServiceProviderInterface;
use Cake\Routing\Route\DashedRoute;
use Cake\Utility\Hash;
use Psr\Http\Message\ServerRequestInterface;

class UserAuthenticationService<PERSON><PERSON>ider implements AuthenticationServiceProviderInterface
{

    public function getAuthenticationService(ServerRequestInterface $request): AuthenticationServiceInterface
    {
        /** @var DashedRoute $route */
        $route = $request->getAttribute("route");
        return new UserAuthenticationService(
            Hash::get($route->options, 'authentication')
        );
    }
}
