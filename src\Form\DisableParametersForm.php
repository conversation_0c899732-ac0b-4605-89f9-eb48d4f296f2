<?php
declare(strict_types=1);

namespace App\Form;

use Cake\Form\Schema;
use Cake\Validation\Validator;

/**
 * パラメータを必要としていないフォーム用
 *  DisableFormParametersTrait
 *
 * @package App\Form
 */
class DisableParametersForm extends ApiForm
{
    /**
     * @param Schema $schema
     * @return Schema
     */
    protected function _buildSchema(Schema $schema): Schema
    {
        return $schema;
    }

    /**
     * @inheritDoc
     */
    public function validationDefault(Validator $validator): Validator
    {
        return $validator;
    }

    /**
     * @inheritDoc
     */
    protected function _getAllowFieldsPath(): array
    {
        return [];
    }
}
