openapi: 3.1.0
info:
    title: private-static
    version: "1.0"
    description: ビルド用
servers:
    - url: "https://swb-sbpg-dev.g.kuroco.app"
      description: "API Backend"
paths:
    /rcms-api/4/ec/maker/list:
        get:
            tags:
                - コンテンツ
            summary: メーカー一覧取得
            description: |

                ### **TopicsCategory::list (v1)**


                ## Controller parameters

                > **topics_group_id** `7`

            parameters:
                - name: _output_format
                  schema:
                      type: string
                      format: ""
                  in: query
                  required: false
                  style: form
                  explode: true
                  description: "形式 (json|xml|csv|zip)"
                - name: _lang
                  schema:
                      type: string
                      format: ""
                  in: query
                  required: false
                  style: form
                  explode: true
                  description: 言語
                - name: _charset
                  schema:
                      type: string
                      format: ""
                  in: query
                  required: false
                  style: form
                  explode: true
                  description: 文字コード
                - name: cnt
                  schema:
                      type: integer
                      format: int32
                      minimum: -2147483648
                      maximum: 2147483647
                  in: query
                  required: false
                  style: form
                  explode: true
                  description: 1ページの行数
            responses:
                200:
                    description: "Topics category data successfully fetched"
            security:
                - Token-Auth: []
            operationId: getRcmsApi4EcMakerList
    /rcms-api/4/ec/product/list:
        get:
            tags:
                - EC
            summary: ""
            description: |

                ### **ECProduct::list (v1)**

            parameters:
                - name: _output_format
                  schema:
                      type: string
                      format: ""
                  in: query
                  required: false
                  style: form
                  explode: true
                  description: "形式 (json|xml|csv|zip)"
                - name: _lang
                  schema:
                      type: string
                      format: ""
                  in: query
                  required: false
                  style: form
                  explode: true
                  description: 言語
                - name: _charset
                  schema:
                      type: string
                      format: ""
                  in: query
                  required: false
                  style: form
                  explode: true
                  description: 文字コード
                - name: cnt
                  schema:
                      type: integer
                      format: int32
                      minimum: -2147483648
                      maximum: 2147483647
                  in: query
                  required: false
                  style: form
                  explode: true
                  description: 1ページあたりの商品数
                - name: pageID
                  schema:
                      type: integer
                      format: int32
                      minimum: -2147483648
                      maximum: 2147483647
                  in: query
                  required: false
                  style: form
                  explode: true
                  description: ページID
                - name: filter
                  schema:
                      type: string
                      format: ""
                  in: query
                  required: false
                  style: form
                  explode: true
                  description: フィルタークエリ
                - name: topics_id
                  schema:
                      type: integer
                      format: int32
                      minimum: -2147483648
                      maximum: 2147483647
                  in: query
                  required: false
                  style: form
                  explode: true
                  description: ID
                - name: topics_group_id
                  schema:
                      type: integer
                      format: int32
                      minimum: -2147483648
                      maximum: 2147483647
                  in: query
                  required: false
                  style: form
                  explode: true
                  description: コンテンツ定義ID
                - name: "product_id[]"
                  schema:
                      type: array
                      items:
                          type: integer
                          format: int32
                          minimum: -2147483648
                          maximum: 2147483647
                      minItems: 0
                  in: query
                  required: false
                  style: form
                  explode: true
                  description: 表示する商品ID、設定がない場合は全ての商品が対象となる
                - name: my_order_flg
                  schema:
                      type: integer
                      format: int32
                      minimum: -2147483648
                      maximum: 2147483647
                  in: query
                  required: false
                  style: form
                  explode: true
                  description: ""
                - name: ymd_sort_change
                  schema:
                      type: string
                      format: ""
                  in: query
                  required: false
                  style: form
                  explode: true
                  description: ""
                - name: topics_keyword
                  schema:
                      type: string
                      format: ""
                  in: query
                  required: false
                  style: form
                  explode: true
                  description: キーワード
                - name: topics_keyword_cond
                  schema:
                      type: string
                      format: ""
                  in: query
                  required: false
                  style: form
                  explode: true
                  description: キーワード検索のキーワード毎の絞り込み方を変える（デフォルト：AND)
                - name: ec_ext_options_search
                  schema:
                      type: integer
                      format: int32
                      minimum: -2147483648
                      maximum: 2147483647
                  in: query
                  required: false
                  style: form
                  explode: true
                  description: "商品一覧を拡張項目のオプションで絞り込むか指定します。指定する:1 指定しない:0"
                - name: "search_ec_ext_col[]"
                  schema:
                      type: array
                      items:
                          type: string
                          format: ""
                      minItems: 0
                  in: query
                  required: false
                  style: form
                  explode: true
                  description: ""
                - name: "order[]"
                  schema:
                      type: array
                      items:
                          type: string
                          format: ""
                      minItems: 0
                  in: query
                  required: false
                  style: form
                  explode: true
                  description: |
                      並び順を設定する 。例）topics_id:asc or topics_id:desc
                      可能なパラメータ {0}
            responses:
                200:
                    description: "EC Product List"
                400:
                    description: "Invalid input"
                401:
                    description: "Invalid credentials"
                406:
                    description: "Specified output format is not supported"
                422:
                    description: "The request was well-formed but was unable to be followed due to semantic errors"
            security:
                - Token-Auth: []
            operationId: getRcmsApi4EcProductList
    /rcms-api/4/ec/product/all-list:
        get:
            tags:
                - EC
            summary: ""
            description: |

                ### **ECProduct::list (v1)**


                ## Controller parameters

                > **ignore_product_open_flg** `1`

            parameters:
                - name: _output_format
                  schema:
                      type: string
                      format: ""
                  in: query
                  required: false
                  style: form
                  explode: true
                  description: "形式 (json|xml|csv|zip)"
                - name: _lang
                  schema:
                      type: string
                      format: ""
                  in: query
                  required: false
                  style: form
                  explode: true
                  description: 言語
                - name: _charset
                  schema:
                      type: string
                      format: ""
                  in: query
                  required: false
                  style: form
                  explode: true
                  description: 文字コード
                - name: cnt
                  schema:
                      type: integer
                      format: int32
                      minimum: -2147483648
                      maximum: 2147483647
                  in: query
                  required: false
                  style: form
                  explode: true
                  description: 1ページあたりの商品数
                - name: pageID
                  schema:
                      type: integer
                      format: int32
                      minimum: -2147483648
                      maximum: 2147483647
                  in: query
                  required: false
                  style: form
                  explode: true
                  description: ページID
                - name: filter
                  schema:
                      type: string
                      format: ""
                  in: query
                  required: false
                  style: form
                  explode: true
                  description: フィルタークエリ
                - name: topics_id
                  schema:
                      type: integer
                      format: int32
                      minimum: -2147483648
                      maximum: 2147483647
                  in: query
                  required: false
                  style: form
                  explode: true
                  description: ID
                - name: topics_group_id
                  schema:
                      type: integer
                      format: int32
                      minimum: -2147483648
                      maximum: 2147483647
                  in: query
                  required: false
                  style: form
                  explode: true
                  description: コンテンツ定義ID
                - name: "product_id[]"
                  schema:
                      type: array
                      items:
                          type: integer
                          format: int32
                          minimum: -2147483648
                          maximum: 2147483647
                      minItems: 0
                  in: query
                  required: false
                  style: form
                  explode: true
                  description: 表示する商品ID、設定がない場合は全ての商品が対象となる
                - name: my_order_flg
                  schema:
                      type: integer
                      format: int32
                      minimum: -2147483648
                      maximum: 2147483647
                  in: query
                  required: false
                  style: form
                  explode: true
                  description: ""
                - name: ymd_sort_change
                  schema:
                      type: string
                      format: ""
                  in: query
                  required: false
                  style: form
                  explode: true
                  description: ""
                - name: topics_keyword
                  schema:
                      type: string
                      format: ""
                  in: query
                  required: false
                  style: form
                  explode: true
                  description: キーワード
                - name: topics_keyword_cond
                  schema:
                      type: string
                      format: ""
                  in: query
                  required: false
                  style: form
                  explode: true
                  description: キーワード検索のキーワード毎の絞り込み方を変える（デフォルト：AND)
                - name: ec_ext_options_search
                  schema:
                      type: integer
                      format: int32
                      minimum: -2147483648
                      maximum: 2147483647
                  in: query
                  required: false
                  style: form
                  explode: true
                  description: "商品一覧を拡張項目のオプションで絞り込むか指定します。指定する:1 指定しない:0"
                - name: "search_ec_ext_col[]"
                  schema:
                      type: array
                      items:
                          type: string
                          format: ""
                      minItems: 0
                  in: query
                  required: false
                  style: form
                  explode: true
                  description: ""
                - name: "order[]"
                  schema:
                      type: array
                      items:
                          type: string
                          format: ""
                      minItems: 0
                  in: query
                  required: false
                  style: form
                  explode: true
                  description: |
                      並び順を設定する 。例）topics_id:asc or topics_id:desc
                      可能なパラメータ {0}
            responses:
                200:
                    description: "EC Product List"
                400:
                    description: "Invalid input"
                401:
                    description: "Invalid credentials"
                406:
                    description: "Specified output format is not supported"
                422:
                    description: "The request was well-formed but was unable to be followed due to semantic errors"
            security:
                - Token-Auth: []
            operationId: getRcmsApi4EcProductAllList
components:
    schemas: {}
    securitySchemes:
        Token-Auth:
            type: apiKey
            in: header
            name: X-RCMS-API-ACCESS-TOKEN
