<script setup lang="ts">
import { ref, onMounted, computed } from "vue";
import { useRuntimeConfig, navigateTo, useRoute } from "#app";
import { isLogin, useAuthStore } from "~/src/stores/auth";
import MemberLogins from "~/src/models/MemberLogins";
import ClientLogins from "~/src/models/ClientLogins";
import SwbLogins from "~/src/models/SwbLogins";

const config = useRuntimeConfig();
const authStore = useAuthStore();
const isLoggedIn = computed(() => isLogin());
const isLoading = ref(true);

const isClient = useRoute().path.startsWith("/client/");
const isSwb = useRoute().path.startsWith("/swb/");

const logout = (): void => {
    const loginModel = isSwb
        ? SwbLogins
        : isClient
          ? ClientLogins
          : MemberLogins;

    loginModel
        .create(config)
        .delete()
        .then((success) => {
            if (!success) {
                console.log("Logout error");
            }
        })
        .finally(() => {
            authStore.clearAuth();
            navigateTo(
                isSwb
                    ? "/swb/account/"
                    : isClient
                      ? "/client/account/"
                      : "/member/account/",
            );
        });
};

const login = (): void => {
    navigateTo("/member/account/");
};

onMounted(() => {
    isLoading.value = false;
});
</script>
<template>
    <!-- <v-app-bar flat absolute class="coverme-header">
        <template #prepend>
            <img
                src="~/assets/styles/logo-300x44.png"
                alt="ロゴ"
                class="coverme-header-logo"
            />
        </template>
    </v-app-bar> -->
    <div class="coverme-header bg-white">
        <div class="v-container px-md-0 py-md-4 pa-2">
            <NuxtLink to="https://coverme.jp/" no-rel>
                <img
                    src="~/assets/styles/logo-300x44.png"
                    alt="ロゴ"
                    class="coverme-header-logo"
                />
            </NuxtLink>
            <div v-if="!isLoading">
                <v-btn
                    v-if="isLoggedIn"
                    :color="
                        isSwb
                            ? 'swbPrimary'
                            : isClient
                              ? 'clientPrimary'
                              : 'primary'
                    "
                    size="small"
                    rounded="xl"
                    flat
                    @click="logout"
                >
                    ログアウト
                </v-btn>
                <v-btn
                    v-else-if="!(isClient || isSwb)"
                    color="primary"
                    size="small"
                    rounded="xl"
                    flat
                    @click="login"
                >
                    マイページ
                </v-btn>
            </div>
        </div>
    </div>
</template>
<style scoped>
.coverme-header-logo {
    width: 218px;
}

@media (max-width: 600px) {
    .coverme-header-logo {
        width: 191px;
    }
}

.coverme-header button.v-btn {
    height: 24px;
    width: 84px;
}

@media (max-width: 600px) {
    .coverme-header button.v-btn {
        width: 79px;
    }
}

.coverme-header > div {
    /* padding: 10px 0; */
    width: 100%;

    display: flex;
    justify-content: space-between;

    max-width: 1200px;
}
</style>
