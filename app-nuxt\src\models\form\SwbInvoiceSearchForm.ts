import { SwbOrderSearchForm } from "./SwbOrderSearchForm";
export class SwbInvoiceSearchForm extends SwbOrderSearchForm {
    constructor() {
        super();
        this._startDate = this.getLastMonthFirstDayInJST().substring(0, 7);
        this._endDate = this.getLastMonthFirstDayInJST().substring(0, 7);
        this._makerId = 0;
        this._isChanged = true;
    }

    public isStartDateValid(): boolean | string {
        if (!this._startDate || !this._endDate) return true;
        return (
            new Date(this._startDate) <= new Date(this._endDate) ||
            "開始月が不正です"
        );
    }

    public isEndDateValid(): boolean | string {
        if (!this._startDate || !this._endDate) return true;
        return (
            (new Date(this._endDate) >= new Date(this._startDate) &&
                new Date(this._endDate) <= new Date()) ||
            "終了月が不正です"
        );
    }

    get data(): Record<string, string | number | string[] | number[]> {
        return {
            from: this._startDate,
            to: this._endDate,
            maker_id: this.makerId,
        };
    }
}
