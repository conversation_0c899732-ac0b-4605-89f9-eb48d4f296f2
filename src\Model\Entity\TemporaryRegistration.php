<?php
declare(strict_types=1);

namespace App\Model\Entity;

use Cake\ORM\Entity;
use Cake\I18n\FrozenTime;
use App\Utility\Encryptor;

/**
 * 仮登録エンティティ
 *
 * @property int $id
 * @property string $email
 * @property string $mask_password
 * @property string $mask_email
 * @property string $profile_data
 * @property string $survey_data
 * @property array $product_ids
 * @property string $verification_token
 * @property bool $is_verified
 * @property \Cake\I18n\FrozenTime $expires
 * @property \Cake\I18n\FrozenTime $created
 * @property \Cake\I18n\FrozenTime $modified
 */
class TemporaryRegistration extends Entity
{
    protected $_accessible = [
        'email' => true,
        'mask_password' => true,
        'mask_email' => true,
        'profile_data' => true,
        'survey_data' => true,
        'product_ids' => true,
        'verification_token' => true,
        'is_verified' => true,
        'expires' => true,
        'created' => true,
        'modified' => true,
    ];

    protected $_virtual = ['array_decrypted_profile_data', 'array_decrypted_survey_data', 'password'];

    protected $_hidden = [
        'mask_password',
        'mask_email',
        // 'profile_data',
        // 'survey_data',
        'verification_token',
    ];

    protected function _setProductIds($value)
    {
        return json_decode($value);
    }

    // 暗号化フィールドのセッター
    protected function _setMaskPassword($value)
    {
        return $value ? Encryptor::encrypt($value) : null;
    }

    protected function _setMaskEmail($value)
    {
        return Encryptor::encrypt($value);
    }

    protected function _setProfileData($value)
    {
        return Encryptor::encrypt($value);
    }

    protected function _setSurveyData($value)
    {
        return Encryptor::encrypt($value);
    }

    // 復号化フィールドのゲッター
    protected function _getPassword()
    {
        $value = $this->mask_password;
        return $value ? Encryptor::decrypt($value) : null;
    }

    // protected function _getProfileData($value)
    // {
    //     if (!$value) {
    //         return null;
    //     }
    //     $decrypted = Encryptor::decrypt($value);

    //     // json形式で復号化されたデータを返す
    //     return $decrypted ? $decrypted : null;
    // }

    // protected function _getSurveyData($value)
    // {
    //     if (!$value) {
    //         return null;
    //     }
    //     $decrypted = Encryptor::decrypt($value);

    //     // json形式で復号化されたデータを返す
    //     return $decrypted ? $decrypted : null;
    // }

    protected function _getArrayDecryptedProfileData()
    {
        // プロファイルデータが空の場合は空の配列を返す
        if (!$this->profile_data) {
            return [];
        }
        
        // 復号化
        $decrypted = Encryptor::decrypt($this->profile_data);
        
        // 復号化されたデータが文字列の場合、JSONデコードを試みる
        if (is_string($decrypted)) {
            $decoded = json_decode($decrypted, true);
            // JSONデコードに失敗した場合は空の配列を返す
            return $decoded !== null ? $decoded : [];
        }
        
        // 復号化されたデータが既に配列の場合はそのまま返す
        if (is_array($decrypted)) {
            return $decrypted;
        }
        
        // それ以外の場合は空の配列を返す
        return [];
    }

    protected function _getArrayDecryptedSurveyData()
    {
        // プロファイルデータが空の場合は空の配列を返す
        if (!$this->survey_data) {
            return [];
        }
        
        // 復号化
        $decrypted = Encryptor::decrypt($this->survey_data);
        
        // 復号化されたデータが文字列の場合、JSONデコードを試みる
        if (is_string($decrypted)) {
            $decoded = json_decode($decrypted, true);
            // JSONデコードに失敗した場合は空の配列を返す
            return $decoded !== null ? $decoded : [];
        }
        
        // 復号化されたデータが既に配列の場合はそのまま返す
        if (is_array($decrypted)) {
            return $decrypted;
        }
        
        // それ以外の場合は空の配列を返す
        return [];
    }

    /**
     * 仮登録が有効かどうかを判定
     */
    public function isValid(): bool
    {
        return !$this->is_verified && $this->expires > FrozenTime::now();
    }

    /**
     * 仮登録が期限切れかどうかを判定
     */
    public function isExpired(): bool
    {
        return $this->expires <= FrozenTime::now();
    }

    /**
     * 認証済みかどうかを判定
     */
    public function isVerified(): bool
    {
        return $this->is_verified;
    }

    /**
     * 認証完了処理
     */
    public function markAsVerified(): void
    {
        $this->is_verified = true;
    }
}

