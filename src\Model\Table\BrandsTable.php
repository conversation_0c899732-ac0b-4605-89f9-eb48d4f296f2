<?php
declare(strict_types=1);

namespace App\Model\Table;

use Cake\ORM\Query;
use Cake\ORM\RulesChecker;
use Cake\ORM\Table;
use Cake\Validation\Validator;

/**
 * ブランドテーブル
 */
class BrandsTable extends Table
{
    public function initialize(array $config): void
    {
        parent::initialize($config);

        $this->setTable('brands');
        $this->setDisplayField('name');
        $this->setPrimaryKey('id');

        // アソシエーション
        $this->belongsTo('Makers', [
            'foreignKey' => 'maker_id',
            'joinType' => 'INNER',
        ]);
        
        $this->hasMany('Products', [
            'foreignKey' => 'brand_id',
            'dependent' => true,
        ]);
        
        $this->hasMany('RandselOrders', [
            'foreignKey' => 'brand_id',
        ]);

        // 論理削除ビヘイビア
        $this->addBehavior('Muffin/Trash.Trash', [
            'field' => 'deleted',
            'events' => ['Model.beforeFind', 'Model.beforeDelete']
        ]);
    }

    public function validationDefault(Validator $validator): Validator
    {
        $validator
            ->integer('maker_id')
            ->requirePresence('maker_id', 'create')
            ->notEmptyString('maker_id');

        $validator
            ->scalar('name')
            ->maxLength('name', 255)
            ->requirePresence('name', 'create', __('ブランド名は必須です'))
            ->notEmptyString('name', __('ブランド名は必須です'));

        $validator
            ->scalar('description')
            ->allowEmptyString('description');

        $validator
            ->scalar('logo_url')
            ->maxLength('logo_url', 500)
            ->allowEmptyString('logo_url')
            ->url('logo_url', __('有効なURLを入力してください'));

        $validator
            ->scalar('brand_image_url')
            ->maxLength('brand_image_url', 500)
            ->allowEmptyString('brand_image_url')
            ->url('brand_image_url', __('有効なURLを入力してください'));

        $validator
            ->scalar('brand_features_html')
            ->allowEmptyString('brand_features_html');

        $validator
            ->scalar('other_features_html')
            ->allowEmptyString('other_features_html');

        $validator
            ->integer('established_year')
            ->allowEmptyString('established_year')
            ->range('established_year', [1800, date('Y') + 10], __('設立年は1800年から' . (date('Y') + 10) . '年の間で入力してください'));

        $validator
            ->integer('target_age_min')
            ->allowEmptyString('target_age_min')
            ->range('target_age_min', [0, 18], __('対象年齢（最小）は0〜18歳の間で入力してください'));

        $validator
            ->integer('target_age_max')
            ->allowEmptyString('target_age_max')
            ->range('target_age_max', [0, 18], __('対象年齢（最大）は0〜18歳の間で入力してください'));

        $validator
            ->integer('target_gender')
            ->allowEmptyString('target_gender')
            ->range('target_gender', [1, 3], __('対象性別は1〜3の値を入力してください'));

        $validator
            ->integer('price_range_min')
            ->allowEmptyString('price_range_min')
            ->greaterThanOrEqual('price_range_min', 0, __('価格帯（最小）は0以上で入力してください'));

        $validator
            ->integer('price_range_max')
            ->allowEmptyString('price_range_max')
            ->greaterThanOrEqual('price_range_max', 0, __('価格帯（最大）は0以上で入力してください'));

        $validator
            ->scalar('feature_tags')
            ->allowEmptyString('feature_tags');

        $validator
            ->scalar('website_url')
            ->maxLength('website_url', 500)
            ->allowEmptyString('website_url')
            ->url('website_url', __('有効なURLを入力してください'));

        $validator
            ->boolean('is_premium')
            ->notEmptyString('is_premium');

        return $validator;
    }

    public function buildRules(RulesChecker $rules): RulesChecker
    {
        $rules->add($rules->existsIn(['maker_id'], 'Makers'), __('指定されたメーカーが存在しません'));
        
        // 同一メーカー内でのブランド名重複チェック
        $rules->add(function ($entity, $options) {
            $conditions = [
                'maker_id' => $entity->maker_id,
                'name' => $entity->name
            ];
            if (!$entity->isNew()) {
                $conditions['id !='] = $entity->id;
            }
            $existing = $this->find()->where($conditions)->first();
            return $existing === null;
        }, 'uniqueNamePerMaker', [
            'errorField' => 'name',
            'message' => __('このメーカーには既に同じ名前のブランドが存在します')
        ]);

        return $rules;
    }

    /**
     * アクティブなブランドを取得するファインダー
     */
    public function findActive(Query $query, array $options): Query
    {
        return $query->where(['deleted IS' => null]);
    }

    /**
     * メーカー別にブランドを取得するファインダー
     */
    public function findByMaker(Query $query, array $options): Query
    {
        $makerId = $options['maker_id'] ?? null;
        if ($makerId !== null) {
            $query->where(['maker_id' => $makerId]);
        }
        return $query;
    }

    /**
     * プレミアムブランドを取得するファインダー
     */
    public function findPremium(Query $query, array $options): Query
    {
        return $query->where(['is_premium' => true]);
    }

    /**
     * 対象性別でブランドを取得するファインダー
     */
    public function findByTargetGender(Query $query, array $options): Query
    {
        $targetGender = $options['target_gender'] ?? null;
        if ($targetGender !== null) {
            $query->where(['target_gender' => $targetGender]);
        }
        return $query;
    }

    /**
     * 指定メーカーのアクティブなブランド一覧を取得
     */
    public function getActiveBrandsByMaker(int $makerId): Query
    {
        return $this->find('active')
            ->find('sorted')
            ->where(['maker_id' => $makerId]);
    }
}
