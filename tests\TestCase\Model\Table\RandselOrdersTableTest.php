<?php
declare(strict_types=1);

namespace App\Test\TestCase\Model\Table;

use App\Model\Table\RandselOrdersTable;
use Cake\TestSuite\TestCase;

/**
 * App\Model\Table\RandselOrdersTable Test Case
 */
class RandselOrdersTableTest extends TestCase
{
    /**
     * Test subject
     *
     * @var RandselOrdersTable
     */
    protected $RandselOrders;

    /**
     * Fixtures
     *
     * @var array<string>
     */
    protected $fixtures = [
        'app.RandselOrders',
    ];

    /**
     * setUp method
     *
     * @return void
     */
    protected function setUp(): void
    {
        parent::setUp();
        $config = $this->getTableLocator()->exists('RandselOrders') ? [] : ['className' => RandselOrdersTable::class];
        $this->RandselOrders = $this->getTableLocator()->get('RandselOrders', $config);
    }

    /**
     * tearDown method
     *
     * @return void
     */
    protected function tearDown(): void
    {
        unset($this->RandselOrders);

        parent::tearDown();
    }

    /**
     * Test validationDefault method
     *
     * @return void
     * @uses RandselOrdersTable::validationDefault
     */
    public function testValidationDefault(): void
    {
        $this->markTestIncomplete('Not implemented yet.');
    }
}
