<?php
declare(strict_types=1);

namespace App\Command;

use Cake\Command\Command;
use Cake\Console\Arguments;
use Cake\Console\ConsoleIo;
use Cake\Console\ConsoleOptionParser;
use Cake\ORM\TableRegistry;
use App\Model\Table\TemporaryRegistrationsTable;
use App\Model\Table\UserTokensTable;
use Cake\I18n\FrozenTime;
// use App\Model\Table\SwbUserTokensTable;
// use App\Model\Table\MakerUserTokensTable;

/**
 * 期限切れデータクリーンアップコマンド
 * 
 * 仮登録データやトークンの期限切れデータを定期的にクリーンアップする
 */
class CleanupExpiredDataCommand extends Command
{
    /**
     * コマンドの説明を設定
     */
    public function buildOptionParser(ConsoleOptionParser $parser): ConsoleOptionParser
    {
        $parser
            ->setDescription('期限切れの仮登録データとトークンをクリーンアップします')
            ->addOption('dry-run', [
                'help' => 'ドライランモード（実際の削除は行わない、削除件数は出力する）',
                'boolean' => true,
                'default' => false,
            ])
            ->addOption('verbose', [
                'help' => '詳細な実行ログを出力',
                'boolean' => true,
                'default' => false,
            ]);

        return $parser;
    }

    /**
     * コマンド実行
     */
    public function execute(Arguments $args, ConsoleIo $io): ?int
    {
        $dryRun = $args->getOption('dry-run');
        $verbose = $args->getOption('verbose');

        $io->out('期限切れデータクリーンアップを開始します...');
        
        if ($dryRun) {
            $io->warning('ドライランモードで実行します（実際の削除は行いません）');
        }

        $totalCleaned = 0;

        // 仮登録データのクリーンアップ
        $totalCleaned += $this->cleanupTemporaryRegistrations($io, $dryRun, $verbose);

        // ユーザートークンのクリーンアップ
        $totalCleaned += $this->cleanupUserTokens($io, $dryRun, $verbose);

        // SWB管理者トークンのクリーンアップ
        $totalCleaned += $this->cleanupSwbUserTokens($io, $dryRun, $verbose);

        // メーカーユーザートークンのクリーンアップ
        $totalCleaned += $this->cleanupMakerUserTokens($io, $dryRun, $verbose);

        $io->success("クリーンアップが完了しました。合計 {$totalCleaned} 件のデータを処理しました。");

        return static::CODE_SUCCESS;
    }

    /**
     * 仮登録データのクリーンアップ
     */
    private function cleanupTemporaryRegistrations(ConsoleIo $io, bool $dryRun, bool $verbose): int
    {
        /** @var TemporaryRegistrationsTable $table */
        $table = TableRegistry::getTableLocator()->get('TemporaryRegistrations');

        if ($verbose) {
            $io->out('仮登録データをクリーンアップしています...');
        }

        if ($dryRun) {
            // ドライランモード：削除対象をカウントのみ
            $expiredCount = $table->find()
                ->where(['expires <' => new FrozenTime(), 'email IS NOT' => null])
                ->count();
            
            $io->out("仮登録データ: {$expiredCount} 件が削除対象です");
            return $expiredCount;
        } else {
            // 実際のクリーンアップ実行
            $cleanedCount = $table->cleanupExpiredRegistrations();
            
            if ($verbose) {
                $io->out("仮登録データ: {$cleanedCount} 件を削除しました");
            }
            
            return $cleanedCount;
        }
    }

    /**
     * ユーザートークンのクリーンアップ
     */
    private function cleanupUserTokens(ConsoleIo $io, bool $dryRun, bool $verbose): int
    {
        /** @var UserTokensTable $table */
        $table = TableRegistry::getTableLocator()->get('UserTokens');

        if ($verbose) {
            $io->out('ユーザートークンをクリーンアップしています...');
        }

        if ($dryRun) {
            $expiredCount = $table->find()
                ->where(['expires <' => new FrozenTime()])
                ->count();
            
            $io->out("ユーザートークン: {$expiredCount} 件が削除対象です");
            return $expiredCount;
        } else {
            $cleanedCount = $table->cleanupExpiredTokens();
            
            if ($verbose) {
                $io->out("ユーザートークン: {$cleanedCount} 件を削除しました");
            }
            
            return $cleanedCount;
        }
    }

    /**
     * SWB管理者トークンのクリーンアップ
     */
    private function cleanupSwbUserTokens(ConsoleIo $io, bool $dryRun, bool $verbose): int
    {
        /** @var SwbUserTokensTable $table */
        $table = TableRegistry::getTableLocator()->get('SwbUserTokens');

        if ($verbose) {
            $io->out('SWB管理者トークンをクリーンアップしています...');
        }

        if ($dryRun) {
            $expiredCount = $table->find()
                ->where(['expires <' => new FrozenTime()])
                ->count();
            
            $io->out("SWB管理者トークン: {$expiredCount} 件が削除対象です");
            return $expiredCount;
        } else {
            $cleanedCount = $table->cleanupExpiredTokens();
            
            if ($verbose) {
                $io->out("SWB管理者トークン: {$cleanedCount} 件を削除しました");
            }
            
            return $cleanedCount;
        }
    }

    /**
     * メーカーユーザートークンのクリーンアップ
     */
    private function cleanupMakerUserTokens(ConsoleIo $io, bool $dryRun, bool $verbose): int
    {
        /** @var MakerUserTokensTable $table */
        $table = TableRegistry::getTableLocator()->get('MakerUserTokens');

        if ($verbose) {
            $io->out('メーカーユーザートークンをクリーンアップしています...');
        }

        if ($dryRun) {
            $expiredCount = $table->find()
                ->where(['expires <' => new FrozenTime()])
                ->count();
            
            $io->out("メーカーユーザートークン: {$expiredCount} 件が削除対象です");
            return $expiredCount;
        } else {
            $cleanedCount = $table->cleanupExpiredTokens();
            
            if ($verbose) {
                $io->out("メーカーユーザートークン: {$cleanedCount} 件を削除しました");
            }
            
            return $cleanedCount;
        }
    }
}
