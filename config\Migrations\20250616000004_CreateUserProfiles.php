<?php
declare(strict_types=1);

use Migrations\AbstractMigration;

class CreateUserProfiles extends AbstractMigration
{
    /**
     * Up Method.
     *
     * More information on this method is available here:
     * https://book.cakephp.org/phinx/0/en/migrations.html#the-up-method
     * @return void
     */
    public function up(): void
    {
        $this->table('user_profiles')
            ->addColumn('general_user_id', 'integer', [
                'comment' => '一般ユーザーID',
                'default' => null,
                'limit' => null,
                'null' => false,
            ])
            ->addColumn('last_name', 'text', [
                'comment' => '姓（暗号化）',
                'default' => null,
                'limit' => null,
                'null' => false,
            ])
            ->addColumn('first_name', 'text', [
                'comment' => '名（暗号化）',
                'default' => null,
                'limit' => null,
                'null' => false,
            ])
            ->addColumn('last_name_kana', 'text', [
                'comment' => '姓カナ（暗号化）',
                'default' => null,
                'limit' => null,
                'null' => false,
            ])
            ->addColumn('first_name_kana', 'text', [
                'comment' => '名カナ（暗号化）',
                'default' => null,
                'limit' => null,
                'null' => false,
            ])
            ->addColumn('zip_code', 'text', [
                'comment' => '郵便番号（暗号化）',
                'default' => null,
                'limit' => null,
                'null' => false,
            ])
            ->addColumn('prefecture_code', 'text', [
                'comment' => '都道府県コード（暗号化）',
                'default' => null,
                'limit' => null,
                'null' => false,
            ])
            ->addColumn('address1', 'text', [
                'comment' => '住所1（暗号化）',
                'default' => null,
                'limit' => null,
                'null' => false,
            ])
            ->addColumn('address2', 'text', [
                'comment' => '住所2（暗号化）',
                'default' => null,
                'limit' => null,
                'null' => false,
            ])
            ->addColumn('address3', 'text', [
                'comment' => '住所3（暗号化）',
                'default' => null,
                'limit' => null,
                'null' => true,
            ])
            ->addColumn('tel', 'text', [
                'comment' => '電話番号（暗号化）',
                'default' => null,
                'limit' => null,
                'null' => false,
            ])
            ->addColumn('email_send_ng_flg', 'boolean', [
                'comment' => 'メール送信拒否フラグ（0: 送信可, 1: 送信拒否）',
                'default' => false,
                'limit' => null,
                'null' => false,
            ])
            ->addColumn('notes', 'text', [
                'comment' => '備考（暗号化）',
                'default' => null,
                'limit' => null,
                'null' => true,
            ])
            ->addColumn('created', 'datetime', [
                'comment' => '作成日時',
                'default' => 'CURRENT_TIMESTAMP',
                'limit' => null,
                'null' => false,
            ])
            ->addColumn('modified', 'datetime', [
                'comment' => '更新日時',
                'default' => 'CURRENT_TIMESTAMP',
                'limit' => null,
                'null' => false,
                'update' => 'CURRENT_TIMESTAMP',
            ])
            ->addIndex(
                [
                    'general_user_id',
                ],
                [
                    'name' => 'uk_user_profiles_general_user_id',
                    'unique' => true,
                ]
            )
            ->addIndex(
                [
                    'created',
                ],
                [
                    'name' => 'idx_user_profiles_created',
                ]
            )
            ->create();
    }

    /**
     * Down Method.
     *
     * More information on this method is available here:
     * https://book.cakephp.org/phinx/0/en/migrations.html#the-down-method
     * @return void
     */
    public function down(): void
    {
        $this->table('user_profiles')->drop()->save();
    }
}
