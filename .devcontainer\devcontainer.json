{"name": "CoverMe Development Environment", "dockerComposeFile": "../docker-compose.yml", "service": "amazonlinux2", "workspaceFolder": "/opt/coverme", "customizations": {"vscode": {"extensions": ["xdebug.php-debug", "ms-azuretools.vscode-docker", "bmewburn.vscode-intelephense-client", "mikestead.dotenv", "editorconfig.editorconfig"], "settings": {"terminal.integrated.defaultProfile.linux": "bash"}}}, "forwardPorts": [80, 3996, 8025, 1025, 8997, 9003], "remoteUser": "ec2-user", "postCreateCommand": "echo 'Dev container is ready!'"}