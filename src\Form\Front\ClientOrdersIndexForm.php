<?php

namespace App\Form\Front;

use App\Enums\EFormFieldType;
use App\Enums\EntityFields\EClientOrderForm;
use App\Form\ApiForm;
use App\Validation\Validator\ClientOrderValidator;
use Cake\Form\Schema;
use Cake\Validation\Validator;

class ClientOrdersIndexForm extends ApiForm
{

    public function _buildSchema(Schema $schema): Schema
    {
        $schema->addField(EClientOrderForm::FROM->value, EFormFieldType::STRING->value);
        $schema->addField(EClientOrderForm::TO->value, EFormFieldType::STRING->value);
        $schema->addField(EClientOrderForm::PRODUCT_ID->value, EFormFieldType::STRING->value);
        return $schema;
    }

    public function validationDefault(Validator $validator): Validator
    {
        return ClientOrderValidator::buildValidator($validator, [
            EClientOrderForm::FROM->value,
            EClientOrderForm::TO->value,
            EClientOrderForm::PRODUCT_ID->value,
        ]);
    }

    protected function _getAllowFieldsPath(): array
    {
        return [
            EClientOrderForm::FROM->value,
            EClientOrderForm::TO->value,
            EClientOrderForm::PRODUCT_ID->value,
        ];
    }
}
