<?php

namespace App\Form\Front;

use App\Enums\EFormFieldType;
use App\Enums\EntityFields\ELogin;
use App\Form\ApiForm;
use App\Validation\Validator\UserPasswordReminderFormValidator;
use Cake\Form\Schema;
use Cake\Validation\Validator;

class UserPasswordRemindersAddForm extends ApiForm
{

    public function _buildSchema(Schema $schema): Schema
    {
        $schema->addField(ELogin::LOGIN_EMAIL->value, EFormFieldType::STRING->value);
        return $schema;
    }

    public function validationDefault(Validator $validator): Validator
    {
        return UserPasswordReminderFormValidator::buildValidator($validator, [
            ELogin::LOGIN_EMAIL->value,
        ]);
    }

    protected function _getAllowFieldsPath(): array
    {
        return [];
    }
}
