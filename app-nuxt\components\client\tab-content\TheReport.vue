<script setup lang="ts">
import { ClientMyBridge } from "~~/src/models/bridge/ClientMyBridge";
import TheClientReportSearchForm from "~/components/client/form/TheClientReportSearchForm.vue";
import TheReportSummary from "~/components/client/tab-content/report/TheReportSummary.vue";
import TheLoading from "~/components/parts/TheLoading.vue";
import TheMonthlyReport from "~/components/client/tab-content/report/TheMonthlyReport.vue";

defineProps<{
    bridge: ClientMyBridge;
}>();
</script>

<template>
    <div>
        <the-client-report-search-form :bridge="bridge" />
        <div v-if="bridge.is_report_loaded">
            <the-report-summary :bridge="bridge" />
            <the-monthly-report :bridge="bridge" />
        </div>
        <v-row v-else justify="center" class="mt-10">
            <the-loading color="clientPrimary">
                集計しています、しばらくお待ちください
            </the-loading>
        </v-row>
    </div>
</template>

<style scoped></style>
