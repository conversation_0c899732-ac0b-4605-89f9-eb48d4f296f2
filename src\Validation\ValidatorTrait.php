<?php
declare(strict_types=1);

namespace App\Validation;

use App\Enums\EValidationErrorMessage;
use BadMethodCallException;
use Cake\ORM\Table;
use Cake\Utility\Inflector;
use Cake\Validation\Validator;

/**
 * Validator共通処理
 * Trait ValidatorTrait
 * @package App\Validation
 * @link http://ivystar.jp/programming/php/cakephp-php-programming/validation-rule-list-of-cakephp-3/
 */
trait ValidatorTrait
{

    /**
     * バリデーション定義処理
     * @param Validator $validator
     * @param array $fields $this->getSchema()->fields()
     * @return Validator
     */
    static public function buildValidator(Validator $validator, array $fields = []): Validator
    {
        foreach ($fields as $field => $options) {
            if (!is_array($options)) {
                $field = $options;
                $options = [];
            }
            $method = Inflector::variable($field);

            if (!method_exists(static::class, $method)) {
                throw new BadMethodCallException('not found validator method : ' . static::class . ':' . $method);
            }
            $validator = static::{$method}($validator, $options);
        }
        return $validator;
    }


}
