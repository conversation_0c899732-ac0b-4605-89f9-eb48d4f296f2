<?php

namespace App\Test\TestCase\Controller\Front;

use App\Test\TestCase\Controller\ApiTestCase;
use Cake\I18n\FrozenTime;
use Cake\Utility\Hash;

class ClientChangePasswordsControllerTest extends ApiTestCase
{

    /**
     * Test edit method
     *
     * @return void
     * @uses \App\Controller\Front\UserChangePasswordsController::edit()
     * php ./vendor/phpunit/phpunit/phpunit --filter "testEdit" ./tests/TestCase/Controller/Front/ClientChangePasswordsControllerTest.php
     */
    public function testEdit(): void
    {
        $now = FrozenTime::now();
        $this->setFrontAuthorized(true);
        $this->put('/front/client-change-passwords/' . $now->format("YmdHis") . '.json', [
            "login_id" => "<EMAIL>",
            "current_password" => "1234qwer",
            "new_password" => "qwer1234",
        ]);
        $response = $this->bodyToArray($this->_response->getBody());
        debug($response);
        $this->assertResponseOk(print_r(Hash::flatten($response), true));
    }
}
