<?php
declare(strict_types=1);

use Migrations\AbstractMigration;

class CreateSwbUserTokens extends AbstractMigration
{
    /**
     * Up Method.
     *
     * More information on this method is available here:
     * https://book.cakephp.org/phinx/0/en/migrations.html#the-up-method
     * @return void
     */
    public function up(): void
    {
        $this->table('swb_user_tokens')
            ->addColumn('swb_user_id', 'integer', [
                'comment' => '管理者ユーザーID',
                'default' => null,
                'limit' => null,
                'null' => false,
            ])
            ->addColumn('token', 'string', [
                'comment' => 'トークン文字列',
                'default' => null,
                'limit' => 255,
                'null' => false,
            ])
            ->addColumn('type', 'string', [
                'comment' => 'トークン種別（password_reset, api_access等）',
                'default' => null,
                'limit' => 50,
                'null' => false,
            ])
            ->addColumn('expires', 'datetime', [
                'comment' => '有効期限',
                'default' => null,
                'limit' => null,
                'null' => false,
            ])
            ->addColumn('created', 'datetime', [
                'comment' => '作成日時',
                'default' => 'CURRENT_TIMESTAMP',
                'limit' => null,
                'null' => false,
            ])
            ->addColumn('modified', 'datetime', [
                'comment' => '更新日時',
                'default' => 'CURRENT_TIMESTAMP',
                'limit' => null,
                'null' => false,
                'update' => 'CURRENT_TIMESTAMP',
            ])
            ->addIndex(
                [
                    'swb_user_id',
                ],
                [
                    'name' => 'idx_swb_user_tokens_swb_user_id',
                ]
            )
            ->addIndex(
                [
                    'token',
                ],
                [
                    'name' => 'uk_swb_user_tokens_token',
                    'unique' => true,
                ]
            )
            ->addIndex(
                [
                    'type',
                ],
                [
                    'name' => 'idx_swb_user_tokens_type',
                ]
            )
            ->addIndex(
                [
                    'expires',
                ],
                [
                    'name' => 'idx_swb_user_tokens_expires',
                ]
            )
            ->addIndex(
                [
                    'created',
                ],
                [
                    'name' => 'idx_swb_user_tokens_created',
                ]
            )
            ->create();
    }

    /**
     * Down Method.
     *
     * More information on this method is available here:
     * https://book.cakephp.org/phinx/0/en/migrations.html#the-down-method
     * @return void
     */
    public function down(): void
    {
        $this->table('swb_user_tokens')->drop()->save();
    }
}
