<?php

namespace <PERSON>roko\Entity;

use App\Kuroko\Entity\ECOrder;
use Cake\I18n\FrozenTime;
use Cake\TestSuite\TestCase;

class ECOrderTest extends TestCase
{
    // php ./vendor/phpunit/phpunit/phpunit --filter "testGetMemberEmail" ./tests/TestCase/Kuroko/Entity/ECOrderTest.php
    public function testGetMemberEmail()
    {
        $data = [
            "member_data" => [
                "email" => "<EMAIL>",
                "name1" => "テストせい2"
            ],
            "product_info" => [
                "product_name" => "天使の羽",
                "product_id" => 41201
            ]
        ];
        $eCOrder = new ECOrder($data);

        $email = $eCOrder->getMemberEmail();
        $this->assertEquals('<EMAIL>', $email);
    }

    // php ./vendor/phpunit/phpunit/phpunit --filter "testGetMemberName1" ./tests/TestCase/Kuroko/Entity/ECOrderTest.php
    public function testGetMemberName1()
    {
        $data = [
            "member_data" => [
                "email" => "<EMAIL>",
                "name1" => "テストせい2"
            ],
            "product_info" => [
                "product_name" => "天使の羽",
                "product_id" => 41201
            ]
        ];
        $eCOrder = new ECOrder($data);

        $name1 = $eCOrder->getMemberName1();
        $this->assertEquals('テストせい2', $name1);
    }

    // php ./vendor/phpunit/phpunit/phpunit --filter "testGetProductName" ./tests/TestCase/Kuroko/Entity/ECOrderTest.php
    public function testGetProductName()
    {
        $data = [
            "member_data" => [
                "email" => "<EMAIL>",
                "name1" => "テストせい2"
            ],
            "product_info" => [
                "product_name" => "天使の羽",
                "product_id" => 41201
            ]
        ];
        $eCOrder = new ECOrder($data);

        $productName = $eCOrder->getProductName();
        $this->assertEquals('天使の羽', $productName);
    }
}
