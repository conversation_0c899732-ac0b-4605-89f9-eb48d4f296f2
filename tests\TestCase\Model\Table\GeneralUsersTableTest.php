<?php
declare(strict_types=1);

namespace App\Test\TestCase\Model\Table;

use App\Model\Table\GeneralUsersTable;
use App\Test\TestCase\AppTestCase;

/**
 * App\Model\Table\GeneralUsersTable Test Case
 */
class GeneralUsersTableTest extends AppTestCase
{
    /**
     * Test fixtures
     */
    protected $fixtures = [
        'app.GeneralUsers',
    ];

    /**
     * Test subject
     */
    protected $GeneralUsers;

    /**
     * setUp method
     */
    public function setUp(): void
    {
        parent::setUp();
        // テスト用の簡単なテーブルクラスを作成
        $config = $this->getTableLocator()->exists('GeneralUsers') ? [] : ['className' => GeneralUsersTable::class];
        $this->GeneralUsers = $this->getTableLocator()->get('GeneralUsers', $config);
    }

    /**
     * tearDown method
     */
    public function tearDown(): void
    {
        unset($this->GeneralUsers);

        parent::tearDown();
    }

    /**
     * Test basic table functionality
     */
    public function testBasicTableFunctionality(): void
    {
        // フィクスチャからユーザーを検索
        $user = $this->GeneralUsers->find()
            ->where(['email' => '<EMAIL>'])
            ->first();

        $this->assertNotNull($user);
        $this->assertEquals('<EMAIL>', $user->email);
        $this->assertNull($user->deleted);

        // 存在しないメールアドレス
        $user = $this->GeneralUsers->find()
            ->where(['email' => '<EMAIL>'])
            ->first();
        $this->assertNull($user);
    }

    /**
     * Test password field
     */
    public function testPasswordField(): void
    {
        // 通常ユーザー（パスワードあり）
        $normalUser = $this->GeneralUsers->find()
            ->where(['email' => '<EMAIL>'])
            ->first();
        $this->assertNotNull($normalUser->password);

        // Kurocoユーザー（パスワードなし）
        $kurocoUser = $this->GeneralUsers->find()
            ->where(['email' => '<EMAIL>'])
            ->first();
        $this->assertNull($kurocoUser->password);
    }

    /**
     * Test entity creation
     */
    public function testEntityCreation(): void
    {
        $user = $this->GeneralUsers->newEntity([
            'email' => '<EMAIL>',
            'password' => 'plainpassword',
        ]);

        $this->assertEquals('<EMAIL>', $user->email);
        // パスワードはハッシュ化されているため、プレーンテキストとは一致しない
        $this->assertNotEquals('plainpassword', $user->password);
        // パスワードがハッシュ化されていることを確認
        $this->assertNotEmpty($user->password);
        // ハッシュ化されたパスワードの長さを確認（bcryptは通常60文字）
        $this->assertGreaterThanOrEqual(60, strlen($user->password));
        // bcryptハッシュのプレフィックスを確認
        $this->assertStringStartsWith('$2y$', $user->password);
        $this->assertNull($user->deleted);
    }

    public function testSoftDelete(): void
    {
        $user = $this->GeneralUsers->get(1);
        $this->GeneralUsers->delete($user);
        
        // 通常の検索では見つからない
        $this->assertNull($this->GeneralUsers->find()->where(['id' => 1])->first());
        
        // withTrashedで検索すると見つかる
        $trashedUser = $this->GeneralUsers->find('withTrashed')->where(['id' => 1])->first();
        $this->assertNotNull($trashedUser);
        $this->assertNotNull($trashedUser->deleted);   
    }
}






