<?php

declare(strict_types=1);

namespace App\Command;

use Cake\Command\Command;
use Cake\Console\Arguments;
use <PERSON>ake\Console\ConsoleIo;
use Cake\Console\ConsoleOptionParser;
use Cake\Filesystem\File;
use Cake\Utility\Hash;
use App\Mailer\AppMailer;
use App\Mailer\Sender\ToUser\NewsletterSender;
use Exception;

/**
 * SendNewsletterMail command.
 */
class SendNewsletterMailCommand extends Command
{
    /**
     * Hook method for defining this command's option parser.
     *
     * @see https://book.cakephp.org/4/en/console-commands/commands.html#defining-arguments-and-options
     * @param \Cake\Console\ConsoleOptionParser $parser The parser to be defined
     * @return \Cake\Console\ConsoleOptionParser The built parser.
     */
    public function buildOptionParser(ConsoleOptionParser $parser): ConsoleOptionParser
    {
        $parser = parent::buildOptionParser($parser);

        $parser->setDescription([
            'メルマガを送信します。',
            // 'Kurocoの注文情報について、未削除のユーザーの注文情報のみを取得します。',
            // 'オプションを指定しない場合は、すべての注文情報を取得します。',
            // '他のオプションを指定する場合は、オプションヘルプを参照してください。',
        ]);

        $parser->addOptions([
            'email_list' => ['help' => '必須オプション：メールアドレスリストのファイル名（ファイルディレクトリはcoverme/tmp/） 例：--email_list=list.txt', 'default' => null, 'required' => true,],
            // 'custom_member_filter_id' => ['help' => 'Kuroco カスタムメンバーフィルターID', 'default' => null],
            // 'member_id' => ['help' => 'kuroco メンバーID（複数指定可） 例：--member_id=1 --member_id=2', 'default' => null, 'multiple' => true],

            'subject' => ['help' => '必須オプション：メールタイトル', 'default' => null, 'required' => true,],
            'body' => ['help' => '必須オプション：メール本文のファイル名（ファイルディレクトリはcoverme/tmp/） html形式対応 例：--body=body.html', 'default' => null, 'required' => true,],

            'batch_size' => ['help' => '一回のメール送信件数', 'default' => 10,],
            'interval' => ['help' => '一回のメールの送信間隔（秒）', 'default' => 1,],

        ]);
        return $parser;
    }

    /**
     * Implement this method with your command's logic.
     *
     * @param \Cake\Console\Arguments $args The command arguments.
     * @param \Cake\Console\ConsoleIo $io The console io
     * @return null|void|int The exit code or null for success
     */
    public function execute(Arguments $args, ConsoleIo $io)
    {
        $options = $args->getOptions();
        $io->out("options: " . json_encode($args->getOptions()));

        $sendfailedEmails = [];

        $emailListFile = Hash::get($options, 'email_list');
        $customMemberFilterId = Hash::get($options, 'custom_member_filter_id');
        $memberId = Hash::get($options, 'member_id');

        if (is_null($emailListFile) && is_null($customMemberFilterId) && is_null($memberId)) {
            $io->error('メールアドレスリストファイルパスを指定してください。');
            $this->abort();
        }

        // メールアドレスリストの読み込み
        if ($emailListFile) {
            $file = new File($emailListFile);
            $emails = $file->read();
            $emailList = explode("\n", trim($emails));
            $emailList = Hash::map($emailList, '{n}', function ($item) {
                return trim($item, '"');
            });
        } else {
            $emailList = []; // 他のオプションが指定されている場合は空のリスト
        }

        if ($bodyFile = Hash::get($options, 'body')) {
            $file = new File($bodyFile);
            $body = $file->read();
        }

        // メール送信のロジック
        foreach (array_chunk($emailList, intval(Hash::get($options, 'batch_size'))) as $chunk) {
            foreach ($chunk as $email) {
                try {
                    $email = trim($email);

                    if (mb_strlen($email) == 0) {
                        continue;
                    }
                    $sender = new NewsletterSender($email, "", Hash::get($options, 'subject', ""));

                    if ($bodyFile) {
                        $sender->setViewVars(['body' => $body]);
                    }
                    AppMailer::sendToUser($sender);
                    $io->success("メールを送信しました: " . $email);
                } catch (Exception $e) {
                    $sendfailedEmails[] = $email;
                    $io->error("メール送信に失敗しました: " . $email . " " . $e->getMessage());
                    continue;
                }
            }
            sleep(intval(Hash::get($options, 'interval'))); // 指定した間隔で待機
        }

        $io->success('すべてのメールを送信しました。');
        if (count($sendfailedEmails) > 0) {
            $io->err("失敗したメールアドレス: " . implode(",", $sendfailedEmails));
        }
    }
}
