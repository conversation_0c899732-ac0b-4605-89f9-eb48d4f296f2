<?php

namespace App\Mailer;

use App\Mailer\Sender\ISender;
use App\Mailer\Sender\IToUserSender;
use App\Mailer\Sender\IToClientSender;
use App\Mailer\Sender\IToSwbSender;
use Cake\Mailer\Mailer;
use Cake\Utility\Hash;

/**
 * https://book.cakephp.org/4/ja/core-libraries/email.html
 */
class AppMailer
{

    public static function sendToUser(IToUserSender $sender): array
    {
        $mailer = new Mailer();

        $viewVars = $sender->getViewVars();
        $viewVars = Hash::insert($viewVars, 'sender', $sender);

        $toName = null;
        if (!empty($sender->getToName())) {
            $toName = $sender->getToName() . "様";
        }

        $mailer
            ->setEmailFormat('html')
            //            ->setTo('<EMAIL>')
            //            ->setFrom('<EMAIL>', "foo")
            ->setTo($sender->getToEmail(), $toName)
            ->setSubject($sender->getSubject())
            ->viewBuilder()
            ->setTemplate($sender->getTemplate())
            ->setLayout('to_user');
        $mailer->setViewVars($viewVars);
        return $mailer->deliver();
    }

    public static function sendToClient(IToClientSender $sender): array
    {
        $mailer = new Mailer();

        $viewVars = $sender->getViewVars();
        $viewVars = Hash::insert($viewVars, 'sender', $sender);

        $mailer
            ->setEmailFormat('html')
            //            ->setTo('<EMAIL>')
            //            ->setFrom('<EMAIL>', "foo")
            ->setTo($sender->getToEmail(), $sender->getToName() . "様")
            ->setSubject($sender->getSubject())
            ->viewBuilder()
            ->setTemplate($sender->getTemplate())
            ->setLayout('to_user');
        $mailer->setViewVars($viewVars);
        return $mailer->deliver();
    }

    public static function sendToSwb(IToSwbSender $sender): array
    {
        $mailer = new Mailer();

        $viewVars = $sender->getViewVars();
        $viewVars = Hash::insert($viewVars, 'sender', $sender);

        $mailer->setEmailFormat('html');
        $mailer->setTo($sender->getToEmail());
        $mailer->setSubject($sender->getSubject())
            ->viewBuilder()
            ->setTemplate($sender->getTemplate())
            ->setLayout('to_user');
        $mailer->setViewVars($viewVars);
        return $mailer->deliver();
    }
}
