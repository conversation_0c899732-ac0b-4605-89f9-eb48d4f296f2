<?php

namespace Utility;

use App\Utility\Encryptor;
use PHPUnit\Framework\TestCase;

class EncryptorTest extends TestCase
{

    public function testToken()
    {
        $privateStatic = "22b5deb07f80d5c3c6a8d5842fe4b7b23e1651bb4bee86417a0744342a5c473e";
        debug(Encryptor::encrypt($privateStatic));
        $static = "9c31c6f8c728669c1635f347df3014244469ebc545be5a9c6707383c10adb275";
        debug(Encryptor::encrypt($static));
        $dynamicSysUser = "162f3812a11152db96665951a5633582506b730660470ec8fd520c2da0d1c833";
        debug(Encryptor::encrypt($dynamicSysUser));

        $this->assertTrue(true);
    }

    public function testBearer(){
        $token = "846be7d596c7003ff933e4509af1350c0619a590f54856c53b61a8c883db3569";
        debug(Encryptor::encrypt($token));
        $this->assertTrue(true);
    }
}
