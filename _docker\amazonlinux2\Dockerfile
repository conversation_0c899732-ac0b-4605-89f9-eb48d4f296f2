FROM amazonlinux:2

# install amazon-linux-extras install
RUN amazon-linux-extras install -y

# yum update & install
RUN yum update -y \
    && yum install \
        systemd \
        tar \
        unzip \
        sudo \
        yum-utils \
        wget \
        jq \
        git \
        -y

RUN yum -y install cronie

RUN amazon-linux-extras install -y epel
RUN yum -y install http://rpms.remirepo.net/enterprise/remi-release-7.rpm
RUN yum -y install php81 php81-php-mbstring php81-php-xml php81-php-intl php81-php-pdo php81-php-mysqlnd php81-php-common php81-php-fpm php81-php-gd \
    php81-php-pear \
    php81-php-devel \
    php81-php-pecl-redis \
    php81-php-pecl-zip \
    php81-php-pecl-xdebug3

RUN alternatives --install /usr/bin/php php /usr/bin/php81 1
RUN amazon-linux-extras install -y nginx1
RUN sed /etc/opt/remi/php81/php-fpm.d/www.conf -i -e 's/^user = apache/user = nginx/g' -e 's/^group = apache/group = nginx/g'
RUN sed /etc/opt/remi/php81/php-fpm.d/www.conf -i -e 's/^;listen.owner = nobody/listen.owner = nginx/g' -e 's/^;listen.group = nobody/listen.group = nginx/g' -e 's/;listen.mode = 0660/listen.mode = 0660/'
RUN systemctl enable php81-php-fpm
RUN systemctl enable nginx

RUN rpm --import https://repo.mysql.com/RPM-GPG-KEY-mysql-2022
RUN yum -y install https://dev.mysql.com/get/mysql80-community-release-el7-3.noarch.rpm
RUN yum-config-manager -y --enable mysql80-community
RUN yum install -y mysql-community-client --nogpgcheck
#RUN yum -y install mysql-community-server
#RUN systemctl enable mysqld.service

# create user
RUN useradd "ec2-user" && echo "ec2-user ALL=NOPASSWD: ALL" >> /etc/sudoers


RUN yum install -y glibc-langpack-ja

ENV LANG ja_JP.utf8
ENV LC_ALL ja_JP.utf8
ENV LANGUAGE ja_JP:ja
ENV TZ JST-9


WORKDIR /root


WORKDIR /opt/coverme

COPY ./entrypoint.sh /opt/entrypoint.sh
RUN chmod +x /opt/entrypoint.sh

# init
CMD ["/sbin/init"]
