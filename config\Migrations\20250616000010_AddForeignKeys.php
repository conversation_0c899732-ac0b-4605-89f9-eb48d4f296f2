<?php
declare(strict_types=1);

use Migrations\AbstractMigration;

class AddForeignK<PERSON>s extends AbstractMigration
{
    /**
     * Up Method.
     *
     * More information on this method is available here:
     * https://book.cakephp.org/phinx/0/en/migrations.html#the-up-method
     * @return void
     */
    public function up(): void
    {
        // USER_PROFILES テーブルの外部キー制約
        $this->table('user_profiles')
            ->addForeignKey(
                'general_user_id',
                'general_users',
                'id',
                [
                    'delete' => 'CASCADE',
                    'update' => 'CASCADE',
                    'constraint' => 'fk_user_profiles_general_user_id'
                ]
            )
            ->update();

        // USER_SURVEYS テーブルの外部キー制約
        $this->table('user_surveys')
            ->addForeignKey(
                'general_user_id',
                'general_users',
                'id',
                [
                    'delete' => 'CASCADE',
                    'update' => 'CASCADE',
                    'constraint' => 'fk_user_surveys_general_user_id'
                ]
            )
            ->update();

        // USER_TOKENS テーブルの外部キー制約
        $this->table('user_tokens')
            ->addForeignKey(
                'general_user_id',
                'general_users',
                'id',
                [
                    'delete' => 'CASCADE',
                    'update' => 'CASCADE',
                    'constraint' => 'fk_user_tokens_general_user_id'
                ]
            )
            ->update();

        // SWB_USER_TOKENS テーブルの外部キー制約
        $this->table('swb_user_tokens')
            ->addForeignKey(
                'swb_user_id',
                'swb_users',
                'id',
                [
                    'delete' => 'CASCADE',
                    'update' => 'CASCADE',
                    'constraint' => 'fk_swb_user_tokens_swb_user_id'
                ]
            )
            ->update();

        // MAKER_USER_TOKENS テーブルの外部キー制約
        $this->table('maker_user_tokens')
            ->addForeignKey(
                'maker_user_id',
                'maker_users',
                'id',
                [
                    'delete' => 'CASCADE',
                    'update' => 'CASCADE',
                    'constraint' => 'fk_maker_user_tokens_maker_user_id'
                ]
            )
            ->update();

        // RANDSEL_ORDERS テーブルの外部キー制約を更新（general_user_idを追加）
        $this->table('randsel_orders')
            ->addColumn('general_user_id', 'integer', [
                'comment' => '一般ユーザーID',
                'default' => null,
                'limit' => null,
                'null' => true,
                'after' => 'member_id',
            ])
            ->addIndex(
                [
                    'general_user_id',
                ],
                [
                    'name' => 'idx_randsel_orders_general_user_id',
                ]
            )
            ->addForeignKey(
                'general_user_id',
                'general_users',
                'id',
                [
                    'delete' => 'SET_NULL',
                    'update' => 'CASCADE',
                    'constraint' => 'fk_randsel_orders_general_user_id'
                ]
            )
            ->update();
    }

    /**
     * Down Method.
     *
     * More information on this method is available here:
     * https://book.cakephp.org/phinx/0/en/migrations.html#the-down-method
     * @return void
     */
    public function down(): void
    {
        // 外部キー制約を削除
        $this->table('user_profiles')
            ->dropForeignKey('general_user_id')
            ->update();

        $this->table('user_surveys')
            ->dropForeignKey('general_user_id')
            ->update();

        $this->table('user_tokens')
            ->dropForeignKey('general_user_id')
            ->update();

        $this->table('swb_user_tokens')
            ->dropForeignKey('swb_user_id')
            ->update();

        $this->table('maker_user_tokens')
            ->dropForeignKey('maker_user_id')
            ->update();

        $this->table('randsel_orders')
            ->dropForeignKey('general_user_id')
            ->removeColumn('general_user_id')
            ->update();
    }
}
