<?php

namespace App\Kuroko\Entity;

use Cake\Utility\Hash;

trait KurokoEntityTrait
{

    private array $_data;

    public function __construct(array $data)
    {
        $this->_data = $this->initialize($data);
    }

    public function getData(): array
    {
        return $this->_data;
    }

    /**
     * @param string $key
     * @param mixed|null $default
     * @return mixed
     */
    public function get(string $key, mixed $default = null): mixed
    {
        return Hash::get($this->getData(), $key, $default);
    }

    public function jsonSerialize(): array
    {
        return $this->getJsonData();
    }

    /**
     * @param array $list
     * @return array
     */
    static public function creates(array $list): array
    {
        $results = [];
        foreach ($list as $data) {
            $results[] = new static($data);
        }
        return $results;
    }
}
