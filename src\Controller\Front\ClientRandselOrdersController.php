<?php

namespace App\Controller\Front;

use App\Controller\FrontController;
use App\Form\Front\ClientRandselOrdersIndexForm;
use App\Service\ClientRandselOrdersService;
use Cake\Event\Event;
use Cake\Log\Log;

class ClientRandselOrdersController extends FrontController
{
    public function index(): void
    {
        $this->_onSuccess(function (Event $event, array $randselOrders) {
            $this->_setSuccessResponse([
                "randsel_orders" => $randselOrders
            ]);
        });
        $this->_onError(function (Event $event, array $errors) {
            Log::debug(print_r($errors, true));
        });
        $this->_index((new ClientRandselOrdersService())
            ->setIdentity($this->Authentication->getIdentity())
            ->setForm(
                new ClientRandselOrdersIndexForm()
            ));
    }
}
