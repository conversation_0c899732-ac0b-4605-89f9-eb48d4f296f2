<?php

declare(strict_types=1);

namespace App\Test\Fixture;

use Cake\TestSuite\Fixture\TestFixture;
use Cake\I18n\FrozenDate;

/**
 * RandselInvoiceAdjustmentsFixture
 */
class RandselInvoiceAdjustmentsFixture extends TestFixture
{
    /**
     * Init method
     *
     * @return void
     */
    public function init(): void
    {
        $this->records = [
            [
                'id' => 1,
                "maker_id" => 17,
                "product_id" => 41202,
                'adjustment_unit_price' => 500,
                'billing_year_month' => '2024-12',
                'adjustment_quantity' => 10,
                'adjustment_note' => 'Lorem ipsum dolor sit amet, aliquet feugiat. Convallis morbi fringilla gravida, phasellus feugiat dapibus velit nunc, pulvinar eget sollicitudin venenatis cum nullam, vivamus ut a sed, mollitia lectus. Nulla vestibulum massa neque ut et, id hendrerit sit, feugiat in taciti enim proin nibh, tempor dignissim, rhoncus duis vestibulum nunc mattis convallis.',
                'status' => 1,
                'confirmed_by' => 1,
                'confirmed' => '2025-03-11 14:52:29',
                'created_by' => 1,
                'created' => '2025-03-11 14:52:29',
                'modified' => '2025-03-11 14:52:29',
            ],
            [
                'id' => 2,
                "maker_id" => 17,
                "product_id" => 41202,
                'adjustment_unit_price' => -321,
                'billing_year_month' => FrozenDate::now()->subMonths(1)->format('Y-m'),
                'adjustment_quantity' => 5,
                'adjustment_note' => 'Lorem ipsum dolor sit amet, aliquet feugiat. Convallis morbi fringilla gravida, phasellus feugiat dapibus velit nunc, pulvinar eget sollicitudin venenatis cum nullam, vivamus ut a sed, mollitia lectus. Nulla vestibulum massa neque ut et, id hendrerit sit, feugiat in taciti enim proin nibh, tempor dignissim, rhoncus duis vestibulum nunc mattis convallis.',
                'status' => 1,
                'confirmed_by' => 1,
                'confirmed' => '2025-03-11 14:52:29',
                'created_by' => 1,
                'created' => '2025-03-11 14:52:29',
                'modified' => '2025-03-11 14:52:29',
            ],
        ];
        parent::init();
    }
}
