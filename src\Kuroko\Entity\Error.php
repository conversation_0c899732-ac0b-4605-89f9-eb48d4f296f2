<?php

namespace App\Kuroko\Entity;

class Error implements IKurokoEntity
{
    use KurokoEntityTrait;

    /**
     * @param array $data
     * @return array
     */
    public function initialize(array $data): array
    {
        return $data;
    }

    public function getJsonData(): array
    {
        return [
            "code" => $this->get("code"),
            "message" => $this->get("message"),
            "field" => $this->get("field"),
        ];
    }
}
