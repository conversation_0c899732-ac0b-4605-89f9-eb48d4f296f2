<?php

namespace <PERSON>roko\Entity;

use App\Kuroko\Entity\AccessToken;
use Cake\I18n\FrozenTime;
use Cake\Routing\Router;
use Cake\TestSuite\TestCase;

class AccessTokenTest extends TestCase
{
    // php ./vendor/phpunit/phpunit/phpunit --filter "testGetAccessToken" ./tests/TestCase/Kuroko/Entity/AccessTokenTest.php
    public function testGetAccessToken()
    {
        $data = [
            'access_token' => [
                'value' => 'test_token'
            ]
        ];
        $accessToken = new AccessToken($data);

        $result = $accessToken->getAccessToken();
        $this->assertEquals('test_token', $result);
    }

    // php ./vendor/phpunit/phpunit/phpunit --filter "testGetJsonData" ./tests/TestCase/Kuroko/Entity/AccessTokenTest.php
    public function testGetJsonData()
    {
        $data = [
            'access_token' => [
                'value' => 'test_token',
            ]
        ];
        $accessToken = new AccessToken($data);

        $result = $accessToken->getJsonData();
        $this->assertArrayHasKey('access_token', $result);
        $this->assertEquals('test_token', $result['access_token']);
    }

    // php ./vendor/phpunit/phpunit/phpunit --filter "testEncryptToken" ./tests/TestCase/Kuroko/Entity/AccessTokenTest.php
    public function testEncryptToken()
    {
        $data = [
            'access_token' => [
                'value' => 'token1234jkhjkfdsaf',
            ]
        ];
        $accessToken = new AccessToken($data);

        $result = $accessToken->encryptToken();
        debug($result);
        $result = $accessToken->decryptToken($result);
        debug($result);
        $this->assertTrue(true);
    }

    // php ./vendor/phpunit/phpunit/phpunit --filter "testDecryptToken" ./tests/TestCase/Kuroko/Entity/AccessTokenTest.php
    public function testDecryptToken()
    {
        $data = [
            'access_token' => [
                'value' => 'test_token',
            ]
        ];
        $accessToken = new AccessToken($data);

        $result = $accessToken->decryptToken("NjA0OWIwMDU5OWI5MmE2N2NmZmE0MzFjMThkYTUwOWNjZTc5ZjEwMjVmMDU3M2FkOTNkN2ZlYWU3N2E5N2YyMgfSdejRJYzyQrVYxOl1rVUeYwFZFoYkm0EYxMfVZKN0NyIE6ImPQlxNW3/4WeWTo3qBl+mZdBDwF+xAxX0lN/y2QIFvzSsnl+K+Iiqa4DTOkbAjGIHfH8D1mrosEKO/Xg==");
        debug($result);
        $this->assertTrue(true);
    }
}
