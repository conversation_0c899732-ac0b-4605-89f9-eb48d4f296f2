import ClientOrdersClient from "~/src/lib/http/coverme-front/ClientOrdersClient";
import Order from "~/src/models/entry/Order";
import { Model } from "~/src/models/Model";

export default class ClientOrders extends Model {
    public index(params?: Record<string, string | number>): Promise<Order[]> {
        const config = this.config;
        return new Promise((resolve, reject) => {
            ClientOrdersClient.create(config)
                .index<TOrdersResponse>(params)
                .then(({ orders }) => resolve(Order.creates(orders)))
                .catch((error) => reject(new Error(error)));
        });
    }
}
