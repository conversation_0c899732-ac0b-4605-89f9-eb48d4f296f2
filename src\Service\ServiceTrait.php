<?php

namespace App\Service;

use Authentication\IdentityInterface;
use Cake\Core\InstanceConfigTrait;
use Cake\Datasource\EntityInterface;
use Cake\Form\Form;
use Cake\ORM\Locator\LocatorAwareTrait;
use Cake\ORM\Query;
use Cake\ORM\Table;

/**
 * Service共通処理
 */
trait ServiceTrait
{

    use InstanceConfigTrait;
    use LocatorAwareTrait;

    /**
     * @var array
     */
    protected array $_errors = [];
    protected Form $_form;

    protected ?Table $_defaultModel = null;


    public function __construct(array $config = [])
    {
        $this->setConfig($config);
        if ($defaultModel = $this->getConfig('defaultModel')) {
            $this->_defaultModel = $this->getTableLocator()->get($defaultModel);
        }
        $this->initialize();
    }

    public function getDefaultModel(): ?Table
    {
        return $this->_defaultModel;
    }


    public function search(array $options = []): Query
    {
        return $this->getDefaultModel()->find('search', $options);
    }

    public function invoiceSearch(array $options = []): Query
    {
        return $this->getDefaultModel()->find('invoiceSearch', $options);
    }

    /**
     * @return array
     */
    public function getErrors(): array
    {
        return $this->_errors;
    }

    /**
     * @param array $errors
     * @return void
     */
    public function setErrors(array $errors): void
    {
        $this->_errors = $errors;
    }

    /**
     * @return bool
     */
    public function isValid(): bool
    {
        return !count($this->getErrors());
    }

    public function setForm(Form $form): IRestService
    {
        $this->_form = $form;
        return $this;
    }

    public function getForm(): Form
    {
        return $this->_form;
    }


    public function save(EntityInterface $entity): ?EntityInterface
    {
        if (!$this->getDefaultModel()->save($entity)) {
            $this->setErrors($entity->getErrors());
            return null;
        }
        return $entity;
    }

    private IdentityInterface $_identity;

    public function getIdentity(): IdentityInterface
    {
        return $this->_identity;
    }

    function setIdentity(IdentityInterface $identity): static
    {
        $this->_identity = $identity;
        return $this;
    }
}
