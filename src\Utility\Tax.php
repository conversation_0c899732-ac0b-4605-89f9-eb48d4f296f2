<?php

namespace App\Utility;

use Cake\Core\Configure;
use Cake\Core\InstanceConfigTrait;
use Cake\I18n\FrozenDate;

class Tax
{
    use InstanceConfigTrait;
    private static ?Tax $instance = null;

    protected array $_defaultConfig = [];

    private function __construct()
    {
        $this->setConfig(Configure::read("TaxRates"));
    }

    private static function getInstance(): static
    {
        if (is_null(static::$instance)) {
            static::$instance = new static();
        }
        return static::$instance;
    }

    /**
     * $dateString で指定された日付に対応する消費税率を取得する
     *
     * @param string $dateString 対象日付 (YYYY-MM-DD)
     * @return float 消費税率
     */
    public static function getTaxRate(string $dateString): float
    {
        $rate = 0.5;
        $date = new FrozenDate($dateString);

        foreach (static::getInstance()->getConfig() as $taxRate) {
            if ($date->greaterThanOrEquals(new FrozenDate($taxRate['startDate']))) {
                $rate = $taxRate['rate'];
                break; // 適用税率が見つかったらループを抜ける (最新の税率を適用)
            }
        }
        return $rate;
    }
}
