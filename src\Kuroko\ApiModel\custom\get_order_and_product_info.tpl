{assign_array var='empty_array' values=""}
{assign var='empty_object' value=$empty_array|@to_object}

{foreach from=$json.list key=key item=details}

    {foreach from=$details.order_details key=i item=order_details}
        {assign var='product_id' value=$order_details.product_id}
        {assign var='order_details.product_name' value=$empty_object}

        {if $product_id }
            {* エンドポイント設定パラメータ *}
            {assign_array var='method_params' values=''}
            {assign var='method_params.product_id' value=$product_id}
            {assign var='method_params.ignore_product_open_flg' value=1}
            {api_method
                            var='response'
                            model='ECProduct'
                            method='details'
                            method_params=$method_params
                            version='1'
                        }
            {append var='order_details' value=$response.details.product_name index='product_name'}

            {assign var='ext' value=$response.details.product_data.ext|json_decode}
            {append var='order_details' value=$ext.4 index='product_memo'}
        {/if}
        {assign_array_set var="details.order_details" key=$i value=$order_details from=$details.order_details}
    {/foreach}

    {* 個人データ取得 *}
    {assign_array var='member_details_method_params' values=''}
    {assign var='member_details_method_params.member_id' value=$details.member_id}
    {api_method
            var='member_details_response'
            model='Member'
            method='details'
            method_params=$member_details_method_params
            version='1'
        }

    {* アンケートデータ取得 *}
    {assign_array var='inquiry_message_method_params'         values=''}
    {assign var='inquiry_message_method_params.inquiry_bn_id' value=$member_details_response.details.pre_form_id}

    {api_method
            var='inquiry_message_response'
            model='InquiryMessage'
            method='details'
            method_params=$inquiry_message_method_params
            version='1'
        }

    {* 拡張データ *}
    {assign_array var='ext_data' values=''}
    {* 商品データセット *}
    {assign var='ext_data.maker_id' value=$response.details.product_data.contents_type}
    {assign var='ext_data.product_id' value=$response.details.product_id}
    {assign var='ext_data.product_name' value=$response.details.product_name}
    {assign var='ext_data.price' value=$response.details.price_02}
    {* 個人データセット *}
    {assign var='ext_data.member_id' value=$member_details_response.details.member_id}
    {assign var='ext_data.name1' value=$member_details_response.details.name1}
    {assign var='ext_data.name2' value=$member_details_response.details.name2}
    {assign var='ext_data.name1_hurigana' value=$member_details_response.details.name1_hurigana}
    {assign var='ext_data.name2_hurigana' value=$member_details_response.details.name2_hurigana}
    {assign var='ext_data.zip_code' value=$member_details_response.details.zip_code}
    {assign var='ext_data.tdfk_cd' value=$member_details_response.details.tdfk_cd}
    {assign var='ext_data.address1' value=$member_details_response.details.address1}
    {assign var='ext_data.address2' value=$member_details_response.details.address2}
    {assign var='ext_data.address3' value=$member_details_response.details.address3}
    {assign var='ext_data.tel' value=$member_details_response.details.tel}
    {assign var='ext_data.email' value=$member_details_response.details.email}
    {assign var='ext_data.email_send_ng_flg' value=$member_details_response.details.email_send_ng_flg}
    {* アンケートデータセット *}
    {assign var='ext_data.survey_json' value=$inquiry_message_response.details.body}

    {assign var='details.ext_data' value=$ext_data }

    {assign_array_set var="json.list" key=$key value=$details from=$json.list}
{/foreach}


{assign var='processed_json' value=$json}