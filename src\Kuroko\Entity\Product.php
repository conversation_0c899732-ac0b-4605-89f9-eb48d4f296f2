<?php

namespace App\Kuroko\Entity;

use Cake\Utility\Hash;

class Product implements IKurokoEntity
{

    use KurokoEntityTrait;

    /**
     * 返却データのマッピング
     * @return array
     */
    public function getJsonData(): array
    {
        return [
            "product_id" => Hash::get($this->getData(), "product_id"),
            "product_name" => Hash::get($this->getData(), "product_name"),
            "topics_name" => Hash::get($this->getData(), "topics_name"),
            "product_data" => Hash::get($this->getData(), "product_data"),
        ];
    }

    /**
     * @param array $data
     * @return array
     */
    public function initialize(array $data): array
    {
        return $data;
    }


}
