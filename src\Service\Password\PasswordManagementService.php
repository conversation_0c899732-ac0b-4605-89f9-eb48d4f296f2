<?php

namespace App\Service\Password;

use App\Kuroko\ApiModel\KurokoApiDynamic\Logins;
use App\Kuroko\Entity\Member;
use App\Model\Entity\GeneralUser;
use App\Model\Entity\SwbUser;
use App\Model\Entity\MakerUser;
use App\Model\Table\GeneralUsersTable;
use App\Model\Table\SwbUsersTable;
use App\Model\Table\MakerUsersTable;
use App\Model\Table\UserTokensTable;
use App\Model\Table\SwbUserTokensTable;
use App\Model\Table\MakerUserTokensTable;
use App\Service\AuthenticationService;
use App\Service\ServiceTrait;
use App\Service\IService;
use App\Mailer\AppMailer;
use App\Mailer\Sender\ToUser\PasswordResetSender;
use App\Mailer\Sender\ToUser\PasswordResetCompletedSender;
use App\Mailer\Sender\ToClient\PasswordResetSender as ClientPasswordResetSender;
use App\Mailer\Sender\ToClient\PasswordResetCompletedSender as ClientPasswordResetCompletedSender;
use Cake\ORM\TableRegistry;
use Cake\Log\Log;
use Exception;

/**
 * 統合パスワード管理サービス
 * 3つのユーザータイプ（一般、SWB、メーカー）のパスワード管理を統合
 */
class PasswordManagementService implements IService
{
    use ServiceTrait;

    // ユーザータイプ定数
    const USER_TYPE_GENERAL = 'general';
    const USER_TYPE_SWB = 'swb';
    const USER_TYPE_MAKER = 'maker';

    private GeneralUsersTable $generalUsersTable;
    private SwbUsersTable $swbUsersTable;
    private MakerUsersTable $makerUsersTable;
    private UserTokensTable $userTokensTable;
    private SwbUserTokensTable $swbUserTokensTable;
    private MakerUserTokensTable $makerUserTokensTable;
    private AuthenticationService $authService;

    protected array $_defaultConfig = [
        "defaultModel" => GeneralUsersTable::class,
    ];

    public function initialize(): void
    {
        $this->generalUsersTable = TableRegistry::getTableLocator()->get('GeneralUsers');
        $this->swbUsersTable = TableRegistry::getTableLocator()->get('SwbUsers');
        $this->makerUsersTable = TableRegistry::getTableLocator()->get('MakerUsers');
        $this->userTokensTable = TableRegistry::getTableLocator()->get('UserTokens');
        $this->swbUserTokensTable = TableRegistry::getTableLocator()->get('SwbUserTokens');
        $this->makerUserTokensTable = TableRegistry::getTableLocator()->get('MakerUserTokens');
        $this->authService = new AuthenticationService();
    }

    /**
     * パスワードリマインダー処理（統合）
     * 新システムのみで処理（Kurocoフォールバック廃止）
     */
    public function sendPasswordReminder(string $email, string $userType = self::USER_TYPE_GENERAL): bool
    {
        try {
            Log::info("Password reminder requested for: {$email}, type: {$userType}");

            // 新システムで処理（Kurocoフォールバックなし）
            $result = $this->tryNewSystemPasswordReminder($email, $userType);
            if ($result) {
                Log::info("New system password reminder sent for: {$email}");
                return true;
            }

            Log::warning("Password reminder failed for: {$email}, type: {$userType}");
            return false;

        } catch (Exception $e) {
            Log::error("Password reminder error for {$email}: " . $e->getMessage());
            return false;
        }
    }

    /**
     * パスワードリセット処理（統合）
     * 新システムのみで処理（Kurocoフォールバック廃止）
     */
    public function resetPassword(string $token, string $newPassword): bool
    {
        try {
            Log::info("Password reset requested with token");

            // 新システムで処理（Kurocoフォールバックなし）
            $result = $this->tryNewSystemPasswordReset($token, $newPassword);
            if ($result) {
                Log::info("New system password reset completed");
                return true;
            }

            Log::warning("Password reset failed - token not found or invalid");
            return false;

        } catch (Exception $e) {
            Log::error("Password reset error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * 新システムパスワードリマインダー処理
     * Kurocoユーザーも含めて新システムで処理
     */
    private function tryNewSystemPasswordReminder(string $email, string $userType): bool
    {
        $user = $this->findUserByEmail($email, $userType);
        if (!$user) {
            Log::debug("User not found in new system: {$email}, type: {$userType}");
            return false;
        }

        // 一般ユーザーの場合、Kurocoユーザーも新システムで処理
        // SWB・メーカーユーザーはKurocoユーザー判定不要（完全移行済み）
        if ($userType === self::USER_TYPE_GENERAL && $this->isKurocoUser($user)) {
            Log::info("Kuroco user requesting password reset, processing in new system: {$email}");
        }

        // パスワードリセットトークンを生成
        $tokenEntity = $this->createPasswordResetToken($user, $userType);
        if (!$tokenEntity) {
            Log::error("Failed to create password reset token for: {$email}");
            return false;
        }

        // メール送信
        return $this->sendPasswordResetEmail($user, $tokenEntity, $userType);
    }

    /**
     * 新システムパスワードリセット処理
     * Kurocoユーザーの新システム移行処理を含む
     */
    private function tryNewSystemPasswordReset(string $token, string $newPassword): bool
    {
        // 各ユーザータイプのトークンテーブルから検索
        $tokenData = $this->findPasswordResetToken($token);
        if (!$tokenData) {
            Log::debug("Password reset token not found in new system");
            return false;
        }

        ['user' => $user, 'userType' => $userType, 'tokenEntity' => $tokenEntity] = $tokenData;

        // パスワードバリデーション
        $passwordErrors = $this->authService->validatePassword($newPassword);
        if (!empty($passwordErrors)) {
            Log::warning("Password validation failed: " . implode(', ', $passwordErrors));
            return false;
        }

        // Kurocoユーザーの新システム移行処理
        $isKurocoMigration = false;
        if ($userType === self::USER_TYPE_GENERAL && $this->isKurocoUser($user)) {
            Log::info("Migrating Kuroco user to new system: {$user->email}");
            $isKurocoMigration = true;
        }

        // パスワード更新（Kurocoユーザーの場合はNULLから新パスワードに更新）
        $user->password = $newPassword; // エンティティで自動ハッシュ化
        $userTable = $this->getUserTable($userType);
        if (!$userTable->save($user)) {
            Log::error("Failed to update password for user: {$user->email}");
            return false;
        }

        // トークンを無効化
        $this->invalidatePasswordResetToken($tokenEntity, $userType);

        // 完了メール送信
        $this->sendPasswordResetCompletedEmail($user, $userType);

        if ($isKurocoMigration) {
            Log::info("Kuroco user successfully migrated to new system: {$user->email}");
        } else {
            Log::info("Password reset completed for user: {$user->email}");
        }

        return true;
    }



    /**
     * メールアドレスでユーザーを検索
     */
    private function findUserByEmail(string $email, string $userType): GeneralUser | SwbUser | MakerUser | null
    {
        switch ($userType) {
            case self::USER_TYPE_GENERAL:
                return $this->generalUsersTable->find()->contain(['UserProfiles'])->where(['email' => $email])->first();
            case self::USER_TYPE_SWB:
                return $this->swbUsersTable->find()->where(['email' => $email])->first();
            case self::USER_TYPE_MAKER:
                return $this->makerUsersTable->find()->where(['email' => $email])->first();
            default:
                return null;
        }
    }

    /**
     * Kurocoユーザーかどうかを判定
     * 一般ユーザーのみ判定、SWB・メーカーユーザーは完全移行済みのため判定不要
     */
    private function isKurocoUser($user): bool
    {
        // 一般ユーザーのみKuroco判定を行う
        if ($user instanceof GeneralUser) {
            return method_exists($user, 'isKurocoUser') ? $user->isKurocoUser() : false;
        }

        // SWB・メーカーユーザーは完全移行済みのためfalse
        return false;
    }

    /**
     * パスワードリセットトークンを生成
     */
    private function createPasswordResetToken(GeneralUser | SwbUser | MakerUser $user, string $userType)
    {
        switch ($userType) {
            case self::USER_TYPE_GENERAL:
                return $this->userTokensTable->createPasswordResetToken($user->id, 1);
            case self::USER_TYPE_SWB:
                return $this->swbUserTokensTable->createPasswordResetToken($user->id, 1);
            case self::USER_TYPE_MAKER:
                return $this->makerUserTokensTable->createPasswordResetToken($user->id, 1);
            default:
                return null;
        }
    }

    /**
     * パスワードリセットトークンを検索
     */
    private function findPasswordResetToken(string $token): ?array
    {
        // 一般ユーザートークンを検索
        $userToken = $this->userTokensTable->findValidToken($token, 'password_reset');
        if ($userToken) {
            return [
                'user' => $userToken->general_user,
                'userType' => self::USER_TYPE_GENERAL,
                'tokenEntity' => $userToken
            ];
        }

        // SWBユーザートークンを検索
        $swbToken = $this->swbUserTokensTable->findValidToken($token, 'password_reset');
        if ($swbToken) {
            return [
                'user' => $swbToken->swb_user,
                'userType' => self::USER_TYPE_SWB,
                'tokenEntity' => $swbToken
            ];
        }

        // メーカーユーザートークンを検索
        $makerToken = $this->makerUserTokensTable->findValidToken($token, 'password_reset');
        if ($makerToken) {
            return [
                'user' => $makerToken->maker_user,
                'userType' => self::USER_TYPE_MAKER,
                'tokenEntity' => $makerToken
            ];
        }

        return null;
    }

    /**
     * ユーザータイプに対応するテーブルを取得
     */
    private function getUserTable(string $userType)
    {
        switch ($userType) {
            case self::USER_TYPE_GENERAL:
                return $this->generalUsersTable;
            case self::USER_TYPE_SWB:
                return $this->swbUsersTable;
            case self::USER_TYPE_MAKER:
                return $this->makerUsersTable;
            default:
                throw new Exception("Invalid user type: {$userType}");
        }
    }

    /**
     * パスワードリセットメール送信
     */
    private function sendPasswordResetEmail(GeneralUser | SwbUser | MakerUser $user, $tokenEntity, string $userType): bool
    {
        try {
            if ($userType === self::USER_TYPE_MAKER) {
                $sender = new ClientPasswordResetSender($user->email, $this->getUserName($user));
                $sender->setViewVars([
                    'token' => $tokenEntity->token,
                    'userType' => $userType,
                    'expires' => $tokenEntity->expires
                ]);
                AppMailer::sendToClient($sender);
            } else {
                $sender = new PasswordResetSender($user->email, $this->getUserName($user));
                $sender->setViewVars([
                    'token' => $tokenEntity->token,
                    'userType' => $userType,
                    'expires' => $tokenEntity->expires
                ]);
                AppMailer::sendToUser($sender);
            }

            return true;
        } catch (Exception $e) {
            Log::error("Failed to send password reset email: " . $e->getMessage());
            return false;
        }
    }

    /**
     * パスワードリセット完了メール送信
     */
    private function sendPasswordResetCompletedEmail($user, string $userType): void
    {
        try {
            if ($userType === self::USER_TYPE_MAKER) {
                $sender = new ClientPasswordResetCompletedSender($user->email, $this->getUserName($user));
                AppMailer::sendToClient($sender);
            } else {
                $sender = new PasswordResetCompletedSender($user->email, $this->getUserName($user));
                AppMailer::sendToUser($sender);
            }
        } catch (Exception $e) {
            Log::error("Failed to send password reset completed email: " . $e->getMessage());
        }
    }

    /**
     * ユーザー名を取得
     */
    private function getUserName($user): string
    {
        if (isset($user->user_profile) && $user->user_profile) {
            return $user->user_profile->decrypted_last_name ?? $user->email;
        }
        return $user->email;
    }

    /**
     * パスワード変更処理（統合）
     * 認証されたユーザーのパスワード変更を処理
     */
    public function changePassword(string $email, string $currentPassword, string $newPassword, string $userType = self::USER_TYPE_GENERAL): bool
    {
        try {
            Log::info("Password change requested for: {$email}, type: {$userType}");

            // ユーザー検索
            $user = $this->findUserByEmail($email, $userType);
            if (!$user) {
                Log::warning("User not found for password change: {$email}");
                return false;
            }

            // 現在のパスワード検証
            if (!$this->verifyCurrentPassword($user, $currentPassword, $userType, $newPassword)) {
                Log::warning("Current password verification failed for: {$email}");
                return false;
            }

            // 新しいパスワードのバリデーション
            $passwordErrors = $this->authService->validatePassword($newPassword);
            if (!empty($passwordErrors)) {
                Log::warning("Password validation failed: " . implode(', ', $passwordErrors));
                return false;
            }

            // パスワード更新
            $user->password = $newPassword; // エンティティで自動ハッシュ化
            $userTable = $this->getUserTable($userType);

            if (!$userTable->save($user)) {
                Log::error("Failed to update password for user: {$email}");
                return false;
            }

            // 完了メール送信
            $this->sendPasswordChangeCompletedEmail($user, $userType);

            Log::info("Password change completed for user: {$email}");
            return true;

        } catch (Exception $e) {
            Log::error("Password change error for {$email}: " . $e->getMessage());
            return false;
        }
    }

    /**
     * 現在のパスワード検証
     * 新システムユーザーとKurocoユーザーの両方に対応
     */
    private function verifyCurrentPassword($user, string $currentPassword, string $userType, string $newPassword): bool
    {
        // 一般ユーザーの場合、Kurocoユーザーの可能性を考慮
        if ($userType === self::USER_TYPE_GENERAL && $this->isKurocoUser($user)) {
            Log::debug("Verifying Kuroco user password: {$user->email}");
            return $this->verifyKurocoPassword($user, $currentPassword, $newPassword);
        }

        // 新システムユーザーの場合
        if (empty($user->password)) {
            Log::warning("User has no password set: {$user->email}");
            return false;
        }

        $passwordHasher = new \Cake\Auth\DefaultPasswordHasher();
        return $passwordHasher->check($currentPassword, $user->password);
    }

    /**
     * Kurocoユーザーのパスワード検証
     * Kuroco APIを使用してパスワードを検証
     */
    private function verifyKurocoPassword($user, string $currentPassword, string $newPassword): bool
    {
        try {
            Log::debug("Kuroco password verification for: {$user->email}");

            /** @var Member $member */
            $member = $this->getIdentity();
            $data = [
                'login_id' => $user->email,
                'current_password' => $currentPassword,
                'new_password' => $newPassword
            ];
            $logins = new Logins();
            $response = $logins->resetPassword($member, $data); // Kuroco API呼び出し

            Log::warning("Kuroco user password change should use password reset flow: {$user->email}");
            Log::debug("Current password provided: " . (!empty($currentPassword) ? 'Yes' : 'No'));
            Log::debug("Kuroco API response: {$response}");

            return empty($response);

        } catch (Exception $e) {
            Log::error("Kuroco password verification failed: " . $e->getMessage());
            return false;
        }
    }

    /**
     * パスワード変更完了メール送信
     * 既存のパスワードリセット完了メール機能を再利用
     */
    private function sendPasswordChangeCompletedEmail($user, string $userType): void
    {
        // 既存のsendPasswordResetCompletedEmailメソッドを再利用
        $this->sendPasswordResetCompletedEmail($user, $userType);
    }

    /**
     * パスワードリセットトークンを無効化
     */
    private function invalidatePasswordResetToken($tokenEntity, string $userType): void
    {
        switch ($userType) {
            case self::USER_TYPE_GENERAL:
                $this->userTokensTable->delete($tokenEntity);
                break;
            case self::USER_TYPE_SWB:
                $this->swbUserTokensTable->delete($tokenEntity);
                break;
            case self::USER_TYPE_MAKER:
                $this->makerUserTokensTable->delete($tokenEntity);
                break;
        }
    }
}
