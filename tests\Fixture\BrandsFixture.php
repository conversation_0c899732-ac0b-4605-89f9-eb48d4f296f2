<?php
declare(strict_types=1);

namespace App\Test\Fixture;

use Cake\TestSuite\Fixture\TestFixture;
use Cake\I18n\FrozenTime;

/**
 * BrandsFixture
 */
class BrandsFixture extends TestFixture
{
    /**
     * Init method
     *
     * @return void
     */
    public function init(): void
    {
        $now = FrozenTime::now();
        
        $this->records = [
            [
                'id' => 1,
                'maker_id' => 1,
                'name' => 'テストブランド1',
                'description' => 'テスト用ブランド1の説明',
                'logo_url' => 'https://example.com/brand1_logo.jpg',
                'brand_image_url' => 'https://example.com/brand1.jpg',
                'brand_features_html' => '<p>ブランド1の特徴</p>',
                'other_features_html' => '<p>その他の特徴</p>',
                'established_year' => 2000,
                'target_age_min' => 6,
                'target_age_max' => 12,
                'target_gender' => 3,
                'price_range_min' => 40000,
                'price_range_max' => 100000,
                'feature_tags' => 'テスト,軽量,丈夫',
                'website_url' => 'https://example.com/brand1',
                'is_premium' => false,
                'deleted' => null,
                'created' => $now->format('Y-m-d H:i:s'),
                'modified' => $now->format('Y-m-d H:i:s'),
            ],
        ];
        
        parent::init();
    }
}
