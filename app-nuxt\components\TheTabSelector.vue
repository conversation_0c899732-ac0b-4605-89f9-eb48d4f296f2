<script setup lang="ts">
import { defineProps, defineEmits, computed } from "vue";

const props = defineProps<{
    tabs: Record<string, { key: symbol; label: string }>;
    activeTab: symbol;
    userRole: string;
}>();
const emit = defineEmits<{
    (e: "update:active-tab", tab: symbol): void;
}>();

// アクティブなタブかを判定する関数
const isActive = (tabKey: symbol): boolean => props.activeTab === tabKey;

const selectTab = (tabKey: symbol): void => {
    emit("update:active-tab", tabKey);
};

// ユーザーロールに基づいてスタイルを返すcomputedプロパティ
const tabStyles = computed(() => {
    switch (props.userRole) {
        case "swb":
            return {
                activeBgColor: "#0092e5",
                activeTextColor: "white",
                inactiveBgColor: "#76c4f1",
                inactiveTextColor: "white",
            };
        case "client":
            return {
                activeBgColor: "#1da800",
                activeTextColor: "white",
                inactiveBgColor: "#a2ab9c",
                inactiveTextColor: "white",
            };
        default:
            return {
                activeBgColor: "#1da800",
                activeTextColor: "white",
                inactiveBgColor: "#a2ab9c",
                inactiveTextColor: "white",
            };
    }
});
</script>

<template>
    <v-row class="mb-6">
        <div
            v-for="tab in tabs"
            :key="tab.key"
            class="ml-1 tab"
            :style="{
                backgroundColor: isActive(tab.key)
                    ? tabStyles.activeBgColor
                    : tabStyles.inactiveBgColor,
                color: isActive(tab.key)
                    ? tabStyles.activeTextColor
                    : tabStyles.inactiveTextColor,
            }"
            @click="selectTab(tab.key)"
        >
            {{ tab.label }}
        </div>
        <v-divider />
    </v-row>
</template>

<style scoped>
.tab {
    font-weight: bold;
    font-size: large;
    padding: 5px 20px;
    cursor: pointer;
}
</style>
