<?php
declare(strict_types=1);

namespace App\Test\Fixture;

use Cake\TestSuite\Fixture\TestFixture;
use Authentication\PasswordHasher\DefaultPasswordHasher;

/**
 * GeneralUsersFixture
 */
class GeneralUsersFixture extends TestFixture
{
    /**
     * Table name
     */
    public $table = 'general_users';

    /**
     * Fields
     */
    public $fields = [
        'id' => ['type' => 'integer', 'length' => 11, 'unsigned' => false, 'null' => false, 'default' => null, 'comment' => '一般ユーザーID', 'autoIncrement' => true, 'precision' => null],
        'email' => ['type' => 'string', 'length' => 255, 'null' => false, 'default' => null, 'collate' => 'utf8mb4_general_ci', 'comment' => 'メールアドレス（ログイン用）', 'precision' => null],
        'password' => ['type' => 'string', 'length' => 255, 'null' => false, 'default' => null, 'collate' => 'utf8mb4_general_ci', 'comment' => 'パスワード（ハッシュ化）', 'precision' => null],
        'created' => ['type' => 'datetime', 'length' => null, 'precision' => null, 'null' => false, 'default' => 'CURRENT_TIMESTAMP', 'comment' => '作成日時'],
        'modified' => ['type' => 'datetime', 'length' => null, 'precision' => null, 'null' => false, 'default' => 'CURRENT_TIMESTAMP', 'comment' => '更新日時'],
        'deleted' => ['type' => 'datetime', 'length' => null, 'precision' => null, 'null' => true, 'default' => null, 'comment' => '削除日時(論理削除)'], // 追加
        '_constraints' => [
            'primary' => ['type' => 'primary', 'columns' => ['id'], 'length' => []],
            'uk_general_users_email' => ['type' => 'unique', 'columns' => ['email'], 'length' => []],
        ],
    ];

    /**
     * Init method
     */
    public function init(): void
    {
        $hasher = new DefaultPasswordHasher();
        $this->records = [
            [
                'id' => 1,
                'email' => '<EMAIL>',
                'password' => $hasher->hash('password1234'),
                // 'is_deleted' => false, // 削除
                'created' => '2025-01-01 00:00:00',
                'modified' => '2025-01-01 00:00:00',
                'deleted' => null, // 追加
            ],
            [
                'id' => 2,
                'email' => '<EMAIL>',
                'password' => null, // Kurocoユーザー（パスワードなし）
                // 'is_deleted' => false,
                'created' => '2024-01-02 11:00:00',
                'modified' => '2024-01-02 11:00:00',
                'deleted' => null,
            ],
            [
                'id' => 3,
                'email' => '<EMAIL>',
                'password' => $hasher->hash('password1234'), // password1234
                // 'is_deleted' => false,
                'created' => '2024-01-03 12:00:00',
                'modified' => '2024-01-03 12:00:00',
                'deleted' => null,
            ],
            [
                'id' => 4,
                'email' => '<EMAIL>',
                'password' => $hasher->hash('password1234'),
                // 'is_deleted' => true, // 削除済みユーザー
                'created' => '2024-01-04 13:00:00',
                'modified' => '2024-01-04 13:00:00',
                'deleted' => '2024-01-04 13:00:00',
            ],
        ];
        parent::init();
    }
}

