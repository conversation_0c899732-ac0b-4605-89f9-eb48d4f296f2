<?php

namespace App\Controller\Front;

use App\Controller\FrontController;
use App\Form\DisableParametersForm;
use App\Service\SwbRandselInvoiceAdjustmentsService;
use Cake\Event\Event;
use Cake\Log\Log;

class SwbRandselInvoiceAdjustmentsController extends FrontController
{
    public function index(): void
    {
        $this->_onSuccess(function (Event $event, array $randselInvoiceAdjustments) {
            $this->_setSuccessResponse([
                "randsel_invoice_adjustments" => $randselInvoiceAdjustments
            ]);
        });
        $this->_onError(function (Event $event, array $errors) {
            Log::debug(print_r($errors, true));
        });
        $this->_index((new SwbRandselInvoiceAdjustmentsService())
            ->setIdentity($this->Authentication->getIdentity())
            ->setForm(
                new DisableParametersForm()
            ));
    }

    public function add(): void
    {
        $this->_onSuccess(function (Event $event) {
            $this->_setSuccessResponse([
                'success' => true,
            ]);
        });
        $this->_onError(function (Event $event, array $errors) {
            Log::debug(print_r($errors, true));
        });
        $this->_add((new SwbRandselInvoiceAdjustmentsService())
                ->setIdentity($this->Authentication->getIdentity())
                ->setForm(
                    new DisableParametersForm()
                )
        );
    }

    public function edit($id = null): void
    {
        $this->_onSuccess(function (Event $event) {
            $this->_setSuccessResponse(['success' => true,]);
        });
        $this->_onError(function (Event $event, array $errors) {
            Log::debug(print_r($errors, true));
        });
        $this->_edit(
            $id,
            (new SwbRandselInvoiceAdjustmentsService())
                ->setIdentity($this->Authentication->getIdentity())
                ->setForm(
                    new DisableParametersForm()
                )
        );
    }

    public function delete($id = null): void
    {
        $this->_onSuccess(function (Event $event) {
            $this->_setSuccessResponse(['success' => true,]);
        });
        $this->_onError(function (Event $event, array $errors) {
            Log::debug(print_r($errors, true));
        });
        $this->_delete(
            $id,
            (new SwbRandselInvoiceAdjustmentsService())
                ->setIdentity($this->Authentication->getIdentity())
                ->setForm(
                    new DisableParametersForm()
                )
        );
    }
}
