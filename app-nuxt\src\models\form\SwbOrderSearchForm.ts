import { ClientOrderSearchForm } from "./ClientOrderSearchForm";
export class SwbOrderSearchForm extends ClientOrderSearchForm {
    protected _makerId: number;

    constructor() {
        super();
        this._makerId = -1;
    }

    get makerId(): number {
        return this._makerId;
    }

    set makerId(value: number) {
        if (this._makerId !== value) {
            this._isChanged = true;
        }
        this._makerId = value;
    }
}
