<?php

namespace App\Kuroko\Entity;

use Cake\Utility\Hash;

class Maker implements IKurokoEntity
{

    use KurokoEntityTrait;

    /**
     * 返却データのマッピング
     * @return array
     */
    public function getJsonData(): array
    {
        return [
            "maker_id" => Hash::get($this->getData(), "topics_category_id"),
            "maker_name" => Hash::get($this->getData(), "category_nm"),
            "ext" => $this->getExt(),
        ];
    }

    /**
     * メーカーID取得
     * @return int
     */
    public function getMakerId(): int
    {
        return Hash::get($this->getData(), "topics_category_id");
    }

    /**
     * メーカー名取得
     * @return string
     */
    public function getMakerName(): string
    {
        return Hash::get($this->getData(), "category_nm");
    }

    /**
     * 拡張データ取得
     * @return array | null
     */
    public function getExt(): array | null
    {
        return json_decode(Hash::get($this->getData(), "ext_col_01"), true);
    }
    /**
     * @param array $data
     * @return array
     */
    public function initialize(array $data): array
    {
        return $data;
    }
}
