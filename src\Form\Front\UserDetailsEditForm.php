<?php

namespace App\Form\Front;

use App\Enums\EFormFieldType;
use App\Enums\EntityFields\EMember;
use App\Form\ApiForm;
use App\Validation\Validator\MemberValidator;
use Cake\Form\Schema;
use Cake\Validation\Validator;

class UserDetailsEditForm extends ApiForm
{

    public function _buildSchema(Schema $schema): Schema
    {
        $schema->addField(EMember::NAME1->value, EFormFieldType::STRING->value);
        $schema->addField(EMember::NAME2->value, EFormFieldType::STRING->value);
        return $schema;
    }

    public function validationDefault(Validator $validator): Validator
    {
        return MemberValidator::buildValidator($validator, [
            EMember::NAME1->value,
            EMember::NAME2->value,
        ]);
    }

    protected function _getAllowFieldsPath(): array
    {
        return [
            EMember::NAME1->value,
            EMember::NAME2->value,
        ];
    }
}
