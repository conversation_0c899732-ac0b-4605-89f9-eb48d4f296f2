<?php

namespace Controller\Front;

use App\Test\TestCase\Controller\ApiTestCase;
use Cake\Utility\Hash;

class ClientOrdersControllerTest extends ApiTestCase
{

    /**
     * Test index method
     *
     * @return void
     * @uses \App\Controller\Front\ClientOrdersController::index()
     * php ./vendor/phpunit/phpunit/phpunit --filter "testIndex" ./tests/TestCase/Controller/Front/ClientOrdersControllerTest.php
     */
    public function testIndex()
    {
        $this->setFrontAuthorized(true);
        $this->get('/front/client-orders.json');
        $response = $this->bodyToArray($this->_response->getBody());
        debug($response);
        $this->assertResponseOk(print_r(Hash::flatten($response), true));
    }
}
