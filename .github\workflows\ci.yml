name: CI

on:
  push:
    branches:
      - '4.x'
      - '4.next'
      - '5.x'
  pull_request:
    branches:
      - '*'
  workflow_dispatch:

permissions:
  contents: read

jobs:
  testsuite:
    runs-on: ubuntu-22.04
    strategy:
      fail-fast: false
      matrix:
        php-version: ['7.4', '8.0', '8.1', '8.2']
    name: PHP ${{ matrix.php-version }}

    steps:
    - uses: actions/checkout@v4

    - name: Setup PHP
      uses: shivammathur/setup-php@v2
      with:
        php-version: ${{ matrix.php-version }}
        extensions: mbstring, intl, pdo_sqlite
        coverage: none

    - name: Composer install
      run: |
        composer update
        composer run-script post-install-cmd --no-interaction

    - name: Run PHPUnit
      run: |
        cp config/app_local.example.php config/app_local.php
        vendor/bin/phpunit
      env:
        DATABASE_TEST_URL: sqlite://./testdb.sqlite

  coding-standard:
    name: Coding Standard & Static Analysis
    runs-on: ubuntu-22.04

    steps:
    - uses: actions/checkout@v4

    - name: Setup PHP
      uses: shivammathur/setup-php@v2
      with:
        php-version: '7.4'
        extensions: mbstring, intl
        coverage: none
        tools: cs2pr, phpstan:1

    - name: Composer install
      run: composer install

    - name: Run PHP CodeSniffer
      run: vendor/bin/phpcs --report=checkstyle | cs2pr

    - name: Run phpstan
      if: always()
      run: phpstan
