<?php
declare(strict_types=1);

namespace App\Form;

use Cake\Form\Form;
use Cake\Form\Schema;
use Cake\Validation\Validator;

/**
 * ユーザー登録フォーム
 */
class UserRegistrationForm extends Form
{
    /**
     * スキーマの定義
     */
    protected function _buildSchema(Schema $schema): Schema
    {
        return $schema
            ->addField('email', 'string')
            ->addField('profile_data', 'array')
            ->addField('survey_data', 'array')
            ->addField('product_ids', 'array');
    }

    /**
     * バリデーションルールの定義
     */
    public function validationDefault(Validator $validator): Validator
    {
        $validator
            ->email('email', false, 'メールアドレスの形式が正しくありません')
            ->requirePresence('email', 'create', 'メールアドレスは必須です')
            ->notEmptyString('email', 'メールアドレスは必須です');

        $validator
            ->requirePresence('profile_data', 'create', 'プロフィール情報は必須です')
            ->notEmptyArray('profile_data', 'プロフィール情報は必須です')
            ->add('profile_data', 'custom', [
                'rule' => [$this, 'validateProfileData'],
                'message' => 'プロフィール情報が不正です'
            ]);

        $validator
            ->requirePresence('survey_data', 'create', 'アンケート情報は必須です')
            ->notEmptyArray('survey_data', 'アンケート情報は必須です')
            ->add('survey_data', 'custom', [
                'rule' => [$this, 'validateSurveyData'],
                'message' => 'アンケート情報が不正です'
            ]);

        $validator
            ->requirePresence('product_ids', 'create', '商品選択は必須です')
            ->notEmptyArray('product_ids', '商品選択は必須です')
            ->add('product_ids', 'custom', [
                'rule' => [$this, 'validateProductIds'],
                'message' => '商品選択が不正です'
            ]);

        return $validator;
    }

    /**
     * プロフィールデータのカスタムバリデーション
     */
    public function validateProfileData($value, array $context): bool
    {
        if (!is_array($value)) {
            return false;
        }

        $requiredFields = [
            'last_name', 'first_name', 'last_name_kana', 'first_name_kana',
            'zip_code', 'prefecture_code', 'address1', 'tel'
        ];

        foreach ($requiredFields as $field) {
            if (empty($value[$field])) {
                return false;
            }
        }

        // 郵便番号の形式チェック
        if (!preg_match('/^\d{3}-\d{4}$/', $value['zip_code'])) {
            return false;
        }

        // 電話番号の形式チェック
        if (!preg_match('/^\d{2,4}-\d{2,4}-\d{4}$/', $value['tel'])) {
            return false;
        }

        return true;
    }

    /**
     * アンケートデータのカスタムバリデーション
     */
    public function validateSurveyData($value, array $context): bool
    {
        if (!is_array($value)) {
            return false;
        }

        $requiredFields = ['year', 'child_sex', 'budget'];

        foreach ($requiredFields as $field) {
            if (!isset($value[$field]) || $value[$field] === '') {
                return false;
            }
        }

        // 年度の妥当性チェック
        $currentYear = (int)date('Y');
        if (!is_numeric($value['year']) || $value['year'] < $currentYear || $value['year'] > $currentYear + 2) {
            return false;
        }

        // 性別の妥当性チェック
        if (!in_array($value['child_sex'], ['male', 'female', 'other'])) {
            return false;
        }

        // 予算の妥当性チェック
        $validBudgets = ['under_30000', '30000_50000', '50000_70000', '70000_100000', 'over_100000'];
        if (!in_array($value['budget'], $validBudgets)) {
            return false;
        }

        return true;
    }

    /**
     * 商品IDのカスタムバリデーション
     */
    public function validateProductIds($value, array $context): bool
    {
        if (!is_array($value) || empty($value)) {
            return false;
        }

        // 全て数値かチェック
        foreach ($value as $id) {
            if (!is_numeric($id) || $id <= 0) {
                return false;
            }
        }

        // 最大選択数のチェック（例：10個まで）
        if (count($value) > 10) {
            return false;
        }

        return true;
    }

    /**
     * フォーム実行処理（実際の処理はサービスクラスで行う）
     */
    protected function _execute(array $data): bool
    {
        // 実際の処理はコントローラーでサービスクラスを呼び出して行う
        return true;
    }
}
