import type { RuntimeConfig } from "nuxt/schema";
import { API_TOKEN_HEADER_KEY } from "~/src/config";
import axios from "axios";

export default abstract class CovermeBuildApiClient {
    protected config: RuntimeConfig;

    constructor(protected runTimeConfig: RuntimeConfig) {
        this.config = runTimeConfig;
    }

    static create<T extends CovermeBuildApiClient>(
        this: new (config: RuntimeConfig) => T,
        runTimeConfig: RuntimeConfig,
    ): T {
        return new this(runTimeConfig);
    }

    protected abstract getResource(): string;

    private getHeader(): ObjType {
        return {
            [API_TOKEN_HEADER_KEY]: String(this.config.buildApiCmToken),
        };
    }

    public async index<T>(
        params?: Record<string, string | number>,
    ): Promise<T> {
        // console.log(
        //     "====================================",
        //     this.getHeader(),
        //     "====================================",
        //     `${this.config.buildApiBase}${this.getResource()}.json`,
        // );
        const response = await axios.get(
            `${this.config.buildApiBase}${this.getResource()}.json`,
            {
                headers: this.getHeader(),
                // withCredentials: true, // 認証情報を含めるために true に設定
                // httpsAgent,
                params,
            },
        );
        // console.log(
        //     "====================================",
        //     this.getHeader(),
        //     "====================================",
        //     `${this.config.buildApiBase}${this.getResource()}.json`,
        //     response,
        // );
        return response.data as T;
    }
}
