<?php
declare(strict_types=1);

namespace App\Model\Behavior;

use Cake\ORM\Behavior;
use Cake\ORM\Query;

class InvoiceAdjustmentHistoriesBehavior extends Behavior
{
    protected $_defaultConfig = [];

    /**
     * 調整金額IDに基づく履歴検索
     *
     * @param Query $query
     * @param array $options
     * @return Query
     */
    public function findForAdjustment(Query $query, array $options): Query
    {
        if (!isset($options['adjustment_id'])) {
            throw new \InvalidArgumentException('adjustment_id is required');
        }

        return $query
            ->where([
                $this->_table->getAlias() . '.invoice_adjustment_id' => $options['adjustment_id']
            ])
            ->orderDesc($this->_table->getAlias() . '.created');
    }

    /**
     * 期間指定での履歴検索
     *
     * @param Query $query
     * @param array $options
     * @return Query
     */
    public function findByPeriod(Query $query, array $options): Query
    {
        if (isset($options['from'])) {
            $query->where([
                $this->_table->getAlias() . '.created >=' => $options['from']
            ]);
        }

        if (isset($options['to'])) {
            $query->where([
                $this->_table->getAlias() . '.created <=' => $options['to']
            ]);
        }

        return $query->orderDesc($this->_table->getAlias() . '.created');
    }
}