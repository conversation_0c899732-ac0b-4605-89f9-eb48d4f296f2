<script setup lang="ts">
const props = withDefaults(
    defineProps<{
        color?: string;
        hideText?: boolean;
    }>(),
    {
        color: "primary",
        hideText: false,
    },
);
const circularColor = props.color || "primary";
</script>

<template>
    <div class="d-inline-flex">
        <v-progress-circular :indeterminate="true" :color="circularColor" />
        <span v-if="!hideText" class="text-h6 ml-3">
            <slot>loading</slot>
        </span>
    </div>
</template>

<style scoped>
/* .loading-container {
    display: flex;
    align-items: center;
} */
</style>
