## Nuxt 3 Project Technical Guide

**Overview**

This guide aims to establish a unified set of development standards and best practices for your Nuxt 3 project. The core technology stack includes Vue 3, Nuxt 3, TypeScript, Pinia, **Vuetify 3**, and Axios. Adhering to this guide helps ensure code cleanliness, maintainability, performance, and team collaboration efficiency.

**1. Code Style and Structure**

*   **Cleanliness and Accuracy**: Write clean, maintainable, and technically accurate TypeScript code.
*   **Composition API**: Strictly use Vue 3's Composition API and the `<script setup>` syntax. Avoid using the Options API.
*   **Functional and Declarative**: Prioritize functional and declarative programming patterns. Avoid unnecessary classes, except for established complex form logic patterns in `src/form`. Prioritize composables for new features.
*   **DRY Principle**: Reduce code duplication through iteration and modularization. Encapsulate reusable logic.
*   **Composables**:
    *   Encapsulate reusable **client-side** logic or state shared across multiple components into composables.
    *   It's recommended to place these functions in a `composables/` directory at the project root.
*   **File Structure**:
    *   Follow the standard Nuxt 3 directory structure (`components/`, `pages/`, `plugins/`, `server/`, `public/`, `assets/`).
    *   Utilize the existing `src/` directory to organize core business logic, models, and utility functions:
        *   `src/lib`: Contains general utility functions, validation logic (`Validations.ts`), CSV processing (`Csv.ts`), and HTTP API client wrappers (`src/lib/http`).
        *   `src/models`: Defines data structures and types (Interfaces preferred).
        *   `src/stores`: Holds Pinia state management modules (`auth.ts`, etc.).
        *   `src/form`: Contains form-related classes or logic encapsulation (e.g., `SchoolBagForm.ts`).
        *   `src/config.ts`: Stores application configuration constants.
    *   Component Internal Structure: Prioritize organizing related logic, types, helper functions, etc., within the component file itself or in nearby modules.

**2. Naming Conventions**

*   **Directories**: Use lowercase letters and hyphens (`kebab-case`) (e.g., `components/client-form`, `composables/use-auth`).
*   **Components**: Use PascalCase (e.g., `components/TheUserProfile.vue`, `components/client/ClientOrderForm.vue`).
*   **Composables**: Use the `use` prefix and camelCase (e.g., `composables/useAuth.ts`, `composables/useApiClient.ts`).
*   **Functions/Variables**: Use camelCase (e.g., `fetchUserData`, `isLoading`).
*   **Types/Interfaces**: Use PascalCase (e.g., `interface UserData`, `type OrderStatus`).
*   **File Exports**: Prefer named exports (`export const myFunc = ...`) for clarity and tree-shaking benefits.

**3. TypeScript Usage**

*   **Mandatory Usage**: All JavaScript code in the project must use TypeScript.
*   **Prefer Interfaces**: Prefer using `interface` for defining object structures (especially in `src/models`) due to better extendability and declaration merging capabilities. Use `type` for simple union types or utility types.
*   **Avoid Enums**: Use constant objects with `as const` or `Map` structures instead of `enum` for better type safety and flexibility.
    ```typescript
    // Instead of: enum Status { Pending, Success, Failed }
    export const STATUS = {
      PENDING: 'pending',
      SUCCESS: 'success',
      FAILED: 'failed',
    } as const;

    export type StatusValue = typeof STATUS[keyof typeof STATUS];
    ```
*   **Type Safety**: Fully leverage TypeScript's type checking by adding explicit type annotations for function parameters, return values, variables, and Props.

**4. UI and Styling**

*   **UI Library**: Primarily use **Vuetify 3** as the UI component library. Configure and integrate it via `plugins/vuetify.ts`.
*   **Styling**: Use **SASS (`.scss`)** for writing and managing styles.
    *   Utilize `assets/styles/main.scss` as the global style entry point.
    *   Define and override SASS variables and Vuetify variables in `assets/styles/utils/_variables.scss` and `assets/styles/utils/vuetifyVariables.scss`.
*   **Responsive Design**: Implement a mobile-first responsive layout using Vuetify's grid system (`<v-row>`, `<v-col>`) and display helper classes (`d-none`, `d-md-flex`, etc.), potentially combined with custom SASS media queries.
*   **Icons**: Use `@mdi/font` in conjunction with Vuetify's `<v-icon>` component.

**5. State Management**

*   **Pinia**: Use Pinia (`@pinia/nuxt`) for global state management.
*   **Modularity**: Organize Pinia Stores by feature modules within the `src/stores/` directory (e.g., `auth.ts`).
*   **Composition API Style**: Define stores using the Composition API style (`ref`, `computed`, `actions`).

**6. Nuxt 3 Specific Guidelines**

*   **Auto Imports**: Fully leverage Nuxt 3's auto-import feature. There's no need to manually import Vue's `ref`, `computed`, `watch`, etc., or Nuxt's `useState`, `useRouter`, `useRoute`, `useFetch`, etc. Components are also auto-imported.
*   **Runtime Config**: Use `useRuntimeConfig` to access environment variables defined in `.env` files that need to be accessible on both the client and server sides (e.g., API endpoints). Configure these in `nuxt.config.ts` under `runtimeConfig`.
*   **Server API**: Create server-side routes within the `server/api/` directory to handle operations that cannot be performed on the client (e.g., direct database interactions, sensitive computations, calling third-party APIs requiring secret keys).
*   **SEO**: Use the `useHead` and `useSeoMeta` composables within pages or layout components to manage `<head>` tag content (title, description, meta information, etc.).
*   **Images**:
    *   Primarily use **Vuetify 3's `<v-img>` component** for displaying images.
    *   Leverage `<v-img>` features like `lazy-src` for lazy loading, `aspect-ratio` for controlling dimensions, and `cover` for cropping behavior.
    *   **Note**: While `<v-img>` handles rendering and loading, ensure the **source image files** themselves are optimized for best performance and display (e.g., use appropriate formats like WebP, apply compression, provide suitably sized versions). Static image assets can be placed in the `public/` or `assets/` directory.
*   **Plugins**: Register plugins that need to run during Vue app initialization in the `plugins/` directory (like `vuetify.ts`).
*   **App Config (`app.config.ts`)**: Use for defining **application-level**, **non-environment-dependent** reactive configuration (e.g., global constants). Note the difference from `runtimeConfig`. Vuetify theming is primarily configured via its plugin (`plugins/vuetify.ts`) and SASS variables (`assets/styles/utils/vuetifyVariables.scss`).

**7. Data Fetching**

*   **Current Approach**: The project currently uses **Axios** along with custom API client wrappers in `src/lib/http` for data requests.
*   **Recommended Approach (New Features / Refactoring)**: It is strongly recommended to gradually adopt Nuxt 3's built-in data fetching utilities to better leverage SSR, caching, loading state management, and Nuxt lifecycle integration:
    1.  **`useFetch`**: **Preferred choice**. Use for fetching data within components (`<script setup>`). It automatically handles SSR, data refreshing on client-side navigation, caching, request status (`pending`, `error`, `data`), etc.
        ```typescript
        const { data: products, pending, error, refresh } = await useFetch('/api/products', {
          query: { category: 'electronics' }
        });
        ```
    2.  **`useAsyncData`**: Use when more complex asynchronous logic is needed (e.g., combining multiple requests, custom caching keys, data transformation) or when fetching data outside components (like in plugins or route middleware).
        ```typescript
        const { data: userProfile } = await useAsyncData('userProfile', async () => {
          const { $fetch } = useNuxtApp(); // Or import if needed outside setup
          const [user, posts] = await Promise.all([
            $fetch('/api/user'),
            $fetch('/api/user/posts')
          ]);
          return { ...user, posts };
        });
        ```
    3.  **`$fetch`**: Use for making direct API requests within event handlers (e.g., button clicks) or scenarios where Nuxt's automatic state management and SSR integration are not needed. It's a wrapper around `ofetch`, providing convenience like type inference and auto-parsing JSON.
        ```typescript
        async function submitForm() {
          try {
            // $fetch is globally available in <script setup>
            const response = await $fetch('/api/submit', { method: 'POST', body: formData.value });
            // Handle success
          } catch (err) {
            // Handle error
          }
        }
        ```
*   **Options**:
    *   `server: false`: Set in `useFetch` or `useAsyncData` options to fetch data only on the client-side, bypassing SSR.
    *   `lazy: true`: Set in `useFetch` or `useAsyncData` options to defer data fetching until after the initial page render, suitable for non-critical data.

**8. Performance Optimization**

*   **Leverage Nuxt**: Rely on Nuxt 3's built-in optimizations (code splitting, preloading, etc.).
*   **Lazy Loading**:
    *   Route components are lazy-loaded by default.
    *   Use Vue's `defineAsyncComponent` or dynamic `import()` for components that are not visible initially or loaded upon user interaction.
    *   Utilize the `lazy-src` prop of `<v-img>` for image lazy loading.
*   **Image Optimization**:
    *   **Optimize Source Files**: Before adding images to the project, ensure they use appropriate formats (prefer WebP), are effectively compressed, and resized close to their actual display dimensions.
    *   **Utilize `<v-img>`**: Use features like `lazy-src` in `<v-img>` to enhance the loading experience.
*   **Code Splitting**: Nuxt handles this automatically. Sensible code organization and routing contribute to optimization.
*   **Third-party Libraries**: Be cautious when introducing large libraries. Analyze bundle size (`nuxi analyze`), consider importing features on demand (tree-shaking), or look for lighter alternatives. `moment.js` is large; consider replacing it with `day.js` or native `Intl` APIs.
*   **VueUse**: Consider introducing the `vueuse` library, which offers many optimized, tree-shakable utility composables (e.g., `useDebounceFn`, `useVirtualList`) that can replace some custom logic.

**9. Linting and Formatting**

*   **Tools**: The project is configured with ESLint (`eslint.config.mjs`) and Prettier.
*   **Adhere to Rules**: Strictly follow the configured ESLint and Prettier rules to ensure code style consistency. Run `eslint . --fix` to automatically fix common issues.

**10. Dependency Management**

*   Regularly review and update project dependencies (`npm update` or `yarn upgrade`), paying attention to breaking changes in release notes.
*   Remove unused dependencies.

---