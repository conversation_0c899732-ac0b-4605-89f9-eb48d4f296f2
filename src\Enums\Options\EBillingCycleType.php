<?php

namespace App\Enums\Options;

enum EBillingCycleType: int implements IEnumOptions
{
    use EnumOptionsTrait;

    case END_OF_MONTH_NEXT_MONTH_END_DAY = 0;   // 末締め翌月末日払い（30日サイト）
    case END_OF_MONTH_NEXT_NEXT_MONTH_10TH = 1;  // 末締め翌々10日（40日サイト）
    case END_OF_MONTH_NEXT_NEXT_MONTH_20TH = 2;  // 末締め翌々20日（50日サイト）
    /**
     * @return string
     */
    public function description(): string
    {
        return match ($this) {
            self::END_OF_MONTH_NEXT_MONTH_END_DAY => '末締め翌月末日払い（30日サイト）',
            self::END_OF_MONTH_NEXT_NEXT_MONTH_10TH => '末締め翌々10日（40日サイト）',
            self::END_OF_MONTH_NEXT_NEXT_MONTH_20TH => '末締め翌々20日（50日サイト）',
        };
    }
}
