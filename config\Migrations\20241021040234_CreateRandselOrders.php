<?php
declare(strict_types=1);

use Migrations\AbstractMigration;

class CreateRandselOrders extends AbstractMigration
{
    /**
     * Up Method.
     *
     * More information on this method is available here:
     * https://book.cakephp.org/phinx/0/en/migrations.html#the-up-method
     * @return void
     */
    public function up(): void
    {
        $this->table('randsel_orders')
            ->addColumn('maker_id', 'integer', [
                'comment' => 'メーカーID',
                'default' => null,
                'limit' => null,
                'null' => false,
            ])
            ->addColumn('member_id', 'integer', [
                'comment' => 'メンバーID（kuroco メンバーIDと同じ）',
                'default' => null,
                'limit' => null,
                'null' => true,
            ])
            ->addColumn('product_id', 'integer', [
                'comment' => '注文商品ID',
                'default' => null,
                'limit' => null,
                'null' => false,
            ])
            ->addColumn('product_name', 'string', [
                'comment' => '注文商品名',
                'default' => null,
                'limit' => 255,
                'null' => false,
            ])
            ->addColumn('price', 'integer', [
                'comment' => '金額',
                'default' => null,
                'limit' => null,
                'null' => false,
            ])
            ->addColumn('status', 'integer', [
                'comment' => '承認状態(1: 承認, 2: 否認, 0: 承認待ち)',
                'default' => '0',
                'limit' => null,
                'null' => true,
            ])
            ->addColumn('status_modified', 'datetime', [
                'comment' => '承認日時',
                'default' => null,
                'limit' => null,
                'null' => true,
            ])
            ->addColumn('approval_type', 'integer', [
                'comment' => '承認タイプ(1: csv, 2: 画面, 3: 自動)',
                'default' => null,
                'limit' => null,
                'null' => true,
            ])
            ->addColumn('is_confirmed', 'boolean', [
                'comment' => '請求確定状態(0: 未確定, 1: 確定)',
                'default' => false,
                'limit' => null,
                'null' => true,
            ])
            ->addColumn('confirmed', 'datetime', [
                'comment' => '請求確定日時',
                'default' => null,
                'limit' => null,
                'null' => true,
            ])
            ->addColumn('name1', 'text', [
                'comment' => '姓（暗号化）',
                'default' => null,
                'limit' => null,
                'null' => false,
            ])
            ->addColumn('name2', 'text', [
                'comment' => '名（暗号化）',
                'default' => null,
                'limit' => null,
                'null' => false,
            ])
            ->addColumn('name1_hurigana', 'text', [
                'comment' => '姓（ふりがな）（暗号化）',
                'default' => null,
                'limit' => null,
                'null' => false,
            ])
            ->addColumn('name2_hurigana', 'text', [
                'comment' => '名（ふりがな）（暗号化）',
                'default' => null,
                'limit' => null,
                'null' => false,
            ])
            ->addColumn('zip_code', 'text', [
                'comment' => '郵便番号（半角数字7桁）（暗号化）',
                'default' => null,
                'limit' => null,
                'null' => false,
            ])
            ->addColumn('tdfk_cd', 'text', [
                'comment' => '都道府県コード（暗号化）',
                'default' => null,
                'limit' => null,
                'null' => false,
            ])
            ->addColumn('address1', 'text', [
                'comment' => '住所1（暗号化）',
                'default' => null,
                'limit' => null,
                'null' => false,
            ])
            ->addColumn('address2', 'text', [
                'comment' => '住所2（暗号化）',
                'default' => null,
                'limit' => null,
                'null' => false,
            ])
            ->addColumn('address3', 'text', [
                'comment' => '住所3（暗号化）',
                'default' => null,
                'limit' => null,
                'null' => true,
            ])
            ->addColumn('tel', 'text', [
                'comment' => '電話番号（暗号化）',
                'default' => null,
                'limit' => null,
                'null' => false,
            ])
            ->addColumn('email', 'text', [
                'comment' => 'メールアドレス（暗号化）',
                'default' => null,
                'limit' => null,
                'null' => false,
            ])
            ->addColumn('email_send_ng_flg', 'boolean', [
                'comment' => 'メルマガ拒否フラグ (0: 送信可, 1: 送信不可)',
                'default' => false,
                'limit' => null,
                'null' => true,
            ])
            ->addColumn('survey_json', 'text', [
                'comment' => 'アンケートJSON形式',
                'default' => null,
                'limit' => null,
                'null' => false,
            ])
            ->addColumn('created', 'datetime', [
                'comment' => '作成日時（注文日時）',
                'default' => 'CURRENT_TIMESTAMP',
                'limit' => null,
                'null' => true,
            ])
            ->addColumn('modified', 'datetime', [
                'comment' => '更新日時',
                'default' => 'CURRENT_TIMESTAMP',
                'limit' => null,
                'null' => true,
            ])
            ->addIndex(
                [
                    'maker_id',
                ],
                [
                    'name' => 'idx_maker_id',
                ]
            )
            ->addIndex(
                [
                    'product_id',
                ],
                [
                    'name' => 'idx_product_id',
                ]
            )
            ->addIndex(
                [
                    'created',
                ],
                [
                    'name' => 'idx_created',
                ]
            )
            ->addIndex(
                [
                    'status',
                ],
                [
                    'name' => 'idx_status',
                ]
            )
            ->addIndex(
                [
                    'status_modified',
                ],
                [
                    'name' => 'idx_status_modified',
                ]
            )
            ->create();
    }

    /**
     * Down Method.
     *
     * More information on this method is available here:
     * https://book.cakephp.org/phinx/0/en/migrations.html#the-down-method
     * @return void
     */
    public function down(): void
    {

        $this->table('randsel_orders')->drop()->save();
    }
}
