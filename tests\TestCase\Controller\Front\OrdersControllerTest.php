<?php

namespace Controller\Front;

use App\Test\TestCase\Controller\ApiTestCase;
use Cake\Utility\Hash;

class OrdersControllerTest extends ApiTestCase
{

    /**
     * Test index method
     *
     * @return void
     * @uses \App\Controller\Front\OrdersController::index()
     * php ./vendor/phpunit/phpunit/phpunit --filter "testIndex" ./tests/TestCase/Controller/Front/OrdersControllerTest.php
     */
    public function testIndex()
    {
        $this->setFrontAuthorized(true);
        $this->get('/front/orders.json');
        $response = $this->bodyToArray($this->_response->getBody());
        debug($response);
        $this->assertResponseOk(print_r(Hash::flatten($response), true));
    }

    /**
     * Test add method
     *
     * @return void
     * @uses \App\Controller\Front\OrdersController::add()
     * php ./vendor/phpunit/phpunit/phpunit --filter "testAdd" ./tests/TestCase/Controller/Front/OrdersControllerTest.php
     */
    public function testAdd()
    {
        $data = [
            [
                "mid" => "",
                "pid" => 41202
            ],
            [
                "mid" => "",
                "pid" => 41201
            ]
        ];

        $this->setFrontAuthorized(true);
        $this->post('/front/orders.json', $data);
        $response = $this->bodyToArray($this->_response->getBody());
        debug($response);
        $this->assertResponseOk(print_r(Hash::flatten($response), true));
    }
}
