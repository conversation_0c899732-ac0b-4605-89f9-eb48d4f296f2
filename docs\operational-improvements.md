# 認証システム運用・監視改善提案

## 現在の状況と改善提案

### 1. ログ管理の強化

#### 現在の実装
- 基本的なデバッグログ出力
- エラーログの記録

#### 改善提案

##### A. 構造化ログの実装
```php
// src/Service/AuthenticationLogService.php
<?php
namespace App\Service;

use Cake\Log\Log;
use Cake\I18n\FrozenTime;

class AuthenticationLogService
{
    public function logAuthenticationAttempt(array $data): void
    {
        $logEntry = [
            'event_type' => 'authentication_attempt',
            'timestamp' => FrozenTime::now()->toISOString(),
            'user_email' => $data['email'] ?? null,
            'user_type' => $data['user_type'] ?? null,
            'ip_address' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
            'success' => $data['success'] ?? false,
            'failure_reason' => $data['failure_reason'] ?? null,
            'session_id' => session_id(),
            'request_id' => $data['request_id'] ?? uniqid()
        ];
        
        Log::write('auth', json_encode($logEntry));
    }
    
    public function logTokenUsage(array $data): void
    {
        $logEntry = [
            'event_type' => 'token_usage',
            'timestamp' => FrozenTime::now()->toISOString(),
            'user_id' => $data['user_id'] ?? null,
            'user_type' => $data['user_type'] ?? null,
            'token_type' => $data['token_type'] ?? null,
            'ip_address' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            'endpoint' => $data['endpoint'] ?? null,
            'success' => $data['success'] ?? false
        ];
        
        Log::write('token', json_encode($logEntry));
    }
}
```

##### B. ログ設定の最適化
```php
// config/app_local.php に追加
'Log' => [
    'auth' => [
        'className' => 'Cake\Log\Engine\FileLog',
        'path' => LOGS,
        'file' => 'auth',
        'levels' => ['info', 'warning', 'error'],
        'scopes' => ['auth']
    ],
    'token' => [
        'className' => 'Cake\Log\Engine\FileLog',
        'path' => LOGS,
        'file' => 'token',
        'levels' => ['info', 'warning', 'error'],
        'scopes' => ['token']
    ],
    'security' => [
        'className' => 'Cake\Log\Engine\FileLog',
        'path' => LOGS,
        'file' => 'security',
        'levels' => ['warning', 'error', 'critical'],
        'scopes' => ['security']
    ]
]
```

### 2. メトリクス収集とダッシュボード

#### A. 認証メトリクスの収集
```php
// src/Service/AuthenticationMetricsService.php
<?php
namespace App\Service;

use Cake\Cache\Cache;
use Cake\I18n\FrozenTime;

class AuthenticationMetricsService
{
    public function recordAuthenticationSuccess(string $userType): void
    {
        $key = "auth_success_{$userType}_" . date('Y-m-d-H');
        $current = Cache::read($key, 'metrics') ?: 0;
        Cache::write($key, $current + 1, 3600); // 1時間保持
    }
    
    public function recordAuthenticationFailure(string $reason): void
    {
        $key = "auth_failure_{$reason}_" . date('Y-m-d-H');
        $current = Cache::read($key, 'metrics') ?: 0;
        Cache::write($key, $current + 1, 3600);
    }
    
    public function getHourlyMetrics(string $date): array
    {
        $metrics = [];
        
        for ($hour = 0; $hour < 24; $hour++) {
            $hourKey = sprintf('%s-%02d', $date, $hour);
            
            $metrics[$hour] = [
                'hour' => $hour,
                'general_success' => Cache::read("auth_success_general_{$hourKey}", 'metrics') ?: 0,
                'swb_success' => Cache::read("auth_success_swb_{$hourKey}", 'metrics') ?: 0,
                'maker_success' => Cache::read("auth_success_maker_{$hourKey}", 'metrics') ?: 0,
                'failures' => [
                    'invalid_credentials' => Cache::read("auth_failure_invalid_credentials_{$hourKey}", 'metrics') ?: 0,
                    'user_not_found' => Cache::read("auth_failure_user_not_found_{$hourKey}", 'metrics') ?: 0,
                    'token_expired' => Cache::read("auth_failure_token_expired_{$hourKey}", 'metrics') ?: 0
                ]
            ];
        }
        
        return $metrics;
    }
}
```

#### B. ダッシュボード用コントローラー
```php
// src/Controller/Admin/AuthMetricsController.php
<?php
namespace App\Controller\Admin;

use App\Controller\AppController;
use App\Service\AuthenticationMetricsService;

class AuthMetricsController extends AppController
{
    public function index()
    {
        $metricsService = new AuthenticationMetricsService();
        $today = date('Y-m-d');
        
        $metrics = [
            'hourly' => $metricsService->getHourlyMetrics($today),
            'summary' => $this->calculateSummary($metricsService->getHourlyMetrics($today))
        ];
        
        $this->set(compact('metrics'));
    }
    
    private function calculateSummary(array $hourlyMetrics): array
    {
        $summary = [
            'total_success' => 0,
            'total_failures' => 0,
            'success_rate' => 0,
            'peak_hour' => 0,
            'peak_count' => 0
        ];
        
        foreach ($hourlyMetrics as $hour => $data) {
            $hourSuccess = $data['general_success'] + $data['swb_success'] + $data['maker_success'];
            $hourFailures = array_sum($data['failures']);
            
            $summary['total_success'] += $hourSuccess;
            $summary['total_failures'] += $hourFailures;
            
            if ($hourSuccess > $summary['peak_count']) {
                $summary['peak_hour'] = $hour;
                $summary['peak_count'] = $hourSuccess;
            }
        }
        
        $total = $summary['total_success'] + $summary['total_failures'];
        $summary['success_rate'] = $total > 0 ? ($summary['total_success'] / $total) * 100 : 0;
        
        return $summary;
    }
}
```

### 3. ヘルスチェック機能

#### A. 認証システムヘルスチェック
```php
// src/Service/AuthenticationHealthCheckService.php
<?php
namespace App\Service;

use Cake\ORM\TableRegistry;
use Cake\Cache\Cache;
use Exception;

class AuthenticationHealthCheckService
{
    public function checkSystemHealth(): array
    {
        $checks = [
            'database' => $this->checkDatabase(),
            'cache' => $this->checkCache(),
            'token_tables' => $this->checkTokenTables(),
            'user_tables' => $this->checkUserTables()
        ];
        
        $overallStatus = array_reduce($checks, function($carry, $check) {
            return $carry && $check['status'] === 'healthy';
        }, true);
        
        return [
            'overall_status' => $overallStatus ? 'healthy' : 'unhealthy',
            'timestamp' => date('Y-m-d H:i:s'),
            'checks' => $checks
        ];
    }
    
    private function checkDatabase(): array
    {
        try {
            $connection = TableRegistry::getTableLocator()->get('GeneralUsers')->getConnection();
            $connection->execute('SELECT 1');
            
            return [
                'status' => 'healthy',
                'message' => 'Database connection successful'
            ];
        } catch (Exception $e) {
            return [
                'status' => 'unhealthy',
                'message' => 'Database connection failed: ' . $e->getMessage()
            ];
        }
    }
    
    private function checkCache(): array
    {
        try {
            $testKey = 'health_check_' . time();
            Cache::write($testKey, 'test', 60);
            $result = Cache::read($testKey);
            Cache::delete($testKey);
            
            if ($result === 'test') {
                return [
                    'status' => 'healthy',
                    'message' => 'Cache system working properly'
                ];
            } else {
                return [
                    'status' => 'unhealthy',
                    'message' => 'Cache read/write test failed'
                ];
            }
        } catch (Exception $e) {
            return [
                'status' => 'unhealthy',
                'message' => 'Cache system error: ' . $e->getMessage()
            ];
        }
    }
    
    private function checkTokenTables(): array
    {
        $tables = ['UserTokens', 'SwbUserTokens', 'MakerUserTokens'];
        $results = [];
        
        foreach ($tables as $tableName) {
            try {
                $table = TableRegistry::getTableLocator()->get($tableName);
                $count = $table->find()->count();
                $results[$tableName] = [
                    'status' => 'healthy',
                    'token_count' => $count
                ];
            } catch (Exception $e) {
                $results[$tableName] = [
                    'status' => 'unhealthy',
                    'error' => $e->getMessage()
                ];
            }
        }
        
        $allHealthy = array_reduce($results, function($carry, $result) {
            return $carry && $result['status'] === 'healthy';
        }, true);
        
        return [
            'status' => $allHealthy ? 'healthy' : 'unhealthy',
            'details' => $results
        ];
    }
    
    private function checkUserTables(): array
    {
        $tables = ['GeneralUsers', 'SwbUsers', 'MakerUsers'];
        $results = [];
        
        foreach ($tables as $tableName) {
            try {
                $table = TableRegistry::getTableLocator()->get($tableName);
                $count = $table->find()->count();
                $results[$tableName] = [
                    'status' => 'healthy',
                    'user_count' => $count
                ];
            } catch (Exception $e) {
                $results[$tableName] = [
                    'status' => 'unhealthy',
                    'error' => $e->getMessage()
                ];
            }
        }
        
        $allHealthy = array_reduce($results, function($carry, $result) {
            return $carry && $result['status'] === 'healthy';
        }, true);
        
        return [
            'status' => $allHealthy ? 'healthy' : 'unhealthy',
            'details' => $results
        ];
    }
}
```

### 4. 自動化されたメンテナンス

#### A. 定期メンテナンスコマンド
```php
// src/Command/AuthMaintenanceCommand.php
<?php
namespace App\Command;

use Cake\Command\Command;
use Cake\Console\Arguments;
use Cake\Console\ConsoleIo;
use Cake\ORM\TableRegistry;
use App\Service\AuthenticationHealthCheckService;

class AuthMaintenanceCommand extends Command
{
    public function execute(Arguments $args, ConsoleIo $io)
    {
        $io->out('Starting authentication system maintenance...');
        
        // 1. 期限切れトークンのクリーンアップ
        $this->cleanupExpiredTokens($io);
        
        // 2. ヘルスチェック実行
        $this->performHealthCheck($io);
        
        // 3. メトリクスの集計
        $this->aggregateMetrics($io);
        
        $io->success('Authentication system maintenance completed.');
    }
    
    private function cleanupExpiredTokens(ConsoleIo $io): void
    {
        $tables = ['UserTokens', 'SwbUserTokens', 'MakerUserTokens'];
        $totalCleaned = 0;
        
        foreach ($tables as $tableName) {
            $table = TableRegistry::getTableLocator()->get($tableName);
            $cleaned = $table->cleanupExpiredTokens();
            $totalCleaned += $cleaned;
            $io->out("Cleaned {$cleaned} expired tokens from {$tableName}");
        }
        
        $io->info("Total expired tokens cleaned: {$totalCleaned}");
    }
    
    private function performHealthCheck(ConsoleIo $io): void
    {
        $healthService = new AuthenticationHealthCheckService();
        $health = $healthService->checkSystemHealth();
        
        if ($health['overall_status'] === 'healthy') {
            $io->success('System health check: PASSED');
        } else {
            $io->error('System health check: FAILED');
            foreach ($health['checks'] as $check => $result) {
                if ($result['status'] !== 'healthy') {
                    $io->error("  {$check}: {$result['message']}");
                }
            }
        }
    }
    
    private function aggregateMetrics(ConsoleIo $io): void
    {
        // メトリクスの日次集計処理
        $io->out('Aggregating daily metrics...');
        // 実装は省略
        $io->info('Metrics aggregation completed');
    }
}
```

### 5. アラート設定

#### A. アラート条件の定義
```php
// config/alerts.php
<?php
return [
    'authentication_failure_rate' => [
        'threshold' => 20, // 20%以上の失敗率
        'window' => 300,   // 5分間
        'severity' => 'warning'
    ],
    'token_usage_spike' => [
        'threshold' => 1000, // 1時間に1000回以上
        'window' => 3600,
        'severity' => 'info'
    ],
    'database_connection_failure' => [
        'threshold' => 1,
        'window' => 60,
        'severity' => 'critical'
    ]
];
```

### 6. 運用手順書

#### A. 日次運用チェックリスト
1. **ヘルスチェック確認**
   - システム全体の健全性確認
   - データベース接続状況確認
   - キャッシュシステム動作確認

2. **メトリクス確認**
   - 認証成功率の確認
   - 異常なアクセスパターンの検出
   - パフォーマンス指標の確認

3. **ログ確認**
   - エラーログの確認
   - セキュリティイベントの確認
   - 異常なアクセスログの確認

#### B. 週次メンテナンス
1. **データクリーンアップ**
   - 期限切れトークンの削除
   - 古いログファイルのアーカイブ
   - メトリクスデータの集計

2. **セキュリティ確認**
   - パスワード強度の統計確認
   - 不審なアクセスパターンの分析
   - セキュリティアップデートの確認

この運用・監視体制により、認証システムの安定性と信頼性を大幅に向上させることができます。
