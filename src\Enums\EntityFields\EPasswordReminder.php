<?php

namespace App\Enums\EntityFields;

enum EPasswordReminder: string
{
    case TOKEN = 'token';
    case TEMP_PWD = 'temp_pwd';
    case MEMBER_ID = 'member_id';
    case MEMBER_NAME1 = 'member_name1';
    case MEMBER_EMAIL = 'member_email';

    /**
     * @return string
     */
    public function description(): string
    {
        return match ($this) {
            self::TOKEN => 'トークン',
            self::TEMP_PWD => '仮パスワード',
            self::MEMBER_ID => 'メンバーID',
            self::MEMBER_NAME1 => 'メンバー姓',
            self::MEMBER_EMAIL => 'メンバーメールアドレス',
        };
    }
}
