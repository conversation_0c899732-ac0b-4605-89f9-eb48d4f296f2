<?php

namespace App\Service;

use App\Kuroko\ApiModel\KurokoApiDynamic\Logins;
use App\Kuroko\Entity\AccessToken;
use Exception;

class LoginsService implements IService
{
    use ServiceTrait;

    protected array $_defaultConfig = [];

    public function initialize(): void {}

    /**
     * @throws Exception
     */
    public function getAccessToken(string $id, string $password): AccessToken
    {

        $logins = new Logins();
        $response = $logins->login([
            "email" => $id,
            "password" => $password,
            "login_save" => 0
        ]);
        //        debug([
        //            "email" => $id,
        //            "password" => $password,
        //            "login_save" => 0
        //        ]);
        if (empty($response)) {
            throw new Exception("login error");
        }
        //        debug(json_encode($response));
        $grantToken = $response->getGrantToken();
        if (empty($grantToken) && empty($response->get("errors", []))) {
            throw new Exception("g token error");
        }
        $accessToken = $logins->token($response->getGrantToken());

        if (empty($accessToken)) {
            throw new Exception("a token error");
        }
        return $accessToken;
    }
}
