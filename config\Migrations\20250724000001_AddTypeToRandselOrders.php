<?php
declare(strict_types=1);

use Migrations\AbstractMigration;

class AddTypeToRandselOrders extends AbstractMigration
{
    /**
     * Up Method.
     *
     * More information on this method is available here:
     * https://book.cakephp.org/phinx/0/en/migrations.html#the-up-method
     * @return void
     */
    public function up(): void
    {
        $this->table('randsel_orders')
            ->addColumn('brand_id', 'integer', [
                'comment' => 'ブランドID',
                'default' => null,
                'limit' => null,
                'null' => true,
                'after' => 'maker_id',
            ])
            ->addColumn('type', 'integer', [
                'comment' => 'カタログタイプ(1:紙, 2:デジタル)',
                'default' => 1,
                'limit' => null,
                'null' => false,
                'after' => 'product_id',
            ])
            ->addIndex(
                [
                    'type',
                ],
                [
                    'name' => 'idx_randsel_orders_type',
                ]
            )
            ->addIndex(
                [
                    'product_id',
                    'type',
                ],
                [
                    'name' => 'idx_randsel_orders_product_type',
                ]
            )
            ->addIndex(
                [
                    'product_id',
                    'type',
                    'created',
                ],
                [
                    'name' => 'idx_randsel_orders_product_type_created',
                ]
            )
            ->update();
    }

    /**
     * Down Method.
     *
     * More information on this method is available here:
     * https://book.cakephp.org/phinx/0/en/migrations.html#the-down-method
     * @return void
     */
    public function down(): void
    {
        $table = $this->table('randsel_orders');
        if ($table->hasIndex('type')) {
            $table->removeIndex('type');
        }
        if ($table->hasIndex(['product_id', 'type'])) {
            $table->removeIndex(['product_id', 'type']);
        }
        if ($table->hasIndex(['product_id', 'type', 'created'])) {
            $table->removeIndex(['product_id', 'type', 'created']);
        }
        if ($table->hasColumn('brand_id')) {
            $table->removeColumn('brand_id');
        }
        if ($table->hasColumn('type')) {
            $table->removeColumn('type');
        }
        
        $table->update();
    }
}
