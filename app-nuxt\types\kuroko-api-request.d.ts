interface TSchoolBagForm {
    // name: string;
    // email: string;
    // /** タイトル */
    product_ids: string;
    // /** 住所1 */
    // ext_02: string;
    // /** 住所2 */
    // ext_03: string;
    // /** 住所3 */
    // ext_04: string;
    // /** 郵便 */
    // ext_05: string;
    // /** tel */
    // ext_06: string;
    // /** 商品IDリストJSON */
    // ext_07: string;
    /** questions等 */
    survey_data: string;
    from_id: number;
    member_id: number;
    user_agent: string;
    ip_address: string;
    validate_only: boolean;

    name1: string;
    name2: string;
    email: string;
    email_send_ng_flg: boolean;
    zip_code: string;
    tdfk_cd: string;
    address2: string;
    address3: string;
    tel: string;
    child_name: string;
    child_sex: string;
    child_birthdate: string;
    custom_budget: string;
    custom_catalog_request_triggers: string[];
    custom_key_points: string[];
}

interface TSchoolBagFormNewMembersAdd {
    // name1: string;
    // name2: string;
    // name1_hurigana: string;
    // name2_hurigana: string;
    email: string;
    password: string;
    // zip_code: string;
    // tdfk_cd: string;
    // address1: string;
    // address2: string;
    // address3: string;
    // tel: string;
    // email_send_ng_flg: boolean;
    product_ids: string;
    survey_data: string;
    profile_data: string;
    type: string;
    // [key: string]: string;
}

interface TPurchase {
    pid: number;
}

// interface TPurchase {
//     ec_payment_id: number;
//     shipping_address: TAddress;
//     order_products: TOrderProduct[];
//     orderer: TOrderer;
//     card_token: string;
//     order_note: string;
//     conveni: number;
//     validate_only: boolean;
// }

interface TAddress {
    name1: string;
    name2: string;
    zip_code: string;
    tdfk_cd: string;
    address1: string;
    address2: string;
    address3: string;
    tel: string;
}

interface TOrderProduct {
    product_id: string;
    quantity: number;
}

interface TOrderer {
    name1: string;
    name2: string;
    zip_code: string;
    tdfk_cd: string;
    address1: string;
    address2: string;
    address3: string;
    tel: string;
    email: string;
}

interface TAccessToken {
    access_token: string;
}

interface TLogin {
    password: string;
    email: string;
    type: string;
}

interface TPasswordReminders {
    email?: string;
    token?: string;
    temp_pwd?: string;
    login_pwd?: string;
}

interface TChangePassword {
    login_id?: string;
    current_password?: string;
    new_password?: string;
}

interface TRandselOrder {
    id: string;
    maker_id?: string;
    member_id?: string;
    product_id?: string;
    status: string;
}

interface TUpdateStatusOrder {
    randsel_orders: TRandselOrder[];
    validate_only?: boolean;
}

interface TMonthlyInvoicesPdf {
    maker_id: number;
    total_price: number;
    status_modified_year_month: string;
    billing_address: string;
    billing_cycle: number;
    customer_code: string;
}

interface TAdjustmentHistory {
    id: number;
    invoice_adjustment_id: number;
    action_type: number;
    changes: Record<string, string>;
    created_by: number;
    created_by_name: string;
    created: string;
}

interface TMonthlyInvoiceAdjustment {
    id?: number;
    billing_year_month: string;
    maker_id: number;
    product_id: number;
    adjustment_unit_price: number;
    adjustment_quantity: number;
    adjustment_note: string;
    status: number;
    histories: TAdjustmentHistory[];
}
