<?php

declare(strict_types=1);

namespace App\Test\TestCase\Command;

use App\Command\InvoiceCalculationCommand;
use Cake\Console\ConsoleIo;
use Cake\Console\Arguments;
use Cake\Console\TestSuite\ConsoleIntegrationTestTrait;
use Cake\Console\ConsoleOptionParser;
use Cake\I18n\FrozenDate;
use Cake\Log\Log;
use Cake\Utility\Hash;
use App\Test\TestCase\AppTestCase;
use App\Service\RandselOrdersService;
use App\Service\RandselInvoiceAdjustmentsService;
use App\Service\RandselInvoicesService;
use App\Service\AllProductsService;

/**
 * App\Command\InvoiceCalculationCommand Test Case
 *
 * @uses \App\Command\InvoiceCalculationCommand
 */
class InvoiceCalculationCommandTest extends AppTestCase
{
    use ConsoleIntegrationTestTrait;

    /**
     * setUp method
     *
     * @return void
     */
    protected function setUp(): void
    {
        parent::setUp();
        $this->useCommandRunner();
    }

    /**
     * tearDown method
     *
     * @return void
     */
    public function tearDown(): void
    {
        parent::tearDown();
        Log::drop('debug');
        Log::drop('error');
    }

    /**
     * Test buildOptionParser method
     *
     * @return void
     * @uses \App\Command\InvoiceCalculationCommand::buildOptionParser()
     * php ./vendor/phpunit/phpunit/phpunit --filter "testBuildOptionParser" ./tests/TestCase/Command/InvoiceCalculationCommandTest.php
     */
    public function testBuildOptionParser(): void
    {
        $defaultYearMonth = FrozenDate::now()->subMonths(1)->format('Y-m');

        $command = new InvoiceCalculationCommand();
        $parser = $command->buildOptionParser(new ConsoleOptionParser('invoice_calculation'));
        $this->assertInstanceOf(ConsoleOptionParser::class, $parser);
        $this->assertSame('請求金額を計算し、テーブル:randsel_invoices に登録します。', $parser->getDescription());
        $this->assertSame("--year_month請求年月 (YYYY-MM) 例：--year_month=2022-01 <comment>(default: " . $defaultYearMonth . ")</comment>", $parser->toArray()["options"]["year_month"]->help());
        $this->assertSame(FrozenDate::now()->subMonths(1)->format('Y-m'), $parser->toArray()["options"]["year_month"]->defaultValue());
    }

    /**
     * Test execute method with invalid year_month option format
     *
     * @return void
     * php ./vendor/phpunit/phpunit/phpunit --filter "testExecuteWithInvalidYearMonthFormat" ./tests/TestCase/Command/InvoiceCalculationCommandTest.php
     */
    public function testExecuteWithInvalidYearMonthFormat(): void
    {
        $this->exec('invoice_calculation --year_month=202403');

        $this->assertExitCode(1); // Command::CODE_ERROR
        $this->assertErrorContains('請求年月は YYYY-MM 形式で指定してください。例: 2024-02');
    }

    /**
     * Test execute method with valid year_month option
     *
     * @return void
     * php ./vendor/phpunit/phpunit/phpunit --filter "testExecuteWithValidYearMonth" ./tests/TestCase/Command/InvoiceCalculationCommandTest.php
     */
    public function testExecuteWithValidYearMonth(): void
    {
        $yearMonth = '2024-12';

        $options = [
            'from' => $yearMonth . '-01',
            'to' => $yearMonth . '-01',
            'group_by' => 'product_id'
        ];

        // 注文テーブルから請求金額を取得
        $invoicesByProduct = Hash::combine((new RandselOrdersService())->getMonthlyInvoices($options), '{n}.product_id', '{n}');

        debug($invoicesByProduct);

        // 調整金額テーブルから調整金額を取得
        $adjustments = (new RandselInvoiceAdjustmentsService())->getAdjustments(['billing_year_month' => $yearMonth, 'status' => 1]);
        debug($adjustments);

        $this->exec('invoice_calculation --year_month=' . $yearMonth);

        $this->assertExitCode(0);
        $this->assertOutputContains('請求年月: 2024-12 で請求金額計算処理を開始します。');
        $this->assertOutputContains('請求金額計算処理完了');
        $this->assertOutputContains('新規登録: 5件, エラー: 0件');

        $invoices = (new RandselInvoicesService())->getInvoices(['billing_year_month' => $yearMonth]);
        debug($invoices);
    }

    /**
     * Test execute method when no invoices and adjustments are found for a product (skip case)
     *
     * @return void
     * php ./vendor/phpunit/phpunit/phpunit --filter "testExecuteNoInvoiceAndAdjustment" ./tests/TestCase/Command/InvoiceCalculationCommandTest.php
     */
    public function testExecuteNoInvoiceAndAdjustment(): void
    {
        $yearMonth = '2024-01';

        $options = [
            'from' => $yearMonth . '-01',
            'to' => $yearMonth . '-01',
            'group_by' => 'product_id'
        ];

        // 注文テーブルから請求金額を取得
        $invoicesByProduct = Hash::combine((new RandselOrdersService())->getMonthlyInvoices($options), '{n}.product_id', '{n}');

        debug($invoicesByProduct);

        // 調整金額テーブルから調整金額を取得
        $adjustments = (new RandselInvoiceAdjustmentsService())->getAdjustments(['billing_year_month' => $yearMonth, 'status' => 1]);
        debug($adjustments);

        $this->exec('invoice_calculation --year_month=' . $yearMonth);

        $this->assertExitCode(0);
        $this->assertOutputContains('請求年月: 2024-01 で請求金額計算処理を開始します。');
        $this->assertOutputContains('請求金額計算処理完了');
        $this->assertOutputContains('新規登録: 0件, エラー: 0件');

        $invoices = (new RandselInvoicesService())->getInvoices(['billing_year_month' => $yearMonth]);
        debug(count($invoices));
    }

    /**
     * Test execute method with default year_month option (omitted)
     *
     * @return void
     * php ./vendor/phpunit/phpunit/phpunit --filter "testExecuteWithDefaultYearMonth" ./tests/TestCase/Command/InvoiceCalculationCommandTest.php
     */
    public function testExecuteWithDefaultYearMonth(): void
    {
        $yearMonth = FrozenDate::now()->subMonths(1)->format('Y-m');

        $options = [
            'from' => $yearMonth . '-01',
            'to' => $yearMonth . '-01',
            'group_by' => 'product_id'
        ];

        // 注文テーブルから請求金額を取得
        $invoicesByProduct = Hash::combine((new RandselOrdersService())->getMonthlyInvoices($options), '{n}.product_id', '{n}');

        debug($invoicesByProduct);

        // 調整金額テーブルから調整金額を取得
        $adjustments = (new RandselInvoiceAdjustmentsService())->getAdjustments(['billing_year_month' => $yearMonth, 'status' => 1]);
        debug($adjustments);

        // デフォルト年月で実行
        $this->exec('invoice_calculation');

        $this->assertExitCode(0);
        $this->assertOutputContains('請求年月: ' . $yearMonth . ' で請求金額計算処理を開始します。');
        $this->assertOutputContains('請求金額計算処理完了');
        $this->assertOutputContains('新規登録: 2件, エラー: 0件');

        $invoices = (new RandselInvoicesService())->getInvoices(['billing_year_month' => $yearMonth]);
        debug($invoices);
    }
}
