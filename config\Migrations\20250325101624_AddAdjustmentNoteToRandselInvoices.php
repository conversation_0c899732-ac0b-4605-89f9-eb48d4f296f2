<?php
declare(strict_types=1);

use Migrations\AbstractMigration;

class AddAdjustmentNoteToRandselInvoices extends AbstractMigration
{
    /**
     * Change Method.
     *
     * More information on this method is available here:
     * https://book.cakephp.org/phinx/0/en/migrations.html#the-change-method
     * @return void
     */
    public function change(): void
    {
        $table = $this->table('randsel_invoices');
        $table->addColumn('total_quantity', 'integer', [
            'null' => true,
            'comment' => '請求数量',
            'default' => 0,
            'limit' => null,
            'after' => 'total_amount'
        ]);
        $table->addColumn('adjustment_quantity', 'integer', [
            'null' => false,
            'comment' => '調整数量',
            'default' => 0,
            'limit' => null,
            'after' => 'adjustment_amount'
        ]);
        $table->addColumn('adjustment_note', 'text', [
            'null' => true,
            'comment' => '調整備考内容',
            'default' => null,
            'limit' => null,
            'after' => 'adjustment_quantity'
        ]);
        $table->update();
    }
}
