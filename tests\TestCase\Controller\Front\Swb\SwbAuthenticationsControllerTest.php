<?php

namespace App\Test\TestCase\Controller\Front\Swb;

use App\Enums\EntityFields\ELogin;
use App\Test\TestCase\Controller\ApiTestCase;
use Cake\Utility\Hash;

class SwbAuthenticationsControllerTest extends ApiTestCase
{
    /**
     * Test add method
     *
     * @return void
     * @uses \App\Controller\Front\SwbAuthenticationsController::add()
     * php ./vendor/phpunit/phpunit/phpunit --filter "testAdd" ./tests/TestCase/Controller/Front/Swb/SwbAuthenticationsControllerTest.php
     */
    public function testAdd(): void
    {
        // メーカーアカウント
        $this->setFrontAuthorized();
        $this->post('/front/swb-authentications.json', [
            ELogin::LOGIN_EMAIL->value => "<EMAIL>",
            ELogin::LOGIN_PWD->value => "pass0000",
        ]);
        $response = $this->bodyToArray($this->_response->getBody());
        debug($response);
        $this->assertResponseOk(print_r(Hash::flatten($response), true));

        // ユーザーアカウント
        $this->setFrontAuthorized();
        $this->post('/front/swb-authentications.json', [
            ELogin::LOGIN_EMAIL->value => "<EMAIL>",
            ELogin::LOGIN_PWD->value => "1234Asdf",
        ]);
        $response = $this->bodyToArray($this->_response->getBody());
        debug($response);
        $this->assertResponseOk(print_r(Hash::flatten($response), true));

        // SWBアカウント
        $this->setFrontAuthorized();
        $this->post('/front/swb-authentications.json', [
            ELogin::LOGIN_EMAIL->value => "<EMAIL>",
            ELogin::LOGIN_PWD->value => "<EMAIL>",
        ]);
        $response = $this->bodyToArray($this->_response->getBody());
        debug($response);
        $this->assertResponseOk(print_r(Hash::flatten($response), true));
    }

    /**
     * Test delete method
     *
     * @return void
     * @uses \App\Controller\Front\SwbAuthenticationsController::delete()
     * php ./vendor/phpunit/phpunit/phpunit --filter "testDelete" ./tests/TestCase/Controller/Front/Swb/SwbAuthenticationsControllerTest.php
     */
    public function testDelete(): void
    {
        $this->setFrontAuthorized(true);
        $this->delete('/front/swb-authentications/1.json');
        $response = $this->bodyToArray($this->_response->getBody());
        debug($response);
        $this->assertResponseOk(print_r(Hash::flatten($response), true));
    }
}
