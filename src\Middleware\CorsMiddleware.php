<?php
declare(strict_types=1);

namespace App\Middleware;

use App\Enums\EHttpStatusCode;
use Cake\Core\Configure;
use Cake\Core\InstanceConfigTrait;
use Cake\Http\Response;
use Cake\Http\ServerRequest;
use Cake\Log\Log;
use Psr\Http\Message\ResponseInterface;
use Psr\Http\Message\ServerRequestInterface;
use Psr\Http\Server\MiddlewareInterface;
use Psr\Http\Server\RequestHandlerInterface;

/**
 * CORS対策
 * Class CorsMiddleware
 * @package App\Middleware
 * @see \Cake\Http\CorsBuilder
 */
class CorsMiddleware implements MiddlewareInterface
{

    use InstanceConfigTrait;

    /**
     * @var array
     */
    protected array $_defaultConfig = [
    ];


    /**
     * CorsMiddleware constructor.
     * @param array $config
     */
    public function __construct(array $config = [])
    {
        $this->setConfig($config);
    }

    /**
     * @inheritDoc
     */
    public function process(ServerRequestInterface $request, RequestHandlerInterface $handler): ResponseInterface
    {
        if ($this->getConfig('use')) {
//            Log::debug("options");
            if ($request instanceof ServerRequest) {
                if ($request->getMethod() === 'OPTIONS') {
                    $response = new Response();
                    $response = $response->withStatus(EHttpStatusCode::OK->value);
                    return $this->_withHeader($request, $response);
                }
                return $this->_withHeader($request, $handler->handle($request));
            }
        }
        return $handler->handle($request);
    }

    /**
     * @param ServerRequest $request
     * @param ResponseInterface $response
     * @return ResponseInterface
     */
    private function _withHeader(ServerRequest $request, ResponseInterface $response): ResponseInterface
    {
        $origin = $request->getHeader('origin');
        $response = $response->withHeader('Access-Control-Allow-Origin',
            //デバッグモードは環境を問わない
            Configure::read('debug') && $origin ? $origin
                : $this->getConfig('withHeaders.Access-Control-Allow-Origin')
        );
//        $response = $response->withHeader('Access-Control-Allow-Credentials', $this->getConfig('withHeaders.Access-Control-Allow-Credentials'));
        $response = $response->withHeader('Access-Control-Allow-Headers', $this->getConfig('withHeaders.Access-Control-Allow-Headers'));
        return $response->withHeader('Access-Control-Allow-Methods', $this->getConfig('withHeaders.Access-Control-Allow-Methods'));
    }

}
