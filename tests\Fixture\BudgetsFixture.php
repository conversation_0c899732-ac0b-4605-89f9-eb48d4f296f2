<?php
declare(strict_types=1);

namespace App\Test\Fixture;

use Cake\TestSuite\Fixture\TestFixture;

/**
 * BudgetsFixture
 */
class BudgetsFixture extends TestFixture
{
    /**
     * Init method
     *
     * @return void
     */
    public function init(): void
    {
        $this->records = [
            // パターン1: 紙とデジタルの併用（商品ID: 1）
            [
                'id' => 1,
                'product_id' => 1,
                'type' => 1, // 紙
                'price' => 350,
                'budget_quantity' => 60,
                'is_active' => true,
                'start_date' => '2025-07-14 00:00:00',
                'end_date' => '2025-08-14 23:59:59',
                'priority' => 2,
                'created' => '2025-07-24 00:00:00',
                'modified' => '2025-07-24 00:00:00',
            ],
            [
                'id' => 2,
                'product_id' => 1,
                'type' => 2, // デジタル
                'price' => 0,
                'budget_quantity' => 999999,
                'is_active' => true,
                'start_date' => '2025-07-14 00:00:00',
                'end_date' => '2025-08-14 23:59:59',
                'priority' => 1,
                'created' => '2025-07-24 00:00:00',
                'modified' => '2025-07-24 00:00:00',
            ],
            
            // パターン2: 同じタイプの条件が重複（商品ID: 2）
            [
                'id' => 3,
                'product_id' => 2,
                'type' => 1, // 紙
                'price' => 350,
                'budget_quantity' => 60,
                'is_active' => true,
                'start_date' => '2025-07-14 00:00:00',
                'end_date' => '2025-08-14 23:59:59',
                'priority' => 2,
                'created' => '2025-07-24 00:00:00',
                'modified' => '2025-07-24 00:00:00',
            ],
            [
                'id' => 4,
                'product_id' => 2,
                'type' => 1, // 紙（重複）
                'price' => 350,
                'budget_quantity' => 80,
                'is_active' => true,
                'start_date' => '2025-07-19 00:00:00',
                'end_date' => '2025-08-24 23:59:59',
                'priority' => 2,
                'created' => '2025-07-24 00:00:00',
                'modified' => '2025-07-24 00:00:00',
            ],
            
            // パターン3: 予算期間途中で紙カタログが追加（商品ID: 3）
            [
                'id' => 5,
                'product_id' => 3,
                'type' => 2, // デジタル
                'price' => 0,
                'budget_quantity' => 999999,
                'is_active' => true,
                'start_date' => '2025-07-14 00:00:00',
                'end_date' => '2025-08-14 23:59:59',
                'priority' => 1,
                'created' => '2025-07-24 00:00:00',
                'modified' => '2025-07-24 00:00:00',
            ],
            [
                'id' => 6,
                'product_id' => 3,
                'type' => 1, // 紙（途中追加）
                'price' => 350,
                'budget_quantity' => 80,
                'is_active' => true,
                'start_date' => '2025-07-19 00:00:00',
                'end_date' => '2025-08-24 23:59:59',
                'priority' => 2,
                'created' => '2025-07-24 00:00:00',
                'modified' => '2025-07-24 00:00:00',
            ],
            
            // 無効な予算（テスト用）
            [
                'id' => 7,
                'product_id' => 4,
                'type' => 1,
                'price' => 350,
                'budget_quantity' => 50,
                'is_active' => false, // 無効
                'start_date' => '2025-07-14 00:00:00',
                'end_date' => '2025-08-14 23:59:59',
                'priority' => 2,
                'created' => '2025-07-24 00:00:00',
                'modified' => '2025-07-24 00:00:00',
            ],
        ];
        
        parent::init();
    }
}
