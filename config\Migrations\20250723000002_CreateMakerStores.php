<?php
declare(strict_types=1);

use Migrations\AbstractMigration;

class CreateMakerStores extends AbstractMigration
{
    /**
     * Up Method.
     *
     * More information on this method is available here:
     * https://book.cakephp.org/phinx/0/en/migrations.html#the-up-method
     * @return void
     */
    public function up(): void
    {
        $this->table('maker_stores')
            ->addColumn('maker_id', 'integer', [
                'comment' => 'メーカーID',
                'default' => null,
                'limit' => null,
                'null' => false,
            ])
            ->addColumn('name', 'string', [
                'comment' => '店舗名',
                'default' => null,
                'limit' => 255,
                'null' => false,
            ])
            ->addColumn('zip_code', 'string', [
                'comment' => '郵便番号',
                'default' => null,
                'limit' => 10,
                'null' => true,
            ])
            ->addColumn('prefecture', 'string', [
                'comment' => '都道府県',
                'default' => null,
                'limit' => 50,
                'null' => true,
            ])
            ->addColumn('city', 'string', [
                'comment' => '市区町村',
                'default' => null,
                'limit' => 100,
                'null' => true,
            ])
            ->addColumn('address', 'string', [
                'comment' => '番地以降の住所',
                'default' => null,
                'limit' => 255,
                'null' => true,
            ])
            ->addColumn('building', 'string', [
                'comment' => '建物名・部屋番号',
                'default' => null,
                'limit' => 255,
                'null' => true,
            ])
            ->addColumn('tel', 'string', [
                'comment' => '電話番号',
                'default' => null,
                'limit' => 20,
                'null' => true,
            ])
            ->addColumn('email', 'string', [
                'comment' => 'メールアドレス',
                'default' => null,
                'limit' => 255,
                'null' => true,
            ])
            ->addColumn('business_hours', 'text', [
                'comment' => '営業時間',
                'default' => null,
                'limit' => null,
                'null' => true,
            ])
            ->addColumn('holiday', 'text', [
                'comment' => '定休日',
                'default' => null,
                'limit' => null,
                'null' => true,
            ])
            ->addColumn('access_info', 'text', [
                'comment' => 'アクセス情報',
                'default' => null,
                'limit' => null,
                'null' => true,
            ])
            ->addColumn('description', 'text', [
                'comment' => '店舗説明',
                'default' => null,
                'limit' => null,
                'null' => true,
            ])
            ->addColumn('deleted', 'datetime', [
                'comment' => '削除日時(論理削除)',
                'default' => null,
                'limit' => null,
                'null' => true,
            ])
            ->addColumn('created', 'datetime', [
                'comment' => '作成日時',
                'default' => 'CURRENT_TIMESTAMP',
                'limit' => null,
                'null' => false,
            ])
            ->addColumn('modified', 'datetime', [
                'comment' => '更新日時',
                'default' => 'CURRENT_TIMESTAMP',
                'limit' => null,
                'null' => false,
                'update' => 'CURRENT_TIMESTAMP',
            ])
            ->addIndex(
                [
                    'maker_id',
                ],
                [
                    'name' => 'idx_maker_stores_maker_id',
                ]
            )
            ->addIndex(
                [
                    'name',
                ],
                [
                    'name' => 'idx_maker_stores_name',
                ]
            )
            ->addIndex(
                [
                    'prefecture',
                ],
                [
                    'name' => 'idx_maker_stores_prefecture',
                ]
            )
            ->addIndex(
                [
                    'deleted',
                ],
                [
                    'name' => 'idx_maker_stores_deleted',
                ]
            )
            ->addIndex(
                [
                    'created',
                ],
                [
                    'name' => 'idx_maker_stores_created',
                ]
            )
            ->addForeignKey(
                'maker_id',
                'makers',
                'id',
                [
                    'delete' => 'CASCADE',
                    'update' => 'CASCADE',
                    'constraint' => 'fk_maker_stores_maker_id'
                ]
            )
            ->create();
    }

    /**
     * Down Method.
     *
     * More information on this method is available here:
     * https://book.cakephp.org/phinx/0/en/migrations.html#the-down-method
     * @return void
     */
    public function down(): void
    {
        $this->table('maker_stores')->drop()->save();
    }
}
