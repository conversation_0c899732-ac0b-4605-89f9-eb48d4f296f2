<?php

namespace App\Authentication\Identifier;

use App\Authentication\Identifier\Resolver\UserResolver;
use ArrayAccess;
use Authentication\Identifier\AbstractIdentifier;
use Authentication\Identifier\Resolver\ResolverAwareTrait;

class UserIdentifier extends AbstractIdentifier
{
    use ResolverAwareTrait;

    protected $_defaultConfig = [
        'resolver' => UserResolver::class,
    ];


    public function identify(array $credentials): ArrayAccess|array|null
    {
        return $this->getResolver()->find($credentials);
    }
}
