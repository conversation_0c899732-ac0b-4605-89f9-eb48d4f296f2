{if !is_null($json.member_id)}

    {assign_array var='method_params'         values=''}
    {assign var='method_params.member_id' value=$json.member_id}

    {api_method
            var='response'
            model='Member'
            method='details'
            method_params=$method_params
            version='1'
        }

    {assign_array var='body'            values=''}
    {assign       var='body.member_id' value=$json.member_id}
    {assign       var='body.member_name1' value=$response.details.name1}
    {assign       var='body.member_email' value=$response.details.email}

    {assign_globals key='tmp_data' value=$body}

{/if}

{assign var="processed_json" value=$json}