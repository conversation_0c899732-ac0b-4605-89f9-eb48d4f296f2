<?php
declare(strict_types=1);

use Migrations\AbstractMigration;

class CreateMakers extends AbstractMigration
{
    /**
     * Up Method.
     *
     * More information on this method is available here:
     * https://book.cakephp.org/phinx/0/en/migrations.html#the-up-method
     * @return void
     */
    public function up(): void
    {
        $this->table('makers')
            ->addColumn('name', 'string', [
                'comment' => 'メーカー名',
                'default' => null,
                'limit' => 255,
                'null' => false,
            ])
            ->addColumn('description', 'text', [
                'comment' => 'メーカー説明',
                'default' => null,
                'limit' => null,
                'null' => true,
            ])
            ->addColumn('maker_image_url', 'string', [
                'comment' => 'メーカー画像URL',
                'default' => null,
                'limit' => 500,
                'null' => true,
            ])
            ->addColumn('maker_features_html', 'text', [
                'comment' => 'メーカー特徴（HTML形式）',
                'default' => null,
                'limit' => null,
                'null' => true,
            ])
            ->addColumn('other_features_html', 'text', [
                'comment' => 'その他機能（HTML形式）',
                'default' => null,
                'limit' => null,
                'null' => true,
            ])
            ->addColumn('billing_address', 'text', [
                'comment' => '請求先住所',
                'default' => null,
                'limit' => null,
                'null' => true,
            ])
            ->addColumn('customer_code', 'string', [
                'comment' => '顧客コード',
                'default' => null,
                'limit' => 100,
                'null' => true,
            ])
            ->addColumn('customer_name', 'string', [
                'comment' => '顧客名',
                'default' => null,
                'limit' => 255,
                'null' => true,
            ])
            ->addColumn('billing_cycle', 'integer', [
                'comment' => '請求サイクル（1:末締め翌月末日払い, 2:末締め翌々10日, 3:末締め翌々20日）',
                'default' => 1,
                'limit' => null,
                'null' => false,
            ])
            ->addColumn('contact_name', 'string', [
                'comment' => '担当者名',
                'default' => null,
                'limit' => 255,
                'null' => true,
            ])
            ->addColumn('deleted', 'datetime', [
                'comment' => '削除日時(論理削除)',
                'default' => null,
                'limit' => null,
                'null' => true,
            ])
            ->addColumn('created', 'datetime', [
                'comment' => '作成日時',
                'default' => 'CURRENT_TIMESTAMP',
                'limit' => null,
                'null' => false,
            ])
            ->addColumn('modified', 'datetime', [
                'comment' => '更新日時',
                'default' => 'CURRENT_TIMESTAMP',
                'limit' => null,
                'null' => false,
                'update' => 'CURRENT_TIMESTAMP',
            ])
            ->addIndex(
                [
                    'name',
                ],
                [
                    'name' => 'idx_makers_name',
                ]
            )
            ->addIndex(
                [
                    'customer_code',
                ],
                [
                    'name' => 'idx_makers_customer_code',
                ]
            )
            ->addIndex(
                [
                    'billing_cycle',
                ],
                [
                    'name' => 'idx_makers_billing_cycle',
                ]
            )
            ->addIndex(
                [
                    'deleted',
                ],
                [
                    'name' => 'idx_makers_deleted',
                ]
            )
            ->addIndex(
                [
                    'created',
                ],
                [
                    'name' => 'idx_makers_created',
                ]
            )
            ->create();
    }

    /**
     * Down Method.
     *
     * More information on this method is available here:
     * https://book.cakephp.org/phinx/0/en/migrations.html#the-down-method
     * @return void
     */
    public function down(): void
    {
        $this->table('makers')->drop()->save();
    }
}
