<?php

namespace App\Service;

use App\Kuroko\Entity\IKurokoEntity;
use Cake\Datasource\EntityInterface;
use Cake\Form\Form;

/**
 * APIと連携するServiceに実装する
 */
interface IRestService extends IService
{
    /**
     * @param array $data
     * @return EntityInterface|IKurokoEntity|null
     */
    public function add(array $data = []): EntityInterface|null|IKurokoEntity;

    /**
     * @param string $id
     * @param array $data
     * @return EntityInterface|IKurokoEntity|null
     */
    public function edit(string $id, array $data = []): EntityInterface|null|IKurokoEntity;

    /**
     * @param string $id
     * @param array $data
     * @return EntityInterface|IKurokoEntity|null
     */
    public function view(string $id, array $data = []): EntityInterface|null|IKurokoEntity;

    /**
     * @param array $data
     * @return array|EntityInterface[]|null
     */
    public function index(array $data = []): array|EntityInterface|null|IKurokoEntity;

    /**
     * @param string $id
     * @param array $data
     * @return bool
     */
    public function delete(string $id, array $data = []): bool;


    /**
     * @return array
     */
    public function getErrors(): array;

    /**
     * @param array $errors
     */
    public function setErrors(array $errors): void;

    /**
     * Returns whether the result represents a successful authentication attempt.
     *
     * @return bool
     */
    public function isValid(): bool;


    public function setForm(Form $form): IRestService;

    public function getForm(): Form;

}
