import axios from "axios";
// import https from "https";
import type { RuntimeConfig } from "nuxt/schema";
import { TOKEN_HEADER_KEY } from "~~/src/config";

// const httpsAgent = new https.Agent({
//     rejectUnauthorized: false, // 証明書検証を無視
// });
// const runtimeConfig = useRuntimeConfig();
/**
 * @deprecated
 */
export default abstract class KurokoApiClient {
    protected config: RuntimeConfig;

    constructor(protected runTimeConfig: RuntimeConfig) {
        this.config = runTimeConfig;
    }

    // 静的メソッドでサブクラスのインスタンスを生成
    static create<T extends KurokoApiClient>(
        this: new (config: RuntimeConfig) => T,
        runTimeConfig: RuntimeConfig,
    ): T {
        return new this(runTimeConfig);
    }

    protected abstract getResource(): string;

    private getHeader(): ObjType {
        return {
            [TOKEN_HEADER_KEY]: String(this.config.apiSecret),
            // "e98b643e84c5dbff41ee0ee209043c54ac3f00f207a4cc7cdecd786100064916",
        };
    }

    // private async request(): Promise<never> {
    //     const response = await axios.get(
    //         `${this.config.public.apiBase}${this.getResource()}`,
    //         {
    //             headers: this.getHeader(),
    //             withCredentials: true, // 認証情報を含めるために true に設定
    //             // httpsAgent,
    //         },
    //     );
    //     return new Promise(response.data);
    // }

    public async list<T>(params?: Record<string, string | number>): Promise<T> {
        // console.log(
        //     "====================================",
        //     this.getHeader(),
        //     "====================================",
        //     `${this.config.public.apiBase}1/${this.getResource()}/list`,
        // );
        const response = await axios.get(
            `${this.config.public.apiBase}${this.getResource()}/list`,
            {
                headers: this.getHeader(),
                withCredentials: true, // 認証情報を含めるために true に設定
                // httpsAgent,
                params,
            },
        );
        return response.data as T;
    }
}

// GET
// https://swb-sbpg.g.kuroco.app/rcms-api/1/content/list
//     Header
// key：X-RCMS-API-ACCESS-TOKEN
// value：f4eb92aef3293a0e6dc8a2b20fa42e006774a1f8580f423a8256cb80b5f51fa3
