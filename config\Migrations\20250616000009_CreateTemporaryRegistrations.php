<?php
declare(strict_types=1);

use Migrations\AbstractMigration;

class CreateTemporaryRegistrations extends AbstractMigration
{
    /**
     * Up Method.
     *
     * More information on this method is available here:
     * https://book.cakephp.org/phinx/0/en/migrations.html#the-up-method
     * @return void
     */
    public function up(): void
    {
        $this->table('temporary_registrations')
            ->addColumn('email', 'string', [
                'comment' => 'メールアドレス',
                'default' => null,
                'limit' => 255,
                'null' => true,
            ])
            ->addColumn('mask_password', 'text', [
                'comment' => 'パスワード（暗号化）',
                'default' => null,
                'limit' => null,
                'null' => true,
            ])
            ->addColumn('mask_email', 'text', [
                'comment' => 'メールアドレス（暗号化）',
                'default' => null,
                'limit' => null,
                'null' => false,
            ])
            ->addColumn('profile_data', 'text', [
                'comment' => 'プロフィールデータJSON（暗号化）',
                'default' => null,
                'limit' => null,
                'null' => true,
            ])
            ->addColumn('survey_data', 'text', [
                'comment' => 'アンケートデータJSON（暗号化）',
                'default' => null,
                'limit' => null,
                'null' => true,
            ])
            ->addColumn('product_ids', 'json', [
                'comment' => '商品ID配列（JSON形式: [1,2,3]）',
                'default' => null,
                'limit' => null,
                'null' => false,
            ])
            ->addColumn('verification_token', 'string', [
                'comment' => '認証トークン',
                'default' => null,
                'limit' => 255,
                'null' => false,
            ])
            ->addColumn('is_verified', 'boolean', [
                'comment' => '認証済みフラグ（0: 未認証, 1: 認証済み）',
                'default' => false,
                'limit' => null,
                'null' => false,
            ])
            ->addColumn('expires', 'datetime', [
                'comment' => '有効期限',
                'default' => null,
                'limit' => null,
                'null' => false,
            ])
            ->addColumn('created', 'datetime', [
                'comment' => '作成日時',
                'default' => 'CURRENT_TIMESTAMP',
                'limit' => null,
                'null' => false,
            ])
            ->addColumn('modified', 'datetime', [
                'comment' => '更新日時',
                'default' => 'CURRENT_TIMESTAMP',
                'limit' => null,
                'null' => false,
                'update' => 'CURRENT_TIMESTAMP',
            ])
            ->addIndex(
                [
                    'email',
                ],
                [
                    'name' => 'idx_temporary_registrations_email',
                ]
            )
            ->addIndex(
                [
                    'verification_token',
                ],
                [
                    'name' => 'uk_temporary_registrations_verification_token',
                    'unique' => true,
                ]
            )
            ->addIndex(
                [
                    'is_verified',
                ],
                [
                    'name' => 'idx_temporary_registrations_is_verified',
                ]
            )
            ->addIndex(
                [
                    'expires',
                ],
                [
                    'name' => 'idx_temporary_registrations_expires',
                ]
            )
            ->addIndex(
                [
                    'created',
                ],
                [
                    'name' => 'idx_temporary_registrations_created',
                ]
            )
            ->create();
    }

    /**
     * Down Method.
     *
     * More information on this method is available here:
     * https://book.cakephp.org/phinx/0/en/migrations.html#the-down-method
     * @return void
     */
    public function down(): void
    {
        $this->table('temporary_registrations')->drop()->save();
    }
}
