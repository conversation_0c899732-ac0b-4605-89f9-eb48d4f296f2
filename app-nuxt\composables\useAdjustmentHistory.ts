type ChangeValue = {
    before?: number | string;
    after: number | string;
};

type Changes = Record<string, ChangeValue>;

export const useAdjustmentHistory = () => {
    const formatChanges = (changes: Changes | null): string => {
        if (!changes) return '';

        const formatters = {
            adjustment_note: {
                label: '備考',
                format: (value: string) => value
            },
            adjustment_quantity: {
                label: '数量',
                format: (value: number) => `${value}`
            },
            adjustment_unit_price: {
                label: '単価',
                format: (value: number) => `${value}円`
            },
        };

        return Object.entries(changes)
            .map(([key, value]) => {
                const formatter = formatters[key as keyof typeof formatters];
                if (!formatter) return null;

                const { label, format } = formatter;
                const before = value.before !== undefined ? format(value.before as never) : '';
                const after = format(value.after as never);

                return `${label}: ${before}${before ? ' → ' : ''}${after}`;
            })
            .filter(Boolean)
            .join('\n');
    };

    const getActionTypeLabel = (actionType: number): string => {
        return {
            1: '作成',
            2: '変更',
            3: '確定'
        }[actionType] || '不明';
    };

    return {
        formatChanges,
        getActionTypeLabel
    };
}; 