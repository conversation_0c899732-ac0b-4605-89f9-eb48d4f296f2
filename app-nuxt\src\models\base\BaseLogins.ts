import { Model } from "~~/src/models/Model";
import type ClientAuthenticationsClient from "~/src/lib/http/coverme-front/ClientAuthenticationsClient";
import type UserAuthenticationsClient from "~/src/lib/http/coverme-front/UserAuthenticationsClient";
import type SwbAuthenticationsClient from "~/src/lib/http/coverme-front/SwbAuthenticationsClient";

export abstract class BaseLogins extends Model {
    protected abstract client:
        | ClientAuthenticationsClient
        | UserAuthenticationsClient
        | SwbAuthenticationsClient;

    public add(data: TLogin): Promise<TResponseAuth | TResponseAuthError> {
        const config = this.config;
        return new Promise((resolve) => {
            this.client
                .create(config)
                .post<TLogin, TResponseAuth | TResponseAuthError>(data)
                .then((response) => resolve(response))
                .catch((error) => resolve(error.response?.data || {
                    success: false,
                    message: "ネットワークエラーが発生しました",
                    code: "NETWORK_ERROR"
                }));
        });
    }

    public delete(id: number): Promise<boolean> {
        const config = this.config;
        return new Promise((resolve) => {
            this.client
                .create(config)
                .delete<TResponseSuccess>(id)
                .then(({ success }) => resolve(!!success))
                .catch(() => resolve(false));
        });
    }
}
