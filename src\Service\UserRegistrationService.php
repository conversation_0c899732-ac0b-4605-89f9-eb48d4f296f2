<?php
declare(strict_types=1);

namespace App\Service;

use App\Model\Table\GeneralUsersTable;
use App\Model\Table\UserProfilesTable;
use App\Model\Table\UserSurveysTable;
use App\Model\Table\TemporaryRegistrationsTable;
use App\Model\Entity\GeneralUser;
use App\Model\Entity\UserProfile;
use App\Model\Entity\UserSurvey;
use App\Model\Entity\TemporaryRegistration;
use Cake\ORM\TableRegistry;
use Cake\Datasource\ConnectionManager;
use Cake\Log\Log;
use Exception;

/**
 * ユーザー登録サービス
 * 
 * 一般ユーザーの仮登録・本登録処理を管理するサービスクラス
 */
class UserRegistrationService
{
    private GeneralUsersTable $generalUsersTable;
    private UserProfilesTable $userProfilesTable;
    private UserSurveysTable $userSurveysTable;
    private TemporaryRegistrationsTable $temporaryRegistrationsTable;
    private AuthenticationService $authService;

    public function __construct()
    {
        $this->generalUsersTable = TableRegistry::getTableLocator()->get('GeneralUsers');
        $this->userProfilesTable = TableRegistry::getTableLocator()->get('UserProfiles');
        $this->userSurveysTable = TableRegistry::getTableLocator()->get('UserSurveys');
        $this->temporaryRegistrationsTable = TableRegistry::getTableLocator()->get('TemporaryRegistrations');
        $this->authService = new AuthenticationService();
    }

    /**
     * 仮登録処理
     * 
     * @param array $registrationData 登録データ
     * @return TemporaryRegistration|null 仮登録エンティティ
     */
    public function createTemporaryRegistration(array $registrationData): ?TemporaryRegistration
    {
        try {
            $email = $registrationData['email'];

            // 既存ユーザーのチェック
            if ($this->generalUsersTable->findByEmail($email)->first()) {
                Log::warning("User already exists: {$email}");
                return null;
            }

            // 同一メールアドレスの古い仮登録を無効化
            $this->temporaryRegistrationsTable->invalidateOldRegistrations($email);

            // 仮登録データの作成
            $tempRegistration = $this->temporaryRegistrationsTable->createTemporaryRegistration([
                'email' => $email,
                'password' => $registrationData['password'] ?? null,
                'profile_data' => $registrationData['profile_data'],
                'survey_data' => $registrationData['survey_data'],
                'product_ids' => $registrationData['product_ids']
            ]);

            if ($tempRegistration) {
                Log::info("Temporary registration created for: {$email}");
                return $tempRegistration;
            }

            return null;
        } catch (Exception $e) {
            Log::error("Temporary registration failed: " . $e->getMessage(). $e->getTraceAsString());
            return null;
        }
    }

    /**
     * 本登録処理（メール認証完了後）
     *
     * @param string $verificationToken 認証トークン
     * @param string|null $password パスワード（nullの場合は仮登録時のパスワードを使用）
     * @return array|null 登録データ
     */
    public function completeRegistration(string $verificationToken, ?string $password = null): ?array
    {
        $connection = ConnectionManager::get('default');
        
        try {
            return $connection->transactional(function () use ($verificationToken, $password) {
                // 仮登録データの取得
                $tempRegistration = $this->temporaryRegistrationsTable->findByVerificationToken($verificationToken);
                
                if (!$tempRegistration) {
                    Log::warning("Invalid verification token: {$verificationToken}");
                    return null;
                }

                // パスワードの取得（仮登録時のパスワードまたは引数のパスワード）
                $userPassword = $password ?? $tempRegistration->password;

                if (empty($userPassword)) {
                    Log::warning("No password available for registration");
                    return null;
                }

                // パスワードの妥当性チェック
                $passwordErrors = $this->authService->validatePassword($userPassword);
                if (!empty($passwordErrors)) {
                    Log::warning("Password validation failed: " . implode(', ', $passwordErrors));
                    return null;
                }

                // 一般ユーザーの作成
                $user = $this->generalUsersTable->newEntity([
                    'email' => $tempRegistration->email,
                    'password' => $userPassword, // エンティティで自動ハッシュ化
                ]);

                $user = $this->generalUsersTable->save($user);
                if (!$user) {
                    throw new Exception('Failed to create user');
                }

                // プロフィール情報の作成
                $profileData = $tempRegistration->array_decrypted_profile_data;
                $profileData['general_user_id'] = $user->id;
                
                $userProfile = $this->userProfilesTable->newEntity($profileData);
                if (!$this->userProfilesTable->save($userProfile)) {
                    throw new Exception('Failed to create user profile');
                }

                // アンケート情報の作成
                $surveyData = $tempRegistration->array_decrypted_survey_data;
                $surveyData['general_user_id'] = $user->id;
                
                $userSurvey = $this->userSurveysTable->newEntity($surveyData);
                if (!$this->userSurveysTable->save($userSurvey)) {
                    throw new Exception('Failed to create user survey');
                }

                // 仮登録データを認証済みにマーク
                $tempRegistration->markAsVerified();
                $this->temporaryRegistrationsTable->save($tempRegistration);

                $registrationData = [
                    'user' => $user,
                    'temp_registration' => $tempRegistration,
                    'profile' => $userProfile,
                    'survey' => $userSurvey
                ];

                Log::info("User registration completed: {$user->email}");
                return $registrationData;
            });
        } catch (Exception $e) {
            Log::error("Registration completion failed: " . $e->getMessage());
            return null;
        }
    }

    /**
     * 仮登録の認証トークンを検証
     */
    public function verifyRegistrationToken(string $token): ?TemporaryRegistration
    {
        return $this->temporaryRegistrationsTable->findByVerificationToken($token);
    }

    /**
     * メールアドレスの重複チェック
     */
    public function isEmailAvailable(string $email): bool
    {
        return !$this->generalUsersTable->findByEmail($email)->first();
    }

    /**
     * 期限切れ仮登録データのクリーンアップ
     */
    public function cleanupExpiredRegistrations(): int
    {
        return $this->temporaryRegistrationsTable->cleanupExpiredRegistrations();
    }

    /**
     * 登録データの妥当性チェック
     */
    public function validateRegistrationData(array $data): array
    {
        $errors = [];

        // メールアドレスのチェック
        if (empty($data['email']) || !filter_var($data['email'], FILTER_VALIDATE_EMAIL)) {
            $errors['email'] = 'メールアドレスが正しくありません';
        } elseif (!$this->isEmailAvailable($data['email'])) {
            $errors['email'] = 'このメールアドレスは既に登録されています';
        }

        // パスワードのチェック
        if (empty($data['password'])) {
            $errors['password'] = 'パスワードは必須です';
        } else {
            $passwordErrors = $this->authService->validatePassword($data['password']);
            if (!empty($passwordErrors)) {
                $errors['password'] = implode(', ', $passwordErrors);
            }
        }

        // プロフィールデータのチェック
        if (empty($data['profile_data']) || !is_string($data['profile_data'])) {
            $errors['profile_data'] = 'プロフィール情報は必須です';
        }

        // アンケートデータのチェック
        if (empty($data['survey_data']) || !is_string($data['survey_data'])) {
            $errors['survey_data'] = 'アンケート情報は必須です';
        }

        // 商品IDのチェック
        if (empty($data['product_ids']) || !is_string($data['product_ids'])) {
            $errors['product_ids'] = '商品選択は必須です';
        }

        return $errors;
    }
}
