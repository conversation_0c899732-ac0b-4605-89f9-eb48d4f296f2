import { defineStore } from "pinia";

const TTL_HOURS = 24;

export const useAuthStore = defineStore("auth", {
    state: () => ({
        accessToken:
            (typeof window !== "undefined" &&
                localStorage.getItem("accessToken")) ||
            null,
        groupIds:
            (typeof window !== "undefined" &&
                JSON.parse(localStorage.getItem("groupIds") || "[]")) ||
            [],
    }),
    actions: {
        setAuth(token: string, groupIds: string[] = []) {
            this.accessToken = token;
            this.groupIds = groupIds;

            if (typeof window !== "undefined") {
                const expiryTime =
                    new Date().getTime() + TTL_HOURS * 60 * 60 * 1000; // 現在時刻 + 24時間
                localStorage.setItem("accessToken", token);
                localStorage.setItem("groupIds", JSON.stringify(groupIds));
                localStorage.setItem(
                    "accessTokenExpiry",
                    expiryTime.toString(),
                );
            }
        },
        clearAuth() {
            this.accessToken = null;
            this.groupIds = [];
            if (typeof window !== "undefined") {
                localStorage.removeItem("accessToken");
                localStorage.removeItem("groupIds");
                localStorage.removeItem("accessTokenExpiry");
            }
        },
        getAccessToken() {
            if (typeof window !== "undefined") {
                const expiry = localStorage.getItem("accessTokenExpiry");
                const now = new Date().getTime();

                if (expiry && now > parseInt(expiry, 10)) {
                    this.clearAuth();
                    return null;
                }

                if (!this.accessToken) {
                    this.accessToken = localStorage.getItem("accessToken");
                }
            }
            return this.accessToken;
        },
        hasGroup(groupId: string): boolean {
            if (typeof window !== "undefined" && this.groupIds.length === 0) {
                const storedGroupIds = localStorage.getItem("groupIds");
                if (storedGroupIds) {
                    this.groupIds = JSON.parse(storedGroupIds);
                }
            }
            return this.groupIds.includes(groupId);
        }
    },
});

export const isLogin = (): boolean => !!useAuthStore().getAccessToken();
