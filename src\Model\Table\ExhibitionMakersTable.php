<?php
declare(strict_types=1);

namespace App\Model\Table;

use Cake\ORM\Query;
use Cake\ORM\RulesChecker;
use Cake\ORM\Table;
use Cake\Validation\Validator;

/**
 * 展示会メーカーテーブル
 */
class ExhibitionMakersTable extends Table
{
    public function initialize(array $config): void
    {
        parent::initialize($config);

        $this->setTable('exhibition_makers');
        $this->setDisplayField('id');
        $this->setPrimaryKey('id');

        // アソシエーション
        $this->belongsTo('Exhibitions', [
            'foreignKey' => 'exhibition_id',
            'joinType' => 'INNER',
        ]);
        
        $this->belongsTo('Makers', [
            'foreignKey' => 'maker_id',
            'joinType' => 'INNER',
        ]);
    }

    public function validationDefault(Validator $validator): Validator
    {
        $validator
            ->integer('exhibition_id')
            ->requirePresence('exhibition_id', 'create')
            ->notEmptyString('exhibition_id');

        $validator
            ->integer('maker_id')
            ->requirePresence('maker_id', 'create')
            ->notEmptyString('maker_id');

        return $validator;
    }

    public function buildRules(RulesChecker $rules): RulesChecker
    {
        $rules->add($rules->existsIn(['exhibition_id'], 'Exhibitions'), __('指定された展示会が存在しません'));
        $rules->add($rules->existsIn(['maker_id'], 'Makers'), __('指定されたメーカーが存在しません'));
        
        // 同一展示会に同一メーカーが重複登録されないようにチェック
        $rules->add($rules->isUnique(['exhibition_id', 'maker_id']), __('このメーカーは既にこの展示会に登録されています'));

        return $rules;
    }

    /**
     * 展示会別にメーカーを取得するファインダー
     */
    public function findByExhibition(Query $query, array $options): Query
    {
        $exhibitionId = $options['exhibition_id'] ?? null;
        if ($exhibitionId !== null) {
            $query->where(['exhibition_id' => $exhibitionId]);
        }
        return $query;
    }

    /**
     * メーカー別に展示会を取得するファインダー
     */
    public function findByMaker(Query $query, array $options): Query
    {
        $makerId = $options['maker_id'] ?? null;
        if ($makerId !== null) {
            $query->where(['maker_id' => $makerId]);
        }
        return $query;
    }

    /**
     * 指定展示会の参加メーカー一覧を取得
     */
    public function getMakersByExhibition(int $exhibitionId): Query
    {
        return $this->find()
            ->where(['exhibition_id' => $exhibitionId])
            ->contain(['Makers'])
            ->order(['Makers.name' => 'ASC']);
    }

    /**
     * 指定メーカーの参加展示会一覧を取得
     */
    public function getExhibitionsByMaker(int $makerId): Query
    {
        return $this->find()
            ->where(['maker_id' => $makerId])
            ->contain(['Exhibitions'])
            ->order(['Exhibitions.start_datetime' => 'ASC']);
    }

    /**
     * 展示会とメーカーの関連を追加
     */
    public function addMakerToExhibition(int $exhibitionId, int $makerId): bool
    {
        // 既に関連が存在するかチェック
        $existing = $this->find()
            ->where([
                'exhibition_id' => $exhibitionId,
                'maker_id' => $makerId
            ])
            ->first();

        if ($existing) {
            return false; // 既に存在する
        }

        $exhibitionMaker = $this->newEntity([
            'exhibition_id' => $exhibitionId,
            'maker_id' => $makerId
        ]);

        return $this->save($exhibitionMaker) !== false;
    }

    /**
     * 展示会とメーカーの関連を削除
     */
    public function removeMakerFromExhibition(int $exhibitionId, int $makerId): bool
    {
        $exhibitionMaker = $this->find()
            ->where([
                'exhibition_id' => $exhibitionId,
                'maker_id' => $makerId
            ])
            ->first();

        if (!$exhibitionMaker) {
            return false; // 関連が存在しない
        }

        return $this->delete($exhibitionMaker);
    }
}
