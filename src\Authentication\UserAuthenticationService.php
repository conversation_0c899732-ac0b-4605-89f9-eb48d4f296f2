<?php

namespace App\Authentication;

use App\Authentication\Authenticator\UserAuthenticator;
use App\Authentication\Identifier\UserIdentifier;
use Authentication\AuthenticationService;

class UserAuthenticationService extends AuthenticationService
{
    const ALLOWED_GROUP = "allowedGroup";
    const CHECK_STATUS_FLAG = "checkStatus";

    /**
     * @param array $config
     */
    public function __construct(array $config = [])
    {
        parent::__construct($config);
        $this->_setup($config);
    }

    /**
     * @param array $config
     * @return void
     */
    protected function _setup(array $config = []): void
    {
        $this->loadIdentifier(UserIdentifier::class, $config);
        $this->loadAuthenticator(UserAuthenticator::class, $config);
    }
}
