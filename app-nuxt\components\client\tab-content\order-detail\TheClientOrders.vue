<script setup lang="ts">
import { ref } from "vue";
import { ClientMyBridge } from "~/src/models/bridge/ClientMyBridge";
import { ClientOrders } from "~/src/lib/functions/ClientOrders";
import type RandselOrder from "~/src/models/entry/RandselOrder";

const props = defineProps<{
    bridge: ClientMyBridge;
    hideApproval?: boolean; // ボタン表示フラグ
}>();

const selectedOrders = ref([]);
const pageContentNum = 20;
const dataLoadingText = "注文情報を読み込んでいます";

const headers = [
    {
        title: "注文ID",
        key: "id",
        value: "id",
        sortable: true,
    },
    {
        title: "注文日時",
        key: "display_created",
        value: "display_created",
        sortable: true,
    },
    {
        title: "ステータス",
        key: "display_status",
        value: "display_status",
        sortable: true,
    },
    {
        title: "カタログ名",
        key: "product_name",
        value: "product_name",
        sortable: true,
    },
    { title: "お名前", key: "name1", value: "name1", sortable: true },
    {
        title: "都道府県",
        key: "display_tdfk_cd",
        value: "display_tdfk_cd",
        sortable: true,
    },
];

const approval = async (): Promise<void> => {
    if (selectedOrders.value.length && props.bridge.member) {
        await ClientOrders.approvalOrders(selectedOrders.value, props.bridge);
        selectedOrders.value = [];
    }
};
const deny = async (): Promise<void> => {
    if (selectedOrders.value.length && props.bridge.member) {
        await ClientOrders.denyOrders(selectedOrders.value, props.bridge);
        selectedOrders.value = [];
    }
};
</script>

<template>
    <v-container>
        <v-row v-if="hideApproval !== true" justify="end" class="mt-4 mb-2">
            <v-btn
                class="csv-dl-btn mx-2"
                density="compact"
                :disabled="
                    !bridge.is_order_loaded || selectedOrders.length === 0
                "
                elevation="0"
                @click="approval"
            >
                <v-icon size="large" class="mr-1">mdi-circle-double</v-icon>
                承認
            </v-btn>
            <v-btn
                class="csv-dl-btn mx-2"
                density="compact"
                :disabled="
                    !bridge.is_order_loaded || selectedOrders.length === 0
                "
                elevation="0"
                @click="deny"
            >
                <v-icon size="large" class="mr-1">mdi-window-close</v-icon>
                否認
            </v-btn>
        </v-row>
        <v-data-table
            v-model="selectedOrders"
            :headers="headers"
            :loading="!bridge.is_order_loaded"
            :loading-text="dataLoadingText"
            :items="bridge.randsel_orders"
            :items-per-page="pageContentNum"
            :item-selectable="(item: RandselOrder) => item.status === 0"
            item-value="ec_order_id"
            :show-select="hideApproval !== true"
            return-object
            items-per-page-text="１ページの表示件数"
        >
            <template #top />
        </v-data-table>
    </v-container>
</template>

<style scoped>
.csv-dl-btn {
    background-color: #85bd2c;
    color: white;
    font-weight: bold;
    padding: 15px;
    align-content: center;
    transition: opacity 0.3s ease;
}
.csv-dl-btn:hover {
    opacity: 0.7;
}
.csv-dl-btn:disabled {
    background-color: #b0b0b0 !important;
    color: white !important;
}
</style>
