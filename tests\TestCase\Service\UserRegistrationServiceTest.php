<?php
declare(strict_types=1);

namespace App\Test\TestCase\Service;

use App\Service\UserRegistrationService;
use App\Model\Entity\TemporaryRegistration;
use App\Model\Entity\GeneralUser;
use Cake\TestSuite\TestCase;
use Cake\ORM\TableRegistry;
use Cake\I18n\FrozenTime;

/**
 * UserRegistrationService Test Case
 */
class UserRegistrationServiceTest extends TestCase
{
    /**
     * Test fixtures
     */
    protected $fixtures = [
        'app.GeneralUsers',
        'app.UserProfiles',
        'app.UserSurveys',
        'app.TemporaryRegistrations',
    ];

    private UserRegistrationService $registrationService;

    public function setUp(): void
    {
        parent::setUp();
        $this->registrationService = new UserRegistrationService();
    }

    public function tearDown(): void
    {
        unset($this->registrationService);
        parent::tearDown();
    }

    /**
     * 仮登録作成テスト
     */
    public function testCreateTemporaryRegistration1(): void
    {
        $registrationData = [
            'email' => '<EMAIL>',
            'password' => 'SecurePassword123!',
            'profile_data' => json_encode([
                'last_name' => '山田',
                'first_name' => '太郎',
                'last_name_kana' => 'ヤマダ',
                'first_name_kana' => 'タロウ',
                'zip_code' => '123-4567',
                'prefecture_code' => '13',
                'address1' => '東京都渋谷区',
                'address2' => '１番地',
                'tel' => '03-1234-5678'
            ]),
            'survey_data' => json_encode([
                'year' => 2025,
                'child_sex' => 1,
                'budget' => 1
            ]),
            'product_ids' => json_encode([1, 2, 3])
        ];

        $result = $this->registrationService->createTemporaryRegistration($registrationData);

        debug($result);

        $this->assertInstanceOf(TemporaryRegistration::class, $result);
        $this->assertEquals('<EMAIL>', $result->email);
        $this->assertEquals('SecurePassword123!', $result->password);
        $this->assertNotEmpty($result->verification_token);
        $this->assertFalse($result->is_verified);
        $this->assertTrue($result->isValid());

        // 暗号化されたデータが正しく保存されているかチェック
        $this->assertNotEmpty($result->mask_password);
        $this->assertNotEmpty($result->mask_email);
        $this->assertNotEmpty($result->profile_data);
        $this->assertNotEmpty($result->survey_data);
    }

    /**
     * 既存ユーザーでの仮登録テスト
     */
    public function testCreateTemporaryRegistrationWithExistingUser(): void
    {
        // 既存ユーザーを作成
        $generalUsersTable = TableRegistry::getTableLocator()->get('GeneralUsers');
        $existingUser = $generalUsersTable->newEntity([
            'email' => '<EMAIL>',
            'password' => 'password123',
        ]);
        $generalUsersTable->save($existingUser);

        $registrationData = [
            'email' => '<EMAIL>',
            'password' => 'SecurePassword123!',
            'profile_data' => json_encode(['test' => 'data']),
            'survey_data' => json_encode(['test' => 'data']),
            'product_ids' => json_encode([1, 2, 3])
        ];

        $result = $this->registrationService->createTemporaryRegistration($registrationData);
        debug($result);

        // 既存ユーザーの場合は仮登録できない
        $this->assertNull($result);
    }

    /**
     * 本登録完了テスト
     */
    public function testCompleteRegistration2(): void
    {
        // 仮登録データを作成
        $tempRegistrationsTable = TableRegistry::getTableLocator()->get('TemporaryRegistrations');
        $tempRegistration = $tempRegistrationsTable->createTemporaryRegistration([
            'email' => '<EMAIL>',
            'password' => 'TempPassword123!',
            'profile_data' => json_encode([
                'last_name' => '山田',
                'first_name' => '太郎',
                'last_name_kana' => 'ヤマダ',
                'first_name_kana' => 'タロウ',
                'zip_code' => '123-4567',
                'prefecture_code' => '13',
                'address1' => '東京都渋谷区',
                'address2' => '○○ビル',
                'tel' => '03-1234-5678'
            ]),
            'survey_data' => json_encode([
                'year' => 2025,
                'child_sex' => 1,
                'budget' => 1
            ]),
            'product_ids' => json_encode([1, 2, 3])
        ]);

        // 仮登録時のパスワードを使用（引数にnullを渡す）
        $result = $this->registrationService->completeRegistration(
            $tempRegistration->verification_token
        );

        debug($result);

        // $this->assertInstanceOf(Array::class, $result);
        $this->assertEquals('<EMAIL>', $result['user']->email);
        $this->assertNotNull($result['user']->password);

        // プロフィールとアンケートが作成されているかチェック
        $userProfilesTable = TableRegistry::getTableLocator()->get('UserProfiles');
        $userSurveysTable = TableRegistry::getTableLocator()->get('UserSurveys');

        $profile = $userProfilesTable->find()->where(['general_user_id' => $result['user']->id])->first();
        $survey = $userSurveysTable->find()->where(['general_user_id' => $result['user']->id])->first();

        $this->assertNotNull($profile);
        $this->assertNotNull($survey);

        debug($profile);
        debug($survey);

        // 仮登録が認証済みになっているかチェック
        $tempRegistrationsTable = TableRegistry::getTableLocator()->get('TemporaryRegistrations');
        $updatedTempRegistration = $tempRegistrationsTable->get($tempRegistration->id);
        $this->assertTrue($updatedTempRegistration->is_verified);
    }



    /**
     * 弱いパスワードでの本登録テスト
     */
    public function testCompleteRegistrationWithWeakPassword(): void
    {
        // 仮登録データを作成
        $tempRegistrationsTable = TableRegistry::getTableLocator()->get('TemporaryRegistrations');
        $tempRegistration = $tempRegistrationsTable->createTemporaryRegistration([
            'email' => '<EMAIL>',
            'password' => 'TempPassword123!',
            'profile_data' => json_encode(['test' => 'data']),
            'survey_data' => json_encode(['test' => 'data']),
            'product_ids' => json_encode([1, 2, 3])
        ]);

        $result = $this->registrationService->completeRegistration(
            $tempRegistration->verification_token,
            'weak' // 弱いパスワード
        );

        $this->assertNull($result);
    }

    /**
     * メールアドレス利用可能性チェックテスト
     */
    public function testIsEmailAvailable(): void
    {
        // 新しいメールアドレスは利用可能
        $this->assertTrue($this->registrationService->isEmailAvailable('<EMAIL>'));

        // 既存ユーザーを作成
        $generalUsersTable = TableRegistry::getTableLocator()->get('GeneralUsers');
        $existingUser = $generalUsersTable->newEntity([
            'email' => '<EMAIL>',
            'password' => 'password123',
        ]);
        $generalUsersTable->save($existingUser);

        // 既存のメールアドレスは利用不可
        $this->assertFalse($this->registrationService->isEmailAvailable('<EMAIL>'));
    }

    /**
     * 登録データ妥当性チェックテスト
     */
    public function testValidateRegistrationData(): void
    {
        // 有効なデータ
        $validData = [
            'email' => '<EMAIL>',
            'password' => 'ValidPassword123!',
            'profile_data' => json_encode(['test' => 'data']),
            'survey_data' => json_encode(['test' => 'data']),
            'product_ids' => json_encode([1, 2, 3])
        ];

        $errors = $this->registrationService->validateRegistrationData($validData);
        $this->assertEmpty($errors);

        // 無効なメールアドレス
        $invalidData = [
            'email' => 'invalid-email',
            'password' => 'ValidPassword123!',
            'profile_data' => json_encode(['test' => 'data']),
            'survey_data' => json_encode(['test' => 'data']),
            'product_ids' => json_encode([1, 2, 3])
        ];

        $errors = $this->registrationService->validateRegistrationData($invalidData);
        $this->assertArrayHasKey('email', $errors);

        // 必須フィールドなし
        $incompleteData = [
            'email' => '<EMAIL>'
        ];

        $errors = $this->registrationService->validateRegistrationData($incompleteData);
        debug($errors);
        $this->assertArrayHasKey('password', $errors);
        $this->assertArrayHasKey('profile_data', $errors);
        $this->assertArrayHasKey('survey_data', $errors);
        $this->assertArrayHasKey('product_ids', $errors);
    }

    /**
     * 仮登録時のパスワード暗号化テスト
     */
    public function testCreateTemporaryRegistrationPasswordEncryption(): void
    {
        $registrationData = [
            'email' => '<EMAIL>',
            'password' => 'TestPassword123!',
            'profile_data' => json_encode(['test' => 'data']),
            'survey_data' => json_encode(['test' => 'data']),
            'product_ids' => json_encode([1, 2, 3])
        ];

        $result = $this->registrationService->createTemporaryRegistration($registrationData);

        $this->assertNotNull($result);
        $this->assertEquals('TestPassword123!', $result->password);

        // 内部的に暗号化されていることを確認
        $encryptedData = $result->getOriginal('mask_password');
        debug($encryptedData);
        $this->assertNotNull($encryptedData);
    }

    /**
     * 本登録時の仮登録パスワード使用テスト
     */
    public function testCompleteRegistrationWithStoredPassword(): void
    {
        // 仮登録データを作成
        $tempRegistrationsTable = TableRegistry::getTableLocator()->get('TemporaryRegistrations');
        $tempRegistration = $tempRegistrationsTable->createTemporaryRegistration([
            'email' => '<EMAIL>',
            'password' => 'StoredPassword123!',
            'profile_data' => json_encode([
                'last_name' => '保存',
                'first_name' => 'パスワード',
                'last_name_kana' => 'ホゾン',
                'first_name_kana' => 'パスワード',
                'zip_code' => '123-4567',
                'prefecture_code' => '13',
                'address1' => '東京都渋谷区',
                'address2' => '１番地',
                'tel' => '03-1234-5678'
            ]),
            'survey_data' => json_encode([
                'year' => 2025,
                'child_sex' => 1,
                'budget' => 1
            ]),
            'product_ids' => json_encode([1, 2, 3])
        ]);

        // 仮登録時のパスワードを使用して本登録
        $result = $this->registrationService->completeRegistration($tempRegistration->verification_token);

        debug($result);

        $this->assertEquals('<EMAIL>', $result['user']->email);
        $this->assertNotNull($result['user']->password);
    }

    /**
     * カスタムパスワードでの本登録テスト
     */
    public function testCompleteRegistrationWithCustomPassword(): void
    {
        // 仮登録データを作成
        $tempRegistrationsTable = TableRegistry::getTableLocator()->get('TemporaryRegistrations');
        $tempRegistration = $tempRegistrationsTable->createTemporaryRegistration([
            'email' => '<EMAIL>',
            'password' => 'StoredPassword123!',
            'profile_data' => json_encode([
                'last_name' => 'カスタム',
                'first_name' => 'パスワード',
                'last_name_kana' => 'カスタム',
                'first_name_kana' => 'パスワード',
                'zip_code' => '123-4567',
                'prefecture_code' => '13',
                'address1' => '東京都渋谷区',
                'address2' => '１番地',
                'tel' => '03-1234-5678'
            ]),
            'survey_data' => json_encode([
                'year' => 2025,
                'child_sex' => 1,
                'budget' => 1
            ]),
            'product_ids' => json_encode([1, 2, 3])
        ]);

        // カスタムパスワードを使用して本登録
        $customPassword = 'CustomPassword123!';
        $result = $this->registrationService->completeRegistration($tempRegistration->verification_token, $customPassword);

        // $this->assertInstanceOf(GeneralUser::class, $result);
        $this->assertEquals('<EMAIL>', $result['user']->email);
        $this->assertNotNull($result['user']->password);
    }

    /**
     * 本登録完了テスト - 暗号化確認
     */
    public function testCompleteRegistrationWithEncryption(): void
    {
        // 仮登録データを作成
        $tempRegistrationsTable = TableRegistry::getTableLocator()->get('TemporaryRegistrations');
        $tempRegistration = $tempRegistrationsTable->createTemporaryRegistration([
            'email' => '<EMAIL>',
            'password' => 'SecurePassword123!',
            'profile_data' => json_encode([
                'last_name' => '暗号化',
                'first_name' => 'テスト',
                'last_name_kana' => 'アンゴウカ',
                'first_name_kana' => 'テスト',
                'zip_code' => '123-4567',
                'prefecture_code' => '13',
                'address1' => '東京都渋谷区',
                'address2' => 'テストビル',
                'tel' => '03-1234-5678'
            ]),
            'survey_data' => json_encode([
                'year' => 2025,
                'child_sex' => 1,
                'budget' => 1
            ]),
            'product_ids' => json_encode([1, 2, 3])
        ]);

        debug($tempRegistration);

        // 本登録を実行
        $result = $this->registrationService->completeRegistration(
            $tempRegistration->verification_token
        );

        // $this->assertInstanceOf(GeneralUser::class, $result);
        
        // プロフィールデータを取得
        $userProfilesTable = TableRegistry::getTableLocator()->get('UserProfiles');
        $profile = $userProfilesTable->find()->where(['general_user_id' => $result['user']->id])->first();
        
        $this->assertNotNull($profile);
        
        debug($profile);

        // 暗号化されたデータが正しく復号化されるか確認
        $this->assertEquals('暗号化', $profile->decrypted_last_name);
        $this->assertEquals('テスト', $profile->decrypted_first_name);
        
        // 内部データが暗号化されていることを確認
        $originalLastName = $profile->getOriginal('last_name');
        $originalFirstName = $profile->getOriginal('first_name');
        
        $this->assertNotEquals('暗号化', $originalLastName);
        $this->assertNotEquals('テスト', $originalFirstName);
    }
}

