
public function login(array $data): ?Login
{
    // 認証していないので、システムユーザで強制的に実行
    $this->_setSysUserToken();
    $response = $this->post($this->getEndPoint("login"), json_encode($data));
    if ($response->isSuccess() && $body = $response->getStringBody()) {
        if ($data = json_decode($body, true)) {
            if (Hash::check($data, "grant_token")) {
                return new Login($data);
            }
        }
    }
    Log::debug(__METHOD__ . " response: " . $response->getStringBody());
    return null;
}
既存のデータベース構造
MySQL接続: 既に設定済み（CakePHP ORM使用）
暗号化機能: Encryptorクラスで実装済み
マイグレーション: Phinxベースのマイグレーション機能あり

2. MySQL移行設計
ユーザー管理用テーブル設計
以下のマイグレーションファイルを作成する必要があります：

GENERAL_USERS テーブル
SWB_USERS テーブル
MAKER_USERS テーブル
USER_PROFILES テーブル
USER_SURVEYS テーブル
USER_TOKENS テーブル
SWB_USER_TOKENS テーブル
MAKER_USER_TOKENS テーブル
TEMPORARY_REGISTRATIONS テーブル
データ移行方法
段階的移行: kurokoユーザーをバッチ処理でMySQLに移行
パスワード処理: 移行時はpasswordをnullに設定し、初回ログイン時にパスワード設定を促す
暗号化データ: 個人情報は既存のEncryptorクラスを使用して暗号化

3. 実装方針
認証システムの実装
新しい認証フロー:

ログイン処理: MySQLベースの認証に変更
トークン管理: JWTまたは独自トークンシステム
セッション管理: データベースベースのセッション管理
実装すべきクラス:

DatabaseUserAuthenticator (新規)
DatabaseUserIdentifier (新規)
UserTokenService (新規)
PasswordHashService (新規)
セキュリティ要件
パスワードハッシュ化: CakePHPのDefaultPasswordHasher使用
トークン管理: 有効期限付きトークン
暗号化: 個人情報の暗号化継続

4. 移行計画
段階的移行手順
フェーズ1: テーブル作成とモデル実装

マイグレーションファイル作成・実行
Entity・Tableクラス作成
基本的なCRUD機能実装
フェーズ2: 認証システム実装

新しい認証クラス実装
パスワードハッシュ化機能
トークン管理システム
フェーズ3: データ移行

kurokoからMySQLへのデータ移行バッチ
既存ユーザーの移行処理
データ整合性チェック
フェーズ4: 認証フロー切り替え

新旧システム並行運用
段階的な切り替え
kuroko認証の無効化
テスト方法
ユニットテスト: 各認証クラスのテスト
統合テスト: 認証フロー全体のテスト
負荷テスト: 大量ユーザーでの性能テスト
ロールバック計画
データベーススナップショット: 移行前の状態保存
設定切り替え: 環境変数での認証方式切り替え
緊急時対応: kuroko認証への即座復旧

5. 具体的な開発タスク
マイグレーションファイル作成 (優先度: 高)
ユーザー関連モデル実装 (優先度: 高)
新認証システム実装 (優先度: 高)
データ移行バッチ作成 (優先度: 中)
テスト実装 (優先度: 中)
ドキュメント更新 (優先度: 低)