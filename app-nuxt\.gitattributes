# Define the line ending behavior of the different file extensions
# Set default behavior, in case users don't have core.autocrlf set.
* text text=auto eol=lf

# Declare files that will always have CRLF line endings on checkout.
*.bat eol=crlf

# Declare files that will always have LF line endings on checkout.
*.pem eol=lf

# Denote all files that are truly binary and should not be modified.
*.png binary
*.jpg binary
*.jpeg binary
*.gif binary
*.webp binary
*.avif binary
*.ico binary
*.mo binary
*.pdf binary
*.xls binary
*.xlsx binary
*.phar binary
*.woff binary
*.woff2 binary
*.ttc binary
*.ttf binary
*.otf binary
*.eot binary
*.gz binary
*.bz2 binary
*.7z binary
*.zip binary
*.webm binary
*.mp4 binary
*.ogv binary
