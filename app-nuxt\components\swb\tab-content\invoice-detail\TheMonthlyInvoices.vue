<script setup lang="ts">
import { ref } from "vue";
import { SwbMyBridge } from "~~/src/models/bridge/SwbMyBridge";
import { InvoiceRecords } from "~~/src/lib/functions/InvoiceRecords";

const props = defineProps<{
    bridge: SwbMyBridge;
}>();

const selectedRecords = ref([]);

const getMakerName = (makerId: number): string => {
    const maker = props.bridge.makers.find(
        (maker) => maker.maker_id === makerId,
    );
    return maker ? maker.maker_name : "";
};

const headers = [
    {
        title: "集計年月",
        align: "start",
        key: "status_modified_year_month",
        value: "status_modified_year_month",
        width: "10%",
    },
    {
        title: "メーカー名",
        align: "start",
        key: "maker_id",
        value: "maker_id",
    },
    {
        title: "請求額",
        align: "end",
        key: "total_price",
        value: "total_price",
        width: "30%",
    },
    {
        title: "請求書",
        align: "center",
        key: "action",
        sortable: false,
        width: "10%",
    },
    {
        key: "data-table-select",
    },
];

const downloadCsv = (): void => {
    InvoiceRecords.downloadRecordCsv(selectedRecords.value, props.bridge);
};
</script>

<template>
    <div class="d-flex justify-end mt-3">
        <v-btn
            class="csv-dl-btn mx-2 py-5"
            :disabled="selectedRecords.length === 0"
            elevation="0"
            density="default"
            size="small"
            @click="downloadCsv"
        >
            <v-icon size="large" class="mr-1">mdi-download-outline</v-icon>
            選択した請求書の <br />
            集計表(CSV)ダウンロード
        </v-btn>
    </div>
    <div class="monthly-report-wrapper">
        <v-data-table
            v-model="selectedRecords"
            :headers="headers"
            :items="bridge.swb_monthly_invoices"
            :loading="!bridge.is_invoice_loaded"
            return-object
        >
            <template #[`item.status_modified_year_month`]="{ item }">
                <span class=""> {{ item.status_modified_year_month }} </span>
            </template>
            <template #[`item.maker_id`]="{ item }">
                <span class="">
                    {{ getMakerName(item.maker_id) }}
                </span>
            </template>
            <template #[`item.total_price`]="{ item }">
                <span class=""> {{ item.total_price.toLocaleString() }}円</span>
            </template>
            <template #item.action="{ item }">
                <v-btn
                    class="mx-2"
                    density="compact"
                    elevation="1"
                    @click="bridge.downloadInvoicePdf(item)"
                >
                    <v-icon size="small" class="mr-1">mdi-download</v-icon>
                    印刷
                </v-btn>
            </template>
        </v-data-table>
    </div>
</template>

<style scoped>
.monthly-report-wrapper {
    position: inherit;
}
.color-gray {
    color: #575757;
}
.v-data-table th {
    font-weight: bold; /* 太字に設定 */
}

.csv-dl-btn {
    background-color: #4472c4;
    color: white;
    font-weight: bold;
    padding: 15px;
    align-content: center;
    transition: opacity 0.3s ease;
}
.csv-dl-btn:hover {
    opacity: 0.7;
}
.csv-dl-btn:disabled {
    background-color: #b0b0b0 !important;
    color: white !important;
}
</style>
