name: Mark stale issues and pull requests

on:
  schedule:
  - cron: "0 0 * * *"

permissions:
  contents: read

jobs:
  stale:

    permissions:
      issues: write  # for actions/stale to close stale issues
      pull-requests: write  # for actions/stale to close stale PRs
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/stale@v8
      with:
        repo-token: ${{ secrets.GITHUB_TOKEN }}
        stale-issue-message: 'This issue is stale because it has been open for 120 days with no activity. Remove the `stale` label or comment or this will be closed in 15 days'
        stale-pr-message: 'This pull request is stale because it has been open 30 days with no activity. Remove the `stale` label or comment on this issue, or it will be closed in 15 days'
        stale-issue-label: 'stale'
        stale-pr-label: 'stale'
        days-before-stale: 120
        days-before-close: 15
        exempt-issue-labels: 'pinned'
        exempt-pr-labels: 'pinned'
