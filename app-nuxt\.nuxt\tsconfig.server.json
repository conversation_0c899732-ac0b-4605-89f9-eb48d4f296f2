{"compilerOptions": {"forceConsistentCasingInFileNames": true, "strict": true, "target": "ESNext", "module": "ESNext", "moduleResolution": "<PERSON><PERSON><PERSON>", "allowJs": true, "resolveJsonModule": true, "jsx": "preserve", "allowSyntheticDefaultImports": true, "jsxFactory": "h", "jsxFragmentFactory": "Fragment", "paths": {"#imports": ["./types/nitro-imports"], "~/*": ["../*", "../*"], "@/*": ["../*", "../*"], "~~/*": ["../*", "../*"], "@@/*": ["../*", "../*"], "nitropack": ["../node_modules/nitropack"], "defu": ["../node_modules/defu"], "h3": ["../node_modules/h3"], "consola": ["../node_modules/consola"], "ofetch": ["../node_modules/ofetch"], "@unhead/vue": ["../node_modules/@unhead/vue"], "@vue/runtime-core": ["../node_modules/@vue/runtime-core"], "@vue/compiler-sfc": ["../node_modules/@vue/compiler-sfc"], "@vue/runtime-dom": ["../node_modules/@vue/runtime-dom"], "@nuxt/schema": ["../node_modules/@nuxt/schema"], "nuxt": ["../node_modules/nuxt"], "#internal/nuxt/paths": ["../node_modules/nuxt/dist/core/runtime/nitro/paths"], "~": ["./.."], "@": ["./.."], "~~": ["./.."], "@@": ["./.."], "assets": ["../assets"], "assets/*": ["../assets/*"], "public": ["../public"], "public/*": ["../public/*"], "pinia": ["../node_modules/pinia/dist/pinia"]}}, "include": ["./types/nitro-nuxt.d.ts", "../node_modules/@nuxtjs/device/runtime/server", "../node_modules/@nuxt/eslint/runtime/server", "../node_modules/@pinia/nuxt/runtime/server", "../node_modules/@nuxt/devtools/runtime/server", "../node_modules/@nuxt/telemetry/runtime/server", "./types/nitro.d.ts", "../**/*", "../server/**/*"], "exclude": ["../node_modules", "../../node_modules", "../node_modules/nuxt/node_modules", "../dist"]}