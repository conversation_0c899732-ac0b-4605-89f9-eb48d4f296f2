<?php

namespace <PERSON><PERSON><PERSON>\ApiModel;

use App\Enums\EntityFields\ELogin;
use App\Kuroko\ApiModel\KurokoApiPrivateStatic\Products;
use App\Kuroko\ApiModel\KurokoApiStatic\Inquiries;
use App\Kuroko\Entity\AccessToken;
use App\Kuroko\Entity\Member;
use App\Test\TestCase\Controller\ApiTestCase;

class KurokoApiStaticModelTest extends ApiTestCase
{

    // php ./vendor/phpunit/phpunit/phpunit --filter "testProducts" ./tests/TestCase/Kuroko/ApiModel/KurokoApiStaticModelTest.php
    public function testProducts()
    {
        $kurokoApiModel = new Products();

        $response = $kurokoApiModel->getProductList();
        debug(json_encode($response));
        $this->assertTrue(true);
    }

    // php ./vendor/phpunit/phpunit/phpunit --filter "testInquiries" ./tests/TestCase/Kuroko/ApiModel/KurokoApiStaticModelTest.php
    public function testInquiries()
    {
        //        $kurokoApiModel = new Products();
        //
        //        $response = $kurokoApiModel->getProductList();
        //        debug(json_encode($response));


        $inquiries = new Inquiries();
        $response = $inquiries->sendInquiry([
            "name" => "テスト名前",
            "email" => "<EMAIL>",
            "ext_01" => "タイトル",
            "body" => "body",
            //            "from_id" => 1,
            //            "member_id" => 0,
        ]);
        debug(json_encode($response));

        $this->assertTrue(true);
    }
}
