<?php

namespace App\Controller\Front;

use App\Controller\FrontController;
use App\Form\Front\ClientRandselOrdersIndexForm;
use App\Form\DisableParametersForm;
use App\Service\ClientCsvRandselOrdersService;
use Cake\Event\Event;
use Cake\Log\Log;

class ClientCsvRandselOrdersController extends FrontController
{
    public function edit($id = null): void
    {
        $this->_onSuccess(function (Event $event,) {
            $this->_setSuccessResponse([
                'success' => true,
            ]);
        });
        $this->_onError(function (Event $event, array $errors) {
            Log::debug(print_r($errors, true));
        });
        $this->_edit($id, (new ClientCsvRandselOrdersService())
            ->setIdentity($this->Authentication->getIdentity())
            ->setForm(
                new ClientRandselOrdersIndexForm()
            ));
    }
}
