<?php

use App\Config\DotEnvConfig;
use Cake\Log\Engine\FileLog;
use Monolog\Handler\RotatingFileHandler;
use Monolog\Level;
use App\Log\Engine\MonologLog;

$withPrefix = fn(string $name) => (isCli() ? "cli-" : "") . $name;

return [
    /*
    * Configures logging options
    */
    'Log' => [
        'debug' => [
            'className' => MonologLog::class,
            // 'path' => LOGS,
            'handler' => new RotatingFileHandler(LOGS . $withPrefix('debug.log'), 0, Level::Debug),
            // 'file' => $withPrefix('debug'),
            // 'url' => DotEnvConfig::read('LOG_DEBUG_URL', null),
            'scopes' => false,
            // 'levels' => ['notice', 'info', 'debug'],
        ],
        'error' => [
            'className' => MonologLog::class,
            // 'path' => LOGS,
            'handler' => new RotatingFileHandler(LOGS . $withPrefix('error.log'), 0, Level::Warning),
            // 'file' => $withPrefix('error'),
            // 'url' => DotEnvConfig::read('LOG_ERROR_URL', null),
            'scopes' => false,
            // 'levels' => ['warning', 'error', 'critical', 'alert', 'emergency'],
        ],
        // To enable this dedicated query log, you need set your datasource's log flag to true
        'orders' => [
            'className' => MonologLog::class,
            // 'path' => LOGS,
            'handler' => new RotatingFileHandler(LOGS . $withPrefix('orders.log'), 0, Level::Info),
            // 'file' => $withPrefix('queries'),
            // 'url' => DotEnvConfig::read('LOG_QUERIES_URL', null),
            'scopes' => ['ordersLog'],
        ],
        'adjustments' => [
            'className' => MonologLog::class,
            'handler' => new RotatingFileHandler(LOGS . $withPrefix('adjustments.log'), 0, Level::Info),
            'scopes' => ['adjustmentsLog'],
        ],
        'queries' => [
            'className' => MonologLog::class,
            'handler' => new RotatingFileHandler(LOGS . $withPrefix('queries.log'), 0, Level::Debug),
            'scopes' => ['queriesLog'],
        ],
    ],
];
