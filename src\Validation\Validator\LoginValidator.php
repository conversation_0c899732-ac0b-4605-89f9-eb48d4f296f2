<?php

namespace App\Validation\Validator;

use App\Enums\EntityFields\ELogin;
use App\Enums\EntityFields\ESchoolBagForm;
use App\Enums\EValidationErrorMessage;
use App\Validation\IValidator;
use App\Validation\ValidatorTrait;
use Cake\Validation\Validator;

class LoginValidator implements IValidator
{
    use ValidatorTrait;


    static public function validateLoginEmail(string $field, Validator $validator, array $insertText): Validator
    {
        $validator
            ->maxLength($field, 50, EValidationErrorMessage::MAX_LENGTH->format(
                $insertText + [
                    'max' => 50
                ]
            ))
            ->requirePresence($field, true, EValidationErrorMessage::REQUIRE_PRESENCE->format($insertText))
            ->notEmptyString($field, EValidationErrorMessage::NOT_EMPTY_STRING->format($insertText));
        return $validator;
    }

    static public function validateLoginPw(string $field, Validator $validator, array $insertText): Validator
    {
        $validator
            ->maxLength($field, 30, EValidationErrorMessage::MAX_LENGTH->format(
                $insertText + [
                    'max' => 30
                ]
            ))
            ->requirePresence($field, true, EValidationErrorMessage::REQUIRE_PRESENCE->format($insertText))
            ->notEmptyString($field, EValidationErrorMessage::NOT_EMPTY_STRING->format($insertText));
        return $validator;
    }

    static private function email(Validator $validator, array $options = []): Validator
    {
        $insertText = [
            'field' => ELogin::LOGIN_EMAIL->description(),
        ];
        $field = ELogin::LOGIN_EMAIL->value;
        return static::validateLoginEmail($field, $validator, $insertText);
    }

    static private function password(Validator $validator, array $options = []): Validator
    {
        $insertText = [
            'field' => ELogin::LOGIN_PWD->description(),
        ];
        $field = ELogin::LOGIN_PWD->value;
        return static::validateLoginPw($field, $validator, $insertText);
    }
}
