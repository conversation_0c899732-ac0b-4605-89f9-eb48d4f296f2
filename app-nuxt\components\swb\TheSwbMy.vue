<script setup lang="ts">
import { ref, onMounted } from "vue";
import { useRuntimeConfig } from "#app";
import { Product } from "~/src/models/entry/Product";
import Makers from "~/src/models/Makers";
import { SwbMyBridge } from "~~/src/models/bridge/SwbMyBridge";
import TheLoading from "~/components/parts/TheLoading.vue";
import TheNotLinkedProducts from "~/components/client/template/error/TheNotLinkedProducts.vue";
import TheSessionTimeOut from "~/components/client/template/error/TheSessionTimeOut.vue";
import TheOrderDetail from "~/components/swb/tab-content/TheOrderDetail.vue";
import TheTabSelector from "~/components/TheTabSelector.vue";
import TheInvoiceDetail from "~/components/swb/tab-content/TheInvoiceDetail.vue";
import TheInvoiceAdjustmentDetail from "~/components/swb/tab-content/TheInvoiceAdjustmentDetail.vue";

const props = defineProps<{
    products: Array<Product>;
}>();

const config = useRuntimeConfig();
const bridge = ref<SwbMyBridge>(new SwbMyBridge(config, props.products));
onMounted(async () => {
    Makers.create(config)
        .getList()
        .then((m) => {
            if (m) bridge.value.makers = m;
        });
});

// タブ関連
const Tabs = {
    Detail: { key: "detail", label: "注文リスト" },
    Invoice: { key: "invoice", label: "請求書管理" },
    InvoiceAdjustment: { key: "invoice_adjustment", label: "調整金額" },
} as const;
const activeTab = ref<(typeof Tabs)[keyof typeof Tabs]["key"]>(Tabs.Detail.key);
const setActiveTab = (
    tabKey: (typeof Tabs)[keyof typeof Tabs]["key"],
): void => {
    activeTab.value = tabKey;
};
const role = "swb";
</script>

<template>
    <v-container class="client-my-wrapper">
        <the-tab-selector
            :tabs="Tabs"
            :active-tab="activeTab"
            :user-role="role"
            @update:active-tab="setActiveTab"
        />
        <div v-if="bridge.is_product_loaded">
            <template v-if="bridge.is_success">
                <the-order-detail
                    v-show="activeTab === Tabs.Detail.key"
                    :bridge="bridge"
                />
                <the-invoice-detail
                    v-show="activeTab === Tabs.Invoice.key"
                    :bridge="bridge"
                />
                <the-invoice-adjustment-detail
                    v-show="activeTab === Tabs.InvoiceAdjustment.key"
                    :bridge="bridge"
                />
            </template>
            <template v-else>
                <the-not-linked-products v-if="!bridge.my_products.length" />
                <the-session-time-out v-else />
            </template>
        </div>
        <the-loading v-else color="swbPrimary">
            情報を読み込んでいます、しばらくお待ちください
        </the-loading>
    </v-container>
</template>

<style scoped></style>
