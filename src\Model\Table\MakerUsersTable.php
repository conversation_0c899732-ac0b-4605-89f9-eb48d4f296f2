<?php
declare(strict_types=1);

namespace App\Model\Table;

use Cake\ORM\Table;
use Cake\Validation\Validator;

/**
 * メーカーユーザーテーブル
 * 
 * @property \App\Model\Table\MakerUserTokensTable&\Cake\ORM\Association\HasMany $MakerUserTokens
 * @property \App\Model\Table\MakersTable&\Cake\ORM\Association\BelongsTo $Makers
 * @method \App\Model\Entity\MakerUser newEmptyEntity()
 * @method \App\Model\Entity\MakerUser newEntity(array $data, array $options = [])
 * @method \App\Model\Entity\MakerUser[] newEntities(array $data, array $options = [])
 * @method \App\Model\Entity\MakerUser get($primaryKey, $options = [])
 * @method \App\Model\Entity\MakerUser findOrCreate($search, ?callable $callback = null, $options = [])
 * @method \App\Model\Entity\MakerUser patchEntity(\Cake\Datasource\EntityInterface $entity, array $data, array $options = [])
 * @method \App\Model\Entity\MakerUser[] patchEntities(iterable $entities, array $data, array $options = [])
 * @method \App\Model\Entity\MakerUser|false save(\Cake\Datasource\EntityInterface $entity, $options = [])
 * @method \App\Model\Entity\MakerUser saveOrFail(\Cake\Datasource\EntityInterface $entity, $options = [])
 * @method \App\Model\Entity\MakerUser[]|\Cake\Datasource\ResultSetInterface|false saveMany(iterable $entities, $options = [])
 * @method \App\Model\Entity\MakerUser[]|\Cake\Datasource\ResultSetInterface saveManyOrFail(iterable $entities, $options = [])
 * @method \App\Model\Entity\MakerUser[]|\Cake\Datasource\ResultSetInterface|false deleteMany(iterable $entities, $options = [])
 * @method \App\Model\Entity\MakerUser[]|\Cake\Datasource\ResultSetInterface deleteManyOrFail(iterable $entities, $options = [])
 */
class MakerUsersTable extends Table
{
    public function initialize(array $config): void
    {
        parent::initialize($config);

        $this->setTable('maker_users');
        $this->setDisplayField('email');
        $this->setPrimaryKey('id');

        $this->belongsTo('Makers', [
            'foreignKey' => 'maker_id',
            'joinType' => 'INNER',
        ]);

        $this->hasMany('MakerUserTokens', [
            'foreignKey' => 'maker_user_id',
            'dependent' => true,
        ]);

        // ソフトデリート動作の設定
        $this->addBehavior('Muffin/Trash.Trash', [
            'field' => 'deleted',
            'events' => ['Model.beforeFind']
        ]);
    }

    public function validationDefault(Validator $validator): Validator
    {
        $validator
            ->integer('id')
            ->allowEmptyString('id', null, 'create');

        $validator
            ->integer('maker_id')
            ->requirePresence('maker_id', 'create')
            ->notEmptyString('maker_id');

        $validator
            ->email('email')
            ->requirePresence('email', 'create')
            ->notEmptyString('email')
            ->add('email', 'unique', ['rule' => 'validateUnique', 'provider' => 'table']);

        $validator
            ->scalar('password')
            ->maxLength('password', 255)
            ->allowEmptyString('password');

        return $validator;
    }

    /**
     * メールアドレスでユーザーを検索
     */
    public function findByEmail(string $email)
    {
        return $this->find()
            ->contain(['Makers'])
            ->where(['MakerUsers.email' => $email, 'MakerUsers.deleted IS' => null])
            ->first();
    }

    /**
     * メーカーIDでユーザーを検索
     */
    public function findByMaker(int $makerId)
    {
        return $this->find()
            ->contain(['Makers'])
            ->where(['maker_id' => $makerId, 'deleted IS' => null]);
    }
}


