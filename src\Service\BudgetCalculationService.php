<?php

namespace App\Service;

use App\Model\Table\BudgetsTable;
use App\Model\Table\RandselOrdersTable;
use Cake\ORM\TableRegistry;
use Cake\I18n\FrozenTime;
use Cake\Log\Log;

/**
 * 予算計算サービス
 * 
 * 複雑な予算計算ロジックを担当し、
 * 同一タイプの予算の合併処理などを行う
 */
class BudgetCalculationService implements IService
{
    use ServiceTrait;

    protected array $_defaultConfig = [];

    /**
     * @var BudgetsTable
     */
    private BudgetsTable $budgetsTable;

    /**
     * @var RandselOrdersTable
     */
    private RandselOrdersTable $randselOrdersTable;

    public function initialize(): void
    {
        $this->budgetsTable = TableRegistry::getTableLocator()->get('Budgets');
        $this->randselOrdersTable = TableRegistry::getTableLocator()->get('RandselOrders');
    }

    /**
     * 商品の統合予算情報を取得
     * 
     * 同一タイプの予算を合併し、統合された予算情報を返す
     * 
     * @param int $productId 商品ID
     * @return array 統合予算情報の配列
     */
    public function getConsolidatedBudgets(int $productId): array
    {
        $now = FrozenTime::now();
        
        // 現在有効な予算を取得
        $activeBudgets = $this->budgetsTable
            ->find()
            ->where([
                'product_id' => $productId,
                'is_active' => true,
                'start_date <=' => $now,
                'end_date >=' => $now
            ])
            ->order(['type' => 'ASC', 'start_date' => 'ASC'])
            ->toArray();

        if (empty($activeBudgets)) {
            return [];
        }

        // タイプ別にグループ化
        $budgetsByType = [];
        foreach ($activeBudgets as $budget) {
            $budgetsByType[$budget->type][] = $budget;
        }

        $consolidatedBudgets = [];
        
        foreach ($budgetsByType as $type => $budgets) {
            $consolidated = $this->consolidateBudgetsByType($budgets);
            $consolidated['order_count'] = $this->getOrderCountForConsolidatedBudget($consolidated);
            $consolidated['is_available'] = $consolidated['order_count'] < $consolidated['budget_quantity'];
            
            $consolidatedBudgets[] = $consolidated;
        }

        // 優先順位でソート
        usort($consolidatedBudgets, function($a, $b) {
            if ($a['priority'] === $b['priority']) {
                return $a['type'] <=> $b['type'];
            }
            return $b['priority'] <=> $a['priority'];
        });

        return $consolidatedBudgets;
    }

    /**
     * 同一タイプの予算を統合
     * 
     * @param array $budgets 同一タイプの予算配列
     * @return array 統合された予算情報
     */
    private function consolidateBudgetsByType(array $budgets): array
    {
        if (count($budgets) === 1) {
            $budget = $budgets[0];
            return [
                'product_id' => $budget->product_id,
                'type' => $budget->type,
                'budget_quantity' => $budget->budget_quantity,
                'start_date' => $budget->start_date,
                'end_date' => $budget->end_date,
                'priority' => $budget->priority,
                'budget_ids' => [$budget->id]
            ];
        }

        // 複数の予算を統合
        $totalQuantity = 0;
        $minStartDate = null;
        $maxEndDate = null;
        $maxPriority = 0;
        $budgetIds = [];
        $productId = null;
        $type = null;

        foreach ($budgets as $budget) {
            $totalQuantity += $budget->budget_quantity;
            $maxPriority = max($maxPriority, $budget->priority);
            $budgetIds[] = $budget->id;
            
            if ($productId === null) {
                $productId = $budget->product_id;
                $type = $budget->type;
            }
            
            if ($minStartDate === null || $budget->start_date < $minStartDate) {
                $minStartDate = $budget->start_date;
            }
            
            if ($maxEndDate === null || $budget->end_date > $maxEndDate) {
                $maxEndDate = $budget->end_date;
            }
        }

        Log::info('BudgetCalculationService: 予算統合', [
            'product_id' => $productId,
            'type' => $type,
            'budget_count' => count($budgets),
            'total_quantity' => $totalQuantity,
            'budget_ids' => $budgetIds
        ]);

        return [
            'product_id' => $productId,
            'type' => $type,
            'budget_quantity' => $totalQuantity,
            'start_date' => $minStartDate,
            'end_date' => $maxEndDate,
            'priority' => $maxPriority,
            'budget_ids' => $budgetIds
        ];
    }

    /**
     * 統合予算の注文数を取得
     * 
     * @param array $consolidatedBudget 統合予算情報
     * @return int 注文数
     */
    private function getOrderCountForConsolidatedBudget(array $consolidatedBudget): int
    {
        return $this->randselOrdersTable
            ->find()
            ->where([
                'product_id' => $consolidatedBudget['product_id'],
                'type' => $consolidatedBudget['type'],
                'created >=' => $consolidatedBudget['start_date']
            ])
            ->count();
    }

    /**
     * 予算の利用状況詳細を取得
     * 
     * @param int $productId 商品ID
     * @return array 予算利用状況の詳細情報
     */
    public function getBudgetUsageDetails(int $productId): array
    {
        $consolidatedBudgets = $this->getConsolidatedBudgets($productId);
        
        $details = [];
        foreach ($consolidatedBudgets as $budget) {
            $usageRate = $budget['budget_quantity'] > 0 
                ? ($budget['order_count'] / $budget['budget_quantity']) * 100 
                : 0;
                
            $details[] = [
                'type' => $budget['type'],
                'type_name' => $budget['type'] === 1 ? '紙' : 'デジタル',
                'budget_quantity' => $budget['budget_quantity'],
                'order_count' => $budget['order_count'],
                'remaining_quantity' => $budget['budget_quantity'] - $budget['order_count'],
                'usage_rate' => round($usageRate, 2),
                'is_available' => $budget['is_available'],
                'priority' => $budget['priority'],
                'start_date' => $budget['start_date'],
                'end_date' => $budget['end_date'],
                'budget_ids' => $budget['budget_ids']
            ];
        }
        
        return $details;
    }

    /**
     * 予算期間の重複チェック
     * 
     * @param int $productId 商品ID
     * @param int $type カタログタイプ
     * @param FrozenTime $startDate 開始日
     * @param FrozenTime $endDate 終了日
     * @param int|null $excludeBudgetId 除外する予算ID
     * @return array 重複する予算の配列
     */
    public function checkBudgetOverlap(
        int $productId, 
        int $type, 
        FrozenTime $startDate, 
        FrozenTime $endDate,
        ?int $excludeBudgetId = null
    ): array {
        $query = $this->budgetsTable
            ->find()
            ->where([
                'product_id' => $productId,
                'type' => $type,
                'is_active' => true,
                'OR' => [
                    [
                        'start_date <=' => $endDate,
                        'end_date >=' => $startDate
                    ]
                ]
            ]);
            
        if ($excludeBudgetId !== null) {
            $query->where(['id !=' => $excludeBudgetId]);
        }
        
        return $query->toArray();
    }

    /**
     * 予算の有効性を検証
     * 
     * @param array $budgetData 予算データ
     * @return array 検証結果
     */
    public function validateBudget(array $budgetData): array
    {
        $errors = [];
        
        // 必須フィールドのチェック
        $requiredFields = ['product_id', 'type', 'budget_quantity', 'start_date', 'end_date'];
        foreach ($requiredFields as $field) {
            if (empty($budgetData[$field])) {
                $errors[] = "{$field}は必須です";
            }
        }
        
        // 日付の妥当性チェック
        if (!empty($budgetData['start_date']) && !empty($budgetData['end_date'])) {
            $startDate = new FrozenTime($budgetData['start_date']);
            $endDate = new FrozenTime($budgetData['end_date']);
            
            if ($endDate <= $startDate) {
                $errors[] = '終了日は開始日より後の日付を指定してください';
            }
        }
        
        // 予算数量の妥当性チェック
        if (!empty($budgetData['budget_quantity']) && $budgetData['budget_quantity'] <= 0) {
            $errors[] = '予算数量は1以上を指定してください';
        }
        
        // カタログタイプの妥当性チェック
        if (!empty($budgetData['type']) && !in_array($budgetData['type'], [1, 2])) {
            $errors[] = 'カタログタイプは1（紙）または2（デジタル）を指定してください';
        }
        
        return [
            'is_valid' => empty($errors),
            'errors' => $errors
        ];
    }
}
