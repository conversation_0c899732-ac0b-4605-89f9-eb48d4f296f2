<?php

namespace App\Mailer\Sender\ToSwb;

use App\Mailer\Sender\IToSwbSender;
use App\Mailer\Sender\SenderTrait;
use App\Mailer\Sender\ToUserSenderTrait;

class SwbInvoiceAdjustmentConfirmSender implements IToSwbSender
{
    use SenderTrait;
    use ToUserSenderTrait;

    public function getSubject(): string
    {
        return "【カバーミー】調整金額確定のお知らせ";
    }

    public function getTemplate(): string
    {
        return "to_swb/SwbInvoiceAdjustmentConfirmSender";
    }
} 