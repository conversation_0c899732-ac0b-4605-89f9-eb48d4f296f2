<?php

namespace App\Controller\Front;

use App\Controller\FrontController;
use App\Form\Front\ClientOrdersIndexForm;
use App\Service\ClientOrdersService;
use Cake\Event\Event;
use Cake\Log\Log;

class ClientOrdersController extends FrontController
{

    public function index(): void
    {
        $this->_onSuccess(function (Event $event, array $orders) {
            $this->_setSuccessResponse([
                "orders" => $orders
            ]);
        });
        $this->_onError(function (Event $event, array $errors) {
            Log::debug(print_r($errors, true));
        });
        $this->_index((new ClientOrdersService())
            ->setIdentity($this->Authentication->getIdentity())
            ->setForm(
                new ClientOrdersIndexForm()
            ));
    }

    // public function add(): void
    // {
    //     $this->_onSuccess(function (Event $event, ECOrder $order) {
    //         $this->_setSuccessResponse([
    //             'success' => true,
    //             'member' => $order->getMemberName1()
    //         ]);
    //     });
    //     $this->_onError(function (Event $event, array $errors) {
    //         Log::debug(print_r($errors, true));
    //     });
    //     $this->_add((new OrdersService())
    //         ->setIdentity($this->Authentication->getIdentity())
    //         ->setForm(
    //             new DisableParametersForm()
    //         ));
    // }
}
