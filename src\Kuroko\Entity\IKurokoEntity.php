<?php

namespace App\Kuroko\Entity;

use JsonSerializable;

interface IKurokoEntity extends JsonSerializable
{
    public function __construct(array $data);

    public function getJsonData(): array;

    /**
     * データの加工を差し込む
     * @param array $data
     * @return array
     */
    public function initialize(array $data): array;

    static public function creates(array $list): array;

    public function get(string $key, mixed $default = null): mixed;
}
