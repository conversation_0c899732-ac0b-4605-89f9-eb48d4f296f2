<?php

namespace App\Authentication\Identifier\Resolver;

use App\Enums\EntityFields\ELogin;
use App\Kuroko\Entity\AccessToken;
use App\Kuroko\Entity\Member;
use App\Model\Entity\GeneralUser;
use App\Model\Entity\SwbUser;
use App\Model\Entity\MakerUser;
use App\Service\AuthenticationService;
use App\Service\LoginsService;
use App\Service\MembersService;
use Authentication\Identifier\Resolver\ResolverInterface;
use Authentication\IdentityInterface;
use Cake\Log\Log;
use Cake\ORM\TableRegistry;
use Cake\Utility\Hash;
use Exception;

class UserResolver implements ResolverInterface
{

    public function find(array $conditions, string $type = self::TYPE_AND): null|Member|IdentityInterface
    {
        Log::debug('UserResolver::find called with conditions: ' . json_encode($conditions));

        // 1. トークン認証の処理（既存）
        if (Hash::check($conditions, ELogin::TOKEN->value)) {
            return $this->findByToken($conditions);
        }

        // 2. 新システム認証とKuroco認証の統合処理
        $loginEmail = Hash::get($conditions, ELogin::LOGIN_EMAIL->value);
        $loginPw = Hash::get($conditions, ELogin::LOGIN_PWD->value);
        $loginType = Hash::get($conditions, ELogin::TYPE->value);

        if (empty($loginPw) || empty($loginEmail)) {
            Log::debug('UserResolver: Email or password is empty');
            return null;
        }

        Log::debug("UserResolver: Attempting authentication for email: {$loginEmail}");

        $newSystemUser = $this->tryNewSystemAuthentication($loginEmail, $loginPw, $loginType);
        if ($newSystemUser) {
            Log::info("UserResolver: New system authentication successful for: {$loginEmail}");
            return $newSystemUser;
        }

        // Kuroco認証にフォールバック
        Log::debug("UserResolver: Falling back to Kuroco authentication for: {$loginEmail}");
        return $this->tryKurocoAuthentication($loginEmail, $loginPw);
    }

    /**
     * トークンによる認証処理
     */
    private function findByToken(array $conditions): ?Member
    {
        $token = Hash::get($conditions, ELogin::TOKEN->value);
        if (empty($token)) {
            Log::debug('UserResolver: Token is empty');
            return null;
        }

        try {
            Log::debug('UserResolver: Attempting token authentication');

            // 新システムのトークン認証を先に試行
            $newSystemMember = $this->tryNewSystemTokenAuthentication($token);
            if ($newSystemMember) {
                Log::info('UserResolver: New system token authentication successful');
                return $newSystemMember;
            }

            // Kurocoトークン認証にフォールバック
            Log::debug('UserResolver: Falling back to Kuroco token authentication');
            $membersService = new MembersService();
            $accessToken = new AccessToken(Hash::insert([], AccessToken::ACCESS_TOKEN_PATH, $token));
            $member = $membersService->getMeByAccessToken($accessToken);
            if (!empty($member)) {
                Log::info('UserResolver: Kuroco token authentication successful');
                return $member->setAccessToken($accessToken);
            }
        } catch (Exception $e) {
            Log::error('UserResolver: Token authentication error: ' . $e->getMessage());
        }

        Log::debug('UserResolver: Token authentication failed');
        return null;
    }

    /**
     * 新システム認証を試行
     */
    private function tryNewSystemAuthentication(string $email, string $password, ?string $userType): ?Member
    {
        try {
            Log::debug("UserResolver: Trying new system authentication for: {$email}");

            $authService = new AuthenticationService();

            // 新システム認証を試行
            /** @var GeneralUser|SwbUser|MakerUser|null $user */
            $user = $authService->authenticate($email, $password, $userType);

            if ($user) {
                Log::info("UserResolver: New system user found: {$email}");
                Log::debug('UserResolver: User type: ' . get_class($user));
                // 新システムユーザーの場合、Memberエンティティに変換してアクセストークンを生成
                return $this->convertNewSystemUserToMember($user);
            } else {
                Log::debug("UserResolver: New system authentication failed for: {$email}");
            }

        } catch (Exception $e) {
            Log::error("UserResolver: New system authentication error for {$email}: " . $e->getMessage());
        }

        return null;
    }

    /**
     * Kuroco認証を試行
     */
    private function tryKurocoAuthentication(string $email, string $password): ?Member
    {
        try {
            Log::debug("UserResolver: Trying Kuroco authentication for: {$email}");

            $loginsService = new LoginsService();
            $accessToken = $loginsService->getAccessToken($email, $password);
            $membersService = new MembersService();
            $member = $membersService->getMeByLogin($accessToken);

            if (!empty($member)) {
                Log::info("UserResolver: Kuroco authentication successful for: {$email}");
                return $member->setAccessToken($accessToken);
            }
        } catch (Exception $e) {
            Log::error("UserResolver: Kuroco authentication error for {$email}: " . $e->getMessage());
        }

        Log::debug("UserResolver: Kuroco authentication failed for: {$email}");
        return null;
    }

    /**
     * 新システムのトークン認証を試行
     */
    private function tryNewSystemTokenAuthentication(string $token): ?Member
    {
        try {
            Log::debug('UserResolver: Trying new system token authentication');

            // 暗号化されたトークンを復号化
            $decryptedToken = AccessToken::decryptToken($token);
            if (empty($decryptedToken)) {
                Log::debug('UserResolver: Failed to decrypt token');
                return null;
            }

            // 各ユーザータイプのトークンテーブルを順次検索
            $tokenTables = [
                'UserTokens' => ['entity_property' => 'general_user', 'type_class' => '\App\Model\Entity\UserToken'],
                'MakerUserTokens' => ['entity_property' => 'maker_user', 'type_class' => '\App\Model\Entity\MakerUserToken'],
                'SwbUserTokens' => ['entity_property' => 'swb_user', 'type_class' => '\App\Model\Entity\SwbUserToken']
            ];

            foreach ($tokenTables as $tableName => $config) {
                /** @var UserTokensTable|MakerUserTokensTable|SwbUserTokensTable $table */
                Log::debug("UserResolver: Checking token in {$tableName}");
                $table = TableRegistry::getTableLocator()->get($tableName);
                $typeClass = $config['type_class'];
                /** @var UserToken|MakerUserToken|SwbUserToken $tokenEntity */
                $tokenEntity = $table->findValidToken($decryptedToken, $typeClass::TYPE_API_ACCESS);

                if ($tokenEntity) {
                    $user = $tokenEntity->{$config['entity_property']};
                    if ($user) {
                        Log::info("UserResolver: Valid new system token found for user ID: {$user->id}");
                        Log::debug('UserResolver: User type: ' . get_class($user));

                        // 新システムユーザーをMemberエンティティに変換
                        return $this->convertNewSystemUserToMember($user, $token);
                    }
                }
            }

        } catch (Exception $e) {
            Log::error('UserResolver: New system token authentication error: ' . $e->getMessage());
        }

        return null;
    }

    /**
     * 新システムユーザーをMemberエンティティに変換
     */
    private function convertNewSystemUserToMember(GeneralUser|SwbUser|MakerUser $user, ?string $existingToken = null): Member
    {
        Log::debug('UserResolver: Converting new system user to Member entity');
        Log::debug('UserResolver: User type: ' . get_class($user));

        // 新システムユーザー用のアクセストークンを生成または使用
        if ($existingToken) {
            $accessToken = new AccessToken([
                'access_token' => ['value' => AccessToken::decryptToken($existingToken)],
                'expires_at' => time() + (24 * 60 * 60) // 24時間後
            ]);
        } else {
            $accessToken = $this->generateNewSystemAccessToken($user);
        }

        // ユーザープロファイル情報を安全に取得
        $userProfile = null;
        if ($user instanceof GeneralUser) {
            $userProfile = $user->user_profile;
        }

        // Memberエンティティのデータ構造に合わせて変換
        $memberData = [
            'id' => $user->id,
            'details' => [
                'member_id' => $user->id,
                'login_id' => '',
                'name1' => $userProfile?->decrypted_last_name ?? '',
                'name2' => $userProfile?->decrypted_first_name ?? '',
                'name1_hurigana' => $userProfile?->decrypted_last_name_kana ?? '',
                'name2_hurigana' => $userProfile?->decrypted_first_name_kana ?? '',
                'tdfk_cd' => $userProfile?->decrypted_prefecture_code ?? '',
                'address1' => $userProfile?->decrypted_address1 ?? '',
                'address2' => $userProfile?->decrypted_address2 ?? '',
                'address3' => $userProfile?->decrypted_address3 ?? '',
                'zip_code' => $userProfile?->decrypted_zip_code ?? '',
                'tel' => $userProfile?->decrypted_tel ?? '',
                'email' => $user->email,
                'open_flg' => 0,
                'inst_ymdhi' => $user->created ?? date('Y-m-d\TH:i:sP'),
                'update_ymdhi' => $user->modified ?? date('Y-m-d\TH:i:sP'),
                'login_ok_flg' => Member::LOGIN_OK_FLG_OK,
                'email_send_ng_flg' => $userProfile?->email_send_ng_flg ?? 0,
                'status' => [
                    'key' => Member::STATUS_VERIFIED,
                    'label' => '認証済み'
                ],
                'maker_id' => $user->maker_id ?? null,
                'is_new_system_user' => true,
                'group_ids' => [$user->getGroupId()] // ユーザーグループID
            ],
        ];

        $member = new Member($memberData);
        $member->setAccessToken($accessToken);
        $member->isIdentityOn();

        Log::info('UserResolver: Successfully converted new system user to Member entity');
        return $member;
    }

    /**
     * 新システムユーザー用のアクセストークンを生成
     */
    private function generateNewSystemAccessToken(GeneralUser|SwbUser|MakerUser $user): AccessToken
    {
        Log::debug('UserResolver: Generating new system access token for user ID: ' . $user->id);
        Log::debug('UserResolver: User type: ' . get_class($user));

        try {
            $tokensTable = TableRegistry::getTableLocator()->get($user->getTokensTableName());

            // 新しいAPIアクセストークンを作成（既存トークンの無効化も含む）
            $tokenEntity = $tokensTable->createApiAccessToken($user->id, 24);

            if ($tokenEntity) {
                Log::info('UserResolver: New system access token generated successfully');

                return new AccessToken([
                    'access_token' => ['value' => $tokenEntity->token],
                    'expires_at' => $tokenEntity->expires->getTimestamp()
                ]);
            }

        } catch (Exception $e) {
            Log::error('UserResolver: Failed to generate new system access token: ' . $e->getMessage());
        }

        // フォールバック: 一時的なトークンを生成
        Log::warning('UserResolver: Using fallback temporary token');
        return new AccessToken([
            'access_token' => ['value' => bin2hex(random_bytes(32))],
            'expires_at' => time() + (24 * 60 * 60)
        ]);
    }
}

