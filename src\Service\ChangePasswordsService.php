<?php

namespace App\Service;

use App\Kuroko\ApiModel\KurokoApiDynamic\Logins;
use App\Kuroko\Entity\IKurokoEntity;
use App\Kuroko\Entity\Member;
use Cake\Datasource\EntityInterface;
use BadMethodCallException;

abstract class ChangePasswordsService implements IRestService
{
    use ServiceTrait;

    protected array $_defaultConfig = [];

    public function add(array $data = []): EntityInterface|null|IKurokoEntity
    {
        throw new BadMethodCallException();
    }

    public function edit(string $id, array $data = []): EntityInterface|null|IKurokoEntity
    {
        /** @var Member $member */
        $member = $this->getIdentity();
        $logins = new Logins();
        $response = $logins->resetPassword($member, $data);

        if ($response == "") {
            $this->sendPasswordResetCompleted($member);
        } else {
            $this->setErrors([
                [
                    //@todo 内容管理
                    'message' => $response
                ]
            ]);
        }

        return null;
    }

    abstract protected function sendPasswordResetCompleted(Member $member): void;

    public function view(string $id, array $data = []): EntityInterface|null|IKurokoEntity
    {
        throw new BadMethodCallException();
    }

    public function index(array $data = []): array|EntityInterface|null|IKurokoEntity
    {
        throw new BadMethodCallException();
    }

    public function delete(string $id, array $data = []): bool
    {
        throw new BadMethodCallException();
    }

    public function initialize(): void {}
}
