<?php
declare(strict_types=1);

namespace App\Model\Table;

use Cake\I18n\FrozenTime;
use Cake\ORM\Query;
use Cake\ORM\RulesChecker;
use Cake\ORM\Table;
use Cake\Validation\Validator;

/**
 * 予算テーブル
 */
class BudgetsTable extends Table
{
    public function initialize(array $config): void
    {
        parent::initialize($config);

        $this->setTable('budgets');
        $this->setDisplayField('id');
        $this->setPrimaryKey('id');

        // アソシエーション
        $this->belongsTo('Products', [
            'foreignKey' => 'product_id',
            'joinType' => 'INNER',
        ]);
    }

    public function validationDefault(Validator $validator): Validator
    {
        $validator
            ->integer('product_id')
            ->requirePresence('product_id', 'create')
            ->notEmptyString('product_id');

        $validator
            ->integer('type')
            ->requirePresence('type', 'create')
            ->notEmptyString('type')
            ->range('type', [1, 2], __('カタログタイプは1（紙）または2（デジタル）を入力してください'));

        $validator
            ->integer('price')
            ->requirePresence('price', 'create')
            ->notEmptyString('price')
            ->greaterThan('price', 0, __('単価は0より大きい値を入力してください'));

        $validator
            ->integer('budget_quantity')
            ->requirePresence('budget_quantity', 'create')
            ->notEmptyString('budget_quantity')
            ->greaterThan('budget_quantity', 0, __('予算数量は0より大きい値を入力してください'));

        $validator
            ->boolean('is_active')
            ->notEmptyString('is_active');

        $validator
            ->dateTime('start_date')
            ->requirePresence('start_date', 'create')
            ->notEmptyDateTime('start_date');

        $validator
            ->dateTime('end_date')
            ->requirePresence('end_date', 'create')
            ->notEmptyDateTime('end_date');

        $validator
            ->integer('priority')
            ->notEmptyString('priority')
            ->greaterThanOrEqual('priority', 0, __('優先順位は0以上で入力してください'));

        return $validator;
    }

    public function buildRules(RulesChecker $rules): RulesChecker
    {
        $rules->add($rules->existsIn(['product_id'], 'Products'), __('指定された商品が存在しません'));
        
        // 終了日が開始日より後であることをチェック
        $rules->add(function ($entity, $options) {
            if ($entity->start_date && $entity->end_date) {
                return $entity->end_date > $entity->start_date;
            }
            return true;
        }, 'validDateRange', [
            'errorField' => 'end_date',
            'message' => __('終了日は開始日より後の日付を入力してください')
        ]);

        return $rules;
    }

    /**
     * アクティブな予算を取得するファインダー
     */
    public function findActive(Query $query, array $options): Query
    {
        return $query->where(['is_active' => true]);
    }

    /**
     * 現在有効な予算を取得するファインダー
     */
    public function findCurrentlyActive(Query $query, array $options): Query
    {
        $now = FrozenTime::now();
        return $query->where([
            'is_active' => true,
            'start_date <=' => $now,
            'end_date >=' => $now
        ]);
    }

    /**
     * 商品別に予算を取得するファインダー
     */
    public function findByProduct(Query $query, array $options): Query
    {
        $productId = $options['product_id'] ?? null;
        if ($productId !== null) {
            $query->where(['product_id' => $productId]);
        }
        return $query;
    }

    /**
     * カタログタイプ別に予算を取得するファインダー
     */
    public function findByType(Query $query, array $options): Query
    {
        $type = $options['type'] ?? null;
        if ($type !== null) {
            $query->where(['type' => $type]);
        }
        return $query;
    }

    /**
     * 優先順位でソートするファインダー
     */
    public function findSorted(Query $query, array $options): Query
    {
        return $query->order(['priority' => 'ASC', 'start_date' => 'ASC']);
    }

    /**
     * 指定商品の現在有効な予算を取得
     */
    public function getCurrentActiveBudgetByProduct(int $productId, int $type): ?object
    {
        return $this->find('currentlyActive')
            ->where([
                'product_id' => $productId,
                'type' => $type
            ])
            ->order(['priority' => 'ASC'])
            ->first();
    }

    /**
     * 指定期間内の予算一覧を取得
     */
    public function getBudgetsByPeriod(FrozenTime $startDate, FrozenTime $endDate): Query
    {
        return $this->find('active')
            ->where([
                'OR' => [
                    [
                        'start_date <=' => $endDate,
                        'end_date >=' => $startDate
                    ]
                ]
            ])
            ->contain(['Products'])
            ->order(['start_date' => 'ASC']);
    }

    /**
     * 予算残数を計算
     */
    public function calculateRemainingBudget(int $budgetId): int
    {
        $budget = $this->get($budgetId);
        
        // TODO: RandselOrdersテーブルから使用済み数量を取得して計算
        // 現在は予算数量をそのまま返す
        return $budget->budget_quantity;
    }
}
