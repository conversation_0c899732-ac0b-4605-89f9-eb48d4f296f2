<?php

namespace App\Controller;

use App\Kuroko\Entity\IKurokoEntity;
use App\Service\IRestService;
use Cake\Datasource\EntityInterface;
use Cake\Event\Event;
use Cake\Http\Exception\MethodNotAllowedException;
use Exception;

/**
 * API処理統合親クラス
 */
abstract class ApiController extends AppController
{

    /**
     * @return void
     * @throws Exception
     */
    public function initialize(): void
    {
        parent::initialize();
    }

    /**
     * serviceのaction処理実行
     * @param IRestService $restService
     * @param callable $action
     * @param $data
     * @return void
     */
    protected function _execute(IRestService $restService, callable $action, $data): void
    {
        if ($restService->getForm()->execute($data) === false) {
            //バリデーションエラー
            $restService->setErrors($restService->getForm()->getErrors());
        } else {
            // action処理の実行
            $result = $action($restService, $data);
            if ($restService->isValid()) {
                //  成功レスポンス処理へ
                $this->_success($result);
                return;
            }
        }
        $this->_error($restService->getErrors());

    }

    /**
     * エラー時イベントの定義
     * @param callable $callable
     * @return void
     */
    protected function _onError(callable $callable): void
    {
        $this->getEventManager()->on('RestApi.error', $callable);
    }


    /**
     * エラー発生時の処理
     * @param array $errors
     * @return void
     */
    protected function _error(array $errors): void
    {
        // 張られているエラーイベントを発火
        $event = new Event('RestApi.error', $this, [
            'errors' => $errors
        ]);
        $this->getEventManager()->dispatch($event);
        $this->set(['errors' => $errors]);
        $this->viewBuilder()->setOption('serialize', [
            'errors'
        ]);
    }


    /**
     * 成功時イベントの定義
     * @param callable $callable
     * @return void
     */
    protected function _onSuccess(callable $callable): void
    {
        $this->getEventManager()->on('RestApi.success', $callable);
    }

    /**
     * 成功時の処理
     * @param EntityInterface|array|bool|IKurokoEntity $resource
     * @return void
     */
    protected function _success(EntityInterface|IKurokoEntity|array|bool|null $resource): void
    {
        $event = new Event('RestApi.success', $this, [
            'result' => $resource
        ]);
        // 成功時イベントの発火（結果レスポンス）
        $this->getEventManager()->dispatch($event);
    }


    /**
     * 結果レスポンスをJSONとして返却するために設定
     * エラー時は _OnError にて処理する
     * @param array $response
     * @return void
     */
    protected function _setSuccessResponse(array $response): void
    {
        $serialize = array_keys($response);
        foreach ($response as $key => $value) {
            $this->set($key, $value);
        }
        $this->viewBuilder()->setOption('serialize', $serialize);
    }


    /**
     * 一覧取得API
     * @param IRestService $restService
     * @return void
     */
    protected function _index(IRestService $restService): void
    {
        $data = $this->getRequest()->getQuery();
        $this->_execute($restService, function (IRestService $restService, array $data) {
            return $restService->index($data);
        }, $data);
    }

    /**
     * 登録API
     * @param IRestService $restService
     * @return void
     */
    protected function _add(IRestService $restService): void
    {
        $data = $this->getRequest()->getData();
        $this->_execute($restService, function (IRestService $restService, array $data) {
            return $restService->add($data);
        }, $data);
    }

    /**
     * 詳細取得API
     * @param string $id
     * @param IRestService $restService
     * @return void
     */
    protected function _view(string $id, IRestService $restService): void
    {
        $data = $this->getRequest()->getQuery();
        $this->_execute($restService, function (IRestService $restService, array $data) use ($id) {
            return $restService->view($id, $data);
        }, $data);
    }

    /**
     * 編集API
     * @param string $id
     * @param IRestService $restService
     * @return void
     */
    protected function _edit(string $id, IRestService $restService): void
    {
        $data = $this->getRequest()->getData();
        $this->_execute($restService, function (IRestService $restService, array $data) use ($id) {
            return $restService->edit($id, $data);
        }, $data);
    }


    /**
     * 削除API
     * @param string $id
     * @param IRestService $restService
     * @return void
     */
    protected function _delete(string $id, IRestService $restService): void
    {
        $data = $this->getRequest()->getData();
        $this->_execute($restService, function (IRestService $restService, array $data) use ($id) {
            return $restService->delete($id, $data);
        }, $data);
    }

    /**
     * ダミーメソッド（利用する場合は継承先でoverride）
     * @return void
     */
    public function index(): void
    {
        throw new MethodNotAllowedException();
    }

    /**
     * ダミーメソッド（利用する場合は継承先でoverride）
     * @param $id
     * @return void
     */
    public function view($id = null): void
    {
        throw new MethodNotAllowedException();
    }

    /**
     * ダミーメソッド（利用する場合は継承先でoverride）
     * @return void
     */
    public function add(): void
    {
        throw new MethodNotAllowedException();

    }

    /**
     * ダミーメソッド（利用する場合は継承先でoverride）
     * @param $id
     * @return void
     */
    public function edit($id = null): void
    {
        throw new MethodNotAllowedException();

    }

    /**
     * ダミーメソッド（利用する場合は継承先でoverride）
     * @param $id
     * @return void
     */
    public function delete($id = null): void
    {
        throw new MethodNotAllowedException();

    }
}
