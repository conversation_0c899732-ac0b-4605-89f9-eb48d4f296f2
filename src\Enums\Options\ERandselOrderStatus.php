<?php

namespace App\Enums\Options;

enum ERandselOrderStatus: int implements IEnumOptions
{
    use EnumOptionsTrait;

    case APPROVED = 1;
    case REJECTED = 2;
    case PENDING = 0;

    /**
     * @return string
     */
    public function description(): string
    {
        return match ($this) {
            self::APPROVED => '承認',
            self::REJECTED => '否認',
            self::PENDING => '承認待ち',
        };
    }
}
