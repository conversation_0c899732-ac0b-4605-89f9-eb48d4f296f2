server {
    gzip on;
    gzip_types application/json text/html;

    listen 80;
    client_max_body_size 20M;
    server_name localhost;

    charset UTF-8;
    root  /opt/coverme/tmp;
    index index.php index.html;

    location / {
        try_files $uri $uri/ /index.php?$args;
    }

    location ~* /\. {
        deny all;
    }

    error_page 500 = /500.json;

    location /catalog {
        alias /opt/coverme/app-nuxt/.output/public;
        try_files $uri $uri/ /index.html;
   }

    location /500.json {
        return 500 "{\"error\":\"System Error\",\"status\":500}";
    }

    location /app-api {
        #root /opt/coverme/webroot;
        alias /opt/coverme/webroot;
        #try_files $uri /app-api/index.php?$args;
        try_files $uri $uri/ /app-api/index.php?$args;
        #try_files $uri $uri/ /app-api/index.php?$query_string;
        index index.php index.html;

        # 静的ファイルの処理
        location ~* \.(jpg|jpeg|gif|css|png|js|ico|html|svg|woff|woff2)$ {
            access_log off;
            expires max;
            log_not_found off;
        }

        # CakePHPのルーティング対応
        if (!-e $request_filename) {
            rewrite ^/app-api/(.*)$ /app-api/index.php?$1 last;
        }

        # PHPファイルの処理
        location ~ ^/app-api/.+\.php$ {
            fastcgi_split_path_info ^/app-api(.+?\.php)(/.*)$;
            fastcgi_pass 127.0.0.1:9000;
            fastcgi_index index.php;
            include fastcgi_params;
    #        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
            fastcgi_param SCRIPT_FILENAME $request_filename;
            fastcgi_param APPLICATION_ENV docker;
            fastcgi_buffers 256 128k;
            fastcgi_buffer_size 128k;
            fastcgi_intercept_errors on;
            fastcgi_read_timeout 120s;
            proxy_pass_header Authorization;
        }
    }

    location ~ \.php$ {
        fastcgi_split_path_info ^(.+?\.php)(/.*)$;
        fastcgi_pass 127.0.0.1:9000;
        fastcgi_index index.php;
        include fastcgi_params;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        fastcgi_param APPLICATION_ENV docker;
        fastcgi_buffers 256 128k;
        fastcgi_buffer_size 128k;
        fastcgi_intercept_errors on;
        fastcgi_read_timeout 120s;
        proxy_pass_header Authorization;
    }

    error_page 405 =200 $uri;
}
