<?php

namespace App\Service;

use App\Enums\EntityFields\ESwbInvoiceForm;
use App\Model\Entity\RandselInvoice;
use App\Model\Table\RandselInvoicesTable;
use Cake\Log\Log;
use Cake\Utility\Hash;
use Exception;

class RandselInvoicesService implements IService
{
    use ServiceTrait;

    protected array $_defaultConfig = [
        "defaultModel" => RandselInvoicesTable::class,
    ];

    /**
     * 請求金額データを返却（1件）
     * @param array $conditions
     * @return RandselInvoice
     */
    public function getInvoice(array $conditions = []): RandselInvoice|null
    {
        $invoice = $this->getDefaultModel()->find('all', ['conditions' => $conditions])->first();

        return $invoice;
    }

    /**
     * 請求金額データを返却
     * @param array $conditions
     * @param string|null $indexBy (オプション) indexBy を適用するフィールド名。null の場合は適用しない
     * @return array
     */
    public function getInvoices(array $conditions = [], ?string $indexBy = null): array 
    {
        $invoices = $this->getDefaultModel()->find('all', ['conditions' => $conditions]);

        if ($indexBy !== null) {
            $invoices = $invoices->all()->indexBy($indexBy);
        } else {
            $invoices = $invoices->all();
        }

        return $invoices->toArray();
    }

    public function initialize(): void {}

    /**
     * 請求金額データを登録
     * @param array $conditions
     * @return array
     */
    public function createRandselInvoice(array $data = []): bool
    {
        $entity = $this->getDefaultModel()->newEntity($data);

        if (!$this->save($entity)) {
            return false;
        }

        return true;
    }

    /**
     * 請求金額データを更新
     * @param array $conditions
     * @return array
     */
    public function updateRandselInvoice(RandselInvoice $entity, array $data = []): bool
    {
        try {
            if (!$this->save($entity)) {
                return false;
            }
    
            return true;
        } catch (Exception $e) {
            $this->setErrors([
                '_system' => $e->getMessage(),
            ]);
        }
        return false;
    }
}
