{"name": "nuxt-app", "private": true, "type": "module", "scripts": {"build": "nuxt build", "local:dist:serve": "serve dist -l 3001", "local:build": "nuxt build --dotenv .env.local", "local:dev": "nuxt dev --dotenv .env.local", "local:generate": "nuxt generate --dotenv .env.local", "develop:generate": "nuxt generate --dotenv .env.develop", "staging:generate": "nuxt generate --dotenv .env.staging", "production:generate": "nuxt generate --dotenv .env.production", "preview": "nuxt preview", "postinstall": "nuxt prepare"}, "dependencies": {"@fontsource/noto-sans-jp": "^5.1.0", "@mdi/font": "^7.4.47", "@nuxtjs/device": "^3.2.4", "@pinia/nuxt": "^0.5.1", "@vuepic/vue-datepicker": "^10.0.0", "axios": "^1.6.8", "encoding-japanese": "^2.2.0", "moment": "^2.30.1", "nuxt": "^3.11.2", "papaparse": "^5.4.1", "pinia": "^2.1.7", "vanilla-autokana": "^1.3.0", "vue": "^3.4.21", "vue-router": "^4.3.0"}, "devDependencies": {"@nuxt/eslint": "^0.3.9", "@typescript-eslint/eslint-plugin": "^7.7.1", "@typescript-eslint/parser": "^7.7.1", "eslint": "^8.57.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-unused-imports": "^3.1.0", "eslint-plugin-vue": "^9.25.0", "prettier": "^3.2.5", "sass": "^1.77.1", "serve": "^14.2.3", "vite-plugin-vuetify": "^2.0.3", "vuetify": "^3.5.17"}}