<?php

namespace App\Validation\Validator;

use App\Enums\EntityFields\ESchoolBagForm;
use App\Enums\EntityFields\EUser;
use App\Enums\EValidationErrorMessage;
use App\Model\Table\UsersTable;
use App\Validation\IValidator;
use App\Validation\ValidatorTrait;
use Cake\Validation\Validator;

class SchoolBagFormValidator implements IValidator
{
    use ValidatorTrait;


    // static private function loginId(Validator $validator, array $options = []): Validator
    // {
    //     $insertText = [
    //         'field' => ESchoolBagForm::LOGIN_ID->description(),
    //     ];
    //     $field = ESchoolBagForm::LOGIN_ID->value;
    //     return LoginValidator::validateLoginId($field, $validator, $insertText);
    // }

    static private function loginPwd(Validator $validator, array $options = []): Validator
    {
        $insertText = [
            'field' => ESchoolBagForm::LOGIN_PWD->description(),
        ];
        $field = ESchoolBagForm::LOGIN_PWD->value;
        return LoginValidator::validateLoginPw($field, $validator, $insertText);
    }

    static private function name1(Validator $validator, array $options = []): Validator
    {
        $insertText = [
            'field' => ESchoolBagForm::NAME1->description(),
        ];
        $field = ESchoolBagForm::NAME1->value;
//        $validator
//            ->maxLength($field, 30, EValidationErrorMessage::MAX_LENGTH->format(
//                $insertText + [
//                    'max' => 100
//                ]
//            ))
//            ->requirePresence($field, true, EValidationErrorMessage::REQUIRE_PRESENCE->format($insertText))
//            ->notEmptyString($field, EValidationErrorMessage::NOT_EMPTY_STRING->format($insertText));
        return MemberValidator::validateName($field, $validator, $insertText);
    }

    static private function email(Validator $validator, array $options = []): Validator
    {
        $insertText = [
            'field' => ESchoolBagForm::EMAIL->description(),
        ];
        $field = ESchoolBagForm::EMAIL->value;
        $validator
            ->email($field, false, EValidationErrorMessage::EMAIL->format($insertText))
            ->requirePresence($field, true, EValidationErrorMessage::REQUIRE_PRESENCE->format($insertText))
            ->notEmptyString($field, EValidationErrorMessage::NOT_EMPTY_STRING->format($insertText));
        return $validator;
    }


}
