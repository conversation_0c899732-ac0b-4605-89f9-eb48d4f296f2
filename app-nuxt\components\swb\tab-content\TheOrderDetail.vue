<script setup lang="ts">
import { SwbMyBridge } from "~~/src/models/bridge/SwbMyBridge";
import TheSwbOrderSearchForm from "~/components/swb/form/TheSwbOrderSearchForm.vue";
import TheOrderSummary from "~/components/swb/tab-content/order-detail/TheOrderSummary.vue";
import TheSwbOrders from "~/components/swb/tab-content/order-detail/TheSwbOrders.vue";
import TheLoading from "~/components/parts/TheLoading.vue";

defineProps<{
    bridge: SwbMyBridge;
}>();
</script>

<template>
    <div>
        <the-swb-order-search-form :bridge="bridge" />
        <template v-if="bridge.is_order_loaded">
            <the-order-summary :bridge="bridge" />
            <the-swb-orders :bridge="bridge" />
        </template>
        <v-row v-else justify="center" class="mt-10">
            <the-loading color="swbPrimary">
                集計しています、しばらくお待ちください
            </the-loading>
        </v-row>
    </div>
</template>

<style scoped></style>
