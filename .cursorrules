**Project Development Guide (CakePHP 4.5)**

**Introduction**

This guide aims to provide a unified set of development standards and best practices for projects based on CakePHP 4.x. Following this guide helps ensure code quality, maintainability, scalability, and team collaboration efficiency. This project uses PHP 8.1+ and CakePHP 4.5+.

**Core Principles**

1.  **Coding Standards:** Strictly adhere to PSR-12 coding standards. Use `cakephp/cakephp-codesniffer` for checking (`composer cs-check`) and fixing (`composer cs-fix`).
2.  **Conciseness and Efficiency:** Write concise, purposeful technical code with necessary comments.
3.  **SOLID Principles:** Actively apply SOLID principles in object-oriented design to enhance code robustness and flexibility.
4.  **Modularity and Reusability:** Prioritize iterative and modular design. Reduce code duplication through Services, Behaviors, Components, Helpers, and Traits.
5.  **Clear Naming:** Use descriptive and consistent naming conventions (for classes, methods, variables, database fields, etc.).
6.  **Dependency Injection:** Prefer constructor or method injection for managing dependencies. Utilize CakePHP's container (if applicable) or inject manually.
7.  **Framework First:** Fully leverage the built-in features and helper functions provided by the CakePHP framework to avoid reinventing the wheel.

**PHP & CakePHP Standards**

1.  **PHP Version:** Use PHP 8.1+ and leverage its new features when appropriate (e.g., typed properties, `match` expressions, enums).
2.  **Strict Types:** Enable strict types `declare(strict_types=1);` at the top of all PHP files.
3.  **Type Hinting:** Add explicit type declarations for method parameters, return values, and class properties.
4.  **File Structure:** Follow CakePHP's standard directory structure (`src`, `config`, `templates`, `tests`, etc.). Use lowercase for directory names.
5.  **Error Handling & Logging:**
    *   Use CakePHP's built-in exception handling mechanisms.
    *   Create custom exception classes as needed (e.g., place them in `src/Exception`).
    *   Use `try-catch` blocks for foreseeable errors (like API call failures).
    *   Configure and use logging (e.g., the `MonologLog` engine, as `monolog/monolog` is included via `composer.json`) to record critical information and errors.
6.  **Validation:**
    *   Use CakePHP's Validators to define data validation rules (typically within Table or Form classes).
    *   For complex forms or API inputs, use CakePHP Form objects (`src/Form`) for data processing and validation.
7.  **Middleware:** Use Middleware to handle cross-request logic, such as authentication (`cakephp/authentication`, `UserAuthenticationMiddleware`), CORS (`CorsMiddleware`), CSRF protection, etc. Register middleware in the `middleware()` method of `src/Application.php`.
8.  **ORM:**
    *   Use the CakePHP ORM (Table / Entity / Query Builder) for database interactions.
    *   Prefer ORM methods over raw SQL queries.
    *   Define table relationships (Associations) and finder methods (Finders) in Table classes (`src/Model/Table`).
    *   Define entity property accessors and mutators in Entity classes (`src/Model/Entity`).
    *   For reusable query logic or data processing logic, consider using Behaviors (`src/Model/Behavior`).
9.  **Database Migrations & Seeding:**
    *   Use the `cakephp/migrations` plugin to manage database schema changes (`config/Migrations`).
    *   Use Seeders (`config/Seeds`) to initialize or populate development/test data.
10. **Naming Conventions:**
    *   Class Names: PascalCase (e.g., `UsersController`, `RandselOrdersTable`, `ClientAuthenticationsService`).
    *   Method Names: camelCase (e.g., `viewOrder`, `calculateTotal`).
    *   Database Table Names: snake_case, plural (e.g., `randsel_orders`, `randsel_invoices`).
    *   Database Column Names: snake_case, singular (e.g., `user_id`, `order_date`).
    *   Variable Names: camelCase (e.g., `$orderCount`, `$currentUser`).
    *   Constants/Enum Members: UPPER_SNAKE_CASE (e.g., `ERandselOrderStatus::PENDING`).

**Project-Specific Practices & Conventions**

1.  **MVC Architecture:** Strictly follow the Model-View-Controller pattern.
    *   **Controllers (`src/Controller`):** Keep controllers thin. Their main responsibilities are receiving requests, calling services or models, preparing data, and selecting the view. Avoid complex business logic. Consider making controllers `final`.
    *   **Models (`src/Model`):** Table classes handle database interactions and query logic. Entity classes represent single data records.
    *   **Views (`templates`):** Use CakePHP's PHP templating engine. Place reusable view snippets in `templates/element/`. Use Helpers to encapsulate view logic.
2.  **Service Layer (`src/Service`):**
    *   Used to encapsulate complex business logic, coordinate operations across multiple models, or interact with external services (like the Kuroko API).
    *   Service classes should be designed to be injectable and are typically stateless. Consider making service classes `final`.
    *   Controllers obtain service instances via dependency injection (recommended) or load them when needed.
3.  **Kuroko Integration (`src/Kuroko`):**
    *   API Clients (`KurokoApiClient`, `KurokoApiMockClient`) are responsible for communication with the Kuroko API.
    *   API Models (`src/Kuroko/ApiModel`) encapsulate the logic for calling specific API endpoints.
    *   Entities (`src/Kuroko/Entity`) represent the data structures received from the Kuroko API.
    *   Keep Mock data (`src/Kuroko/Http/Client/Mock`) synchronized with actual API responses for testing and development.
4.  **Authentication (`src/Authentication`):**
    *   Use the `cakephp/authentication` plugin for user authentication.
    *   `UserAuthenticationService` and `UserAuthenticationServiceProvider` configure the authentication service.
    *   Custom Authenticators (`UserAuthenticator`) and Identifiers (`UserIdentifier`) handle specific authentication logic.
5.  **Form Handling (`src/Form`):**
    *   Use Form classes for non-model-bound forms or scenarios requiring complex validation/processing logic.
6.  **Command-Line Tools (`src/Command`):**
    *   Use CakePHP Command classes to create command-line tasks (e.g., `InvoiceCalculationCommand`, `KurocoOrdersToDbCommand`).
    *   Execute `bin/cake` commands periodically via Cron or other schedulers.
7.  **Mailer (`src/Mailer`):**
    *   Use `AppMailer` or specific Mailer classes to send emails.
    *   Define clear Sender classes (`src/Mailer/Sender`) to encapsulate the sending logic and template selection for different email types.
    *   Use the `FrontLink` service to generate links to the frontend application.
8.  **PDF Generation:**
    *   Utilize `friendsofcake/cakepdf` and `dompdf/dompdf` to generate PDF files (e.g., invoices via `SwbInvoicePdfService`).
    *   Font files are stored in the `pdfFont` directory.
9.  **Routing (`config/routes.php`):**
    *   Define clear, RESTful routing rules.
    *   Use route scopes and prefixes to organize related routes (e.g., `Front`, `Api`, `NuxtBuild`).
10. **Testing (`tests`):**
    *   Use PHPUnit to write Unit and Integration tests.
    *   Follow the structure under `tests/TestCase` to organize test cases.
    *   Write tests for critical components like Services, Tables, Commands, Controllers, etc.
    *   Use Fixtures (`tests/Fixture`) to provide test data.
    *   Utilize Mock objects (like `KurokoApiMockClient`) to isolate external dependencies.
    *   Run tests: `composer test`.
11. **Configuration Management:**
    *   Core configuration resides in `config/app*.php` files.
    *   Use environment variables for sensitive information (like API keys, database passwords), loaded via `josegonzalez/dotenv` (`config/app_dot_env.php`) from a `.env` file. Provide `app_local.example.php` as a template for local configuration.
12. **Security:**
    *   Enable and configure CakePHP's CSRF protection middleware.
    *   Validate and sanitize all user input.
    *   Use `cakephp/authentication` and CakePHP's authorization features to control access.
13. **API Design (If Applicable):**
    *   Design API interfaces (`ApiController`, `ApiForm`) following RESTful principles.
    *   Use a consistent JSON response structure. Consider using view variables or custom response classes to format output.
    *   Implement API versioning if providing a stable public API is required.
14. **Caching:**
    *   Utilize CakePHP's caching engines (`config/app_cache.php`) to cache frequently accessed data or computation results, improving performance.

**Conclusion**

This guide aims to provide a solid foundation for ensuring consistency and high quality in the project's codebase. This guide may be updated as the project evolves. All team members should familiarize themselves with and adhere to these conventions.

---