<?php

namespace App\Controller\Front;

use App\Controller\FrontController;
use App\Form\DisableParametersForm;
use App\Service\SwbMonthlyInvoicesService;
use Cake\Event\Event;
use Cake\Log\Log;

class SwbMonthlyInvoicesController extends FrontController
{
    public function index(): void
    {
        $this->_onSuccess(function (Event $event, array $randselOrders) {
            $this->_setSuccessResponse([
                "monthly_invoices" => $randselOrders
            ]);
        });
        $this->_onError(function (Event $event, array $errors) {
            Log::debug(print_r($errors, true));
        });
        $this->_index((new SwbMonthlyInvoicesService())
            ->setIdentity($this->Authentication->getIdentity())
            ->setForm(
                new DisableParametersForm()
            ));
    }
}
