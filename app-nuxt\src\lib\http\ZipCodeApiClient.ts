import axios from "axios";
// import https from "https";
import type { RuntimeConfig } from "nuxt/schema";
import { ZIP_CODE_API_HEADER__KEY } from "~~/src/config";

export default abstract class ZipCodeApiClient {
    protected config: RuntimeConfig;

    constructor(protected runTimeConfig: RuntimeConfig) {
        this.config = runTimeConfig;
    }

    // 静的メソッドでサブクラスのインスタンスを生成
    static create<T extends ZipCodeApiClient>(
        this: new (config: RuntimeConfig) => T,
        runTimeConfig: RuntimeConfig,
    ): T {
        return new this(runTimeConfig);
    }

    private getHeader(): ObjType {
        return {
            [ZIP_CODE_API_HEADER__KEY]: String(
                this.config.public.zipCodeApiKey,
            ),
        };
    }

    // 郵便番号検索
    public async get<T>(postcode: string): Promise<T> {
        const response = await axios.get(
            // パスパラメータで postcode を指定するようにURLを修正
            `${this.config.public.zipCodeApiEndpoint}${postcode}`,
            {
                headers: this.getHeader(),
            },
        );
        return response.data as T;
    }
}
