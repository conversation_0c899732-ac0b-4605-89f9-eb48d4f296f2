<?php
declare(strict_types=1);

namespace App\Model\Entity;

use Cake\ORM\Entity;
use Cake\I18n\FrozenTime;

/**
 * メーカーユーザートークンエンティティ
 * 
 * @property int $id
 * @property int $maker_user_id
 * @property string $token
 * @property string $type
 * @property \Cake\I18n\FrozenTime $expires
 * @property \Cake\I18n\FrozenTime $created
 * @property \Cake\I18n\FrozenTime $modified
 * @property \App\Model\Entity\MakerUser $maker_user
 */
class MakerUserToken extends Entity
{
    // トークンタイプの定数
    const TYPE_PASSWORD_RESET = 'password_reset';
    const TYPE_API_ACCESS = 'api_access';
    const TYPE_EMAIL_VERIFICATION = 'email_verification';

    protected $_accessible = [
        'maker_user_id' => true,
        'token' => true,
        'type' => true,
        'expires' => true,
        'created' => true,
        'modified' => true,
        'maker_user' => true,
    ];

    protected $_hidden = [
        'token',
    ];

    /**
     * トークンが有効かどうかを判定
     */
    public function isValid(): bool
    {
        return $this->expires > FrozenTime::now();
    }

    /**
     * トークンが期限切れかどうかを判定
     */
    public function isExpired(): bool
    {
        return !$this->isValid();
    }

    /**
     * パスワードリセット用トークンかどうかを判定
     */
    public function isPasswordResetToken(): bool
    {
        return $this->type === self::TYPE_PASSWORD_RESET;
    }

    /**
     * API アクセス用トークンかどうかを判定
     */
    public function isApiAccessToken(): bool
    {
        return $this->type === self::TYPE_API_ACCESS;
    }

    /**
     * メール認証用トークンかどうかを判定
     */
    public function isEmailVerificationToken(): bool
    {
        return $this->type === self::TYPE_EMAIL_VERIFICATION;
    }
}
