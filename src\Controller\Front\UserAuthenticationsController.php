<?php

namespace App\Controller\Front;

use App\Controller\FrontController;
use App\Form\Front\UserAuthenticationsAddForm;
use App\Form\DisableParametersForm;
use App\Kuroko\Entity\Member;
use App\Service\UserAuthenticationsService;
use Cake\Event\Event;
use Cake\Event\EventInterface;
use Cake\Log\Log;

class UserAuthenticationsController extends FrontController
{


    public function beforeFilter(EventInterface $event): void
    {
        parent::beforeFilter($event);
        $this->Authentication->addUnauthenticatedActions(['add']);
    }


    /**
     * ログイン
     * @return void
     */
    public function add(): void
    {
        $this->_onSuccess(function (Event $event, Member $member) {
            // 成功時にはトークンとユーザ情報を返却する
            $accessToken = $member->getAccessToken();
            $response = [
                'member' => $member,
                'access_token' => $accessToken->encryptToken(),
            ];

            // 新システムユーザーの場合は追加情報を含める
            if ($accessToken->getExpiresAt()) {
                $response['expires_at'] = $accessToken->getExpiresAt();
            }

            // ユーザータイプの判定（グループIDから）
            $userType = 'general'; // デフォルト
            if ($member->isBelongsToSwbGroup()) {
                $userType = 'swb';
            } elseif ($member->isBelongsToClientGroup()) {
                $userType = 'maker';
            }
            $response['user_type'] = $userType;

            Log::info("UserAuthenticationsController: Login successful for user type: {$userType}");
            $this->_setSuccessResponse($response);
        });
        $this->_onError(function (Event $event, array $errors) {
            Log::debug(print_r($errors, true));
        });
        $this->_add(
            (new UserAuthenticationsService())
                ->setAuthenticationComponent($this->Authentication)
                ->setForm(
                    new UserAuthenticationsAddForm()
                )
        );
    }

    /**
     * ログアウト
     * @return void
     */
    public function delete($id = null): void
    {
        $this->_onSuccess(function (Event $event, bool $success) {
            // 成功時にログアウト成功を返却する
            $this->_setSuccessResponse([
                'success' => $success
            ]);
        });
        $this->_onError(function (Event $event, array $errors) {
            Log::debug(print_r($errors, true));
        });
        $this->_delete(
            $id,
            (new UserAuthenticationsService())
                ->setIdentity($this->Authentication->getIdentity())
                ->setForm(
                    new DisableParametersForm()
                )
        );
    }
}
