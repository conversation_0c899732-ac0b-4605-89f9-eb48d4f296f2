<?php

namespace App\Kuroko\ApiModel;

abstract class KurokoApiPrivateStaticModel extends KurokoApiModel
{

    protected function getEndPoint(string $endpoint)
    {
        return parent::getEndPoint("privateStatic." . $endpoint);
    }


    public function __construct(array $config = [])
    {
        parent::__construct($config);
        $this->setToken($this->getConfig("token.privateStatic"));
    }

}
