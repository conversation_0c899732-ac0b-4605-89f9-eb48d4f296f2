<?php

namespace App\Kuroko\ApiModel\KurokoApiDynamic;

use App\Kuroko\ApiModel\KurokoApiDynamicModel;
use App\Kuroko\Entity\AccessToken;
use App\Kuroko\Entity\Login;
use App\Kuroko\Entity\Member;
use Cake\Log\Log;
use Cake\Utility\Hash;

class Logins extends KurokoApiDynamicModel
{

    /**
     * ログイン
     * @param array $data
     * @return Login|null
     */
    public function login(array $data): ?Login
    {
        // 認証していないので、システムユーザで強制的に実行
        $this->_setSysUserToken();
        $response = $this->post($this->getEndPoint("login"), json_encode($data));
        if ($response->isSuccess() && $body = $response->getStringBody()) {
            if ($data = json_decode($body, true)) {
                if (Hash::check($data, "grant_token")) {
                    return new Login($data);
                }
            }
        }
        Log::debug(__METHOD__ . " response: " . $response->getStringBody());
        return null;
    }

    /**
     * grantTokenからトークン取得
     * @param string $grantToken
     * @return AccessToken|null
     */
    public function token(string $grantToken): ?AccessToken
    {
        // 認証していないので、システムユーザで強制的に実行
        $this->_setSysUserToken();
        $response = $this->post($this->getEndPoint("loginToken"), json_encode([
            "grant_token" => $grantToken
        ]));
        if ($response->isSuccess() && $body = $response->getStringBody()) {
            if ($data = json_decode($body, true)) {
                if (Hash::check($data, "access_token")) {
                    return new AccessToken($data);
                }
            }
        }
        Log::debug(__METHOD__ . " response: " . $response->getStringBody());
        return null;
    }

    /**
     * ログアウト
     * @param AccessToken $accessToken
     * @return bool
     */
    public function logout(AccessToken $accessToken): bool
    {
        $this->setToken($accessToken->getAccessToken());
        $response = $this->post($this->getEndPoint("logout"));
        if ($response->isSuccess() && $body = $response->getStringBody()) {
            if ($data = json_decode($body, true)) {
                if (Hash::check($data, "messages")) {
                    return true;
                }
            }
        }
        Log::debug(__METHOD__ . " request data: " . json_encode($data));
        Log::debug(__METHOD__ . " response: " . $response->getStringBody());
        return false;
    }

    /**
     * パスワード再発行
     * @param array $data(email)
     * @return bool
     */
    public function reminder(array $data): array
    {
        // 認証していないので、システムユーザで強制的に実行
        $this->_setSysUserToken();
        $response = $this->post($this->getEndPoint("loginReminder"), json_encode($data));
        if ($response->isSuccess() && $body = $response->getStringBody()) {
            if ($data = json_decode($body, true)) {
                if (Hash::check($data, "tmp_data") && empty(Hash::get($data, "errors", []))) {
                    return Hash::get($data, "tmp_data");
                }
            }
        }
        Log::debug(__METHOD__ . " response: " . $response->getStringBody());
        return [];
    }

    /**
     * パスワード変更
     * @param Member $member
     * @param array $data(email,current_password,new_password)
     * @return bool
     */
    public function resetPassword(Member $member, array $data): string
    {
        $message = "";
        $this->setToken($member->getAccessToken()->getAccessToken());
        $response = $this->post($this->getEndPoint("loginResetPassword"), json_encode($data));
        if ($response->isSuccess() && $body = $response->getStringBody()) {
            if ($data = json_decode($body, true)) {
                if (empty(Hash::get($data, "messages", [])) && empty(Hash::get($data, "errors", []))) {
                    // return Hash::get($data, "tmp_data");
                    return "";
                }
            }
        }

        Log::debug(__METHOD__ . " response: " . $response->getStringBody());
        switch ($response->getStatusCode()) {
            case 422:
                $message = Hash::get(json_decode($response->getStringBody(), true), "errors.0.message", "");
                break;
            case 401:
                $message = "現在のパスワードが間違っています。";
                break;
            default:
                $message = "パスワード変更失敗しました。";
                break;
        }
        return $message;
    }
}
