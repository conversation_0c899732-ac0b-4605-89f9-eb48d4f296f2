<?php

use App\Config\DotEnvConfig;
use Cake\Utility\Hash;

$host =  DotEnvConfig::read("FRONT_BASE_URL");

$frontLink = [];
$frontLink = Hash::insert($frontLink, 'member', [
    'links' => [
        'formalRegist' => [
            'url' => $host . 'member/registration-success/',
            'queryKeys' => [
                'accessToken' => 't',
            ]
        ],
        'passwordReset' => [
            'url' => $host . 'member/password-reset/',
            'queryKeys' => [
                'tmpToken' => 'tt',
                'tmpPassword' => 'tp',
            ]
        ],
        'my' => [
            'url' => $host . 'member/account/',
        ],
        // 'login' => [
        //     'url' => $host . 'member/login',
        // ],
    ]
]);

return [
    'FrontLink' => $frontLink,
];
