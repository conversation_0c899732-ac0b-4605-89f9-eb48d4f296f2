import { Product } from "~~/src/models/entry/Product";
import RandselOrder from "~~/src/models/entry/RandselOrder";

export default class SchoolBagForm {
    private readonly _products: Product[];
    // private readonly _member: Member;
    private readonly _orders: RandselOrder[];

    private _valid: boolean = false;

    private _order_product_ids: number[] = [];

    private _errors: { [key: string]: string } = {};

    constructor(
        productsData: TProduct[] | [],
        orderData: TRandselOrder[] | [],
        // memberData: TMember,
    ) {
        this._products = Product.creates(productsData);
        this._orders = RandselOrder.creates(orderData);
        // this._member = Member.create(memberData);
    }

    get ordered_product_ids(): number[] {
        return this._orders
            .map((order) => order.product_id);
    }

    get order_product_ids(): number[] {
        return [...this._order_product_ids, ...this.ordered_product_ids];
    }

    set order_product_ids(values: number[]) {
        const ordered_product_ids = this.ordered_product_ids;
        this._order_product_ids = values.filter(
            (value) => !ordered_product_ids.includes(value),
        );
    }

    get valid(): boolean {
        return this._valid;
    }

    set valid(value: boolean) {
        this._valid = value;
    }

    get products(): Product[] {
        return this._products;
    }

    // get member(): Member {
    //     return this._member;
    // }

    get add_order_product_ids(): number[] {
        const ordered_product_ids = this.ordered_product_ids;
        const add_order_product_ids = this.order_product_ids.filter(
            (value) => !ordered_product_ids.includes(value),
        );
        return add_order_product_ids;
    }

    get errors(): { [key: string]: string } {
        return this._errors;
    }

    set errors(value: { [key: string]: string }) {
        this._errors = value;
    }

    get data(): number[] {
        // const data = this.add_order_product_ids.map((v: number) => {
        //     return {
        //         pid: v,
        //     };
        // });
        return this.add_order_product_ids;
    }

    // エラーメッセージをクリア
    clearError(field: string): void {
        this.errors[field] = "";
    }

    orderProductIdsValidate(): boolean {
        if (this.add_order_product_ids.length < 1) {
            this.errors.order_product_ids =
                "カタログは1つ以上選択してください。";
            return false;
        }
        this.errors.order_product_ids = "";
        return true;
    }
}
