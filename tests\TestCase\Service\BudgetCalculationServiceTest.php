<?php

namespace App\Test\TestCase\Service;

use App\Service\BudgetCalculationService;
use App\Model\Entity\Budget;
use App\Model\Entity\RandselOrder;
use Cake\TestSuite\TestCase;
use Cake\ORM\TableRegistry;
use Cake\I18n\FrozenTime;

/**
 * BudgetCalculationServiceTest
 */
class BudgetCalculationServiceTest extends TestCase
{
    /**
     * @var array
     */
    protected $fixtures = [
        'app.Products',
        // 'app.Budgets',
        // 'app.RandselOrders',
        'app.Makers',
        'app.Brands',
        'app.GeneralUsers'
    ];

    /**
     * @var BudgetCalculationService
     */
    private BudgetCalculationService $service;

    /**
     * @var \App\Model\Table\BudgetsTable
     */
    private $budgetsTable;

    /**
     * @var \App\Model\Table\RandselOrdersTable
     */
    private $randselOrdersTable;

    public function setUp(): void
    {
        parent::setUp();
        
        $this->service = new BudgetCalculationService();
        $this->budgetsTable = TableRegistry::getTableLocator()->get('Budgets');
        $this->randselOrdersTable = TableRegistry::getTableLocator()->get('RandselOrders');
    }

    /**
     * 統合予算情報取得のテスト
     */
    public function testGetConsolidatedBudgets(): void
    {
        // テストデータの準備
        $productId = 1;
        
        // 同一タイプの複数予算を作成
        $this->createTestBudget([
            'product_id' => $productId,
            'type' => 1, // 紙
            'budget_quantity' => 50,
            'priority' => 2
        ]);
        
        $this->createTestBudget([
            'product_id' => $productId,
            'type' => 1, // 紙
            'budget_quantity' => 30,
            'priority' => 2
        ]);
        
        $this->createTestBudget([
            'product_id' => $productId,
            'type' => 2, // デジタル
            'budget_quantity' => 999999,
            'priority' => 1
        ]);
        
        // テスト実行
        $result = $this->service->getConsolidatedBudgets($productId);
        
        debug($result);

        // 検証
        $this->assertIsArray($result);
        $this->assertCount(2, $result); // 紙とデジタルの2タイプ
        
        // 紙カタログの統合予算をチェック
        $paperBudget = array_filter($result, function($budget) {
            return $budget['type'] === 1;
        });
        $paperBudget = array_values($paperBudget)[0];
        
        $this->assertEquals(80, $paperBudget['budget_quantity']); // 50 + 30
        $this->assertEquals(2, $paperBudget['priority']);
        $this->assertCount(2, $paperBudget['budget_ids']);
    }

    /**
     * 予算統合のテスト（単一予算）
     */
    public function testConsolidateSingleBudget(): void
    {
        // テストデータの準備
        $productId = 1;
        
        $this->createTestBudget([
            'product_id' => $productId,
            'type' => 1,
            'budget_quantity' => 100,
            'priority' => 2
        ]);
        
        // テスト実行
        $result = $this->service->getConsolidatedBudgets($productId);

        debug($result);
        
        // 検証
        $this->assertCount(1, $result);
        $this->assertEquals(100, $result[0]['budget_quantity']);
        $this->assertCount(1, $result[0]['budget_ids']);
    }

    /**
     * 予算利用状況詳細取得のテスト
     */
    public function testGetBudgetUsageDetails(): void
    {
        // テストデータの準備
        $productId = 1;
        
        $this->createTestBudget([
            'product_id' => $productId,
            'type' => 1,
            'budget_quantity' => 100,
            'priority' => 2
        ]);
        
        // 注文を作成（利用率50%）
        for ($i = 0; $i < 50; $i++) {
            $this->createTestOrder([
                'maker_id' => 1,
                'brand_id' => 1,
                'product_id' => $productId,
                'type' => 1
            ]);
        }
        
        // テスト実行
        $result = $this->service->getBudgetUsageDetails($productId);

        debug($result);
        
        // 検証
        $this->assertIsArray($result);
        $this->assertNotEmpty($result);
        
        $detail = $result[0];
        $this->assertEquals(1, $detail['type']);
        $this->assertEquals('紙', $detail['type_name']);
        $this->assertEquals(100, $detail['budget_quantity']);
        $this->assertEquals(50, $detail['order_count']);
        $this->assertEquals(50, $detail['remaining_quantity']);
        $this->assertEquals(50.0, $detail['usage_rate']);
        $this->assertTrue($detail['is_available']);
    }

    /**
     * 予算期間重複チェックのテスト
     */
    public function testCheckBudgetOverlap(): void
    {
        // テストデータの準備
        $productId = 1;
        $type = 1;
        
        // 既存の予算（2025-01-01 ～ 2025-01-31）
        $this->createTestBudget([
            'product_id' => $productId,
            'type' => $type,
            'start_date' => new FrozenTime('2025-01-01'),
            'end_date' => new FrozenTime('2025-01-31')
        ]);
        
        // テスト実行：重複する期間
        $overlapping = $this->service->checkBudgetOverlap(
            $productId,
            $type,
            new FrozenTime('2025-01-15'),
            new FrozenTime('2025-02-15')
        );

        debug($overlapping);
        
        // 検証：重複が検出されること
        $this->assertNotEmpty($overlapping);
        
        // テスト実行：重複しない期間
        $nonOverlapping = $this->service->checkBudgetOverlap(
            $productId,
            $type,
            new FrozenTime('2025-02-01'),
            new FrozenTime('2025-02-28')
        );

        debug($nonOverlapping);
        
        // 検証：重複が検出されないこと
        $this->assertEmpty($nonOverlapping);
    }

    /**
     * 予算バリデーションのテスト
     */
    public function testValidateBudget(): void
    {
        // 正常なデータのテスト
        $validData = [
            'product_id' => 1,
            'type' => 1,
            'budget_quantity' => 100,
            'start_date' => '2025-01-01',
            'end_date' => '2025-01-31'
        ];
        
        $result = $this->service->validateBudget($validData);
        $this->assertTrue($result['is_valid']);
        $this->assertEmpty($result['errors']);
        
        // 不正なデータのテスト
        $invalidData = [
            'product_id' => '',
            'type' => 3, // 無効なタイプ
            'budget_quantity' => -1,
            'start_date' => '2025-01-31',
            'end_date' => '2025-01-01' // 開始日より前の終了日
        ];
        
        $result = $this->service->validateBudget($invalidData);
        $this->assertFalse($result['is_valid']);
        $this->assertNotEmpty($result['errors']);
        $this->assertContains('product_idは必須です', $result['errors']);
        $this->assertContains('終了日は開始日より後の日付を指定してください', $result['errors']);
        $this->assertContains('予算数量は1以上を指定してください', $result['errors']);
        $this->assertContains('カタログタイプは1（紙）または2（デジタル）を指定してください', $result['errors']);
    }

    /**
     * 有効期間外の予算が除外されることのテスト
     */
    public function testExcludeInactiveBudgets(): void
    {
        // テストデータの準備
        $productId = 1;
        
        // 有効期間外の予算
        $this->createTestBudget([
            'product_id' => $productId,
            'type' => 1,
            'start_date' => FrozenTime::now()->addDays(1), // 未来の開始日
            'end_date' => FrozenTime::now()->addDays(30)
        ]);
        
        // 有効期間内の予算
        $this->createTestBudget([
            'product_id' => $productId,
            'type' => 2,
            'start_date' => FrozenTime::now()->subDays(1),
            'end_date' => FrozenTime::now()->addDays(30)
        ]);
        
        // テスト実行
        $result = $this->service->getConsolidatedBudgets($productId);
        
        debug($result);

        // 検証：有効期間内の予算のみが取得されること
        $this->assertCount(1, $result);
        $this->assertEquals(2, $result[0]['type']); // デジタルのみ
    }

    /**
     * テスト用予算を作成
     */
    private function createTestBudget(array $data = []): Budget
    {
        $defaultData = [
            'product_id' => 1,
            'type' => 2,
            'price' => 500,
            'budget_quantity' => 999999,
            'is_active' => true,
            'start_date' => FrozenTime::now()->subDays(1),
            'end_date' => FrozenTime::now()->addDays(30),
            'priority' => 1
        ];
        
        $budgetData = array_merge($defaultData, $data);
        $budget = $this->budgetsTable->newEntity($budgetData);
        
        return $this->budgetsTable->save($budget);
    }

    /**
     * テスト用注文を作成
     */
    private function createTestOrder(array $data = []): RandselOrder
    {
        $defaultData = [
            'general_user_id' => 1,
            'product_id' => 1,
            'type' => 2,
            'product_name' => 'テスト商品',
            'price' => 500,
            'status' => 1
        ];
        
        $orderData = array_merge($defaultData, $data);
        $order = $this->randselOrdersTable->newEntity($orderData);
        
        return $this->randselOrdersTable->save($order);
    }
}
