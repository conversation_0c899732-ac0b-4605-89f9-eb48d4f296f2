export default class OrderDetail {
    private readonly _order_detail_id: number;
    private readonly _product_id: number;
    private readonly _product_name: string;
    private readonly _product_memo: string;
    constructor(orderDetail: TOrderDetail) {
        this._order_detail_id = orderDetail.order_detail_id;
        this._product_id = orderDetail.product_id;
        this._product_name = orderDetail.product_name;
        this._product_memo = orderDetail.product_memo;
    }

    get order_detail_id(): number {
        return this._order_detail_id;
    }

    get product_id(): number {
        return this._product_id;
    }

    get product_name(): string {
        return this._product_name;
    }

    get product_memo(): string {
        return this._product_memo;
    }

    static creates(orderDetails: TOrderDetail[]): OrderDetail[] {
        return orderDetails.map((orderDetail: TOrderDetail) =>
            OrderDetail.create(orderDetail),
        );
    }

    static create(orderDetail: TOrderDetail): OrderDetail {
        return new OrderDetail(orderDetail);
    }
}
