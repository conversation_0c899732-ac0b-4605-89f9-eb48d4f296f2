<?php
declare(strict_types=1);

use Migrations\AbstractMigration;

class CreateLiveStreams extends AbstractMigration
{
    /**
     * Up Method.
     *
     * More information on this method is available here:
     * https://book.cakephp.org/phinx/0/en/migrations.html#the-up-method
     * @return void
     */
    public function up(): void
    {
        $this->table('live_streams')
            ->addColumn('title', 'string', [
                'comment' => '配信タイトル',
                'default' => null,
                'limit' => 255,
                'null' => false,
            ])
            ->addColumn('description', 'text', [
                'comment' => '配信説明',
                'default' => null,
                'limit' => null,
                'null' => true,
            ])
            ->addColumn('start_datetime', 'datetime', [
                'comment' => '開始日時',
                'default' => null,
                'limit' => null,
                'null' => false,
            ])
            ->addColumn('end_datetime', 'datetime', [
                'comment' => '終了日時',
                'default' => null,
                'limit' => null,
                'null' => false,
            ])
            ->addColumn('platform', 'string', [
                'comment' => '配信プラットフォーム',
                'default' => null,
                'limit' => 100,
                'null' => true,
            ])
            ->addColumn('stream_url', 'string', [
                'comment' => '配信URL',
                'default' => null,
                'limit' => 500,
                'null' => true,
            ])
            ->addColumn('deleted', 'datetime', [
                'comment' => '削除日時(論理削除)',
                'default' => null,
                'limit' => null,
                'null' => true,
            ])
            ->addColumn('created', 'datetime', [
                'comment' => '作成日時',
                'default' => 'CURRENT_TIMESTAMP',
                'limit' => null,
                'null' => false,
            ])
            ->addColumn('modified', 'datetime', [
                'comment' => '更新日時',
                'default' => 'CURRENT_TIMESTAMP',
                'limit' => null,
                'null' => false,
                'update' => 'CURRENT_TIMESTAMP',
            ])
            ->addIndex(
                [
                    'title',
                ],
                [
                    'name' => 'idx_live_streams_title',
                ]
            )
            ->addIndex(
                [
                    'start_datetime',
                ],
                [
                    'name' => 'idx_live_streams_start_datetime',
                ]
            )
            ->addIndex(
                [
                    'end_datetime',
                ],
                [
                    'name' => 'idx_live_streams_end_datetime',
                ]
            )
            ->addIndex(
                [
                    'platform',
                ],
                [
                    'name' => 'idx_live_streams_platform',
                ]
            )
            ->addIndex(
                [
                    'deleted',
                ],
                [
                    'name' => 'idx_live_streams_deleted',
                ]
            )
            ->addIndex(
                [
                    'created',
                ],
                [
                    'name' => 'idx_live_streams_created',
                ]
            )
            ->create();
    }

    /**
     * Down Method.
     *
     * More information on this method is available here:
     * https://book.cakephp.org/phinx/0/en/migrations.html#the-down-method
     * @return void
     */
    public function down(): void
    {
        $this->table('live_streams')->drop()->save();
    }
}
