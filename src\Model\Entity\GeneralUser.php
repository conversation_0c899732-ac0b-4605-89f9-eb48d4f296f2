<?php
declare(strict_types=1);

namespace App\Model\Entity;

use Authentication\PasswordHasher\DefaultPasswordHasher;
use Cake\Core\Configure;
use Cake\ORM\Entity;

/**
 * 一般ユーザーエンティティ
 * 
 * @property int $id
 * @property string $email
 * @property string|null $password
 * @property \Cake\I18n\FrozenTime|null $deleted
 * @property \Cake\I18n\FrozenTime $created
 * @property \Cake\I18n\FrozenTime $modified
 * @property \App\Model\Entity\UserProfile $user_profile
 * @property \App\Model\Entity\UserSurvey $user_survey
 * @property \App\Model\Entity\UserToken[] $user_tokens
 * @property \App\Model\Entity\RandselOrder[] $randsel_orders
 */
class GeneralUser extends Entity
{
    protected $_accessible = [
        'email' => true,
        'password' => true,
        'deleted' => true,
        'created' => true,
        'modified' => true,
        'user_profile' => true,
        'user_survey' => true,
        'user_tokens' => true,
        'randsel_orders' => true,
    ];

    protected $_hidden = [
        'password',
    ];

    // パスワードのハッシュ化
    protected function _setPassword($password)
    {
        if ($password) {
            return (new DefaultPasswordHasher())->hash($password);
        }
        return null;
    }

    // Kurocoユーザーかどうかを判定
    public function isKurocoUser(): bool
    {
        return $this->password === null;
    }

    public function getTokensTableName(): string
    {
        return 'UserTokens';
    }

    public function getGroupId(): string
    {
        return Configure::read("Kuroko.api.userGroupId");
    }
}
