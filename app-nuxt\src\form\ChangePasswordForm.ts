import { Validations } from "~~/src/lib/Validations";
import { Member } from "~/src/models/entry/Member";

export default class ChangePasswordForm {
    private _login_id: string;
    private _current_password: string;
    private _new_password: string;
    private _new_password_confirmation: string;
    private _valid: boolean = false;

    constructor(member: Member) {
        this._login_id = member.email;
        this._current_password = "";
        this._new_password = "";
        this._new_password_confirmation = "";
    }

    get valid(): boolean {
        return this._valid;
    }

    set valid(value: boolean) {
        this._valid = value;
    }

    get current_password(): string {
        return this._current_password;
    }

    set current_password(value: string) {
        this._current_password = value;
    }

    get password_rules(): TValidationFunction[] {
        return [
            Validations.notEmptyString("パスワード"),
            Validations.password2("パスワード"),
        ];
    }

    get new_password(): string {
        return this._new_password;
    }

    set new_password(value: string) {
        this._new_password = value;
    }

    get new_password_confirmation(): string {
        return this._new_password_confirmation;
    }

    set new_password_confirmation(value: string) {
        this._new_password_confirmation = value;
    }

    get password_confirmation_rules(): TValidationFunction[] {
        return [
            Validations.notEmptyString("パスワード"),
            Validations.password("パスワード"),
            Validations.matchedConfirmation(
                this.new_password,
                "新しいパスワード",
                "パスワード",
            ),
        ];
    }

    get data(): TChangePassword {
        return {
            login_id: this._login_id,
            current_password: this._current_password,
            new_password: this._new_password,
        };
    }
}
