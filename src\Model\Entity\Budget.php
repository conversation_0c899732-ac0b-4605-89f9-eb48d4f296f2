<?php
declare(strict_types=1);

namespace App\Model\Entity;

use Cake\I18n\FrozenTime;
use Cake\ORM\Entity;

/**
 * 予算エンティティ
 * 
 * @property int $id
 * @property int $product_id
 * @property int $type
 * @property int $price
 * @property int $budget_quantity
 * @property bool $is_active
 * @property \Cake\I18n\FrozenTime $start_date
 * @property \Cake\I18n\FrozenTime $end_date
 * @property int $priority
 * @property \Cake\I18n\FrozenTime $created
 * @property \Cake\I18n\FrozenTime $modified
 * @property \App\Model\Entity\Product $product
 */
class Budget extends Entity
{
    protected $_accessible = [
        'product_id' => true,
        'type' => true,
        'price' => true,
        'budget_quantity' => true,
        'is_active' => true,
        'start_date' => true,
        'end_date' => true,
        'priority' => true,
        'created' => true,
        'modified' => true,
        'product' => true,
    ];

    protected $_hidden = [];

    /**
     * カタログタイプ名を取得
     */
    public function getTypeName(): string
    {
        switch ($this->type) {
            case 1:
                return '紙';
            case 2:
                return 'デジタル';
            default:
                return '不明';
        }
    }

    /**
     * 現在有効な予算かどうかを判定
     */
    public function isCurrentlyActive(): bool
    {
        if (!$this->is_active) {
            return false;
        }
        
        $now = FrozenTime::now();
        return $now >= $this->start_date && $now <= $this->end_date;
    }

    /**
     * 予算期間が開始されているかどうかを判定
     */
    public function hasStarted(): bool
    {
        return FrozenTime::now() >= $this->start_date;
    }

    /**
     * 予算期間が終了しているかどうかを判定
     */
    public function hasEnded(): bool
    {
        return FrozenTime::now() > $this->end_date;
    }

    /**
     * 予算期間の残り日数を取得
     */
    public function getRemainingDays(): int
    {
        if ($this->hasEnded()) {
            return 0;
        }
        
        $now = FrozenTime::now();
        return $now->diffInDays($this->end_date, false);
    }

    /**
     * 予算期間の文字列表現を取得
     */
    public function getPeriodString(): string
    {
        return $this->start_date->format('Y/m/d') . ' 〜 ' . $this->end_date->format('Y/m/d');
    }

    /**
     * 単価の文字列表現を取得
     */
    public function getPriceString(): string
    {
        return number_format($this->price) . '円';
    }

    /**
     * 予算数量の文字列表現を取得
     */
    public function getBudgetQuantityString(): string
    {
        return number_format($this->budget_quantity) . '件';
    }
}
