<?php

declare(strict_types=1);

namespace App\Test\TestCase\Model\Behavior;

use App\Model\Behavior\RandselOrdersBehavior;
use Cake\ORM\Table;
use Cake\TestSuite\TestCase;

/**
 * App\Model\Behavior\RandselOrdersBehavior Test Case
 */
class RandselOrdersBehaviorTest extends TestCase
{
    /**
     * Test subject
     *
     * @var RandselOrdersBehavior
     */
    protected $RandselOrders;

    /**
     * setUp method
     *
     * @return void
     */
    protected function setUp(): void
    {
        parent::setUp();
        $table = new Table(['alias' => 'RandselOrders']);
        $this->RandselOrders = new RandselOrdersBehavior($table);
    }

    /**
     * tearDown method
     *
     * @return void
     */
    protected function tearDown(): void
    {
        unset($this->RandselOrders);

        parent::tearDown();
    }


    /**
     * Test findSearch method
     *
     * @return void
     */
    // php ./vendor/phpunit/phpunit/phpunit --filter "testFindSearch" ./tests/TestCase/Model/Behavior/RandselOrdersBehaviorTest.php
    public function testFindSearch(): void
    {
        // 検証用のオプションを設定
        $options = [
            'id' => [1, 2, 3],
            'maker_id' => 1,
            'member_id' => 2,
            'product_id' => 3,
            'status' => [1, 2, 0],
            'approval_type' => [1, 2],
            'email_send_ng_flg' => 0,
            'from' => '2024-01-01',
            'to' => '2024-01-02',
            'searchDateType' => 'status-updated-date',
        ];

        $query = $this->RandselOrders->findSearch($this->RandselOrders->table()->selectQuery());
        $this->assertNull($query->clause('where'));

        $query = $this->RandselOrders->findSearch($this->RandselOrders->table()->selectQuery(), $options);
        $where = $query->clause('where');
        $conditions = [];
        $where->traverse(function ($expression) use (&$conditions) {
            $conditions[] = $expression;
        });
        debug($query->sql());
        // debug($conditions);

        $options = [
            'from' => '2024-01-01',
            'to' => '2024-01-02',
            'searchDateType' => 'order-date',
        ];

        $query = $this->RandselOrders->findSearch($this->RandselOrders->table()->selectQuery());
        $this->assertNull($query->clause('where'));

        $query = $this->RandselOrders->findSearch($this->RandselOrders->table()->selectQuery(), $options);
        $where = $query->clause('where');
        $conditions = [];
        $where->traverse(function ($expression) use (&$conditions) {
            $conditions[] = $expression;
        });
        debug($conditions);

        $this->assertTrue(true);
    }

    /**
     * Test findInvoice method
     *
     * @return void
     */
    // php ./vendor/phpunit/phpunit/phpunit --filter "testFindInvoice" ./tests/TestCase/Model/Behavior/RandselOrdersBehaviorTest.php
    public function testFindInvoice(): void
    {
        // 検証用のオプションを設定
        $options = [
            // 'maker_id' => 1,
            'from' => '2024-12-01',
            'to' => '2025-01-01',
        ];

        $query = $this->RandselOrders->findInvoiceSearch($this->RandselOrders->table()->selectQuery(), $options);
        debug($query->sql());
        $where = $query->clause('where');
        $conditions = [];
        $where->traverse(function ($expression) use (&$conditions) {
            $conditions[] = $expression;
        });
        debug($conditions);

        $this->assertTrue(true);
        // 検証用のオプションを設定
        $options = [
            'maker_id' => 1,
            'from' => '2024-12-01',
            'to' => '2025-01-01',
            'group_by' => 'product_id',
        ];

        $query = $this->RandselOrders->findInvoiceSearch($this->RandselOrders->table()->selectQuery(), $options);
        debug($query->sql());
        $where = $query->clause('where');
        $conditions = [];
        $where->traverse(function ($expression) use (&$conditions) {
            $conditions[] = $expression;
        });
        debug($conditions);

        $this->assertTrue(true);
    }
}
