import type { Member } from "~/src/models/entry/Member";
import type <PERSON><PERSON>Order from "~/src/models/entry/RandselOrder";

export default class UpdateStatusOrder {
    private _id: number;
    private _maker_id: number;
    private _member_id: number;
    private _product_id: number;
    private _status: number;

    constructor(order?: <PERSON>selOrder, client?: Member) {
        this._id = order ? order.id : 0;
        this._maker_id = client ? Number(client.maker_id) : 0;
        this._member_id = order ? order.member_id : 0;
        this._product_id = order ? order.product_id : 0;

        this._status = 0;
    }

    get id(): number {
        return this._id;
    }

    set id(value: number) {
        this._id = value;
    }

    get maker_id(): number {
        return this._maker_id;
    }

    set maker_id(value: number) {
        this._maker_id = value;
    }

    get member_id(): number {
        return this._member_id;
    }

    set member_id(value: number) {
        this._member_id = value;
    }

    get product_id(): number {
        return this._product_id;
    }

    set product_id(value: number) {
        this._product_id = value;
    }

    get status(): number {
        return this._status;
    }
    set status(value: number) {
        this._status = value;
    }

    static create(
        OrderUpdate: RandselOrder,
        client: Member,
    ): UpdateStatusOrder {
        return new UpdateStatusOrder(OrderUpdate, client);
    }

    static creates(
        orders: RandselOrder[],
        client: Member,
    ): UpdateStatusOrder[] {
        return orders.map((order: RandselOrder) =>
            UpdateStatusOrder.create(order, client),
        );
    }

    get approval_data(): Record<string, string | number> {
        return this.createData(1);
    }

    get deny_data(): Record<string, string | number> {
        return this.createData(2);
    }

    get mixed_data(): Record<string, string | number> {
        return this.createData(0);
    }

    private createData(status: number): Record<string, string | number> {
        return {
            id: this._id,
            maker_id: this._maker_id,
            member_id: this._member_id,
            product_id: this._product_id,
            status: this._status === 0 ? status : this._status,
        };
    }
}
