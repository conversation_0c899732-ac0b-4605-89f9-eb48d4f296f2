import OrdersClient from "~/src/lib/http/coverme-front/OrdersClient";
import RandselOrder from "~/src/models/entry/RandselOrder";
import { Model } from "~/src/models/Model";

export default class Orders extends Model {
    public index(): Promise<RandselOrder[]> {
        const config = this.config;
        return new Promise((resolve, reject) => {
            OrdersClient.create(config)
                .index<TRandselOrdersResponse>()
                .then(({ orders }) => resolve(RandselOrder.creates(orders)))
                .catch((error) => reject(new Error(error)));
        });
    }

    public add(data: number[]): Promise<boolean> {
        const config = this.config;
        return new Promise((resolve) => {
            OrdersClient.create(config)
                .post<number[], TResponseSuccess>(data)
                .then(({ success }) => resolve(!!success))
                .catch(() => resolve(false));
        });
    }
}
