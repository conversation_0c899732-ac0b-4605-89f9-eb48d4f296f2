<?php
declare(strict_types=1);

namespace App\Test\Fixture;

use Cake\TestSuite\Fixture\TestFixture;
use App\Utility\Encryptor;

/**
 * TemporaryRegistrationsFixture
 */
class TemporaryRegistrationsFixture extends TestFixture
{
    /**
     * Table name
     */
    public $table = 'temporary_registrations';

    /**
     * Fields
     */
    public $fields = [
        'id' => ['type' => 'integer', 'length' => null, 'unsigned' => false, 'null' => false, 'default' => null, 'comment' => '', 'autoIncrement' => true, 'precision' => null],
        'email' => ['type' => 'string', 'length' => 255, 'null' => true, 'default' => null, 'collate' => 'utf8mb4_unicode_ci', 'comment' => 'メールアドレス', 'precision' => null],
        'mask_password' => ['type' => 'text', 'length' => null, 'null' => true, 'default' => null, 'collate' => 'utf8mb4_unicode_ci', 'comment' => 'パスワード（暗号化）', 'precision' => null],
        'mask_email' => ['type' => 'text', 'length' => null, 'null' => false, 'default' => null, 'collate' => 'utf8mb4_unicode_ci', 'comment' => 'メールアドレス（暗号化）', 'precision' => null],
        'profile_data' => ['type' => 'text', 'length' => null, 'null' => true, 'default' => null, 'collate' => 'utf8mb4_unicode_ci', 'comment' => 'プロフィールデータJSON（暗号化）', 'precision' => null],
        'survey_data' => ['type' => 'text', 'length' => null, 'null' => true, 'default' => null, 'collate' => 'utf8mb4_unicode_ci', 'comment' => 'アンケートデータJSON（暗号化）', 'precision' => null],
        'product_ids' => ['type' => 'json', 'length' => null, 'null' => false, 'default' => null, 'comment' => '商品ID配列（JSON形式: [1,2,3]）', 'precision' => null],
        'verification_token' => ['type' => 'string', 'length' => 255, 'null' => false, 'default' => null, 'collate' => 'utf8mb4_unicode_ci', 'comment' => '認証トークン', 'precision' => null],
        'is_verified' => ['type' => 'boolean', 'length' => null, 'null' => false, 'default' => false, 'comment' => '認証済みフラグ（0: 未認証, 1: 認証済み）', 'precision' => null],
        'expires' => ['type' => 'datetime', 'length' => null, 'precision' => null, 'null' => false, 'default' => null, 'comment' => '有効期限'],
        'created' => ['type' => 'datetime', 'length' => null, 'precision' => null, 'null' => false, 'default' => 'CURRENT_TIMESTAMP', 'comment' => '作成日時'],
        'modified' => ['type' => 'datetime', 'length' => null, 'precision' => null, 'null' => false, 'default' => 'CURRENT_TIMESTAMP', 'comment' => '更新日時'],
        '_indexes' => [
            'idx_temporary_registrations_email' => ['type' => 'index', 'columns' => ['email'], 'length' => []],
            'idx_temporary_registrations_is_verified' => ['type' => 'index', 'columns' => ['is_verified'], 'length' => []],
            'idx_temporary_registrations_expires' => ['type' => 'index', 'columns' => ['expires'], 'length' => []],
            'idx_temporary_registrations_created' => ['type' => 'index', 'columns' => ['created'], 'length' => []],
        ],
        '_constraints' => [
            'primary' => ['type' => 'primary', 'columns' => ['id'], 'length' => []],
            'uk_temporary_registrations_verification_token' => ['type' => 'unique', 'columns' => ['verification_token'], 'length' => []],
        ],
        '_options' => [
            'engine' => 'InnoDB',
            'collation' => 'utf8mb4_unicode_ci'
        ],
    ];

    /**
     * Init method
     */
    public function init(): void
    {
        $this->records = [
            [
                'id' => 1,
                'email' => '<EMAIL>',
                'mask_password' => Encryptor::encrypt('test_password_123'), // テスト時に暗号化される
                'mask_email' => Encryptor::encrypt('<EMAIL>'), // テスト時に暗号化される
                'profile_data' => '{"last_name":"田中","first_name":"次郎","last_name_kana":"タナカ","first_name_kana":"ジロウ","zip_code":"456-7890","prefecture_code":"14","address1":"神奈川県横浜市港北区新横浜1-1-1","tel":"045-1234-5678"}', // テスト時に暗号化される
                'survey_data' => '{"year":2025,"child_sex":"male","budget":"70000_100000","purchase_trigger":"catalog","important_points":["durability","capacity"],"color_preference":"navy","brand_preference":"brand_c"}', // テスト時に暗号化される
                'product_ids' => '[1, 3, 5]',
                'verification_token' => 'test_token_123456789abcdef',
                'is_verified' => false,
                'expires' => '2030-12-31 23:59:59', // 有効期限内
                'created' => '2024-01-01 10:00:00',
                'modified' => '2024-01-01 10:00:00',
            ],
            [
                'id' => 2,
                'email' => '<EMAIL>',
                'mask_password' => Encryptor::encrypt('test_password_456'), // テスト時に暗号化される
                'mask_email' => Encryptor::encrypt('<EMAIL>'),
                'profile_data' => '{"last_name":"鈴木","first_name":"美咲","last_name_kana":"スズキ","first_name_kana":"ミサキ","zip_code":"789-0123","prefecture_code":"23","address1":"愛知県名古屋市中区栄3-3-3","tel":"052-9876-5432"}',
                'survey_data' => '{"year":2026,"child_sex":"female","budget":"under_30000","purchase_trigger":"exhibition","important_points":["design","weight"],"color_preference":"red","brand_preference":"brand_d"}',
                'product_ids' => '[2, 4, 6]',
                'verification_token' => 'test_token_fedcba987654321',
                'is_verified' => true, // 認証済み
                'expires' => '2030-12-31 23:59:59',
                'created' => '2024-01-02 11:00:00',
                'modified' => '2024-01-02 11:30:00',
            ],
            [
                'id' => 3,
                'email' => null, // 期限切れでクリア済み - NULLを許容するように変更
                'mask_password' => null, // 期限切れでクリア済み
                'mask_email' => '<EMAIL>', // 暗号化されたメールアドレスを保持
                'profile_data' => null, // 期限切れでクリア済み
                'survey_data' => null, // 期限切れでクリア済み
                'product_ids' => '[7, 8, 9]',
                'verification_token' => 'expired_token_111222333',
                'is_verified' => false,
                'expires' => '2023-12-31 23:59:59', // 期限切れ
                'created' => '2023-12-01 10:00:00',
                'modified' => '2023-12-01 10:00:00',
            ],
            [
                'id' => 4,
                'email' => '<EMAIL>',
                'mask_password' => Encryptor::encrypt('test_password_456'), // テスト時に暗号化される
                'mask_email' => Encryptor::encrypt('<EMAIL>'),
                'profile_data' => '{"last_name":"鈴木","first_name":"美咲","last_name_kana":"スズキ","first_name_kana":"ミサキ","zip_code":"789-0123","prefecture_code":"23","address1":"愛知県名古屋市中区栄3-3-3","tel":"052-9876-5432"}',
                'survey_data' => '{"year":2026,"child_sex":"female","budget":"under_30000","purchase_trigger":"exhibition","important_points":["design","weight"],"color_preference":"red","brand_preference":"brand_d"}',
                'product_ids' => '[2, 4, 6]',
                'verification_token' => 'test_token_lkjh7654',
                'is_verified' => true, // 認証済み
                'expires' => '2024-12-31 23:59:59',
                'created' => '2024-01-02 11:00:00',
                'modified' => '2024-01-02 11:30:00',
            ],
        ];
        parent::init();
    }
}


