<?php

namespace App\Model\Behavior;

use Cake\ORM\Query;
use Cake\Utility\Hash;
use Cake\ORM\Table;

trait BehaviorTrait
{

    /**
     * @param Query $query
     * @param string $field
     * @param array $options
     * @param Table|null $table
     * @return Query
     */
    public function whereEqualIn(Query $query, string $field, array $options, ?Table $table = null): Query
    {
        $table = $table ?? $this->_table;
        if (Hash::check($options, $field)) {
            $value = Hash::get($options, $field);
            if (is_array($value)) {
                $query->where([
                    $table->getAlias() . '.' . $field . ' IN' => $value
                ]);
            } else {
                $query->where([
                    $table->getAlias() . '.' . $field => $value
                ]);
            }
        }
        return $query;
    }


    /**
     * @param Query $query
     * @param string $expression
     * @return Query
     */
    public function sumQuery(Query $query, string $expression): Query
    {
        return $query->select([
            'sum' => $query->func()->sum($expression)
        ]);
    }

    /**
     * @param Query $query
     * @param string $field
     * @param string $expression
     * @return Query
     */
    public function countQuery(Query $query, string $field, string $expression = '*'): Query
    {
        return $query->select([
            $field,
            'count' => $query->func()->count($expression)
        ])
            ->group($field);
    }
}
