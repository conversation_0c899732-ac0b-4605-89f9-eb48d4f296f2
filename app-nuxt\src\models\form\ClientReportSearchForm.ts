import { ClientOrderSearchForm } from "~/src/models/form/ClientOrderSearchForm";

export class ClientReportSearchForm extends ClientOrderSearchForm {
    constructor() {
        super();
        this._searchDateType = "order-date";
        this._searchStatusType = [0, 1, 2];
        this._startDate = this.getSixMonthAgoFirstDayInJST();
    }

    public isStartDateValid(): boolean | string {
        if (!this._startDate || !this._endDate) return true;
        return (
            new Date(this._startDate) <= new Date(this._endDate) ||
            "開始月が不正です"
        );
    }

    public isEndDateValid(): boolean | string {
        if (!this._startDate || !this._endDate) return true;
        return (
            (new Date(this._endDate) >= new Date(this._startDate) &&
                new Date(this._endDate) <= new Date()) ||
            "終了月が不正です"
        );
    }
}
