<?php

namespace App\Mailer\Sender\ToUser;

use App\Mailer\Sender\IToUserSender;
use App\Mailer\Sender\SenderTrait;
use App\Mailer\Sender\ToUserSenderTrait;

class PasswordResetCompletedSender implements IToUserSender
{
    use SenderTrait;
    use ToUserSenderTrait;

    public function getSubject(): string
    {
        return "【カバーミー】パスワード再設定完了のお知らせ";
    }

    public function getTemplate(): string
    {
        return "to_user/PasswordResetCompletedSender";
    }
}
