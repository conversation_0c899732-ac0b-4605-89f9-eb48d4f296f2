<?php

namespace App\Enums\EntityFields;

enum EUserProfile: string
{
    // 基本フィールド
    case ID = 'id';
    case GENERAL_USER_ID = 'general_user_id';
    case CREATED = 'created';
    case MODIFIED = 'modified';

    // 暗号化フィールド（個人情報）
    case LAST_NAME = 'last_name';
    case FIRST_NAME = 'first_name';
    case LAST_NAME_KANA = 'last_name_kana';
    case FIRST_NAME_KANA = 'first_name_kana';
    case ZIP_CODE = 'zip_code';
    case PREFECTURE_CODE = 'prefecture_code';
    case ADDRESS1 = 'address1';
    case ADDRESS2 = 'address2';
    case ADDRESS3 = 'address3';
    case TEL = 'tel';
    case NOTES = 'notes';

    // 非暗号化フィールド
    case EMAIL_SEND_NG_FLG = 'email_send_ng_flg';

    // 仮想フィールド（復号化）
    case DECRYPTED_LAST_NAME = 'decrypted_last_name';
    case DECRYPTED_FIRST_NAME = 'decrypted_first_name';
    case DECRYPTED_LAST_NAME_KANA = 'decrypted_last_name_kana';
    case DECRYPTED_FIRST_NAME_KANA = 'decrypted_first_name_kana';
    case DECRYPTED_ZIP_CODE = 'decrypted_zip_code';
    case DECRYPTED_PREFECTURE_CODE = 'decrypted_prefecture_code';
    case DECRYPTED_ADDRESS1 = 'decrypted_address1';
    case DECRYPTED_ADDRESS2 = 'decrypted_address2';
    case DECRYPTED_ADDRESS3 = 'decrypted_address3';
    case DECRYPTED_TEL = 'decrypted_tel';
    case DECRYPTED_NOTES = 'decrypted_notes';

    /**
     * フィールドの説明を取得
     *
     * @return string
     */
    public function description(): string
    {
        return match ($this) {
            // 基本フィールド
            self::ID => 'ユーザープロファイルID',
            self::GENERAL_USER_ID => '一般ユーザーID（外部キー）',
            self::CREATED => '作成日時',
            self::MODIFIED => '更新日時',

            // 暗号化フィールド（個人情報）
            self::LAST_NAME => '姓（暗号化）',
            self::FIRST_NAME => '名（暗号化）',
            self::LAST_NAME_KANA => '姓（ふりがな）（暗号化）',
            self::FIRST_NAME_KANA => '名（ふりがな）（暗号化）',
            self::ZIP_CODE => '郵便番号（暗号化）',
            self::PREFECTURE_CODE => '都道府県コード（暗号化）',
            self::ADDRESS1 => '住所1（暗号化）',
            self::ADDRESS2 => '住所2（暗号化）',
            self::ADDRESS3 => '住所3（暗号化、nullable）',
            self::TEL => '電話番号（暗号化）',
            self::NOTES => '備考（暗号化、nullable）',

            // 非暗号化フィールド
            self::EMAIL_SEND_NG_FLG => 'メール送信拒否フラグ (false: 送信可, true: 送信不可)',

            // 仮想フィールド（復号化）
            self::DECRYPTED_LAST_NAME => '復号化された姓',
            self::DECRYPTED_FIRST_NAME => '復号化された名',
            self::DECRYPTED_LAST_NAME_KANA => '復号化された姓（ふりがな）',
            self::DECRYPTED_FIRST_NAME_KANA => '復号化された名（ふりがな）',
            self::DECRYPTED_ZIP_CODE => '復号化された郵便番号',
            self::DECRYPTED_PREFECTURE_CODE => '復号化された都道府県コード',
            self::DECRYPTED_ADDRESS1 => '復号化された住所1',
            self::DECRYPTED_ADDRESS2 => '復号化された住所2',
            self::DECRYPTED_ADDRESS3 => '復号化された住所3',
            self::DECRYPTED_TEL => '復号化された電話番号',
            self::DECRYPTED_NOTES => '復号化された備考',
        };
    }

    /**
     * 暗号化フィールドかどうかを判定
     *
     * @return bool
     */
    public function isEncrypted(): bool
    {
        return match ($this) {
            self::LAST_NAME,
            self::FIRST_NAME,
            self::LAST_NAME_KANA,
            self::FIRST_NAME_KANA,
            self::ZIP_CODE,
            self::PREFECTURE_CODE,
            self::ADDRESS1,
            self::ADDRESS2,
            self::ADDRESS3,
            self::TEL,
            self::NOTES => true,
            default => false,
        };
    }

    /**
     * 仮想フィールド（復号化フィールド）かどうかを判定
     *
     * @return bool
     */
    public function isVirtual(): bool
    {
        return match ($this) {
            self::DECRYPTED_LAST_NAME,
            self::DECRYPTED_FIRST_NAME,
            self::DECRYPTED_LAST_NAME_KANA,
            self::DECRYPTED_FIRST_NAME_KANA,
            self::DECRYPTED_ZIP_CODE,
            self::DECRYPTED_PREFECTURE_CODE,
            self::DECRYPTED_ADDRESS1,
            self::DECRYPTED_ADDRESS2,
            self::DECRYPTED_ADDRESS3,
            self::DECRYPTED_TEL,
            self::DECRYPTED_NOTES => true,
            default => false,
        };
    }

    /**
     * 必須フィールドかどうかを判定
     *
     * @return bool
     */
    public function isRequired(): bool
    {
        return match ($this) {
            self::GENERAL_USER_ID,
            self::LAST_NAME,
            self::FIRST_NAME,
            self::LAST_NAME_KANA,
            self::FIRST_NAME_KANA,
            self::ZIP_CODE,
            self::PREFECTURE_CODE,
            self::ADDRESS1,
            self::TEL,
            self::EMAIL_SEND_NG_FLG => true,
            default => false,
        };
    }

    /**
     * nullable（NULL許可）フィールドかどうかを判定
     *
     * @return bool
     */
    public function isNullable(): bool
    {
        return match ($this) {
            self::ADDRESS3,
            self::NOTES,
            self::DECRYPTED_ADDRESS3,
            self::DECRYPTED_NOTES => true,
            default => false,
        };
    }
}
