<?php

namespace App\Config;

use Cake\Core\Configure;
use Cake\Core\InstanceConfigTrait;

class DotEnvConfig
{

    use InstanceConfigTrait;

    /**
     * @var DotEnvConfig|null
     */
    private static ?DotEnvConfig $instance = null;

    /**
     * @var array
     */
    protected array $_defaultConfig = [
    ];

    private function __construct()
    {
        $this->setConfig(Configure::read('DotEnv'));
    }

    /**
     * @param string|null $key
     * @param mixed|null $default
     * @return mixed
     */
    public static function read(?string $key = null, mixed $default = null): mixed
    {
        return static::getInstance()->getConfig($key, $default);
    }

    /**
     * @param string $key
     * @param bool $default
     * @return bool
     */
    public static function readBoolean(string $key, bool $default): bool
    {
        return filter_var(static::read($key, $default), FILTER_VALIDATE_BOOLEAN);
    }

    /**
     * @return DotEnvConfig
     */
    private static function getInstance(): DotEnvConfig
    {
        if (static::$instance === null) {
            static::$instance = new static();
        }
        return static::$instance;
    }
}
