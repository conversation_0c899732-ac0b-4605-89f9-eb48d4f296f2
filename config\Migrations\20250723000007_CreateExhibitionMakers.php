<?php
declare(strict_types=1);

use Migrations\AbstractMigration;

class CreateExhibitionMakers extends AbstractMigration
{
    /**
     * Up Method.
     *
     * More information on this method is available here:
     * https://book.cakephp.org/phinx/0/en/migrations.html#the-up-method
     * @return void
     */
    public function up(): void
    {
        $this->table('exhibition_makers')
            ->addColumn('exhibition_id', 'integer', [
                'comment' => '展示会ID',
                'default' => null,
                'limit' => null,
                'null' => false,
            ])
            ->addColumn('maker_id', 'integer', [
                'comment' => 'メーカーID',
                'default' => null,
                'limit' => null,
                'null' => false,
            ])
            ->addColumn('created', 'datetime', [
                'comment' => '作成日時',
                'default' => 'CURRENT_TIMESTAMP',
                'limit' => null,
                'null' => false,
            ])
            ->addColumn('modified', 'datetime', [
                'comment' => '更新日時',
                'default' => 'CURRENT_TIMESTAMP',
                'limit' => null,
                'null' => false,
                'update' => 'CURRENT_TIMESTAMP',
            ])
            ->addIndex(
                [
                    'exhibition_id',
                ],
                [
                    'name' => 'idx_exhibition_makers_exhibition_id',
                ]
            )
            ->addIndex(
                [
                    'maker_id',
                ],
                [
                    'name' => 'idx_exhibition_makers_maker_id',
                ]
            )
            ->addIndex(
                [
                    'exhibition_id',
                    'maker_id',
                ],
                [
                    'name' => 'uk_exhibition_makers_exhibition_maker',
                    'unique' => true,
                ]
            )
            ->addIndex(
                [
                    'created',
                ],
                [
                    'name' => 'idx_exhibition_makers_created',
                ]
            )
            ->addForeignKey(
                'exhibition_id',
                'exhibitions',
                'id',
                [
                    'delete' => 'CASCADE',
                    'update' => 'CASCADE',
                    'constraint' => 'fk_exhibition_makers_exhibition_id'
                ]
            )
            ->addForeignKey(
                'maker_id',
                'makers',
                'id',
                [
                    'delete' => 'CASCADE',
                    'update' => 'CASCADE',
                    'constraint' => 'fk_exhibition_makers_maker_id'
                ]
            )
            ->create();
    }

    /**
     * Down Method.
     *
     * More information on this method is available here:
     * https://book.cakephp.org/phinx/0/en/migrations.html#the-down-method
     * @return void
     */
    public function down(): void
    {
        $this->table('exhibition_makers')->drop()->save();
    }
}
