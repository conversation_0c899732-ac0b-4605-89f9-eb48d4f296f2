<?php
declare(strict_types=1);

namespace App\Test\TestCase\Controller\Front;

use App\Kuroko\Entity\AccessToken;
use App\Model\Entity\UserToken;
use App\Test\TestCase\Controller\ApiTestCase;
use Cake\I18n\FrozenTime;
use Cake\ORM\TableRegistry;
use Cake\TestSuite\IntegrationTestTrait;

/**
 * UserAuthenticationsController統合テストケース
 */
class UserAuthenticationsControllerIntegrationTest extends ApiTestCase
{
    use IntegrationTestTrait;

    /**
     * Test fixtures
     */
    protected $fixtures = [
        'app.GeneralUsers',
        'app.UserTokens',
        'app.UserProfiles',
    ];

    /**
     * setUp method
     */
    public function setUp(): void
    {
        parent::setUp();
        // $this->configRequest([
        //     'headers' => [
        //         'CM-TOKEN' => 'front',
        //         'Content-Type' => 'application/json',
        //         'Accept' => 'application/json',
        //     ]
        // ]);
    }

    /**
     * 新システムユーザーのログインテスト
     */
    public function testLoginNewSystemUser(): void
    {
        $this->setFrontAuthorized(false);
        // ログインリクエスト
        $this->post('/front/user-authentications.json', [
            'email' => '<EMAIL>',
            'password' => 'password1234',
        ]);

        // レスポンス検証
        $this->assertResponseOk();
        $this->assertContentType('application/json');
        
        $response = json_decode((string)$this->_response->getBody(), true);
        debug($response);
        $this->assertArrayHasKey('member', $response);
        $this->assertArrayHasKey('access_token', $response);
        $this->assertArrayHasKey('user_type', $response);
        $this->assertEquals('general', $response['user_type']);
        $this->assertEquals('<EMAIL>', $response['member']['email']);
        $this->assertNotEmpty($response['access_token']);
    }

    /**
     * Kurocoユーザーのログインテスト
     */
    public function testLoginKurocoUser(): void
    {
        $this->setFrontAuthorized(false);

        // ログインリクエスト(kuroco認証にリダイレクトされる)
        $this->post('/front/user-authentications.json', [
            'email' => '<EMAIL>',   // Kuroco develop環境ユーザー
            'password' => 'as9sdf3',
        ]);

        // レスポンス検証
        // $this->assertResponseError();
        $this->assertContentType('application/json');
        
        $response = json_decode((string)$this->_response->getBody(), true);
        debug($response);
        $this->assertArrayHasKey('member', $response);
        $this->assertArrayHasKey('access_token', $response);
        $this->assertArrayHasKey('user_type', $response);
        $this->assertEquals('general', $response['user_type']);
        $this->assertEquals('<EMAIL>', $response['member']['email']);
        $this->assertNotEmpty($response['access_token']);
    }

    /**
     * 存在しないユーザーのログインテスト
     */
    public function testLoginNonExistentUser(): void
    {
        $this->setFrontAuthorized(false);
        // ログインリクエスト
        $this->post('/front/user-authentications.json', [
            'email' => '<EMAIL>',
            'password' => 'password123',
        ]);

        // レスポンス検証（認証失敗）
        $this->assertResponseError();
    }

    /**
     * 間違ったパスワードでのログインテスト
     */
    public function testLoginWithWrongPassword(): void
    {
        $this->setFrontAuthorized(false);

        // 間違ったパスワードでログインリクエスト
        $this->post('/front/user-authentications.json', [
            'email' => '<EMAIL>',
            'password' => 'wrongpassword',
        ]);

        // レスポンス検証（認証失敗）
        $this->assertResponseError();
    }

    /**
     * 無効なリクエストデータでのログインテスト
     */
    public function testLoginWithInvalidData(): void
    {
        $this->setFrontAuthorized(false);
        // 空のメールアドレスでログインリクエスト
        $this->post('/front/user-authentications.json', [
            'email' => '',
            'password' => 'password123',
        ]);

        $response = json_decode((string)$this->_response->getBody(), true);
        debug($response);
        // レスポンス検証（バリデーションエラー）
        $this->assertArrayHasKey('email', $response['errors']);

        $this->setFrontAuthorized(false);
        // 空のパスワードでログインリクエスト
        $this->post('/front/user-authentications.json', [
            'email' => '<EMAIL>',
            'password' => null,
        ]);

        $response = json_decode((string)$this->_response->getBody(), true);
        debug($response);
        // レスポンス検証（バリデーションエラー）
        $this->assertArrayHasKey('password', $response['errors']);
    }

    /**
     * 新システムユーザーのトークン認証でのログアウトテスト
     */
    public function testLogoutNewSystemUserWithToken(): void
    {
        // テスト用の新システムユーザーを作成
        $generalUsersTable = TableRegistry::getTableLocator()->get('GeneralUsers');
        $user = $generalUsersTable->newEntity([
            'email' => '<EMAIL>',
            'password' => 'password123',
        ]);
        $savedUser = $generalUsersTable->save($user);
        $this->assertNotEmpty($savedUser);

        // APIアクセストークンを作成
        $userTokensTable = TableRegistry::getTableLocator()->get('UserTokens');
        $tokenEntity = $userTokensTable->createApiAccessToken($savedUser->id, 24);
        $this->assertNotEmpty($tokenEntity);

        // トークンを暗号化
        $encryptedToken = (new AccessToken([
            'access_token' => ['value' => $tokenEntity->token]
        ]))->encryptToken();

        // ログアウトリクエスト
        $this->configRequest([
            'headers' => [
                'CM-TOKEN' => 'front',
                'Authorization-coverme' => 'Bearer ' . $encryptedToken,
                'Content-Type' => 'application/json',
                'Accept' => 'application/json',
            ]
        ]);

        $this->delete('/front/user-authentications/1.json');

        // レスポンス検証
        $this->assertResponseOk();
        $this->assertContentType('application/json');

        $response = json_decode((string)$this->_response->getBody(), true);
        debug($response);
        $this->assertArrayHasKey('success', $response);
        $this->assertTrue($response['success']);

        // トークンが削除されていることを確認
        $remainingToken = $userTokensTable->find()
            ->where(['token' => $tokenEntity->token])
            ->first();
        $this->assertNull($remainingToken);
    }

    /**
     * Kurocoユーザーのトークン認証でのログアウトテスト
     */
    public function testLogoutKurocoUserWithToken(): void
    {
        $encryptedToken = "MWRjY2VhZDliMWVkNzM0MWYwNTgxZTI4ZTBhMWI1NGRiMWMzMzYzODlmOTE2ZDE5M2ZhZDUzYWU5OTQ4NDAzNy26rBtWG+ijB2mpCqq3HqTPTPEstjcQ+2Gt1Dx8R2T5zenB1EgrQDNUKWdSlfTwOB3DshWCregKtclGt4n5uGwcpUH5GEjv39PDQ5bEdTg8Fvc/THCOjTU0ocysrD/J4g==";

        // ログアウトリクエスト
        $this->configRequest([
            'headers' => [
                'CM-TOKEN' => 'front',
                'Authorization-coverme' => 'Bearer ' . $encryptedToken,
                'Content-Type' => 'application/json',
                'Accept' => 'application/json',
            ]
        ]);

        $this->delete('/front/user-authentications/1.json');

        // レスポンス検証
        $this->assertResponseOk();
        $this->assertContentType('application/json');

        $response = json_decode((string)$this->_response->getBody(), true);
        debug($response);
        $this->assertArrayHasKey('success', $response);
        $this->assertTrue($response['success']);
        $this->assertArrayNotHasKey('errors', $response);
    }

    /**
     * 無効なトークンでのログアウトテスト
     */
    public function testLogoutWithInvalidToken(): void
    {
        // 無効なトークンでログアウトリクエスト
        $this->configRequest([
            'headers' => [
                'CM-TOKEN' => 'front',
                'Authorization-coverme' => 'Bearer invalid_token',
                'Content-Type' => 'application/json',
                'Accept' => 'application/json',
            ]
        ]);

        $this->delete('/front/user-authentications/1.json');

        // レスポンス検証（認証エラー）
        $this->assertResponseError();
    }

    /**
     * 有効期限切れトークンでのログアウトテスト
     */
    public function testLogoutWithExpiredToken(): void
    {
        // テスト用の新システム一般ユーザーを作成
        $generalUsersTable = TableRegistry::getTableLocator()->get('GeneralUsers');
        $user = $generalUsersTable->newEntity([
            'email' => '<EMAIL>',
            'password' => 'password123',
        ]);
        $savedUser = $generalUsersTable->save($user);
        $this->assertNotEmpty($savedUser);

        // 期限切れのAPIアクセストークンを作成
        $userTokensTable = TableRegistry::getTableLocator()->get('UserTokens');
        $tokenEntity = $userTokensTable->newEntity([
            'general_user_id' => $savedUser->id,
            'token' => $userTokensTable->generateToken(),
            'type' => UserToken::TYPE_API_ACCESS,
            'expires' => FrozenTime::now()->subHours(1) // 1時間前に期限切れ
        ]);
        $savedToken = $userTokensTable->save($tokenEntity);
        $this->assertNotEmpty($savedToken);

        // トークンを暗号化
        $encryptedToken = (new AccessToken([
            'access_token' => ['value' => $savedToken->token]
        ]))->encryptToken();

        // 有効期限切れのトークンでログアウトリクエスト
        $this->configRequest([
            'headers' => [
                'CM-TOKEN' => 'front',
                'Authorization-coverme' => 'Bearer ' . $encryptedToken,
                'Content-Type' => 'application/json',
                'Accept' => 'application/json',
            ]
        ]);

        $this->delete('/front/user-authentications/1.json');

        // レスポンス検証（認証エラー）
        $this->assertResponseError();
    }

    /**
     * トークンなしでのログアウトテスト
     */
    public function testLogoutWithoutToken(): void
    {
        // トークンなしでログアウトリクエスト
        $this->configRequest([
            'headers' => [
                'CM-TOKEN' => 'front',
                'Content-Type' => 'application/json',
                'Accept' => 'application/json',
            ]
        ]);

        $this->delete('/front/user-authentications/1.json');

        // レスポンス検証（認証エラー）
        $this->assertResponseError();
    }

    /**
     * CM-TOKENなしでのアクセステスト
     */
    public function testAccessWithoutCmToken(): void
    {
        // CM-TOKENなしでリクエスト
        $this->configRequest([
            'headers' => [
                'Content-Type' => 'application/json',
                'Accept' => 'application/json',
            ]
        ]);

        $this->post('/front/user-authentications.json', [
            'email' => '<EMAIL>',
            'password' => 'password123',
        ]);

        // レスポンス検証（認証エラー）
        $this->assertResponseError();
        $response = json_decode((string)$this->_response->getBody(), true);
        debug($response);
    }

    /**
     * 無効なCM-TOKENでのアクセステスト
     */
    public function testAccessWithInvalidCmToken(): void
    {
        // 無効なCM-TOKENでリクエスト
        $this->configRequest([
            'headers' => [
                'CM-TOKEN' => 'invalid_token',
                'Content-Type' => 'application/json',
                'Accept' => 'application/json',
            ]
        ]);

        $this->post('/front/user-authentications.json', [
            'email' => '<EMAIL>',
            'password' => 'password123',
        ]);

        // レスポンス検証（認証エラー）
        $this->assertResponseError();
        $response = json_decode((string)$this->_response->getBody(), true);
        debug($response);
    }

    /**
     * JSONレスポンス形式の検証テスト
     */
    public function testJsonResponseFormat(): void
    {
        // テスト用の新システムユーザーを作成
        $generalUsersTable = TableRegistry::getTableLocator()->get('GeneralUsers');
        $user = $generalUsersTable->newEntity([
            'email' => '<EMAIL>',
            'password' => 'password123',
        ]);
        $savedUser = $generalUsersTable->save($user);
        $this->assertNotEmpty($savedUser);

        $this->setFrontAuthorized(false);
        // ログインリクエスト
        $this->post('/front/user-authentications.json', [
            'email' => '<EMAIL>',
            'password' => 'password123',
        ]);

        // レスポンス形式の詳細検証
        $this->assertResponseOk();
        $this->assertContentType('application/json');
        
        $response = json_decode((string)$this->_response->getBody(), true);
        debug($response);
        
        // 必須フィールドの存在確認
        $this->assertArrayHasKey('member', $response);
        $this->assertArrayHasKey('access_token', $response);
        $this->assertArrayHasKey('user_type', $response);
        
        // memberオブジェクトの構造確認
        $this->assertArrayHasKey('email', $response['member']);
        $this->assertArrayHasKey('status', $response['member']);
        $this->assertArrayHasKey('login_ok_flg', $response['member']);
        
        // access_tokenの形式確認
        $this->assertIsString($response['access_token']);
        $this->assertNotEmpty($response['access_token']);
        
        // user_typeの値確認
        $this->assertEquals('general', $response['user_type']);
    }
}

