<?php
declare(strict_types=1);

use Migrations\AbstractMigration;

class CreateGeneralUsers extends AbstractMigration
{
    /**
     * Up Method.
     *
     * More information on this method is available here:
     * https://book.cakephp.org/phinx/0/en/migrations.html#the-up-method
     * @return void
     */
    public function up(): void
    {
        $this->table('general_users')
            ->addColumn('email', 'string', [
                'comment' => 'メールアドレス（ログイン用）',
                'default' => null,
                'limit' => 255,
                'null' => false,
            ])
            ->addColumn('password', 'string', [
                'comment' => 'パスワード（ハッシュ化）',
                'default' => null,
                'limit' => 255,
                'null' => true,
            ])
            ->addColumn('deleted', 'datetime', [
                'comment' => '削除日時(論理削除)',
                'default' => null,
                'limit' => null,
                'null' => true,
            ])
            ->addColumn('created', 'datetime', [
                'comment' => '作成日時',
                'default' => 'CURRENT_TIMESTAMP',
                'limit' => null,
                'null' => false,
            ])
            ->addColumn('modified', 'datetime', [
                'comment' => '更新日時',
                'default' => 'CURRENT_TIMESTAMP',
                'limit' => null,
                'null' => false,
                'update' => 'CURRENT_TIMESTAMP',
            ])
            ->addIndex(
                [
                    'email',
                ],
                [
                    'name' => 'uk_general_users_email',
                    'unique' => true,
                ]
            )
            ->addIndex(
                [
                    'deleted',
                ],
                [
                    'name' => 'idx_general_users_deleted',
                ]
            )
            ->addIndex(
                [
                    'created',
                ],
                [
                    'name' => 'idx_general_users_created',
                ]
            )
            ->create();
    }

    /**
     * Down Method.
     *
     * More information on this method is available here:
     * https://book.cakephp.org/phinx/0/en/migrations.html#the-down-method
     * @return void
     */
    public function down(): void
    {
        $this->table('general_users')->drop()->save();
    }
}
