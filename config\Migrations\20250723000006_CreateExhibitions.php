<?php
declare(strict_types=1);

use Migrations\AbstractMigration;

class CreateExhibitions extends AbstractMigration
{
    /**
     * Up Method.
     *
     * More information on this method is available here:
     * https://book.cakephp.org/phinx/0/en/migrations.html#the-up-method
     * @return void
     */
    public function up(): void
    {
        $this->table('exhibitions')
            ->addColumn('title', 'string', [
                'comment' => '展示会タイトル',
                'default' => null,
                'limit' => 255,
                'null' => false,
            ])
            ->addColumn('description', 'text', [
                'comment' => '展示会説明',
                'default' => null,
                'limit' => null,
                'null' => true,
            ])
            ->addColumn('start_datetime', 'datetime', [
                'comment' => '開始日時',
                'default' => null,
                'limit' => null,
                'null' => false,
            ])
            ->addColumn('end_datetime', 'datetime', [
                'comment' => '終了日時',
                'default' => null,
                'limit' => null,
                'null' => false,
            ])
            ->addColumn('venue_name', 'string', [
                'comment' => '会場名',
                'default' => null,
                'limit' => 255,
                'null' => true,
            ])
            ->addColumn('address', 'text', [
                'comment' => '住所',
                'default' => null,
                'limit' => null,
                'null' => true,
            ])
            ->addColumn('access_info', 'text', [
                'comment' => 'アクセス情報',
                'default' => null,
                'limit' => null,
                'null' => true,
            ])
            ->addColumn('capacity', 'integer', [
                'comment' => '定員数',
                'default' => null,
                'limit' => null,
                'null' => true,
            ])
            ->addColumn('requires_reservation', 'boolean', [
                'comment' => '予約必須フラグ',
                'default' => false,
                'limit' => null,
                'null' => false,
            ])
            ->addColumn('reservation_url', 'string', [
                'comment' => '予約URL',
                'default' => null,
                'limit' => 500,
                'null' => true,
            ])
            ->addColumn('deleted', 'datetime', [
                'comment' => '削除日時(論理削除)',
                'default' => null,
                'limit' => null,
                'null' => true,
            ])
            ->addColumn('created', 'datetime', [
                'comment' => '作成日時',
                'default' => 'CURRENT_TIMESTAMP',
                'limit' => null,
                'null' => false,
            ])
            ->addColumn('modified', 'datetime', [
                'comment' => '更新日時',
                'default' => 'CURRENT_TIMESTAMP',
                'limit' => null,
                'null' => false,
                'update' => 'CURRENT_TIMESTAMP',
            ])
            ->addIndex(
                [
                    'title',
                ],
                [
                    'name' => 'idx_exhibitions_title',
                ]
            )
            ->addIndex(
                [
                    'start_datetime',
                ],
                [
                    'name' => 'idx_exhibitions_start_datetime',
                ]
            )
            ->addIndex(
                [
                    'end_datetime',
                ],
                [
                    'name' => 'idx_exhibitions_end_datetime',
                ]
            )
            ->addIndex(
                [
                    'requires_reservation',
                ],
                [
                    'name' => 'idx_exhibitions_requires_reservation',
                ]
            )
            ->addIndex(
                [
                    'deleted',
                ],
                [
                    'name' => 'idx_exhibitions_deleted',
                ]
            )
            ->addIndex(
                [
                    'created',
                ],
                [
                    'name' => 'idx_exhibitions_created',
                ]
            )
            ->create();
    }

    /**
     * Down Method.
     *
     * More information on this method is available here:
     * https://book.cakephp.org/phinx/0/en/migrations.html#the-down-method
     * @return void
     */
    public function down(): void
    {
        $this->table('exhibitions')->drop()->save();
    }
}
