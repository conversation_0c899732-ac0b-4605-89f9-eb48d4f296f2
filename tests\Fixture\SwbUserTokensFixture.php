<?php
declare(strict_types=1);

namespace App\Test\Fixture;

use Cake\TestSuite\Fixture\TestFixture;

/**
 * SwbUserTokensFixture
 */
class SwbUserTokensFixture extends TestFixture
{
    /**
     * Init method
     *
     * @return void
     */
    public function init(): void
    {
        $this->records = [
            [
                'id' => 1,
                'swb_user_id' => 1,
                'token' => 'swb_test_token_1',
                'type' => 'api_access',
                'expires' => '2025-03-31 23:59:59',
                'created' => '2024-01-01 00:00:00',
                'modified' => '2024-01-01 00:00:00',
            ],
            [
                'id' => 2,
                'swb_user_id' => 1,
                'token' => 'swb_test_token_2',
                'type' => 'password_reset',
                'expires' => '2025-12-31 23:59:59',
                'created' => '2024-01-01 00:00:00',
                'modified' => '2024-01-01 00:00:00',
            ],
            [
                'id' => 3,
                'swb_user_id' => 2,
                'token' => 'swb_test_token_3',
                'type' => 'api_access',
                'expires' => '2025-12-31 23:59:59',
                'created' => '2024-01-01 00:00:00',
                'modified' => '2024-01-01 00:00:00',
            ],
            [
                'id' => 4,
                'swb_user_id' => 3,
                'token' => 'swb_expired_token',
                'type' => 'password_reset',
                'expires' => '2023-12-31 23:59:59', // 期限切れ
                'created' => '2023-01-01 00:00:00',
                'modified' => '2023-01-01 00:00:00',
            ],
        ];
        parent::init();
    }

    /**
     * Fields
     *
     * @var array
     */
    public $fields = [
        'id' => ['type' => 'integer', 'length' => null, 'unsigned' => false, 'null' => false, 'default' => null, 'comment' => 'トークンID', 'autoIncrement' => true, 'precision' => null],
        'swb_user_id' => ['type' => 'integer', 'length' => null, 'unsigned' => false, 'null' => false, 'default' => null, 'comment' => '管理者ユーザーID', 'precision' => null, 'autoIncrement' => null],
        'token' => ['type' => 'string', 'length' => 255, 'null' => false, 'default' => null, 'collate' => 'utf8mb4_general_ci', 'comment' => 'トークン文字列', 'precision' => null],
        'type' => ['type' => 'string', 'length' => 50, 'null' => false, 'default' => null, 'collate' => 'utf8mb4_general_ci', 'comment' => 'トークン種別（password_reset, api_access等）', 'precision' => null],
        'expires' => ['type' => 'datetime', 'length' => null, 'precision' => null, 'null' => false, 'default' => null, 'comment' => '有効期限'],
        'created' => ['type' => 'datetime', 'length' => null, 'precision' => null, 'null' => false, 'default' => 'CURRENT_TIMESTAMP', 'comment' => '作成日時'],
        'modified' => ['type' => 'datetime', 'length' => null, 'precision' => null, 'null' => false, 'default' => 'CURRENT_TIMESTAMP', 'comment' => '更新日時'],
        '_indexes' => [
            'idx_swb_user_tokens_swb_user_id' => ['type' => 'index', 'columns' => ['swb_user_id'], 'length' => []],
            'idx_swb_user_tokens_type' => ['type' => 'index', 'columns' => ['type'], 'length' => []],
            'idx_swb_user_tokens_expires' => ['type' => 'index', 'columns' => ['expires'], 'length' => []],
            'idx_swb_user_tokens_created' => ['type' => 'index', 'columns' => ['created'], 'length' => []],
        ],
        '_constraints' => [
            'primary' => ['type' => 'primary', 'columns' => ['id'], 'length' => []],
            'uk_swb_user_tokens_token' => ['type' => 'unique', 'columns' => ['token'], 'length' => []],
        ],
        '_options' => [
            'engine' => 'InnoDB',
            'collation' => 'utf8mb4_general_ci'
        ],
    ];
}