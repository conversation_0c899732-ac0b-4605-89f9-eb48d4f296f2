<?php

namespace App\Controller\Front;

use App\Controller\AppController;
use Cake\Utility\Hash;
use App\Service\SwbInvoicePdfService;
use App\Service\Traits\PdfImageTrait;

class SwbInvoicePdfController extends AppController
{
    use PdfImageTrait;

    public function initialize(): void
    {
        parent::initialize();
        $this->loadComponent('Authentication.Authentication');
    }

    public function add()
    {
        $this->viewBuilder()->enableAutoLayout(false);
        $this->viewBuilder()->setClassName('CakePdf.Pdf');
        $this->viewBuilder()->setOption('pdfConfig', []);

        $data = $this->getRequest()->getData();

        $viewVars = Hash::merge($data, $this->getPdfImageViewVars(), SwbInvoicePdfService::generateInvoiceViewVars($data));

        $this->set('invoice', $viewVars);
    }
}
