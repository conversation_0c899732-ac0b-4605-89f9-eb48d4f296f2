<?php
declare(strict_types=1);

use Migrations\AbstractMigration;

class CreateUserSurveys extends AbstractMigration
{
    /**
     * Up Method.
     *
     * More information on this method is available here:
     * https://book.cakephp.org/phinx/0/en/migrations.html#the-up-method
     * @return void
     */
    public function up(): void
    {
        $this->table('user_surveys')
            ->addColumn('general_user_id', 'integer', [
                'comment' => '一般ユーザーID',
                'default' => null,
                'limit' => null,
                'null' => false,
            ])
            ->addColumn('year', 'integer', [
                'comment' => '年度',
                'default' => null,
                'limit' => null,
                'null' => false,
            ])
            ->addColumn('child_sex', 'integer', [
                'comment' => 'お子様の性別（1:男, 2:女, 3:その他）',
                'default' => null,
                'limit' => null,
                'null' => true,
            ])
            ->addColumn('budget', 'integer', [
                'comment' => 'ご予算（1:1万円未満～3万円, 2:3万円～7万円, 3:7万円～10万円, 4:10万円～30万円, 5:30万円以上）',
                'default' => null,
                'limit' => null,
                'null' => true,
            ])
            ->addColumn('question_1_1', 'boolean', [
                'comment' => 'きっかけ1',
                'default' => false,
                'limit' => null,
                'null' => false,
            ])
            ->addColumn('question_1_2', 'boolean', [
                'comment' => 'きっかけ2',
                'default' => false,
                'limit' => null,
                'null' => false,
            ])
            ->addColumn('question_1_3', 'boolean', [
                'comment' => 'きっかけ3',
                'default' => false,
                'limit' => null,
                'null' => false,
            ])
            ->addColumn('question_1_4', 'boolean', [
                'comment' => 'きっかけ4',
                'default' => false,
                'limit' => null,
                'null' => false,
            ])
            ->addColumn('question_2_1', 'boolean', [
                'comment' => '重視するポイント1',
                'default' => false,
                'limit' => null,
                'null' => false,
            ])
            ->addColumn('question_2_2', 'boolean', [
                'comment' => '重視するポイント2',
                'default' => false,
                'limit' => null,
                'null' => false,
            ])
            ->addColumn('question_2_3', 'boolean', [
                'comment' => '重視するポイント3',
                'default' => false,
                'limit' => null,
                'null' => false,
            ])
            ->addColumn('question_2_4', 'boolean', [
                'comment' => '重視するポイント4',
                'default' => false,
                'limit' => null,
                'null' => false,
            ])
            ->addColumn('question_2_5', 'boolean', [
                'comment' => '重視するポイント5',
                'default' => false,
                'limit' => null,
                'null' => false,
            ])
            ->addColumn('question_2_6', 'boolean', [
                'comment' => '重視するポイント6',
                'default' => false,
                'limit' => null,
                'null' => false,
            ])
            ->addColumn('question_2_7', 'boolean', [
                'comment' => '重視するポイント7',
                'default' => false,
                'limit' => null,
                'null' => false,
            ])
            ->addColumn('question_2_8', 'boolean', [
                'comment' => '重視するポイント8',
                'default' => false,
                'limit' => null,
                'null' => false,
            ])
            ->addColumn('question_2_9', 'boolean', [
                'comment' => '重視するポイント9',
                'default' => false,
                'limit' => null,
                'null' => false,
            ])
            ->addColumn('question_2_10', 'boolean', [
                'comment' => '重視するポイント10',
                'default' => false,
                'limit' => null,
                'null' => false,
            ])
            ->addColumn('question_2_11', 'boolean', [
                'comment' => '重視するポイント11',
                'default' => false,
                'limit' => null,
                'null' => false,
            ])
            ->addColumn('created', 'datetime', [
                'comment' => '作成日時',
                'default' => 'CURRENT_TIMESTAMP',
                'limit' => null,
                'null' => false,
            ])
            ->addColumn('modified', 'datetime', [
                'comment' => '更新日時',
                'default' => 'CURRENT_TIMESTAMP',
                'limit' => null,
                'null' => false,
                'update' => 'CURRENT_TIMESTAMP',
            ])
            ->addIndex(
                [
                    'general_user_id',
                ],
                [
                    'name' => 'uk_user_surveys_general_user_id',
                    'unique' => true,
                ]
            )
            ->addIndex(
                [
                    'year',
                ],
                [
                    'name' => 'idx_user_surveys_year',
                ]
            )
            ->addIndex(
                [
                    'child_sex',
                ],
                [
                    'name' => 'idx_user_surveys_child_sex',
                ]
            )
            ->addIndex(
                [
                    'budget',
                ],
                [
                    'name' => 'idx_user_surveys_budget',
                ]
            )
            ->create();
    }

    /**
     * Down Method.
     *
     * More information on this method is available here:
     * https://book.cakephp.org/phinx/0/en/migrations.html#the-down-method
     * @return void
     */
    public function down(): void
    {
        $this->table('user_surveys')->drop()->save();
    }
}
