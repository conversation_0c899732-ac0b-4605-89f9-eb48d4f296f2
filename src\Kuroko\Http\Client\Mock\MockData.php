<?php

namespace App\Kuroko\Http\Client\Mock;

class MockData
{
    /**
     * 同一階層のjsonファイルをモックデータとして返却する
     * @param string $name
     * @return string
     */
    protected static function _load(string $name): string
    {
        $path = dirname(__FILE__) . DS . basename($name) . '.json';
        return file_get_contents($path);
    }


    public static function privateStaticProductList(): string
    {
        return static::_load('priavate-static-product-list');
    }

    public static function privateStaticMakerList(): string
    {
        return static::_load('priavate-static-maker-list');
    }

    public static function staticSendInquiry(): string
    {
        return static::_load('static-send-inquiry');
    }

    public static function dynamicInquiriesList(): string
    {
        return static::_load('dynamic-inquiries-list');
    }

    public static function dynamicMemberRegist(): string
    {
        return static::_load('dynamic-member-regist');
    }

    public static function dynamicMemberRegistValidateOnly(): string
    {
        return static::_load('dynamic-member-regist-validate-only');
        // return static::_load('dynamic-member-regist-validate-only-error');
    }

    public static function dynamicMemberMe(): string
    {
        return static::_load('dynamic-member-me');
        // return static::_load('dynamic-client-me');
        // return static::_load('dynamic-swb-me');
    }

    public static function dynamicMemberUpdate(): string
    {
        return static::_load('dynamic-member-update');
    }

    public static function dynamicMemberList(): string
    {
        return static::_load('dynamic-member-list');
    }

    public static function dynamicMemberSwbList(): string
    {
        return static::_load('dynamic-swb-member-list');
    }

    public static function dynamicUpdateStatusAndGetFormInfo(): string
    {
        return static::_load('dynamic-member-status-update');
    }

    public static function dynamicECOrderPurchase(): string
    {
        return static::_load('dynamic-ec-order-purchase');
    }

    public static function dynamicECOrderList(): string
    {
        // return static::_load('dynamic-ec-order-list');
        return static::_load('dynamic-ec-order-list-for-user');
    }

    public static function dynamicECOrderAllList(): string
    {
        return static::_load('dynamic-ec-order-list');
    }

    public static function dynamicECOrderAllListByOrderId(): string
    {
        return static::_load('dynamic-ec-order-list-by-order-id');
    }

    public static function dynamicLoginLogin(): string
    {
        return static::_load('dynamic-login-login');
    }

    public static function dynamicLoginLogout(): string
    {
        return static::_load('dynamic-login-logout');
    }

    public static function dynamicLoginToken(): string
    {
        return static::_load('dynamic-login-token');
    }

    public static function dynamicLoginReminder(): string
    {
        return static::_load('dynamic-login-reminder');
        // return static::_load('dynamic-login-reminder-error');
        // return static::_load('dynamic-login-reminder-password-reset');
        // return static::_load('dynamic-login-reminder-password-reset-error');
    }

    public static function dynamicLoginResetPassword(): string
    {
        return static::_load('dynamic-login-reset-password');
        // return static::_load('dynamic-login-reset-password-error');
        // return static::_load('dynamic-login-reminder-password-reset');
        // return static::_load('dynamic-login-reminder-password-reset-error');
    }
}
