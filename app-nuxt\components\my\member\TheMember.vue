<script setup lang="ts">
import TheMemberDetail from "~/components/my/member/TheMemberDetail.vue";
import type { Member } from "~/src/models/entry/Member";

const emit = defineEmits(["change-password", "member-update"]);
const gotoChangePassword = (member: Member): void => {
    emit("change-password", member);
};
const gotoMemberUpdate = (member: Member): void => {
    emit("member-update", member);
};
</script>

<template>
    <v-container>
        <p class="text-h6 font-weight-bold">登録情報の照会</p>
        <v-divider class="mt-1 mb-7"></v-divider>
        <the-member-detail
            :show-update-dialog="true"
            @change-password="gotoChangePassword"
            @member-update="gotoMemberUpdate"
        ></the-member-detail>
        <!-- <the-member-update></the-member-update> -->
        <p class="text-body-1 mt-7">
            退会希望の方は<a
                href="https://coverme.jp/contact"
                target="_blank"
                rel="noopener noreferrer"
                >こちら</a
            >よりご連絡ください。
        </p>
    </v-container>
</template>

<style scoped></style>
