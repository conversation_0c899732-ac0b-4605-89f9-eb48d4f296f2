<?php

declare(strict_types=1);

namespace App\Command;

use Cake\Command\Command;
use Cake\Console\Arguments;
use <PERSON>ake\Console\ConsoleIo;
use Cake\Console\ConsoleOptionParser;
use Cake\Log\Log;
use Cake\I18n\FrozenTime;
use Cake\Utility\Hash;
use App\Service\RandselOrdersService;
use App\Kuroko\ApiModel\KurokoApiDynamic\ECOrders;

/**
 * KurocoOrdersToDB command.
 */
class KurocoOrdersToDbCommand extends Command
{
    /**
     * Hook method for defining this command's option parser.
     *
     * @see https://book.cakephp.org/4/en/console-commands/commands.html#defining-arguments-and-options
     * @param \Cake\Console\ConsoleOptionParser $parser The parser to be defined
     * @return \Cake\Console\ConsoleOptionParser The built parser.
     */
    public function buildOptionParser(ConsoleOptionParser $parser): ConsoleOptionParser
    {
        $parser = parent::buildOptionParser($parser);

        $parser->setDescription([
            'Kurocoの注文情報をDBに登録します。',
            'Kurocoの注文情報について、未削除のユーザーの注文情報のみを取得します。',
            'オプションを指定しない場合は、すべての注文情報を取得します。',
            '他のオプションを指定する場合は、オプションヘルプを参照してください。',
        ]);

        $parser->addOptions([
            'from' => ['help' => '注文日の開始日(yyyy-mm-dd) 例：--from=2022-01-01', 'default' => null],
            'to' => ['help' => '注文日の終了日(yyyy-mm-dd) 例：--to=2022-12-31', 'default' => null],
            'product_id' => ['help' => '商品ID 例：--product_id=1', 'default' => null],
            'order_id' => ['help' => '注文ID（複数指定可） 例：--order_id=1 --order_id=2', 'default' => null, 'multiple' => true],
        ]);
        return $parser;
    }

    /**
     * Implement this method with your command's logic.
     *
     * @param \Cake\Console\Arguments $args The command arguments.
     * @param \Cake\Console\ConsoleIo $io The console io
     * @return null|void|int The exit code or null for success
     */
    public function execute(Arguments $args, ConsoleIo $io)
    {
        $ecOrders = new ECOrders();
        $options = $args->getOptions();

        $io->out("options: " . json_encode($options));

        $randselOrdersService = new RandselOrdersService();

        $invalidOrderIds = [];
        $saveFailedOrderIds = [];
        $comfirmedFailedOrderIds = [];

        foreach (Hash::get($options, 'order_id', [0]) as $orderId) {
            if ((int)$orderId > 0) {
                $options['ec_order_id'] = $orderId;
            }
            $response = $ecOrders->listForMigration($options);
            $totalPageCnt = Hash::get($response, 'totalPageCnt');
            $io->out("Total Page: $totalPageCnt");

            $pageID = Hash::get($options, 'pageID', 1);
            while ($pageID <= $totalPageCnt) {
                $options['pageID'] = $pageID;
                $response = $ecOrders->listForMigration($options);

                foreach (Hash::get($response, 'list') as $ecOrder) {
                    $data = Hash::get($ecOrder->getData(), "ext_data");
                    $data = Hash::insert($data, "id", Hash::get($ecOrder->getData(), "ec_order_id"));

                    $created = FrozenTime::parse(Hash::get($ecOrder->getData(), "inst_ymdhi"))->format('Y-m-d H:i:s');
                    $data = Hash::insert($data, "created", $created);

                    $io->out(sprintf(
                        "Order ID: %s Product ID: %s Member ID: %s Maker ID: %s",
                        Hash::get($data, "id"),
                        Hash::get($data, "product_id"),
                        Hash::get($data, "member_id"),
                        Hash::get($data, "maker_id")
                    ));

                    if (
                        Hash::get($ecOrder->getData(), "ext_data.member_id") == null
                        || Hash::get($ecOrder->getData(), "ext_data.product_id") == null
                        || Hash::get($ecOrder->getData(), "ext_data.maker_id") == null
                    ) {
                        $io->err("Order ID: " . Hash::get($data, "id") . " data created failed");

                        $invalidOrderIds[] = Hash::get($data, "id");
                        continue;
                    }

                    if ($entity = $randselOrdersService->createRandselOrder($data)) {
                        $io->out("Order ID: " . Hash::get($data, "id") . " data created successfully");
                        $io->out("Order ID: " . Hash::get($data, "id") . " comfirmed : " . static::comfirmed($data, $entity->responseData()));

                        if (static::comfirmed($data, $entity->responseData()) == false) {
                            $comfirmedFailedOrderIds[] = Hash::get($data, "id");
                        }
                    } else {
                        $io->err("Order ID: " . Hash::get($data, "id") . " data created failed");

                        $saveFailedOrderIds[] = Hash::get($data, "id");
                    }
                }

                $pageID++;
            }
        }
        $io->out("Kuroco orders migration is completed.");

        if (count($invalidOrderIds) > 0) {
            $io->err("Invalid Order IDs: " . implode(",", $invalidOrderIds));
        }

        if (count($saveFailedOrderIds) > 0) {
            $io->err("Save Failed Order IDs: " . implode(",", $saveFailedOrderIds));
        }

        if (count($comfirmedFailedOrderIds) > 0) {
            $io->err("Comfirmed Failed Order IDs: " . implode(",", $comfirmedFailedOrderIds));
        }
    }

    private static function comfirmed(array $data, array $responseData): bool
    {
        foreach ($data as $key => $value) {
            $responseValue = $responseData[$key];

            if ($responseValue instanceof \Cake\I18n\FrozenTime) {
                if ($responseValue->i18nFormat('yyyy-MM-dd HH:mm:ss') != $value) {
                    return false;
                }
            } elseif ($responseValue != $value) {
                return false;
            }
        }
        return true;
    }
}
