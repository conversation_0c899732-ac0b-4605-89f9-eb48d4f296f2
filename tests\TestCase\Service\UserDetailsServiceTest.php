<?php
declare(strict_types=1);

namespace App\Test\TestCase\Service;

use App\Service\UserDetailsService;
use App\Model\Entity\GeneralUser;
use App\Model\Entity\UserProfile;
use App\Mailer\AppMailer;
use App\Mailer\Sender\ToUser\UserDetailUpdateCompletedSender;
use App\Utility\Encryptor;
use Cake\TestSuite\TestCase;
use Cake\ORM\TableRegistry;
use Cake\Log\Log;
use Cake\TestSuite\EmailTrait;
use Exception;

/**
 * UserDetailsService Test Case
 */
class UserDetailsServiceTest extends TestCase
{
    use EmailTrait;

    /**
     * Test fixtures
     */
    protected $fixtures = [
        'app.GeneralUsers',
        'app.UserProfiles',
        'app.UserTokens',
    ];

    private UserDetailsService $userDetailsService;
    private $generalUsersTable;
    private $userProfilesTable;

    public function setUp(): void
    {
        parent::setUp();
        $this->userDetailsService = new UserDetailsService();
        $this->userDetailsService->initialize();

        $this->generalUsersTable = TableRegistry::getTableLocator()->get('GeneralUsers');
        $this->userProfilesTable = TableRegistry::getTableLocator()->get('UserProfiles');
    }

    public function tearDown(): void
    {
        unset($this->userDetailsService);
        unset($this->generalUsersTable);
        unset($this->userProfilesTable);
        parent::tearDown();
    }

    /**
     * 正常系：ユーザー情報の更新テスト（email + プロフィール情報）
     */
    public function testEditUserSuccess(): void
    {
        // テストデータ
        $userId = '1';
        $updateData = [
            "member_id" => 1,
            "name1" => "07071てすとせい",
            "name2" => "007072しとう",
            "name1_hurigana" => "ジョ",
            "name2_hurigana" => "シトウ",
            "email" => "<EMAIL>",
            "zip_code" => "1231234",
            "tdfk_cd" => "04",
            "address1" => "いち",
            "address2" => "１－１",
            "address3" => "",
            "tel" => "030-1122-3344",
            "email_send_ng_flg" => true
        ];

        // 更新前のデータを確認
        $beforeUser = $this->generalUsersTable->find()
            ->contain(['UserProfiles'])
            ->where(['GeneralUsers.id' => $userId])
            ->first();
        
        $this->assertNotNull($beforeUser);
        $this->assertNotEquals($updateData['email'], $beforeUser->email);

        // サービスメソッド実行
        $result = $this->userDetailsService->edit($userId, $updateData);

        // 結果の検証
        $this->assertNotNull($result);
        $this->assertInstanceOf(GeneralUser::class, $result);

        // general_usersテーブルの更新確認
        $updatedUser = $this->generalUsersTable->find()
            ->contain(['UserProfiles'])
            ->where(['GeneralUsers.id' => $userId])
            ->first();
        
        $this->assertEquals($updateData['email'], $updatedUser->email);

        // user_profilesテーブルの更新確認
        $profile = $updatedUser->user_profile;
        $this->assertNotNull($profile);
        
        // 暗号化フィールドの確認（復号化して比較）
        $this->assertEquals($updateData['name1'], $profile->decrypted_last_name);
        $this->assertEquals($updateData['name2'], $profile->decrypted_first_name);
        $this->assertEquals($updateData['name1_hurigana'], $profile->decrypted_last_name_kana);
        $this->assertEquals($updateData['name2_hurigana'], $profile->decrypted_first_name_kana);
        $this->assertEquals($updateData['zip_code'], $profile->decrypted_zip_code);
        $this->assertEquals($updateData['tdfk_cd'], $profile->decrypted_prefecture_code);
        $this->assertEquals($updateData['address1'], $profile->decrypted_address1);
        $this->assertEquals($updateData['address2'], $profile->decrypted_address2);
        $this->assertEquals($updateData['address3'], $profile->decrypted_address3);
        $this->assertEquals($updateData['tel'], $profile->decrypted_tel);
        $this->assertEquals($updateData['email_send_ng_flg'], $profile->email_send_ng_flg);
    }

    /**
     * 正常系：プロフィールが存在しないユーザーの更新テスト
     */
    public function testEditUserWithoutProfile(): void
    {
        // プロフィールが存在しないユーザー（ID: 2）を使用
        $userId = '2';
        $updateData = [
            "name1" => "新規姓",
            "name2" => "新規名",
            "name1_hurigana" => "シンキセイ",
            "name2_hurigana" => "シンキメイ",
            "email" => "<EMAIL>",
            "zip_code" => "9876543",
            "tdfk_cd" => "01",
            "address1" => "北海道札幌市",
            "address2" => "中央区",
            "address3" => "テストビル",
            "tel" => "011-1234-5678",
            "email_send_ng_flg" => true
        ];

        // サービスメソッド実行
        $result = $this->userDetailsService->edit($userId, $updateData);

        // 結果の検証
        $this->assertNotNull($result);

        // 新規プロフィールが作成されたことを確認
        $updatedUser = $this->generalUsersTable->find()
            ->contain(['UserProfiles'])
            ->where(['GeneralUsers.id' => $userId])
            ->first();
        
        $this->assertEquals($updateData['email'], $updatedUser->email);
        $this->assertNotNull($updatedUser->user_profile);
        
        $profile = $updatedUser->user_profile;
        $this->assertEquals($updateData['name1'], $profile->decrypted_last_name);
        $this->assertEquals($updateData['name2'], $profile->decrypted_first_name);
    }

    /**
     * 異常系：存在しないユーザーIDでの更新試行
     */
    public function testEditNonExistentUser(): void
    {
        $nonExistentUserId = '999';
        $updateData = [
            "email" => "<EMAIL>",
            "name1" => "テスト姓",
            "name2" => "テスト名"
        ];

        // 例外が発生することを確認
        $this->expectException(Exception::class);
        $this->expectExceptionMessage("User not found with ID: {$nonExistentUserId}");

        $this->userDetailsService->edit($nonExistentUserId, $updateData);
    }

    /**
     * 正常系：部分的なデータ更新テスト
     */
    public function testEditPartialData(): void
    {
        $userId = '1';
        $updateData = [
            "member_id" => 1,
            "email" => "<EMAIL>",
            "name1" => "部分更新姓"
        ];

        // 更新前のプロフィール情報を取得
        $beforeUser = $this->generalUsersTable->find()
            ->contain(['UserProfiles'])
            ->where(['GeneralUsers.id' => $userId])
            ->first();
        
        $beforeFirstName = $beforeUser->user_profile->decrypted_first_name;

        // サービスメソッド実行
        $result = $this->userDetailsService->edit($userId, $updateData);

        // 結果の検証
        $updatedUser = $this->generalUsersTable->find()
            ->contain(['UserProfiles'])
            ->where(['GeneralUsers.id' => $userId])
            ->first();
        
        // 更新されたフィールドの確認
        $this->assertEquals($updateData['email'], $updatedUser->email);
        $this->assertEquals($updateData['name1'], $updatedUser->user_profile->decrypted_last_name);
        
        // 更新されていないフィールドが保持されていることを確認
        $this->assertEquals($beforeFirstName, $updatedUser->user_profile->decrypted_first_name);
    }

    /**
     * 正常系：メール送信処理の検証
     */
    public function testEmailSending(): void
    {
        $userId = '1';
        $updateData = [
            "member_id" => 1,
            "email" => "<EMAIL>",
            "name1" => "メール太郎"
        ];

        // サービスメソッド実行
        $result = $this->userDetailsService->edit($userId, $updateData);

        // メール送信が成功したことを確認
        $this->assertNull($result);
    }

    /**
     * 正常系：空文字列の処理テスト
     */
    public function testEditWithEmptyStrings(): void
    {
        $userId = '1';
        $updateData = [
            "member_id" => 1,
            "email" => "<EMAIL>",
            "name1" => "空文字テスト",
            "address3" => "", // 空文字列
        ];

        // サービスメソッド実行
        $result = $this->userDetailsService->edit($userId, $updateData);

        // 結果の検証
        $this->assertNull($result);

        $updatedUser = $this->generalUsersTable->find()
            ->contain(['UserProfiles'])
            ->where(['GeneralUsers.id' => $userId])
            ->first();

        $profile = $updatedUser->user_profile;
        $this->assertEquals("", $profile->decrypted_address3);
    }

    /**
     * 異常系：無効なメールアドレスでの更新試行
     */
    public function testEditWithInvalidEmail(): void
    {
        $userId = '1';
        $updateData = [
            "member_id" => 1,
            "email" => "invalid-email", // 無効なメールアドレス
            "name1" => "テスト姓"
        ];

        // バリデーションエラーが発生することを確認
        $this->expectException(Exception::class);

        $this->userDetailsService->edit($userId, $updateData);
    }

    /**
     * 異常系：重複メールアドレスでの更新試行
     */
    public function testEditWithDuplicateEmail(): void
    {
        $userId = '1';
        $updateData = [
            "member_id" => 1,
            "email" => "<EMAIL>", // 既に存在するメールアドレス（ID:3のユーザー）
            "name1" => "重複テスト"
        ];

        // 重複エラーが発生することを確認
        $this->expectException(Exception::class);

        $this->userDetailsService->edit($userId, $updateData);
    }

    /**
     * 正常系：トランザクション処理の検証
     */
    public function testTransactionHandling(): void
    {
        $userId = '1';
        $updateData = [
            "member_id" => 1,
            "email" => "<EMAIL>",
            "name1" => "トランザクション太郎",
            "name2" => "テスト",
            "zip_code" => "1234567"
        ];

        // 更新前のデータを保存
        $beforeUser = $this->generalUsersTable->find()
            ->contain(['UserProfiles'])
            ->where(['GeneralUsers.id' => $userId])
            ->first();

        // サービスメソッド実行
        $result = $this->userDetailsService->edit($userId, $updateData);

        // トランザクションが正常に完了したことを確認
        $this->assertNull($result);

        $updatedUser = $this->generalUsersTable->find()
            ->contain(['UserProfiles'])
            ->where(['GeneralUsers.id' => $userId])
            ->first();

        // 全ての更新が反映されていることを確認
        $this->assertEquals($updateData['email'], $updatedUser->email);
        $this->assertEquals($updateData['name1'], $updatedUser->user_profile->decrypted_last_name);
        $this->assertEquals($updateData['name2'], $updatedUser->user_profile->decrypted_first_name);
        $this->assertEquals($updateData['zip_code'], $updatedUser->user_profile->decrypted_zip_code);
    }

    /**
     * 正常系：暗号化フィールドの処理確認
     */
    public function testEncryptionHandling(): void
    {
        $userId = '1';
        $sensitiveData = [
            "email" => "<EMAIL>",
            "name1" => "暗号化太郎",
            "name2" => "テスト",
            "tel" => "090-1234-5678",
            "address1" => "東京都新宿区テスト町1-2-3"
        ];

        // サービスメソッド実行
        $result = $this->userDetailsService->edit($userId, $sensitiveData);

        // データベースから直接取得して暗号化されていることを確認
        $userProfile = $this->userProfilesTable->find()
            ->where(['general_user_id' => $userId])
            ->first();

        // 暗号化されたデータは元の値と異なることを確認
        $this->assertNotEquals($sensitiveData['name1'], $userProfile->last_name);
        $this->assertNotEquals($sensitiveData['name2'], $userProfile->first_name);
        $this->assertNotEquals($sensitiveData['tel'], $userProfile->tel);
        $this->assertNotEquals($sensitiveData['address1'], $userProfile->address1);

        // 復号化した値が正しいことを確認
        $this->assertEquals($sensitiveData['name1'], $userProfile->decrypted_last_name);
        $this->assertEquals($sensitiveData['name2'], $userProfile->decrypted_first_name);
        $this->assertEquals($sensitiveData['tel'], $userProfile->decrypted_tel);
        $this->assertEquals($sensitiveData['address1'], $userProfile->decrypted_address1);
    }

    /**
     * 正常系：フィールドマッピングの確認
     */
    public function testFieldMapping(): void
    {
        $userId = '1';
        $mappingTestData = [
            "member_id" => 1,
            "email" => "<EMAIL>",
            "name1" => "マッピング姓",           // → last_name
            "name2" => "マッピング名",           // → first_name
            "name1_hurigana" => "マッピングセイ", // → last_name_kana
            "name2_hurigana" => "マッピングメイ", // → first_name_kana
            "tdfk_cd" => "13",                  // → prefecture_code
            "zip_code" => "1234567",            // → zip_code
            "address1" => "住所1",              // → address1
            "address2" => "住所2",              // → address2
            "address3" => "住所3",              // → address3
            "tel" => "03-1234-5678",           // → tel
            "email_send_ng_flg" => true        // → email_send_ng_flg
        ];

        // サービスメソッド実行
        $result = $this->userDetailsService->edit($userId, $mappingTestData);

        // フィールドマッピングが正しく動作していることを確認
        $updatedUser = $this->generalUsersTable->find()
            ->contain(['UserProfiles'])
            ->where(['GeneralUsers.id' => $userId])
            ->first();

        $profile = $updatedUser->user_profile;

        // 各フィールドのマッピングを確認
        $this->assertEquals($mappingTestData['email'], $updatedUser->email);
        $this->assertEquals($mappingTestData['name1'], $profile->decrypted_last_name);
        $this->assertEquals($mappingTestData['name2'], $profile->decrypted_first_name);
        $this->assertEquals($mappingTestData['name1_hurigana'], $profile->decrypted_last_name_kana);
        $this->assertEquals($mappingTestData['name2_hurigana'], $profile->decrypted_first_name_kana);
        $this->assertEquals($mappingTestData['tdfk_cd'], $profile->decrypted_prefecture_code);
        $this->assertEquals($mappingTestData['zip_code'], $profile->decrypted_zip_code);
        $this->assertEquals($mappingTestData['address1'], $profile->decrypted_address1);
        $this->assertEquals($mappingTestData['address2'], $profile->decrypted_address2);
        $this->assertEquals($mappingTestData['address3'], $profile->decrypted_address3);
        $this->assertEquals($mappingTestData['tel'], $profile->decrypted_tel);
        $this->assertEquals($mappingTestData['email_send_ng_flg'], $profile->email_send_ng_flg);
    }
}
