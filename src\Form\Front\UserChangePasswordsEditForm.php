<?php

namespace App\Form\Front;

use App\Enums\EFormFieldType;
use App\Enums\EntityFields\EUserChangePassword;
use App\Form\ApiForm;
use App\Validation\Validator\UserChangePasswordFormValidator;
use Cake\Form\Schema;
use Cake\Validation\Validator;

class UserChangePasswordsEditForm extends ApiForm
{

    public function _buildSchema(Schema $schema): Schema
    {
        $schema->addField(EUserChangePassword::LOGIN_ID->value, EFormFieldType::STRING->value);
        $schema->addField(EUserChangePassword::CURRENT_PASSWORD->value, EFormFieldType::STRING->value);
        $schema->addField(EUserChangePassword::NEW_PASSWORD->value, EFormFieldType::STRING->value);
        return $schema;
    }

    public function validationDefault(Validator $validator): Validator
    {
        return UserChangePasswordFormValidator::buildValidator($validator, [
            EUserChangePassword::LOGIN_ID->value,
            EUserChangePassword::CURRENT_PASSWORD->value,
            EUserChangePassword::NEW_PASSWORD->value,
        ]);
    }

    protected function _getAllowFieldsPath(): array
    {
        return [];
    }
}
