<?php

namespace App\Service;

use App\Model\Entity\Product;
use App\Model\Table\ProductsTable;
use App\Model\Table\BudgetsTable;
use App\Model\Table\RandselOrdersTable;
use Cake\ORM\TableRegistry;
use Cake\Log\Log;
use Cake\I18n\FrozenTime;
use App\Service\BudgetCalculationService;
use App\Service\ProductPerformanceService;

/**
 * 商品表示制御サービス
 * 
 * 商品の掲載タイプ（紙、デジタル、売り切れ）を判定し、
 * 申し込みフォームでの表示制御を行う
 */
class ProductDisplayService implements IService
{
    use ServiceTrait;

    protected array $_defaultConfig = [];
    /**
     * @var ProductsTable
     */
    private ProductsTable $productsTable;

    /**
     * @var BudgetsTable
     */
    private BudgetsTable $budgetsTable;

    /**
     * @var RandselOrdersTable
     */
    private RandselOrdersTable $randselOrdersTable;

    /**
     * @var BudgetCalculationService
     */
    private BudgetCalculationService $budgetCalculationService;

    /**
     * @var ProductPerformanceService
     */
    private ProductPerformanceService $performanceService;

    /**
     * 表示タイプ定数
     */
    public const DISPLAY_TYPE_PAPER = 'paper';
    public const DISPLAY_TYPE_DIGITAL = 'digital';
    public const DISPLAY_TYPE_SOLD_OUT = 'sold_out';
    public const DISPLAY_TYPE_HIDDEN = 'hidden';

    /**
     * カタログタイプ定数
     */
    public const CATALOG_TYPE_PAPER = 1;
    public const CATALOG_TYPE_DIGITAL = 2;

    public function initialize(): void
    {
        $this->productsTable = TableRegistry::getTableLocator()->get('Products');
        $this->budgetsTable = TableRegistry::getTableLocator()->get('Budgets');
        $this->randselOrdersTable = TableRegistry::getTableLocator()->get('RandselOrders');
        $this->budgetCalculationService = new BudgetCalculationService();
        $this->performanceService = new ProductPerformanceService();
    }

    /**
     * 表示可能な商品一覧を取得（パフォーマンス測定付き）
     *
     * @param int|null $year 年度（nullの場合は1年先）
     * @return array 表示可能な商品配列（display_typeプロパティ付き）
     */
    public function getAvailableProducts(?int $year = null): array
    {
        // パフォーマンス測定付きで商品一覧を生成
        return $this->performanceService->measureProductListGeneration(
            function() use ($year) {
                return $this->generateProductList($year);
            }
        );
    }

    /**
     * 商品一覧を生成（内部処理）
     *
     * @param int|null $year 年度（nullの場合は1年先）
     * @return array 商品配列
     */
    private function generateProductList(?int $year = null): array
    {
        // 表示フラグがtrueの商品を取得（最適化）
        $products = $this->productsTable
            ->find()
            ->select([
                'id', 'maker_id', 'brand_id', 'display_name',
                'description_html', 'note_html', 'mask_image_description',
                'image_url', 'pdf_url',
                'sort_order', 'is_display', 'year'
            ])
            ->where(['is_display' => true, 'year' => $year ?? date('Y') + 1]) // 1年先の商品を表示（年度指定がある場合はその年度の商品を表示）
            ->contain([
                'Makers' => ['fields' => ['id', 'name']],
                'Brands' => ['fields' => ['id', 'name']]
            ])
            ->order(['sort_order' => 'DESC'])
            ->limit(1000) // 安全のための上限設定（1000件以上は表示されない）
            ->toArray();

        $result = [];

        foreach ($products as $product) {
            $displayType = $this->determineDisplayType($product);

            // 非表示以外の商品を結果に含める
            if ($displayType !== self::DISPLAY_TYPE_HIDDEN) {
                $product->display_type = $displayType;
                $result[] = $product;
            }
        }

        Log::info('ProductDisplayService: 商品一覧生成完了', [
            'total_products' => count($products),
            'available_products' => count($result)
        ]);

        return $result;
    }

    /**
     * 指定商品の表示タイプを判定（リアルタイム計算）
     *
     * @param Product $product 商品エンティティ
     * @return string 表示タイプ（paper/digital/sold_out）
     */
    public function determineDisplayType(Product $product): string
    {
        // パフォーマンス測定付きでリアルタイム計算
        return $this->performanceService->measureDisplayTypeCalculation(
            $product->id,
            function() use ($product) {
                return $this->calculateDisplayType($product);
            }
        );
    }

    /**
     * 商品の表示タイプを計算（リアルタイム計算）
     *
     * @param Product $product 商品エンティティ
     * @return string 表示タイプ
     */
    private function calculateDisplayType(Product $product): string
    {
        // 現在有効な予算条件を取得
        $activeBudgets = $this->getActiveBudgetsByProduct($product->id);

        if (empty($activeBudgets)) {
            Log::warning('ProductDisplayService: 有効な予算が見つかりません', [
                'product_id' => $product->id
            ]);
            return self::DISPLAY_TYPE_HIDDEN;
        }

        // 各予算条件の注文数を計算
        $budgetResults = [];
        foreach ($activeBudgets as $budget) {
            $orderCount = $this->getOrderCountByBudget($budget);
            $isAvailable = $orderCount < $budget->budget_quantity;

            $budgetResults[] = [
                'budget' => $budget,
                'order_count' => $orderCount,
                'is_available' => $isAvailable,
                'type' => $budget->type,
                'priority' => $budget->priority
            ];
        }

        // 利用可能な予算のみをフィルタリング
        $availableBudgets = array_filter($budgetResults, function($result) {
            return $result['is_available'];
        });

        if (empty($availableBudgets)) {
            return self::DISPLAY_TYPE_SOLD_OUT;
        }

        // 優先順位でソート（降順）
        usort($availableBudgets, function($a, $b) {
            if ($a['priority'] === $b['priority']) {
                // 優先順位が同じ場合はタイプで比較（紙を優先）
                return $a['type'] <=> $b['type'];
            }
            return $b['priority'] <=> $a['priority'];
        });

        // 最も優先順位の高い予算のタイプを返す
        $topBudget = $availableBudgets[0];

        return $topBudget['type'] === self::CATALOG_TYPE_PAPER
            ? self::DISPLAY_TYPE_PAPER
            : self::DISPLAY_TYPE_DIGITAL;
    }

    /**
     * 指定商品の現在有効な予算一覧を取得（最適化版）
     *
     * @param int $productId 商品ID
     * @return array 有効な予算配列
     */
    private function getActiveBudgetsByProduct(int $productId): array
    {
        $now = FrozenTime::now();

        // インデックスを活用した最適化クエリ
        return $this->budgetsTable
            ->find()
            ->select([
                'id', 'product_id', 'type', 'price', 'budget_quantity',
                'start_date', 'end_date', 'priority'
            ])
            ->where([
                'product_id' => $productId,
                'is_active' => true,
                'start_date <=' => $now,
                'end_date >=' => $now
            ])
            ->order(['priority' => 'DESC', 'type' => 'ASC'])
            ->limit(10) // 通常は予算数が少ないため制限
            ->toArray();
    }

    /**
     * 指定予算の注文数を取得（最適化版）
     *
     * @param object $budget 予算エンティティ
     * @return int 注文数
     */
    private function getOrderCountByBudget(object $budget): int
    {
        // インデックスを活用した最適化クエリ
        return $this->randselOrdersTable
            ->find()
            ->where([
                'product_id' => $budget->product_id,
                'type' => $budget->type,
                'created >=' => $budget->start_date,
                'created <=' => $budget->end_date // 期間を限定してパフォーマンス向上
            ])
            ->count();
    }

    /**
     * 商品表示タイプのキャッシュをクリア（非推奨：リアルタイム計算のため不要）
     *
     * @deprecated リアルタイム計算に変更したため、この機能は不要
     * @param int|null $productId 商品ID
     * @return void
     */
    public function clearDisplayTypeCache(?int $productId = null): void
    {
        // リアルタイム計算のため、何も実行しない
        Log::debug('ProductDisplayService: キャッシュクリア要求（リアルタイム計算のため無視）', [
            'product_id' => $productId
        ]);
    }

    /**
     * 申し込み時の売り切れ判定
     * 
     * @param int $productId 商品ID
     * @param int $catalogType カタログタイプ
     * @return bool true: 申し込み可能, false: 売り切れ
     */
    public function isOrderAvailable(int $productId, int $catalogType): bool
    {
        $product = $this->productsTable->get($productId);
        $displayType = $this->determineDisplayType($product);
        
        // 売り切れの場合は申し込み不可
        if ($displayType === self::DISPLAY_TYPE_SOLD_OUT) {
            return false;
        }
        
        // 指定されたカタログタイプが利用可能かチェック
        $expectedType = $catalogType === self::CATALOG_TYPE_PAPER 
            ? self::DISPLAY_TYPE_PAPER 
            : self::DISPLAY_TYPE_DIGITAL;
            
        return $displayType === $expectedType;
    }
}
