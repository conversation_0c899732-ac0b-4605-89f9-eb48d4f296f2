<?php

namespace App\Utility;

use Cake\Utility\Security;

class Encryptor
{
    /**
     * 暗号化メソッド
     *
     * @param string $plain 平文の文字列
     * @return string 暗号化された文字列
     */
    public static function encrypt(string $plain): string
    {
        return base64_encode(Security::encrypt($plain, Security::getSalt()));
    }

    /**
     * 復号化メソッド
     *
     * @param string $cipher 暗号化された文字列
     * @return string|null 復号化された文字列
     */
    public static function decrypt(string $cipher): ?string
    {
        return Security::decrypt(base64_decode($cipher), Security::getSalt());
    }
}
