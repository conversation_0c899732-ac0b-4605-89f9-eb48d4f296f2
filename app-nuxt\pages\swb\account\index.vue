<script setup lang="ts">
import TheSwbMy from "~/components/swb/TheSwbMy.vue";
import Products from "~~/src/models/Products";
import { Product } from "~~/src/models/entry/Product";
import { useAsyncData, useRuntimeConfig } from "#app";

definePageMeta({
  name: 'SwbAccount',
  type: 'swb'
});

const config = useRuntimeConfig();
const { data } = useAsyncData("products", async () => {
    const products = await Products.create(config).getAllList();
    return {
        products: products.map((t) => t.data),
    };
});
const products = Product.creates(data.value?.products || []);
</script>

<template>
    <the-req-login>
        <the-swb-my :products="products" />
    </the-req-login>
</template>

<style scoped></style>
