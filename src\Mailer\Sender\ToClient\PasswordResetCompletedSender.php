<?php

namespace App\Mailer\Sender\ToClient;

use App\Mailer\Sender\IToClientSender;
use App\Mailer\Sender\SenderTrait;
use App\Mailer\Sender\ToUserSenderTrait;

class PasswordResetCompletedSender implements IToClientSender
{
    use SenderTrait;
    use ToUserSenderTrait;

    public function getSubject(): string
    {
        return "【カバーミー】パスワード再設定完了のお知らせ";
    }

    public function getTemplate(): string
    {
        return "to_client/PasswordResetCompletedSender";
    }
}
