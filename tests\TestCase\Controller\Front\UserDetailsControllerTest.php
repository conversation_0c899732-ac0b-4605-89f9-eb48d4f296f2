<?php

namespace App\Test\TestCase\Controller\Front;

use App\Enums\EntityFields\EMember;
use App\Test\TestCase\Controller\ApiTestCase;
use Cake\I18n\FrozenTime;
use Cake\Utility\Hash;

class UserDetailsControllerTest extends ApiTestCase
{
    /**
     * Test index method
     *
     * @return void
     * @uses \App\Controller\Front\UserDetailsController::index()
     * php ./vendor/phpunit/phpunit/phpunit --filter "testIndex" ./tests/TestCase/Controller/Front/UserDetailsControllerTest.php
     */
    public function testIndex(): void
    {
        $this->setFrontAuthorized(true);
        $this->get('/front/user-details.json');
        $response = $this->bodyToArray($this->_response->getBody());
        debug($response);
        $this->assertResponseOk(print_r(Hash::flatten($response), true));
    }

    /**
     * Test edit method
     *
     * @return void
     * @uses \App\Controller\Front\UserDetailsController::edit()
     * php ./vendor/phpunit/phpunit/phpunit --filter "testEdit" ./tests/TestCase/Controller/Front/UserDetailsControllerTest.php
     */
    public function testEdit(): void
    {
        $now = FrozenTime::now();
        $this->setFrontAuthorized(true);
        $this->put('/front/user-details/' . $now->format("YmdHis") . '.json', [
            EMember::NAME1->value => 'システムユーザ姓' . $now->format("YmdHis"),
            EMember::NAME2->value => 'システムユーザ名' . $now->format("YmdHis"),
        ]);
        $response = $this->bodyToArray($this->_response->getBody());
        debug($response);
        $this->assertResponseOk(print_r(Hash::flatten($response), true));
    }
}
