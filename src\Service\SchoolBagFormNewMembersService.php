<?php

namespace App\Service;

use App\Enums\EntityFields\ESchoolBagForm;
use App\Kuroko\ApiModel\KurokoApiStatic\Inquiries;
use App\Kuroko\Entity\IKurokoEntity;
use App\Kuroko\Entity\Inquiry;
use App\Kuroko\Entity\Member;
use App\Kuroko\Entity\ValidationResult;
use App\Model\Entity\TemporaryRegistration;
use App\Mailer\AppMailer;
use App\Mailer\Sender\ToUser\SchoolBagMemberRegistSender;
use App\Service\UserRegistrationService;
use Cake\Utility\Hash;
use Cake\Log\Log;
use Exception;

class SchoolBagFormNewMembersService extends SchoolBagFormsService
{
    public function add(array $data = []): IKurokoEntity | TemporaryRegistration | null
    {
        try {
            $userRegistrationService = new UserRegistrationService();
            /** @var TemporaryRegistration $tempRegistration */
            $tempRegistration = $userRegistrationService->createTemporaryRegistration($data);
            Log::debug("SchoolBagFormNewMembersService::add");
            
            // プロファイルデータから姓（last_name）だけを取得して表示
            $profileData = $tempRegistration->array_decrypted_profile_data;
            
            AppMailer::sendToUser((new SchoolBagMemberRegistSender(
                Hash::get($data, ESchoolBagForm::EMAIL->value),
                Hash::get($profileData, "last_name")
            ))->setViewVars([
                'accessToken' => $tempRegistration->verification_token,
            ]));
            return $tempRegistration;
        } catch (Exception $exception) {
            $this->setErrors([
                '_system' => $exception->getMessage(),
            ]);
        }
        return null;
    }

    public function edit(string $id, array $data = []): ?IKurokoEntity
    {
        try {
            // メールアドレスを取得
            $email = Hash::get($data, ESchoolBagForm::EMAIL->value);

            if (empty($email)) {
                $this->setErrors([
                    'email' => 'メールアドレスは必須です',
                ]);
                return null;
            }

            // UserRegistrationServiceを使用してメールアドレスの重複チェック
            $userRegistrationService = new UserRegistrationService();

            if (!$userRegistrationService->isEmailAvailable($email)) {
                $this->setErrors([
                    'email' => 'このメールアドレスは既に登録されています',
                ]);
                return null;
            }

            // メールアドレスが利用可能な場合、成功レスポンスを返す
            return new ValidationResult([]);

        } catch (Exception $exception) {
            $this->setErrors([
                '_system' => $exception->getMessage(),
            ]);
        }
        return null;
    }


    // /**
    //  * 会員登録
    //  * @throws Exception
    //  */
    // public function _createMember(array $data, Inquiry $inquiry): Member
    // {
    //     $membersService = new MembersService();
    //     $member = $membersService->preFormTmpMember([
    //         "name1" => Hash::get($data, ESchoolBagForm::NAME1->value),
    //         "name2" => Hash::get($data, ESchoolBagForm::NAME2->value),
    //         "name1_hurigana" => Hash::get($data, ESchoolBagForm::NAME1_HURIGANA->value),
    //         "name2_hurigana" => Hash::get($data, ESchoolBagForm::NAME2_HURIGANA->value),
    //         "email" => Hash::get($data, ESchoolBagForm::EMAIL->value),
    //         "login_pwd" => Hash::get($data, ESchoolBagForm::LOGIN_PWD->value),
    //         "zip_code" => Hash::get($data, ESchoolBagForm::ZIP_CODE->value),
    //         "tdfk_cd" => Hash::get($data, ESchoolBagForm::TDFK_CD->value),
    //         "address1" => Hash::get($data, ESchoolBagForm::ADDRESS1->value),
    //         "address2" => Hash::get($data, ESchoolBagForm::ADDRESS2->value),
    //         "address3" => Hash::get($data, ESchoolBagForm::ADDRESS3->value),
    //         "tel" => Hash::get($data, ESchoolBagForm::TEL->value),
    //         "email_send_ng_flg" => !!Hash::get($data, ESchoolBagForm::EMAIL_SEND_NG_FLG->value),
    //         "tel_send_ng_flg" => true,
    //         "validate_only" => false,
    //         "auto_login" => 0,
    //         "login_ok_flg" => Member::LOGIN_OK_FLG_OK,
    //         "status" => Member::STATUS_UNVERIFIED,
    //         "pre_form_id" => intval($inquiry->getId()),
    //     ]);
    //     if (empty($member)) {
    //         //@todo エラーメッセージをkurokoAPIからとるばあいは要調整
    //         throw new Exception("member データ作成しっぱい");
    //     }
    //     return $member;
    // }

    // /**
    //  * フォームデータ作成
    //  * @throws Exception
    //  */
    // protected function _createInquiry(array $data): Inquiry
    // {
    //     $inquiries = new Inquiries();
    //     $response = $inquiries->sendInquiry($this->_createInquiryData($data));
    //     if (empty($response)) {
    //         //@todo エラーメッセージをkurokoAPIからとるばあいは要調整
    //         throw new Exception("form データ作成しっぱい");
    //     }
    //     return $response;
    // }


    // protected function _createInquiryData(array $data): array
    // {
    //     $response = [];
    //     $response = Hash::insert($response, "name", Hash::get($data, ESchoolBagForm::NAME1->value) . Hash::get($data, ESchoolBagForm::NAME2->value));
    //     $response = Hash::insert($response, "email", Hash::get($data, ESchoolBagForm::EMAIL->value));

    //     $response = Hash::insert($response, "ext_01", Hash::get($data, ESchoolBagForm::EXT_01->value));
    //     $response = Hash::insert($response, "body", Hash::get($data, ESchoolBagForm::BODY->value));
    //     return $response;
    // }
}
