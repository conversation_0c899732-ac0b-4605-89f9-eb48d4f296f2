export class MonthlyInvoice {
    private readonly _maker_id: number;
    private readonly _total_price: number;
    private readonly _status_modified_year_month: string;

    constructor(data: TMonthlyInvoice) {
        this._maker_id = Number(data.maker_id);
        this._total_price = Number(data.total_price);
        this._status_modified_year_month = data.status_modified_year_month;
    }

    static creates(monthlyInvoices: TMonthlyInvoice[]): MonthlyInvoice[] {
        return monthlyInvoices.map((order: TMonthlyInvoice) =>
            MonthlyInvoice.create(order),
        );
    }

    static create(randselOrder: TMonthlyInvoice): MonthlyInvoice {
        return new MonthlyInvoice(randselOrder);
    }

    get data(): TMonthlyInvoice {
        return {
            maker_id: this.maker_id,
            total_price: this.total_price,
            status_modified_year_month: this.status_modified_year_month,
        };
    }

    get maker_id(): number {
        return this._maker_id;
    }

    get total_price(): number {
        return this._total_price;
    }

    get status_modified_year_month(): string {
        return this._status_modified_year_month;
    }
}
