<?php
declare(strict_types=1);

namespace App\Model\Entity;

use Cake\ORM\Entity;

/**
 * ブランドエンティティ
 * 
 * @property int $id
 * @property int $maker_id
 * @property string $name
 * @property string|null $description
 * @property string|null $logo_url
 * @property string|null $brand_image_url
 * @property string|null $brand_features_html
 * @property string|null $other_features_html
 * @property int|null $established_year
 * @property int|null $target_age_min
 * @property int|null $target_age_max
 * @property int|null $target_gender
 * @property int|null $price_range_min
 * @property int|null $price_range_max
 * @property string|null $feature_tags
 * @property string|null $website_url
 * @property bool $is_premium
 * @property \Cake\I18n\FrozenTime|null $deleted
 * @property \Cake\I18n\FrozenTime $created
 * @property \Cake\I18n\FrozenTime $modified
 * @property \App\Model\Entity\Maker $maker
 * @property \App\Model\Entity\Product[] $products
 * @property \App\Model\Entity\RandselOrder[] $randsel_orders
 */
class Brand extends Entity
{
    protected $_accessible = [
        'maker_id' => true,
        'name' => true,
        'description' => true,
        'logo_url' => true,
        'brand_image_url' => true,
        'brand_features_html' => true,
        'other_features_html' => true,
        'established_year' => true,
        'target_age_min' => true,
        'target_age_max' => true,
        'target_gender' => true,
        'price_range_min' => true,
        'price_range_max' => true,
        'feature_tags' => true,
        'website_url' => true,
        'is_premium' => true,
        'deleted' => true,
        'created' => true,
        'modified' => true,
        'maker' => true,
        'products' => true,
        'randsel_orders' => true,
    ];

    protected $_hidden = [];

    /**
     * 対象性別名を取得
     */
    public function getTargetGenderName(): string
    {
        switch ($this->target_gender) {
            case 1:
                return '男子';
            case 2:
                return '女子';
            case 3:
                return '男女共用';
            default:
                return '不明';
        }
    }

    /**
     * 対象年齢範囲を取得
     */
    public function getTargetAgeRange(): ?string
    {
        if ($this->target_age_min === null && $this->target_age_max === null) {
            return null;
        }
        
        if ($this->target_age_min === $this->target_age_max) {
            return $this->target_age_min . '歳';
        }
        
        $min = $this->target_age_min ?? '';
        $max = $this->target_age_max ?? '';
        
        return $min . '歳〜' . $max . '歳';
    }

    /**
     * 価格帯を取得
     */
    public function getPriceRange(): ?string
    {
        if ($this->price_range_min === null && $this->price_range_max === null) {
            return null;
        }
        
        if ($this->price_range_min === $this->price_range_max) {
            return number_format($this->price_range_min) . '円';
        }
        
        $min = $this->price_range_min ? number_format($this->price_range_min) : '';
        $max = $this->price_range_max ? number_format($this->price_range_max) : '';
        
        return $min . '円〜' . $max . '円';
    }

    /**
     * 特徴タグを配列で取得
     */
    public function getFeatureTagsArray(): array
    {
        if (empty($this->feature_tags)) {
            return [];
        }
        
        return array_map('trim', explode(',', $this->feature_tags));
    }

    /**
     * プレミアムブランドかどうかを判定
     */
    public function isPremium(): bool
    {
        return $this->is_premium;
    }

    /**
     * 論理削除されているかどうかを判定
     */
    public function isDeleted(): bool
    {
        return $this->deleted !== null;
    }

    /**
     * アクティブなブランドかどうかを判定
     */
    public function isActive(): bool
    {
        return !$this->isDeleted();
    }
}
