import { Model } from "~~/src/models/Model";
import moment from "moment";
import { MonthlyInvoiceAdjustment } from "~~/src/models/entry/MonthlyInvoiceAdjustment";
import SwbMonthlyInvoiceAdjustmentsClient from "~/src/lib/http/coverme-front/SwbMonthlyInvoiceAdjustmentsClient";
import SwbConfirmRandselInvoiceAdjustmentsClient from "~/src/lib/http/coverme-front/SwbConfirmRandselInvoiceAdjustmentsClient";
export default class SwbInvoiceAdjustments extends Model {
    public getMonthlyInvoiceAdjustments(
        params?: Record<string, string | number | string[] | number[]>,
    ): Promise<MonthlyInvoiceAdjustment[]> {
        const config = this.config;
        return new Promise((resolve) => {
            SwbMonthlyInvoiceAdjustmentsClient.create(config)
                .index<TResponseMonthlyInvoiceAdjustments>(params)
                .then(({ randsel_invoice_adjustments }) => {
                    resolve(
                        MonthlyInvoiceAdjustment.creates(
                            randsel_invoice_adjustments,
                        ),
                    );
                })
                .catch((e) => console.error(e));
        });
    }

    public add(data: TMonthlyInvoiceAdjustment): Promise<boolean> {
        const config = this.config;
        return new Promise((resolve) => {
            SwbMonthlyInvoiceAdjustmentsClient.create(config)
                .post<TMonthlyInvoiceAdjustment, TResponseSuccess>(data)
                //@todo レスポンスをオブジェクトクラスにする、レスポンス項目は検討が必要
                .then(({ success }) => resolve(!!success))
                .catch(() => resolve(false));
        });
    }

    public put(data: TMonthlyInvoiceAdjustment): Promise<boolean> {
        const config = this.config;
        const id = moment().format("YYYYMMDDHHmmss");
        return new Promise((resolve) => {
            SwbMonthlyInvoiceAdjustmentsClient.create(config)
                .put<TMonthlyInvoiceAdjustment, TResponseSuccess>(id, data)
                //@todo レスポンスをオブジェクトクラスにする、レスポンス項目は検討が必要
                .then(({ success }) => resolve(!!success))
                .catch(() => resolve(false));
        });
    }

    public confirm(data: TMonthlyInvoiceAdjustment[]): Promise<boolean> {
        const config = this.config;
        const id = moment().format("YYYYMMDDHHmmss");
        return new Promise((resolve) => {
            SwbConfirmRandselInvoiceAdjustmentsClient.create(config)
                .put<TMonthlyInvoiceAdjustment[], TResponseSuccess>(id, data)
                //@todo レスポンスをオブジェクトクラスにする、レスポンス項目は検討が必要
                .then(({ success }) => resolve(!!success))
                .catch(() => resolve(false));
        });
    }

    public delete(id: number): Promise<boolean> {
        const config = this.config;
        return new Promise((resolve) => {
            SwbMonthlyInvoiceAdjustmentsClient.create(config)
                .delete<TResponseSuccess>(id)
                .then(({ success }) => resolve(!!success))
                .catch(() => resolve(false));
        });
    }
}
