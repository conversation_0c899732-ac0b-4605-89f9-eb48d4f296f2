<?xml version="1.0" encoding="UTF-8"?>
<phpunit
    colors="true"
    processIsolation="false"
    stopOnFailure="false"
    bootstrap="tests/bootstrap.php"
    >
    <php>
        <ini name="memory_limit" value="-1"/>
        <ini name="apc.enable_cli" value="1"/>
    </php>

    <!-- Add any additional test suites you want to run here -->
    <testsuites>
        <testsuite name="app">
            <directory>tests/TestCase/</directory>
        </testsuite>
        <!-- Add plugin test suites here. -->
    </testsuites>

    <!-- Load extension for fixtures -->
    <extensions>
        <extension class="Cake\TestSuite\Fixture\PHPUnitExtension"/>
    </extensions>

    <!-- Ignore vendor tests in code coverage reports -->
    <filter>
        <whitelist>
            <directory suffix=".php">src/</directory>
            <directory suffix=".php">plugins/*/src/</directory>
            <exclude>
                <file>src/Console/Installer.php</file>
            </exclude>
        </whitelist>
    </filter>
</phpunit>
