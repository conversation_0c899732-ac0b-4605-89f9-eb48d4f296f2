# Migration

### cmd

#### ステータス確認

```bash
bin/cake migrations status
```

#### migration実行

```bash
bin/cake migrations migrate
```

#### rollback

```bash
bin/cake migrations rollback -t XXXXXXXX
# 指定したバージョンが適応された状態まで戻る
```

### 既存のデータベースからマイグレーションファイルを作成する

```bash
bin/cake bake migration_snapshot Initial
```

### 差分からマイグレーションファイルを作成する

もし、すでにマイグレーションの履歴を持つアプリケーション上で diff 機能を使用したい場合、 マニュアルで比較に使用するダンプファイルを作成する必要があります。

```bash
bin/cake migrations dump
```

migration作成

```bash
bin/cake bake migration_diff CreateRandselOrders
```

### migrationファイルの作成

```bash
bin/cake bake migration NameOfTheMigrations
```

### unittestでテーブルないっていわれたらこれ

```shell
bin/cake migrations migrate --connection test
```

### データベースの作成

```sql
CREATE
DATABASE coverme_local DEFAULT CHARACTER SET utf8mb4
```

## Bake History

### Migrations

```shell

```

### Models

```shell
bin/cake bake model randsel_orders

bin/cake bake behavior randsel_orders
```

### Controllers

```shell

```

### Components

```shell
```
