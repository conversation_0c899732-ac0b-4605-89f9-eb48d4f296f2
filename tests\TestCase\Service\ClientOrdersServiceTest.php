<?php

namespace Service;

use App\Kuroko\Entity\AccessToken;
use App\Kuroko\Entity\Member;
use App\Service\ClientOrdersService;
use PHPUnit\Framework\TestCase;

class ClientOrdersServiceTest extends TestCase
{
    public function testIndex()
    {
        $member = new Member([]);
        $member->setAccessToken((new AccessToken([
            'access_token' => [
                'value' => "NTEwYjkwM2JlMGVhZTEyYjdiNTk2NGU4NGUwMWRkNmUxODVkODI5MTVmNWZkYTIwM2NlODBlMjUzZGZlMTQ0MeZWz/8w8wCe1GuWURYYSpiuouOTjhOXMwfgXyihsqr7RKSqOzWrpBH5N4l80t97CdROD0adoN4gG5MTzRM1mjMtXnZcelefIWdxR47P/3hVjTMzL+fAeFW36rKTvvo8pg=="
            ]
        ])));
        $service = (new ClientOrdersService())->setIdentity($member);
        $response = $service->index();
        $this->assertTrue(!!$response);
    }
}
