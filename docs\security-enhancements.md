# 認証システムセキュリティ強化提案

## 現在の実装状況と改善提案

### 1. トークンセキュリティの強化

#### 現在の実装
- 64文字のランダムトークン生成
- 暗号化による保護
- 有効期限管理

#### 改善提案

##### A. トークンローテーション機能
```php
// UserResolverクラスに追加
private function rotateTokenIfNeeded($tokenEntity): ?string
{
    // トークンが有効期限の半分を過ぎた場合、自動ローテーション
    $halfLife = $tokenEntity->expires->subHours(12); // 24時間の半分
    
    if (FrozenTime::now() > $halfLife) {
        $table = TableRegistry::getTableLocator()->get($tokenEntity->getSource());
        $newToken = $table->createApiAccessToken($tokenEntity->user_id, 24);
        
        if ($newToken) {
            // 古いトークンを無効化
            $table->delete($tokenEntity);
            return $newToken->token;
        }
    }
    
    return null;
}
```

##### B. レート制限の実装
```php
// src/Middleware/AuthenticationRateLimitMiddleware.php
<?php
namespace App\Middleware;

use Cake\Cache\Cache;
use Cake\Http\Exception\TooManyRequestsException;
use Psr\Http\Message\ResponseInterface;
use Psr\Http\Message\ServerRequestInterface;
use Psr\Http\Server\MiddlewareInterface;
use Psr\Http\Server\RequestHandlerInterface;

class AuthenticationRateLimitMiddleware implements MiddlewareInterface
{
    private const MAX_ATTEMPTS = 5;
    private const WINDOW_MINUTES = 15;

    public function process(ServerRequestInterface $request, RequestHandlerInterface $handler): ResponseInterface
    {
        $clientIp = $request->getClientIp();
        $email = $request->getData('email');
        
        if ($email) {
            $key = "auth_attempts_{$clientIp}_{$email}";
            $attempts = Cache::read($key, 'default') ?: 0;
            
            if ($attempts >= self::MAX_ATTEMPTS) {
                throw new TooManyRequestsException('Too many authentication attempts');
            }
            
            // 認証試行回数を記録
            Cache::write($key, $attempts + 1, self::WINDOW_MINUTES * 60);
        }
        
        return $handler->handle($request);
    }
}
```

### 2. パスワードセキュリティの強化

#### 現在の実装
- DefaultPasswordHasher使用
- 基本的なバリデーション

#### 改善提案

##### A. パスワード強度チェックの強化
```php
// AuthenticationServiceクラスに追加
public function validatePasswordStrength(string $password): array
{
    $errors = [];
    
    // 長さチェック
    if (strlen($password) < 12) {
        $errors[] = 'パスワードは12文字以上で入力してください';
    }
    
    // 複雑性チェック
    if (!preg_match('/[A-Z]/', $password)) {
        $errors[] = 'パスワードには大文字を含めてください';
    }
    
    if (!preg_match('/[a-z]/', $password)) {
        $errors[] = 'パスワードには小文字を含めてください';
    }
    
    if (!preg_match('/[0-9]/', $password)) {
        $errors[] = 'パスワードには数字を含めてください';
    }
    
    if (!preg_match('/[!@#$%^&*(),.?":{}|<>]/', $password)) {
        $errors[] = 'パスワードには特殊文字を含めてください';
    }
    
    // 辞書攻撃対策
    $commonPasswords = [
        'password123', '123456789', 'qwerty123',
        'admin123', 'password1', 'welcome123'
    ];
    
    if (in_array(strtolower($password), $commonPasswords)) {
        $errors[] = '一般的なパスワードは使用できません';
    }
    
    // 連続文字チェック
    if (preg_match('/(.)\1{2,}/', $password)) {
        $errors[] = '同じ文字を3回以上連続して使用できません';
    }
    
    return $errors;
}
```

##### B. パスワード履歴管理
```php
// src/Model/Table/PasswordHistoryTable.php
<?php
namespace App\Model\Table;

use Cake\ORM\Table;
use Authentication\PasswordHasher\DefaultPasswordHasher;

class PasswordHistoryTable extends Table
{
    public function initialize(array $config): void
    {
        parent::initialize($config);
        $this->setTable('password_history');
        $this->setPrimaryKey('id');
    }
    
    public function addPasswordHistory(int $userId, string $userType, string $hashedPassword): void
    {
        $entity = $this->newEntity([
            'user_id' => $userId,
            'user_type' => $userType,
            'password_hash' => $hashedPassword,
            'created' => new \DateTime()
        ]);
        
        $this->save($entity);
        
        // 古い履歴を削除（最新5件のみ保持）
        $this->deleteAll([
            'user_id' => $userId,
            'user_type' => $userType,
            'id NOT IN' => $this->find()
                ->select(['id'])
                ->where(['user_id' => $userId, 'user_type' => $userType])
                ->orderDesc('created')
                ->limit(5)
                ->toArray()
        ]);
    }
    
    public function isPasswordReused(int $userId, string $userType, string $password): bool
    {
        $hasher = new DefaultPasswordHasher();
        $histories = $this->find()
            ->where(['user_id' => $userId, 'user_type' => $userType])
            ->orderDesc('created')
            ->limit(5)
            ->toArray();
            
        foreach ($histories as $history) {
            if ($hasher->check($password, $history->password_hash)) {
                return true;
            }
        }
        
        return false;
    }
}
```

### 3. ログ監視とアラート機能

#### セキュリティイベントの監視
```php
// src/Service/SecurityMonitoringService.php
<?php
namespace App\Service;

use Cake\Log\Log;
use Cake\Mailer\Mailer;

class SecurityMonitoringService
{
    public function logSecurityEvent(string $event, array $data): void
    {
        $logData = [
            'event' => $event,
            'timestamp' => date('Y-m-d H:i:s'),
            'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
            'data' => $data
        ];
        
        Log::write('security', json_encode($logData));
        
        // 重要なイベントの場合、アラートを送信
        if ($this->isHighRiskEvent($event)) {
            $this->sendSecurityAlert($event, $logData);
        }
    }
    
    private function isHighRiskEvent(string $event): bool
    {
        $highRiskEvents = [
            'multiple_failed_logins',
            'suspicious_token_usage',
            'password_brute_force',
            'account_lockout'
        ];
        
        return in_array($event, $highRiskEvents);
    }
    
    private function sendSecurityAlert(string $event, array $data): void
    {
        $mailer = new Mailer('default');
        $mailer
            ->setTo('<EMAIL>')
            ->setSubject("Security Alert: {$event}")
            ->deliver(json_encode($data, JSON_PRETTY_PRINT));
    }
}
```

### 4. 二要素認証（2FA）の実装準備

#### TOTP（Time-based One-Time Password）の基盤
```php
// src/Service/TwoFactorAuthService.php
<?php
namespace App\Service;

use OTPHP\TOTP;
use ParagonIE\ConstantTime\Base32;

class TwoFactorAuthService
{
    public function generateSecret(): string
    {
        return Base32::encodeUpper(random_bytes(32));
    }
    
    public function generateQRCode(string $secret, string $email): string
    {
        $totp = TOTP::create($secret);
        $totp->setLabel($email);
        $totp->setIssuer('CoverMe');
        
        return $totp->getProvisioningUri();
    }
    
    public function verifyCode(string $secret, string $code): bool
    {
        $totp = TOTP::create($secret);
        return $totp->verify($code, null, 30); // 30秒の時間窓
    }
}
```

### 5. セキュリティヘッダーの実装

#### セキュリティミドルウェア
```php
// src/Middleware/SecurityHeadersMiddleware.php
<?php
namespace App\Middleware;

use Psr\Http\Message\ResponseInterface;
use Psr\Http\Message\ServerRequestInterface;
use Psr\Http\Server\MiddlewareInterface;
use Psr\Http\Server\RequestHandlerInterface;

class SecurityHeadersMiddleware implements MiddlewareInterface
{
    public function process(ServerRequestInterface $request, RequestHandlerInterface $handler): ResponseInterface
    {
        $response = $handler->handle($request);
        
        return $response
            ->withHeader('X-Content-Type-Options', 'nosniff')
            ->withHeader('X-Frame-Options', 'DENY')
            ->withHeader('X-XSS-Protection', '1; mode=block')
            ->withHeader('Strict-Transport-Security', 'max-age=31536000; includeSubDomains')
            ->withHeader('Content-Security-Policy', "default-src 'self'")
            ->withHeader('Referrer-Policy', 'strict-origin-when-cross-origin');
    }
}
```

### 6. 実装優先度

1. **高優先度**
   - レート制限の実装
   - パスワード強度チェックの強化
   - セキュリティログの実装

2. **中優先度**
   - トークンローテーション機能
   - パスワード履歴管理
   - セキュリティヘッダーの実装

3. **低優先度**
   - 二要素認証の実装
   - 高度な監視・アラート機能

これらの改善により、認証システムのセキュリティレベルを大幅に向上させることができます。
