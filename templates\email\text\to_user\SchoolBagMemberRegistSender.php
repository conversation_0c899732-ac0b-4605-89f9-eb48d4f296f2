<?php

use App\Kuroko\Entity\AccessToken;
use App\Kuroko\Entity\Member;
use App\Mailer\Sender\ToUser\SchoolBagMemberRegistSender;
use App\View\AppView;
use App\Mailer\FrontLink;

/**
 * @var AppView $this
 * @var SchoolBagMemberRegistSender $sender
 * @var Member $member
 * @var AccessToken $accessToken
 */
?>

<?= $sender->getToName(); ?>(<?= $sender->getToEmail(); ?>)

有効期限：２４時間
<?= $accessToken->getAccessToken() ?>

URL: <?= FrontLink::getFormalRegistLink($accessToken); ?>


<?= $member->getId() ?>