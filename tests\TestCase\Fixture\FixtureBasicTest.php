<?php
declare(strict_types=1);

namespace App\Test\TestCase\Fixture;

use Cake\TestSuite\TestCase;

/**
 * フィクスチャの基本動作テスト
 */
class FixtureBasicTest extends TestCase
{
    /**
     * Test fixtures
     */
    protected $fixtures = [
        'app.GeneralUsers',
        'app.SwbUsers',
        'app.MakerUsers',
        'app.UserProfiles',
        'app.UserSurveys',
        'app.TemporaryRegistrations',
    ];

    /**
     * フィクスチャが正しく読み込まれるかテスト
     */
    public function testFixturesLoaded(): void
    {
        // GeneralUsersフィクスチャのテスト
        $generalUsersTable = $this->getTableLocator()->get('GeneralUsers');
        $count = $generalUsersTable->find()->count();
        $this->assertGreaterThan(0, $count, 'GeneralUsersフィクスチャが読み込まれていません');

        // 特定のレコードが存在するかテスト
        $testUser = $generalUsersTable->find()
            ->where(['email' => '<EMAIL>'])
            ->first();
        $this->assertNotNull($testUser, 'テストユーザーが見つかりません');
        $this->assertEquals('<EMAIL>', $testUser->email);

        // Kurocoユーザーのテスト
        $kurocoUser = $generalUsersTable->find()
            ->where(['email' => '<EMAIL>'])
            ->first();
        $this->assertNotNull($kurocoUser, 'Kurocoユーザーが見つかりません');
        $this->assertNull($kurocoUser->password, 'Kurocoユーザーのパスワードがnullではありません');
    }

    /**
     * SwbUsersフィクスチャのテスト
     */
    public function testSwbUsersFixture(): void
    {
        $swbUsersTable = $this->getTableLocator()->get('SwbUsers');
        $count = $swbUsersTable->find()->count();
        $this->assertGreaterThan(0, $count, 'SwbUsersフィクスチャが読み込まれていません');

        // 管理者ユーザーのテスト
        $admin = $swbUsersTable->find()
            ->where(['email' => '<EMAIL>'])
            ->first();
        $this->assertNotNull($admin, '管理者ユーザーが見つかりません');
        $this->assertEquals(100, $admin->authority_id, '管理者の権限IDが正しくありません');
    }

    /**
     * MakerUsersフィクスチャのテスト
     */
    public function testMakerUsersFixture(): void
    {
        $makerUsersTable = $this->getTableLocator()->get('MakerUsers');
        $count = $makerUsersTable->find()->count();
        $this->assertGreaterThan(0, $count, 'MakerUsersフィクスチャが読み込まれていません');

        // メーカーユーザーのテスト
        $makerUser = $makerUsersTable->find()
            ->where(['email' => '<EMAIL>'])
            ->first();
        $this->assertNotNull($makerUser, 'メーカーユーザーが見つかりません');
        $this->assertEquals(1, $makerUser->maker_id, 'メーカーIDが正しくありません');
    }

    /**
     * UserProfilesフィクスチャのテスト
     */
    public function testUserProfilesFixture(): void
    {
        $userProfilesTable = $this->getTableLocator()->get('UserProfiles');
        $count = $userProfilesTable->find()->count();
        $this->assertGreaterThan(0, $count, 'UserProfilesフィクスチャが読み込まれていません');

        // プロフィールデータのテスト
        $profile = $userProfilesTable->find()
            ->where(['general_user_id' => 1])
            ->first();
        $this->assertNotNull($profile, 'ユーザープロフィールが見つかりません');
        $this->assertEquals('山田', $profile->last_name, '姓が正しくありません');
    }

    /**
     * UserSurveysフィクスチャのテスト
     */
    public function testUserSurveysFixture(): void
    {
        $userSurveysTable = $this->getTableLocator()->get('UserSurveys');
        $count = $userSurveysTable->find()->count();
        $this->assertGreaterThan(0, $count, 'UserSurveysフィクスチャが読み込まれていません');

        // アンケートデータのテスト
        $survey = $userSurveysTable->find()
            ->where(['general_user_id' => 1])
            ->first();
        $this->assertNotNull($survey, 'ユーザーアンケートが見つかりません');
        $this->assertEquals(2025, $survey->year, '年度が正しくありません');
        $this->assertEquals(1, $survey->child_sex, '性別が正しくありません');
    }

    /**
     * TemporaryRegistrationsフィクスチャのテスト
     */
    public function testTemporaryRegistrationsFixture(): void
    {
        $tempRegistrationsTable = $this->getTableLocator()->get('TemporaryRegistrations');
        $count = $tempRegistrationsTable->find()->count();
        $this->assertGreaterThan(0, $count, 'TemporaryRegistrationsフィクスチャが読み込まれていません');

        // 仮登録データのテスト
        $tempRegistration = $tempRegistrationsTable->find()
            ->where(['email' => '<EMAIL>'])
            ->first();
        $this->assertNotNull($tempRegistration, '仮登録データが見つかりません');
        $this->assertEquals('test_token_123456789abcdef', $tempRegistration->verification_token, '認証トークンが正しくありません');
        $this->assertFalse($tempRegistration->is_verified, '認証フラグが正しくありません');
    }

    /**
     * データベース接続のテスト
     */
    public function testDatabaseConnection(): void
    {
        $connection = $this->getTableLocator()->get('GeneralUsers')->getConnection();
        $this->assertTrue($connection->getDriver()->isConnected(), 'データベースに接続できません');
    }
}

