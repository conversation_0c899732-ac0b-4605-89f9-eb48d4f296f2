<script setup lang="ts">
import { ref, reactive, computed } from "vue";
import { useAuthStore } from "~~/src/stores/auth";
import type { SwbMyBridge } from "~~/src/models/bridge/SwbMyBridge";
import { SwbInvoiceAdjustmentForm } from "~/src/models/form/SwbInvoiceAdjustmentForm";
import type { MonthlyInvoiceAdjustment } from "~~/src/models/entry/MonthlyInvoiceAdjustment";
import { SWB_CONFIRM_GROUP_ID } from "~~/src/config";
import { useAdjustmentHistory } from '~/composables/useAdjustmentHistory';
import moment from "moment";

const props = defineProps<{
    bridge: SwbMyBridge;
}>();

const authStore = useAuthStore();
const selectedRecords = ref([]);
const dialog = ref(false);
const isDialogEditing = ref(false);
const searchForm = props.bridge.swb_invoice_adjustment_search_form;
const inputForm = reactive(
    new SwbInvoiceAdjustmentForm({
    billingYearMonth: searchForm.billingYearMonth,
    }),
);
const myProducts = ref(props.bridge.products);

const { formatChanges, getActionTypeLabel } = useAdjustmentHistory();

const getMakerName = (makerId: number): string => {
    const maker = props.bridge.makers.find(
        (maker) => maker.maker_id === makerId,
    );
    return maker ? maker.maker_name : "";
};

const getProductName = (productId: number): string => {
    const product = props.bridge.products.find(
        (product) => product.product_id === productId,
    );
    return product ? product.product_name : "";
};

const changeProduct = (e: number): void => {
    myProducts.value = props.bridge.products.filter((product) => {
        return product.product_data.contents_type === e;
    });
    inputForm.productId = null;
};

const changeAdjustment = (item: MonthlyInvoiceAdjustment): void => {
    inputForm.id = item.id;
    inputForm.makerId = item.maker_id;
    myProducts.value = props.bridge.products.filter((product) => {
        return product.product_data.contents_type === item.maker_id;
    });
    inputForm.productId = item.product_id;
    inputForm.adjustmentUnitPrice = item.adjustment_unit_price;
    inputForm.adjustmentQuantity = item.adjustment_quantity;
    inputForm.adjustmentNote = item.adjustment_note;
    inputForm.status = item.status;
    inputForm.isChanged = false;
};

const isLastMonth = (): boolean => {
    return (
        searchForm.billingYearMonth ===
        searchForm.getLastMonthFirstDayInJST().substring(0, 7)
    );
};

const clearInputForm = (): void => {
    inputForm.id = 0;
    inputForm.makerId = null;
    inputForm.productId = null;
    inputForm.adjustmentUnitPrice = null;
    inputForm.adjustmentQuantity = null;
    inputForm.adjustmentNote = "";
    inputForm.status = null;
};

const isConfirmable = authStore.hasGroup(SWB_CONFIRM_GROUP_ID);

const confirmDelete = (): boolean => {
    return window.confirm('削除してもよろしいですか?');
};

// フォーマット用の関数を追加
const formatDateTime = (dateStr: string): string => {
    return moment(dateStr).format('YYYY-MM-DD HH:mm:ss');
};

const headers: Record<string, string | boolean | number>[] = [
    {
        title: "集計年月",
        align: "start", 
        key: "billing_year_month",
        value: "billing_year_month",
        width: "8%",
        sortable: false,
    },
    {
        title: "メーカー名",
        align: "start",
        key: "maker_id",
        value: "maker_id",
    },
    {
        title: "カタログ",
        align: "start",
        key: "product_id",
        value: "product_id",
    },
    {
        title: "調整金額",
        align: "end",
        key: "total_adjustment_price",
        value: "total_adjustment_price",
        width: "15%",
    },
    {
        title: "備考内容",
        align: "start",
        key: "display_adjustment_note",
        sortable: false,
    },
    {
        title: "変更",
        align: "center",
        key: "action",
        sortable: false,
        width: "8%",
    },
    {
        title: "状態",
        align: "center",
        key: "display_status",
        value: "display_status",
        sortable: false,
        width: "10%",
    }
];

if (isConfirmable) {
    headers.push({
        title: "選択",
        align: "center",
        key: "data-table-select",
        sortable: false,
        width: "3%"
    });
}

headers.push({
    title: "履歴",
    align: "center",
    key: "data-table-expand",
    width: "3%",
    sortable: false,
});

// 履歴の表示用ヘッダー
const historyHeaders = [
    { title: '操作者', key: 'created_by_name', align: 'start' },
    { title: '操作日時', key: 'created', align: 'start' },
    { title: '操作種類', key: 'action_type', align: 'start', sortable: false, },
    { title: '操作内容', key: 'operation_detail', align: 'start', width: '40%', sortable: false, },
];

const getOperationDetail = (history: any): string => {
    switch (history.action_type) {
        case 1:
            return '新規追加';
        case 3:
            return '確定済み';
        case 2:
            return formatChanges(history.changes);
        default:
            return '';
    }
};

</script>

<template>
    <div class="d-flex justify-end mt-3">
        <v-btn
            v-if="isConfirmable"
            class="csv-dl-btn mx-2 py-5"
            :disabled="selectedRecords.length === 0"
            elevation="0"
            density="compact"
            @click="bridge.confirmAdjustments(selectedRecords)"
        >
            確定
        </v-btn>
        <v-btn
            class="csv-dl-btn mx-2 py-5"
            :disabled="!isLastMonth()"
            elevation="0"
            density="compact"
            @click="
                clearInputForm();
                isDialogEditing = false;
                dialog = true;
            "
        >
            行追加
        </v-btn>
    </div>
    <div class="monthly-report-wrapper">
        <!-- ポップアップダイアログ -->
        <v-dialog v-model="dialog" scrollable persistent max-width="1000px">
            <v-card>
                <v-card-title>
                    {{ isDialogEditing ? "調整金額変更" : "調整金額追加" }}
                </v-card-title>
                <v-card-text>
                    <v-row no-gutters>
                        <v-col cols="3">
                            <v-select
                                v-model="inputForm.makerId"
                                class="mr-2"
                                label="メーカー"
                                :disabled="isDialogEditing"
                                :items="bridge.makers"
                                item-title="maker_name"
                                item-value="maker_id"
                                hide-details="true"
                                bg-color="white"
                                @update:model-value="changeProduct"
                            />
                        </v-col>
                        <v-col cols="3">
                            <v-select
                                v-model="inputForm.productId"
                                class="ml-2"
                                label="カタログ"
                                :disabled="isDialogEditing"
                                :items="myProducts"
                                item-title="product_name"
                                item-value="product_id"
                                hide-details="true"
                                bg-color="white"
                            />
                        </v-col>
                    </v-row>
                    <div class="d-flex flex-row mt-4">
                        <v-text-field
                            v-model="inputForm.adjustmentUnitPrice"
                            label="単価"
                            type="number"
                            hide-details="true"
                        />
                        <span class="d-flex align-center mx-2"> × </span>
                        <v-text-field
                            v-model="inputForm.adjustmentQuantity"
                            label="数量"
                            type="number"
                            hide-details="true"
                        />
                        <span class="d-flex align-center mx-2">：</span>
                        <v-text-field
                            v-model="inputForm.adjustmentNote"
                            label="コメント"
                            type="text"
                            hide-details="true"
                            class="w-50"
                        />
                    </div>
                </v-card-text>
                <v-card-actions>
                    <v-btn v-if="inputForm.status === 0" color="red" variant="flat" @click="confirmDelete() && bridge.deleteInvoiceAdjustment(inputForm.id).then(() => dialog = false)"
                            >削除</v-btn
                        >
                    <v-layout>
                        <v-spacer></v-spacer>
                        <v-btn variant="flat" @click="dialog = false"
                            >キャンセル</v-btn
                        >
                        <v-btn
                            class="csv-dl-btn"
                            :disabled="isDialogEditing ? !inputForm.isEditable : !inputForm.isAddable"
                            @click="
                                isDialogEditing
                                    ? bridge.putInvoiceAdjustment(inputForm).then(() => dialog = false)
                                    : bridge.addInvoiceAdjustment(inputForm).then(() => dialog = false)
                            "
                            >{{ isDialogEditing ? "変更" : "追加" }}</v-btn
                        >
                    </v-layout>
                </v-card-actions>
            </v-card>
        </v-dialog>
        <v-data-table
            v-model="selectedRecords"
            :headers="headers"
            :items="bridge.swb_monthly_invoice_adjustments"
            :loading="!bridge.is_adjustment_loaded"
            :item-selectable="(item: MonthlyInvoiceAdjustment) => item.status !== 1 && isLastMonth()"
            show-expand
            return-object
        >
            <template #[`item.billing_year_month`]="{ item }">
                <span class=""> {{ item.billing_year_month }} </span>
            </template>
            <template #[`item.maker_id`]="{ item }">
                <span class="">
                    {{ getMakerName(item.maker_id) }}
                </span>
            </template>
            <template #[`item.product_id`]="{ item }">
                <span class=""> {{ getProductName(item.product_id) }}</span>
            </template>
            <template #[`item.total_adjustment_price`]="{ item }">
                <span class="">
                    {{ item.total_adjustment_price.toLocaleString() }}円</span
                >
            </template>
            <template #[`item.display_adjustment_note`]="{ item }">
                <span class="">
                    {{ item.adjustment_unit_price }} ×
                    {{ item.adjustment_quantity }}：
                    {{ item.adjustment_note }}</span
                >
            </template>
            <template #[`item.action`]="{ item }">
                <v-btn
                    class="mx-2"
                    density="compact"
                    elevation="1"
                    :disabled="!isLastMonth()"
                    @click="
                        dialog = true;
                        isDialogEditing = true;
                        changeAdjustment(item);
                    "
                >
                    変更
                </v-btn>
            </template>
            <template #[`item.display_status`]="{ item }">
                <span class=""> {{ item.display_status }}</span>
            </template>
            <!-- 展開時の履歴表示テンプレート -->
            <template #expanded-row="{ item }">
                <tr>
                    <td :colspan="headers.length">
                        <v-data-table
                            :headers="historyHeaders"
                            :items="item.histories"
                            density="compact"
                            items-per-page="-1"
                            class="history-table"
                        >
                            <template #[`item.action_type`]="{ item: history }">
                                {{ getActionTypeLabel(history.action_type) }}
                            </template>
                            <template #[`item.created`]="{ item: history }">
                                {{ formatDateTime(history.created) }}
                            </template>
                            <template #[`item.operation_detail`]="{ item: history }">
                                {{ getOperationDetail(history) }}
                            </template>
                            <template #bottom></template>
                        </v-data-table>
                    </td>
                </tr>
            </template>
        </v-data-table>
    </div>
</template>

<style scoped>
.monthly-report-wrapper {
    position: inherit;
}
.color-gray {
    color: #575757;
}
.v-data-table th {
    font-weight: bold; /* 太字に設定 */
}

.csv-dl-btn {
    background-color: #4472c4;
    color: white;
    font-weight: bold;
    padding: 15px;
    align-content: center;
    transition: opacity 0.3s ease;
}
.csv-dl-btn:hover {
    opacity: 0.7;
}
.csv-dl-btn:disabled {
    background-color: #b0b0b0 !important;
    color: white !important;
}
/* 履歴テーブルのスタイル */
:deep(.v-data-table--expanded) {
    background-color: #f5f5f5;
}

/* 履歴テーブルのスタイル */
:deep(.history-table) {
    border: 1px solid #ddd !important;
    margin: 16px 0 !important; /* 上下に16pxの余白を追加 */
}

:deep(.history-table .v-table) {
    background-color: transparent !important;
    border-radius: 0;
}

:deep(.history-table .v-table__wrapper) {
    border: none !important;
}

:deep(.history-table th) {
    background-color: #f5f5f5 !important;
    color: #333 !important;
    font-weight: bold !important;
    border-bottom: 1px solid #ddd !important;
}

:deep(.history-table td) {
    white-space: pre-line !important;
    border-bottom: 1px solid #eee !important;
    color: #333;
}
</style>
