<?php

namespace App\Service;

use App\Kuroko\ApiModel\KurokoApiDynamic\ECOrders;
use App\Kuroko\ApiModel\KurokoApiDynamic\Inquiries;
use App\Kuroko\ApiModel\KurokoApiDynamic\Members;
use App\Kuroko\Entity\IKurokoEntity;
use App\Kuroko\Entity\Inquiry;
use App\Kuroko\Entity\Member;
use App\Kuroko\Entity\Order;
use App\Model\Table\RandselOrdersTable;
use Cake\Datasource\EntityInterface;
use Cake\Utility\Hash;
use BadMethodCallException;

class ClientScreenRandselOrdersService implements IRestService
{
    use ServiceTrait;

    protected array $_defaultConfig = [
        "defaultModel" => RandselOrdersTable::class,
    ];

    public function initialize(): void {}

    public function edit(string $id, array $data = []): EntityInterface|null|IKurokoEntity
    {
        $success = (new RandselOrdersService())
            ->setIdentity($this->getIdentity())
            ->screenApproval(Hash::get($data, 'randsel_orders'));

        if (!$success) {
            $this->setErrors([
                '_error' => "approval error",
            ]);
        }
        return null;
    }

    public function index(array $data = []): array|EntityInterface|null|IKurokoEntity
    {
        throw new BadMethodCallException();
    }

    public function add(array $data = []): EntityInterface|null|IKurokoEntity
    {
        throw new BadMethodCallException();
    }

    public function view(string $id, array $data = []): EntityInterface|null|IKurokoEntity
    {
        throw new BadMethodCallException();
    }


    public function delete(string $id, array $data = []): bool
    {
        throw new BadMethodCallException();
    }
}
