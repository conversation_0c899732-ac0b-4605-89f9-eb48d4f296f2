<?php

declare(strict_types=1);

use Migrations\AbstractMigration;

class AlterRandselInvoiceAdjustments extends AbstractMigration
{
    /**
     * Change Method.
     *
     * More information on this method is available here:
     * https://book.cakephp.org/phinx/0/en/migrations.html#the-change-method
     * @return void
     */
    public function change(): void
    {
        $table = $this->table('randsel_invoice_adjustments');
        $table->changeColumn('is_confirmed', 'integer', [
            'comment' => '状態(0: 未確定, 1: 確定, 2: 確定後変更あり)',
            'default' => 0,
            'limit' => null,
            'null' => false,
        ]);
        $table->update();
        $table->renameColumn('is_confirmed', 'status');
        $table->update();
    }
}
