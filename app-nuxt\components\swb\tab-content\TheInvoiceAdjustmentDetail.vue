<script setup lang="ts">
import type { SwbMyBridge } from "~~/src/models/bridge/SwbMyBridge";
import TheSwbInvoiceAdjustmentSearchForm from "~/components/swb/form/TheSwbInvoiceAdjustmentSearchForm.vue";
import TheMonthlyInvoiceAdjustments from "~/components/swb/tab-content/invoice-adjustment-detail/TheMonthlyInvoiceAdjustments.vue";
import TheLoading from "~/components/parts/TheLoading.vue";

defineProps<{
    bridge: SwbMyBridge;
}>();
</script>

<template>
    <div>
        <the-swb-invoice-adjustment-search-form :bridge="bridge" />
        <div v-if="bridge.is_adjustment_loaded">
            <the-monthly-invoice-adjustments :bridge="bridge" />
        </div>
        <v-row v-else justify="center" class="mt-10">
            <the-loading color="swbPrimary">
                しばらくお待ちください。
            </the-loading>
        </v-row>
    </div>
</template>

<style scoped></style>
