<?php

namespace App\Kuroko\Entity;

use App\Utility\Encryptor;
use Cake\Utility\Hash;

class AccessToken implements IKurokoEntity
{
    use KurokoEntityTrait;

    const ACCESS_TOKEN_PATH = "access_token.value";
    const ACCESS_TOKEN_EXPIRES_AT = "access_token.expiresAt";

    public function getAccessToken(): ?string
    {
        return Hash::get($this->getData(), static::ACCESS_TOKEN_PATH);
    }

    public function getExpiresAt(): ?int
    {
        return Hash::get($this->getData(), static::ACCESS_TOKEN_EXPIRES_AT) * 1000;
    }

    public function getJsonData(): array
    {
        return [
            "access_token" => $this->encryptToken(),
            "expires_at" => $this->getExpiresAt(),
        ];
    }

    public function encryptToken(): string
    {
        return Encryptor::encrypt($this->getAccessToken());
    }

    static function decryptToken($string): ?string
    {
        return Encryptor::decrypt($string);
    }

    /**
     * @param array $data
     * @return array
     */
    public function initialize(array $data): array
    {
        return $data;
    }
}
