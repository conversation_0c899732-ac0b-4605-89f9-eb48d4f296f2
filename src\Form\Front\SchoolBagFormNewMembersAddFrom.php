<?php

namespace App\Form\Front;

use App\Enums\EFormFieldType;
use App\Enums\EntityFields\ESchoolBagForm;
use App\Form\ApiForm;
use App\Validation\Validator\SchoolBagFormValidator;
use Cake\Form\Schema;
use Cake\Validation\Validator;

class SchoolBagFormNewMembersAddFrom extends ApiForm
{

    public function _buildSchema(Schema $schema): Schema
    {
        $schema->addField(ESchoolBagForm::EMAIL->value, EFormFieldType::STRING->value);
        $schema->addField(ESchoolBagForm::NAME1->value, EFormFieldType::STRING->value);
        $schema->addField(ESchoolBagForm::LOGIN_PWD->value, EFormFieldType::STRING->value);
        return $schema;
    }

    public function validationDefault(Validator $validator): Validator
    {
        return SchoolBagFormValidator::buildValidator($validator, [
            ESchoolBagForm::EMAIL->value,
            ESchoolBagForm::NAME1->value,
            ESchoolBagForm::LOGIN_PWD->value,
        ]);
    }

    protected function _getAllowFieldsPath(): array
    {
        return [
            ESchoolBagForm::EMAIL->value,
            ESchoolBagForm::NAME1->value,
            ESchoolBagForm::LOGIN_PWD->value,
        ];
    }
}
