<?php

namespace Controller\Front;

use App\Enums\EntityFields\EClientOrderForm;
use App\Test\TestCase\Controller\ApiTestCase;
use Cake\I18n\FrozenTime;
use Cake\Utility\Hash;

class ClientCsvRandselOrdersControllerTest extends ApiTestCase
{

    /**
     * Test edit method
     *
     * @return void
     * @uses \App\Controller\Front\ClientCsvRandselOrdersController::edit()
     * php ./vendor/phpunit/phpunit/phpunit --filter "testEdit" ./tests/TestCase/Controller/Front/ClientCsvRandselOrdersControllerTest.php
     */
    public function testEdit(): void
    {
        $now = FrozenTime::now();
        $this->setFrontAuthorized(true);
        $this->put('/front/client-csv-randsel-orders/' . $now->format("YmdHis") . '.json', [
            'randsel_orders' => [
                [
                    'id' => 1,
                    'status' => 1,
                ],
                [
                    'id' => 2,
                    'status' => 2,
                ],
            ],
        ]);
        $response = $this->bodyToArray($this->_response->getBody());
        debug($response);
        $this->assertResponseOk(print_r(Hash::flatten($response), true));
    }
}
