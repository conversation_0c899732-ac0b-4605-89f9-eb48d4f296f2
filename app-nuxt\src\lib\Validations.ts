/**
 * @param {*} v
 * @returns {boolean}
 */
export const empty = (v: TFormValue): TValidationSuccess =>
    v === null || v === undefined || !v.toString().length;

export class Validations {
    /**
     * @type {number}
     */
    static MAX_NUMBER: number = 2147483647;

    /**
     * @type {number}
     */
    static TEXT_MAX_LENGTH: number = 10000;

    /**
     * @type {RegExp}
     */
    static ZENKAKU_REGEXP: RegExp = /^[^\u0020-\u007E]+$/;

    /**
     * @type {RegExp}
     */
    static PASSWORD_REGEXP: RegExp =
        /^(?=.*[a-zA-Z])(?=.*[0-9])[a-zA-Z0-9!@#$%^&*()_+\-=[\]{};':"\\|,.<>/?]{8,30}$/;

    /**
     * @returns {function(*): boolean | string}
     */
    static notEmptyString(item_name: string): TValidationFunction {
        return (v) => !empty(v) || `${item_name}：必須です。`;
    }

    /**
     * @param max
     * @returns {function(*): boolean|string}
     */
    static maxLength(max: number, item_name: string): TValidationFunction {
        return (v) =>
            empty(v) ||
            max >= v.toString().length ||
            `${item_name}：${max}文字以内です。`;
    }

    /**
     * @param min
     * @returns {function(*): boolean|string}
     */
    static minLength(min: number): TValidationFunction {
        return (v) =>
            empty(v) || min <= v.toString().length || `${min}文字以上です。`;
    }

    /**
     * @returns {function(*=): boolean|string}
     */
    static integer(): TValidationFunction {
        return (v) =>
            empty(v) ||
            /^-?[0-9]+$/.test(String(v)) ||
            `整数で入力してください。`;
    }

    /**
     * @returns {function(*=): boolean|string}
     */
    static float(): TValidationFunction {
        return (v) =>
            empty(v) ||
            /^[-]?([1-9]\d*|0)(\.\d+)?$/.test(String(v)) ||
            `数値（小数含）で入力してください。`;
    }

    /**
     * @param {number} max
     * @return {function(*=): boolean|string}
     */
    static max(max: number): TValidationFunction {
        return (v) =>
            empty(v) || max >= Number(v) || `${max}以下で入力してください。`;
    }

    /**
     * @param {number} min
     * @return {function(*=): boolean|string}
     */
    static min(min: number): TValidationFunction {
        return (v) =>
            empty(v) || Number(v) >= min || `${min}以上で入力してください。`;
    }

    /**
     * @returns {function(*=): boolean|string}
     */
    static email(item_name: string): TValidationFunction {
        return (v) =>
            empty(v) ||
            /^([a-zA-Z0-9\-_.]+([a-zA-Z0-9\-_./+]+)?@[a-zA-Z0-9\-_.]+\.[a-zA-Z0-9\-_.]+)?$/.test(
                String(v),
            ) ||
            `${item_name}：形式が正しくありません。`;
    }

    /**
     * @param {string} value
     * @returns {function(*): boolean|string}
     */
    static matchedConfirmation(
        value: string,
        target_item_name: string,
        item_name: string,
    ): TValidationFunction {
        return (v) =>
            empty(v) ||
            String(v) === value ||
            `${item_name}：${target_item_name}と一致しません。`;
    }

    /**
     * @return {function(*=): boolean|string}
     */
    static kana(): TValidationFunction {
        return (v) =>
            empty(v) ||
            /^[\u3000\u30a0-\u30ff]+$/.test(String(v)) ||
            `全角カナで入力してください。`;
    }

    /**
     * @param {string} pattern
     * @param {string} flags
     * @return {function(*=): boolean|string}
     */
    static regex(pattern: string, flags = undefined): TValidationFunction {
        const regex = new RegExp(pattern, flags);
        return (v) =>
            empty(v) || !!regex.exec(String(v)) || `形式が正しくありません。`;
    }

    /**
     * @return {function(*=): boolean|string}
     */
    static doubleByteSpace(): TValidationFunction {
        return (v) =>
            empty(v) ||
            !/\u3000/.test(String(v)) ||
            "スペースは半角で入力してください。";
    }

    static maxLines(max: number): TValidationFunction {
        return (v) =>
            empty(v) ||
            String(v).trim().split("\n").length <= max ||
            `${max}行以下で入力してください。`;
    }

    /**
     * @return {function(*=): boolean|string}
     */
    static zipCode(item_name: string): TValidationFunction {
        return (v) =>
            empty(v) ||
            /^[0-9]{7}$/.test(String(v)) ||
            `${item_name}：「1234567」の形式で入力してください。`;
    }

    /**
     * @return {function(*=): boolean|string}
     */
    static tel1(item_name: string): TValidationFunction {
        return (v) =>
            empty(v) ||
            /^([0-9]{2,5})$/.test(String(v)) ||
            `${item_name}：形式が正しくありません。`;
    }

    /**
     * @return {function(*=): boolean|string}
     */
    static tel2(item_name: string): TValidationFunction {
        return (v) =>
            empty(v) ||
            /^([0-9]{2,4})$/.test(String(v)) ||
            `${item_name}：形式が正しくありません。`;
    }

    /**
     * @return {function(*=): boolean|string}
     */
    static password(item_name: string): TValidationFunction {
        return (v) =>
            empty(v) ||
            this.PASSWORD_REGEXP.test(String(v)) ||
            `${item_name}：半角英数字をそれぞれ1文字以上含む、8～30文字で設定してください。`;
    }

    /**
     * @return {function(*=): boolean|string}
     */
    static password2(item_name: string): TValidationFunction {
        return (v) =>
            empty(v) ||
            this.PASSWORD_REGEXP.test(String(v)) ||
            `${item_name}：半角英数字をそれぞれ1文字以上含む、8～30文字で入力してください。`;
    }

    /**
     * @return {function(*=): boolean|string}
     */
    static zenkaku(item_name: string): TValidationFunction {
        return (v) =>
            empty(v) ||
            this.ZENKAKU_REGEXP.test(String(v)) ||
            `${item_name}：全角文字で記入してください。`;
    }
}
