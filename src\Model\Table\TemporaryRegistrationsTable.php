<?php
declare(strict_types=1);

namespace App\Model\Table;

use App\Utility\Encryptor;
use Cake\I18n\FrozenTime;
use Cake\ORM\Query;
use Cake\ORM\RulesChecker;
use Cake\ORM\Table;
use Cake\Validation\Validator;
use Cake\Utility\Security;

class TemporaryRegistrationsTable extends Table
{
    public function initialize(array $config): void
    {
        parent::initialize($config);

        $this->setTable('temporary_registrations');
        $this->setDisplayField('email');
        $this->setPrimaryKey('id');
    }

    public function validationDefault(Validator $validator): Validator
    {
        $validator
            ->email('email', false, __('有効なメールアドレスを入力してください'))
            ->requirePresence('email', 'create', __('メールアドレスは必須です'))
            ->notEmptyString('email', __('メールアドレスは必須です'));
            
        $validator
            ->scalar('verification_token')
            ->maxLength('verification_token', 255)
            ->requirePresence('verification_token', 'create')
            ->notEmptyString('verification_token')
            ->add('verification_token', 'unique', ['rule' => 'validateUnique', 'provider' => 'table']);
            
        $validator
            ->boolean('is_verified')
            ->notEmptyString('is_verified');
            
        $validator
            ->dateTime('expires')
            ->requirePresence('expires', 'create')
            ->notEmptyDateTime('expires');

        return $validator;
    }

    public function buildRules(RulesChecker $rules): RulesChecker
    {
        $rules->add($rules->isUnique(['verification_token']));
        return $rules;
    }
    
    // 有効期限内の仮登録を取得するファインダー
    public function findValid(Query $query, array $options)
    {
        return $query->where([
            'expires >' => FrozenTime::now(),
            'is_verified' => false
        ]);
    }
    
    // 期限切れデータをクリアするメソッド（バッチ処理用）
    public function clearExpiredData()
    {
        $now = FrozenTime::now();
        $expired = $this->find()
            ->where(['expires <' => $now, 'email IS NOT' => null])
            ->all();
            
        foreach ($expired as $record) {
            $record->email = null;
            $record->profile_data = null;
            $this->save($record);
        }
        
        return $expired->count();
    }
    
    // プロファイルデータの暗号化
    protected function _setProfileData($value)
    {
        if (is_array($value)) {
            $value = json_encode($value);
        }
        return Encryptor::encrypt($value);
    }
    
    // プロファイルデータの復号化
    protected function _getProfileData($value)
    {
        $decrypted = Encryptor::decrypt($value);
        return json_decode($decrypted, true);
    }

    /**
     * 認証トークンを生成
     */
    public function generateVerificationToken(): string
    {
        return bin2hex(Security::randomBytes(32));
    }

    /**
     * 仮登録を作成
     */
    public function createTemporaryRegistration(array $data, int $expirationHours = 24)
    {
        $tempRegistration = $this->newEntity([
            'email' => $data['email'],
            'mask_password' => $data['password'] ?? null, // 暗号化される
            'mask_email' => $data['email'], // 暗号化される
            'profile_data' => $data['profile_data'], // 暗号化される
            'survey_data' => $data['survey_data'], // 暗号化される
            'product_ids' => $data['product_ids'],
            'verification_token' => $this->generateVerificationToken(),
            'is_verified' => false,
            'expires' => FrozenTime::now()->addHours($expirationHours)
        ]);

        return $this->save($tempRegistration);
    }

    /**
     * 認証トークンで仮登録を検索
     */
    public function findByVerificationToken(string $token)
    {
        return $this->find()
            ->where([
                'verification_token' => $token,
                'is_verified' => false,
                'expires >' => FrozenTime::now()
            ])
            ->first();
    }

    /**
     * メールアドレスで有効な仮登録を検索
     */
    public function findValidByEmail(string $email)
    {
        return $this->find()
            ->where([
                'email' => $email,
                'is_verified' => false,
                'expires >' => FrozenTime::now()
            ])
            ->orderDesc('created')
            ->first();
    }

    /**
     * 期限切れの仮登録データをクリーンアップ
     */
    public function cleanupExpiredRegistrations(): int
    {
        // 期限切れデータの個人情報をクリア
        return $this->updateAll(
            [
                'email' => null,
                'mask_password' => null,
                'profile_data' => null,
                'survey_data' => null
            ],
            [
                'expires <' => FrozenTime::now(),
                'email IS NOT' => null
            ]
        );
    }

    /**
     * 同一メールアドレスの古い仮登録を無効化
     */
    public function invalidateOldRegistrations(string $email): int
    {
        return $this->updateAll(
            ['is_verified' => true], // 無効化フラグとして使用
            [
                'email' => $email,
                'is_verified' => false
            ]
        );
    }
}