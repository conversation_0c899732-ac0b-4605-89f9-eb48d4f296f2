<?php

namespace App\Mailer\Sender\ToUser;

use App\Mailer\Sender\IToUserSender;
use App\Mailer\Sender\SenderTrait;
use App\Mailer\Sender\ToUserSenderTrait;

class PasswordResetSender implements IToUserSender
{
    use SenderTrait;
    use ToUserSenderTrait;

    public function getSubject(): string
    {
        return "【カバーミー】パスワード再設定のお願い";
    }

    public function getTemplate(): string
    {
        return "to_user/PasswordResetSender";
    }
}
