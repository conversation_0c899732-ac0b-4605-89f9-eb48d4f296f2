#!/usr/bin/bash

set -eu

if grep -q "APPLICATION_ENV" /etc/environment; then
    MY_ENVIRONMENT=$(grep "APPLICATION_ENV" /etc/environment | cut -d '=' -f2)
else
    echo "APPLICATION_ENV is not set in /etc/environment"
    exit 1
fi

BIN_DIR=$(cd "$(dirname "$0")";pwd)
PROJECT_DIR=$(dirname "${BIN_DIR}")
APP_NUXT_DIR="${PROJECT_DIR}/app-nuxt"

cd "${PROJECT_DIR}"

git fetch -p && git pull

php composer.phar install --no-interaction

cd "${APP_NUXT_DIR}"

npm i

npm run "${MY_ENVIRONMENT}":generate
