<?php

namespace App\Kuroko\Http\Client;

use App\Enums\EHttpStatusCode;
use App\Kuroko\Http\Client\Adapter\KurokoMockAdapter;
use Cake\Http\Client\Message;
use Cake\Http\Client\Response;

class KurokoApiMockClient extends KurokoApiClient
{
    public function __construct(array $config = [])
    {
        //        debug("もっくがうごいています");
        parent::__construct($config);
        if (!static::$_mockAdapter) {
            static::$_mockAdapter = new KurokoMockAdapter();
            $this->_buildMock();
        }
    }

    /**
     * モックデータ
     * @return void
     */
    protected function _buildMock(): void
    {
        $buildMockUrl = fn(string $endpoint, ?array $query = [], ?string $id = null): string => $this->buildUrl($this->getConfig($endpoint), $query, $this->getConfig()) . ($id ? "/" . $id : "");

        /**
         * 商品一覧
         */
        static::addMockResponse(
            Message::METHOD_GET,
            $buildMockUrl("endPoint.privateStatic.products", [
                "cnt" => 1000000,
            ]),
            (new Response(
                [],
                Mock\MockData::privateStaticProductList()
            ))->withStatus(EHttpStatusCode::OK->value)
        );

        /**
         * 商品一覧（非公開も含む）
         */
        static::addMockResponse(
            Message::METHOD_GET,
            $buildMockUrl("endPoint.privateStatic.productAllList", [
                "cnt" => 1000000,
            ]),
            (new Response(
                [],
                Mock\MockData::privateStaticProductList()
            ))->withStatus(EHttpStatusCode::OK->value)
        );

        /**
         * メーカー一覧
         */
        static::addMockResponse(
            Message::METHOD_GET,
            $buildMockUrl("endPoint.privateStatic.makers", [
                "cnt" => 1000000,
            ]),
            (new Response(
                [],
                Mock\MockData::privateStaticMakerList()
            ))->withStatus(EHttpStatusCode::OK->value)
        );

        /**
         * フォーム送信
         */
        static::addMockResponse(
            Message::METHOD_POST,
            $buildMockUrl("endPoint.static.sendInquiry"),
            (new Response(
                [],
                Mock\MockData::staticSendInquiry()
            ))->withStatus(EHttpStatusCode::OK->value)
        );

        /**
         * フォーム一覧
         */
        static::addMockResponse(
            Message::METHOD_GET,
            $buildMockUrl("endPoint.dynamic.inquiries"),
            (new Response(
                [],
                Mock\MockData::dynamicInquiriesList()
            ))->withStatus(EHttpStatusCode::OK->value)
        );

        static::addMockResponse(
            Message::METHOD_POST,
            $buildMockUrl("endPoint.dynamic.memberRegist"),
            (new Response(
                [],
                Mock\MockData::dynamicMemberRegist()
            ))->withStatus(EHttpStatusCode::OK->value)
        );

        static::addMockResponse(
            Message::METHOD_POST,
            $buildMockUrl("endPoint.dynamic.memberRegistValidateOnly"),
            (new Response(
                [],
                Mock\MockData::dynamicMemberRegistValidateOnly()
            ))->withStatus(EHttpStatusCode::OK->value)
        );

        static::addMockResponse(
            Message::METHOD_POST,
            $buildMockUrl("endPoint.dynamic.updateStatusAndGetFormInfo"),
            (new Response(
                [],
                Mock\MockData::dynamicUpdateStatusAndGetFormInfo()
            ))->withStatus(EHttpStatusCode::OK->value)
        );

        static::addMockResponse(
            Message::METHOD_POST,
            $buildMockUrl("endPoint.dynamic.eCOrderPurchase"),
            (new Response(
                [],
                Mock\MockData::dynamicECOrderPurchase()
            ))->withStatus(EHttpStatusCode::OK->value)
        );

        static::addMockResponse(
            Message::METHOD_GET,
            $buildMockUrl("endPoint.dynamic.eCOrderList", [
                "cnt" => 1000000,
            ]),
            (new Response(
                [],
                Mock\MockData::dynamicECOrderList()
            ))->withStatus(EHttpStatusCode::OK->value)
        );

        static::addMockResponse(
            Message::METHOD_GET,
            $buildMockUrl("endPoint.dynamic.eCOrderAllList", [
                "topics_category_id" => 17,
                "cnt" => 1000000,
            ]),
            // $ECOrderAllListUrl,
            (new Response(
                [],
                Mock\MockData::dynamicECOrderAllList()
            ))->withStatus(EHttpStatusCode::OK->value)
        );

        static::addMockResponse(
            Message::METHOD_GET,
            $buildMockUrl("endPoint.dynamic.eCOrderAllList", [
                "topics_category_id" => 17,
                "cnt" => 1000000,
                "inst_ymdhi[from]" => "2024-09-01",
                "inst_ymdhi[to]" => "2024-10-23",
                "product_id[0]" => 41206,
            ]),
            // $ECOrderAllListUrl,
            (new Response(
                [],
                Mock\MockData::dynamicECOrderAllList()
            ))->withStatus(EHttpStatusCode::OK->value)
        );

        static::addMockResponse(
            Message::METHOD_GET,
            $buildMockUrl("endPoint.dynamic.memberMe"),
            (new Response(
                [],
                Mock\MockData::dynamicMemberMe()
            ))->withStatus(EHttpStatusCode::OK->value)
        );

        static::addMockResponse(
            Message::METHOD_POST,
            $buildMockUrl("endPoint.dynamic.memberUpdate"),
            (new Response(
                [],
                Mock\MockData::dynamicMemberUpdate()
            ))->withStatus(EHttpStatusCode::OK->value)
        );

        /**
         * 会員一覧
         */
        static::addMockResponse(
            Message::METHOD_GET,
            $buildMockUrl("endPoint.dynamic.memberList"),
            (new Response(
                [],
                Mock\MockData::dynamicMemberList()
            ))->withStatus(EHttpStatusCode::OK->value)
        );

        /**
         * SWBメンバー一覧
         */
        static::addMockResponse(
            Message::METHOD_GET,
            $buildMockUrl("endPoint.dynamic.memberSwbList"),
            (new Response(
                [],
                Mock\MockData::dynamicMemberSwbList()
            ))->withStatus(EHttpStatusCode::OK->value)
        );
        static::addMockResponse(
            Message::METHOD_GET,
            $buildMockUrl("endPoint.dynamic.memberSwbList",[
                "member_id" => [13],
            ]),
            (new Response(
                [],
                Mock\MockData::dynamicMemberSwbList()
            ))->withStatus(EHttpStatusCode::OK->value)
        );

        static::addMockResponse(
            Message::METHOD_POST,
            $buildMockUrl("endPoint.dynamic.login"),
            (new Response(
                [],
                Mock\MockData::dynamicLoginLogin()
            ))->withStatus(EHttpStatusCode::OK->value)
        );

        static::addMockResponse(
            Message::METHOD_POST,
            $buildMockUrl("endPoint.dynamic.loginToken"),
            (new Response(
                [],
                Mock\MockData::dynamicLoginToken()
            ))->withStatus(EHttpStatusCode::OK->value)
        );

        static::addMockResponse(
            Message::METHOD_POST,
            $buildMockUrl("endPoint.dynamic.logout"),
            (new Response(
                [],
                Mock\MockData::dynamicLoginLogout()
            ))->withStatus(EHttpStatusCode::OK->value)
        );

        static::addMockResponse(
            Message::METHOD_POST,
            $buildMockUrl("endPoint.dynamic.loginReminder"),
            (new Response(
                [],
                Mock\MockData::dynamicLoginReminder()
            ))->withStatus(EHttpStatusCode::OK->value)
        );

        static::addMockResponse(
            Message::METHOD_POST,
            $buildMockUrl("endPoint.dynamic.loginResetPassword"),
            (new Response(
                [],
                Mock\MockData::dynamicLoginResetPassword()
            ))->withStatus(EHttpStatusCode::OK->value)
        );

        static::addMockResponse(
            Message::METHOD_GET,
            $buildMockUrl("endPoint.dynamic.eCOrderAllList", [
                "pageID" => 1,
            ]),
            // $ECOrderAllListUrl,
            (new Response(
                [],
                Mock\MockData::dynamicECOrderAllList()
            ))->withStatus(EHttpStatusCode::OK->value)
        );

        static::addMockResponse(
            Message::METHOD_GET,
            $buildMockUrl("endPoint.dynamic.eCOrderAllList", [
                "ec_order_id" => 1,
                "pageID" => 1,
            ]),
            // $ECOrderAllListUrl,
            (new Response(
                [],
                Mock\MockData::dynamicECOrderAllListByOrderId()
            ))->withStatus(EHttpStatusCode::OK->value)
        );
    }
}
