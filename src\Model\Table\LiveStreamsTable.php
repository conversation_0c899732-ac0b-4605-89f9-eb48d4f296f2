<?php
declare(strict_types=1);

namespace App\Model\Table;

use Cake\I18n\FrozenTime;
use Cake\ORM\Query;
use Cake\ORM\RulesChecker;
use Cake\ORM\Table;
use Cake\Validation\Validator;

/**
 * ライブ配信テーブル
 */
class LiveStreamsTable extends Table
{
    public function initialize(array $config): void
    {
        parent::initialize($config);

        $this->setTable('live_streams');
        $this->setDisplayField('title');
        $this->setPrimaryKey('id');

        // アソシエーション
        $this->hasMany('LiveStreamMakers', [
            'foreignKey' => 'live_stream_id',
            'dependent' => true,
        ]);

        // 論理削除ビヘイビア
        $this->addBehavior('Muffin/Trash.Trash', [
            'field' => 'deleted',
            'events' => ['Model.beforeFind', 'Model.beforeDelete']
        ]);
    }

    public function validationDefault(Validator $validator): Validator
    {
        $validator
            ->scalar('title')
            ->maxLength('title', 255)
            ->requirePresence('title', 'create', __('配信タイトルは必須です'))
            ->notEmptyString('title', __('配信タイトルは必須です'));

        $validator
            ->scalar('description')
            ->allowEmptyString('description');

        $validator
            ->dateTime('start_datetime')
            ->requirePresence('start_datetime', 'create')
            ->notEmptyDateTime('start_datetime');

        $validator
            ->dateTime('end_datetime')
            ->requirePresence('end_datetime', 'create')
            ->notEmptyDateTime('end_datetime');

        $validator
            ->scalar('platform')
            ->maxLength('platform', 100)
            ->allowEmptyString('platform');

        $validator
            ->scalar('stream_url')
            ->maxLength('stream_url', 500)
            ->allowEmptyString('stream_url')
            ->url('stream_url', __('有効なURLを入力してください'));

        return $validator;
    }

    public function buildRules(RulesChecker $rules): RulesChecker
    {
        // 終了日時が開始日時より後であることをチェック
        $rules->add(function ($entity, $options) {
            if ($entity->start_datetime && $entity->end_datetime) {
                return $entity->end_datetime > $entity->start_datetime;
            }
            return true;
        }, 'validDateTimeRange', [
            'errorField' => 'end_datetime',
            'message' => __('終了日時は開始日時より後の日時を入力してください')
        ]);

        return $rules;
    }

    /**
     * アクティブなライブ配信を取得するファインダー
     */
    public function findActive(Query $query, array $options): Query
    {
        return $query->where(['deleted IS' => null]);
    }

    /**
     * 配信予定のライブ配信を取得するファインダー
     */
    public function findUpcoming(Query $query, array $options): Query
    {
        $now = FrozenTime::now();
        return $query->where([
            'start_datetime >' => $now,
            'deleted IS' => null
        ]);
    }

    /**
     * 現在配信中のライブ配信を取得するファインダー
     */
    public function findCurrentlyLive(Query $query, array $options): Query
    {
        $now = FrozenTime::now();
        return $query->where([
            'start_datetime <=' => $now,
            'end_datetime >=' => $now,
            'deleted IS' => null
        ]);
    }

    /**
     * 終了したライブ配信を取得するファインダー
     */
    public function findFinished(Query $query, array $options): Query
    {
        $now = FrozenTime::now();
        return $query->where([
            'end_datetime <' => $now,
            'deleted IS' => null
        ]);
    }

    /**
     * プラットフォーム別にライブ配信を取得するファインダー
     */
    public function findByPlatform(Query $query, array $options): Query
    {
        $platform = $options['platform'] ?? null;
        if (!empty($platform)) {
            $query->where(['platform' => $platform]);
        }
        return $query;
    }

    /**
     * 日付順でソートするファインダー
     */
    public function findSorted(Query $query, array $options): Query
    {
        return $query->order(['start_datetime' => 'ASC']);
    }

    /**
     * タイトルで検索するファインダー
     */
    public function findByTitle(Query $query, array $options): Query
    {
        $title = $options['title'] ?? null;
        if (!empty($title)) {
            $query->where(['title LIKE' => '%' . $title . '%']);
        }
        return $query;
    }

    /**
     * 指定期間内のライブ配信一覧を取得
     */
    public function getLiveStreamsByPeriod(FrozenTime $startDate, FrozenTime $endDate): Query
    {
        return $this->find('active')
            ->where([
                'OR' => [
                    [
                        'start_datetime <=' => $endDate,
                        'end_datetime >=' => $startDate
                    ]
                ]
            ])
            ->order(['start_datetime' => 'ASC']);
    }

    /**
     * 今後配信予定のライブ配信一覧を取得
     */
    public function getUpcomingLiveStreams(int $limit = 10): Query
    {
        return $this->find('upcoming')
            ->find('sorted')
            ->limit($limit);
    }

    /**
     * 現在配信中のライブ配信一覧を取得
     */
    public function getCurrentLiveStreams(): Query
    {
        return $this->find('currentlyLive')
            ->find('sorted');
    }

    /**
     * 指定プラットフォームのライブ配信一覧を取得
     */
    public function getLiveStreamsByPlatform(string $platform): Query
    {
        return $this->find('active')
            ->where(['platform' => $platform])
            ->order(['start_datetime' => 'DESC']);
    }
}
