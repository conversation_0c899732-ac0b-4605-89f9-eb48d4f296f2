import { getList<PERSON><PERSON>lByValue, PREFECTURE_LIST } from "~~/src/list";

export class Member {
    private readonly _member_id: number;
    private readonly _name1: string;
    private readonly _name2: string;
    private readonly _name1_hurigana: string;
    private readonly _name2_hurigana: string;
    private readonly _email: string;
    private readonly _zip_code: string;
    private readonly _tdfk_cd: string;
    private readonly _address1: string;
    private readonly _address2: string;
    private readonly _address3: string;
    private readonly _tel: string;
    private readonly _email_send_ng_flg: boolean;
    private readonly _maker_id: number | null;

    constructor(member: TMember) {
        this._member_id = member.member_id;
        this._name1 = member.name1;
        this._name2 = member.name2;
        this._name1_hurigana = member.name1_hurigana;
        this._name2_hurigana = member.name2_hurigana;
        this._email = member.email;
        this._zip_code = member.zip_code;
        this._tdfk_cd = member.tdfk_cd;
        this._address1 = member.address1;
        this._address2 = member.address2;
        this._address3 = member.address3;
        this._tel = member.tel;
        this._email_send_ng_flg = member.email_send_ng_flg;
        this._maker_id = member.maker_id;
    }

    get member_id(): number {
        return this._member_id;
    }

    get name1(): string {
        return this._name1;
    }

    get name2(): string {
        return this._name2;
    }

    get name1_hurigana(): string {
        return this._name1_hurigana;
    }

    get name2_hurigana(): string {
        return this._name2_hurigana;
    }

    get email(): string {
        return this._email;
    }

    get zip_code(): string {
        return this._zip_code;
    }

    get tdfk_cd(): string {
        return this._tdfk_cd;
    }

    get display_tdfk_cd(): string {
        return getListLabelByValue(this.tdfk_cd, PREFECTURE_LIST);
    }

    get address1(): string {
        return this._address1;
    }

    get address2(): string {
        return this._address2;
    }

    get address3(): string {
        return this._address3;
    }

    get tel(): string {
        return this._tel;
    }

    get email_send_ng_flg(): boolean {
        return this._email_send_ng_flg;
    }

    get maker_id(): number | null {
        return this._maker_id;
    }

    get data(): TMember {
        return {
            member_id: this.member_id,
            name1: this.name1,
            name2: this.name2,
            name1_hurigana: this.name1_hurigana,
            name2_hurigana: this.name2_hurigana,
            email: this.email,
            zip_code: this.zip_code,
            tdfk_cd: this.tdfk_cd,
            address1: this.address1,
            address2: this.address2,
            address3: this.address3,
            tel: this.tel,
            email_send_ng_flg: this.email_send_ng_flg,
            maker_id: this.maker_id,
        };
    }

    static create(member: TMember): Member {
        return new Member(member);
    }
}
