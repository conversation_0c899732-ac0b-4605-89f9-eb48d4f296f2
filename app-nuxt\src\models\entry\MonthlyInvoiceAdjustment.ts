import {
    RANDSEL_INVOICE_ADJUSTMENT_STATUS_LIST,
    getListLabelByValue,
} from "~~/src/list";

export class MonthlyInvoiceAdjustment {
    private _id: number;
    private _maker_id: number;
    private _product_id: number;
    private _billing_year_month: string;
    private _adjustment_unit_price: number;
    private _adjustment_quantity: number;
    private _adjustment_note: string;
    private _status: number;
    private _histories: TAdjustmentHistory[]; // 履歴配列を追加

    constructor(data: TMonthlyInvoiceAdjustment) {
        this._id = Number(data.id);
        this._maker_id = Number(data.maker_id);
        this._product_id = Number(data.product_id);
        this._billing_year_month = data.billing_year_month;
        this._adjustment_unit_price = Number(data.adjustment_unit_price);
        this._adjustment_quantity = Number(data.adjustment_quantity);
        this._adjustment_note = data.adjustment_note;
        this._status = Number(data.status);
        this._histories = data.histories || []; // 履歴データの初期化
    }

    static creates(
        MonthlyInvoiceAdjustments: TMonthlyInvoiceAdjustment[],
    ): MonthlyInvoiceAdjustment[] {
        return MonthlyInvoiceAdjustments.map(
            (adjustment: TMonthlyInvoiceAdjustment) =>
                MonthlyInvoiceAdjustment.create(adjustment),
        );
    }

    static create(
        adjustment: TMonthlyInvoiceAdjustment,
    ): MonthlyInvoiceAdjustment {
        return new MonthlyInvoiceAdjustment(adjustment);
    }

    get data(): TMonthlyInvoiceAdjustment {
        return {
            id: this._id,
            maker_id: this._maker_id,
            product_id: this._product_id,
            billing_year_month: this._billing_year_month,
            adjustment_unit_price: this._adjustment_unit_price,
            adjustment_quantity: this._adjustment_quantity,
            adjustment_note: this._adjustment_note,
            status: this._status,
            histories: this._histories,
        };
    }

    get id(): number {
        return this._id;
    }

    get maker_id(): number {
        return this._maker_id;
    }

    get product_id(): number {
        return this._product_id;
    }

    get billing_year_month(): string {
        return this._billing_year_month;
    }

    get adjustment_unit_price(): number {
        return this._adjustment_unit_price;
    }

    get adjustment_quantity(): number {
        return this._adjustment_quantity;
    }

    get adjustment_note(): string {
        return this._adjustment_note;
    }

    get status(): number {
        return this._status;
    }

    get display_status(): string {
        return getListLabelByValue(
            this._status,
            RANDSEL_INVOICE_ADJUSTMENT_STATUS_LIST,
        );
    }

    get total_adjustment_price(): number {
        return this._adjustment_unit_price * this._adjustment_quantity;
    }

    // 履歴データのゲッター
    get histories(): TAdjustmentHistory[] {
        return this._histories;
    }
}
