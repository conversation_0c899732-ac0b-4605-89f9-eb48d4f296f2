<?php

namespace App\Test\TestCase\Service\Password;

use App\Service\Password\PasswordManagementService;
use App\Kuroko\Entity\Member;
use App\Kuroko\Entity\AccessToken;
use App\Model\Entity\UserToken;
use App\Model\Entity\SwbUserToken;
use App\Model\Entity\MakerUserToken;
use App\Test\TestCase\Controller\ApiTestCase;
use Cake\ORM\TableRegistry;
use Cake\I18n\FrozenTime;

/**
 * PasswordManagementService Test Case
 */
class PasswordManagementServiceTest extends ApiTestCase
{
    /**
     * Test fixtures
     */
    protected $fixtures = [
        'app.GeneralUsers',
        'app.SwbUsers',
        'app.MakerUsers',
        'app.UserTokens',
        'app.SwbUserTokens',
        'app.MakerUserTokens',
        'app.UserProfiles',
    ];

    /**
     * Test subject
     */
    protected $PasswordManagementService;

    /**
     * setUp method
     */
    public function setUp(): void
    {
        parent::setUp();
        $this->PasswordManagementService = new PasswordManagementService();
    }

    /**
     * tearDown method
     */
    public function tearDown(): void
    {
        unset($this->PasswordManagementService);
        parent::tearDown();
    }

    /**
     * 一般ユーザーのパスワードリマインダーテスト
     */
    public function testSendPasswordReminderGeneralUser(): void
    {
        // テストユーザーを作成
        // $generalUsersTable = TableRegistry::getTableLocator()->get('GeneralUsers');
        // $user = $generalUsersTable->newEntity([
        //     'email' => '<EMAIL>',
        //     'password' => 'password123', // 新システムユーザー
        // ]);
        // $generalUsersTable->save($user);

        // パスワードリマインダー送信
        $result = $this->PasswordManagementService->sendPasswordReminder(
            '<EMAIL>',
            PasswordManagementService::USER_TYPE_GENERAL
        );

        $this->assertTrue($result);

        // トークンが生成されているかチェック
        $userTokensTable = TableRegistry::getTableLocator()->get('UserTokens');

        /** @var \App\Model\Entity\UserToken $token */
        $token = $userTokensTable->find()
            ->where([
                'general_user_id' => 1,
                'type' => UserToken::TYPE_PASSWORD_RESET
            ])
            ->first();

        $this->assertNotNull($token);
        $this->assertTrue($token->isValid());
    }

    /**
     * Kurocoユーザーの新システム処理テスト
     */
    public function testSendPasswordReminderKurocoUserNewSystem(): void
    {
        // Kurocoユーザー（password=null）を作成
        // $generalUsersTable = TableRegistry::getTableLocator()->get('GeneralUsers');
        // $user = $generalUsersTable->newEntity([
        //     'email' => '<EMAIL>',
        //     'password' => null, // Kurocoユーザー
        // ]);
        // $generalUsersTable->save($user);

        // パスワードリマインダー送信（新システムで処理）
        $result = $this->PasswordManagementService->sendPasswordReminder(
            '<EMAIL>',
            PasswordManagementService::USER_TYPE_GENERAL
        );

        // 新システムで処理されることを確認
        $this->assertTrue($result);

        // トークンが生成されているかチェック
        $userTokensTable = TableRegistry::getTableLocator()->get('UserTokens');

        /** @var \App\Model\Entity\UserToken $token */
        $token = $userTokensTable->find()
            ->where([
                'general_user_id' => 2,
                'type' => UserToken::TYPE_PASSWORD_RESET
            ])
            ->first();

        $this->assertNotNull($token);
        $this->assertTrue($token->isValid());
    }

    /**
     * SWBユーザーのパスワードリマインダーテスト
     */
    public function testSendPasswordReminderSwbUser(): void
    {
        // SWBユーザーを作成
        $swbUsersTable = TableRegistry::getTableLocator()->get('SwbUsers');
        $user = $swbUsersTable->newEntity([
            'authority_id' => 100,
            'email' => '<EMAIL>',
            'password' => 'password123',
        ]);
        $swbUsersTable->save($user);

        // パスワードリマインダー送信
        $result = $this->PasswordManagementService->sendPasswordReminder(
            '<EMAIL>',
            PasswordManagementService::USER_TYPE_SWB
        );

        $this->assertTrue($result);

        // トークンが生成されているかチェック
        $swbUserTokensTable = TableRegistry::getTableLocator()->get('SwbUserTokens');

        /** @var \App\Model\Entity\SwbUserToken $token */
        $token = $swbUserTokensTable->find()
            ->where([
                'swb_user_id' => $user->id,
                'type' => SwbUserToken::TYPE_PASSWORD_RESET
            ])
            ->first();

        $this->assertNotNull($token);
        $this->assertTrue($token->isValid());
    }

    /**
     * メーカーユーザーのパスワードリマインダーテスト
     */
    public function testSendPasswordReminderMakerUser(): void
    {
        // メーカーユーザーを作成
        $makerUsersTable = TableRegistry::getTableLocator()->get('MakerUsers');
        $user = $makerUsersTable->newEntity([
            'maker_id' => 1,
            'email' => '<EMAIL>',
            'password' => 'password123',
        ]);
        $makerUsersTable->save($user);

        // パスワードリマインダー送信
        $result = $this->PasswordManagementService->sendPasswordReminder(
            '<EMAIL>',
            PasswordManagementService::USER_TYPE_MAKER
        );

        $this->assertTrue($result);

        // トークンが生成されているかチェック
        $makerUserTokensTable = TableRegistry::getTableLocator()->get('MakerUserTokens');

        /** @var \App\Model\Entity\MakerUserToken $token */
        $token = $makerUserTokensTable->find()
            ->where([
                'maker_user_id' => $user->id,
                'type' => MakerUserToken::TYPE_PASSWORD_RESET
            ])
            ->first();

        $this->assertNotNull($token);
        $this->assertTrue($token->isValid());
    }

    /**
     * パスワードリセットテスト
     */
    public function testResetPassword1(): void
    {
        // テストユーザーとトークンを作成
        $generalUsersTable = TableRegistry::getTableLocator()->get('GeneralUsers');
        $user = $generalUsersTable->newEntity([
            'email' => '<EMAIL>',
            'password' => 'oldpassword123',
        ]);
        $generalUsersTable->save($user);

        $userTokensTable = TableRegistry::getTableLocator()->get('UserTokens');
        $token = $userTokensTable->createPasswordResetToken($user->id);

        // パスワードリセット実行
        $result = $this->PasswordManagementService->resetPassword(
            $token->token,
            'newpassword123'
        );

        $this->assertTrue($result);

        // パスワードが更新されているかチェック
        $updatedUser = $generalUsersTable->get($user->id);
        $passwordHasher = new \Cake\Auth\DefaultPasswordHasher();
        $this->assertTrue($passwordHasher->check('newpassword123', $updatedUser->password));

        // トークンが無効化されているかチェック
        $expiredToken = $userTokensTable->find()->where(['id' => $token->id])->first();
        $this->assertNull($expiredToken); // 削除されている
    }

    /**
     * 無効なトークンでのパスワードリセットテスト
     */
    public function testResetPasswordWithInvalidToken(): void
    {
        $result = $this->PasswordManagementService->resetPassword(
            'invalid_token',
            'newpassword123'
        );

        $this->assertFalse($result);
    }

    /**
     * 期限切れトークンでのパスワードリセットテスト
     */
    public function testResetPasswordWithExpiredToken(): void
    {
        // 期限切れトークンを作成
        $generalUsersTable = TableRegistry::getTableLocator()->get('GeneralUsers');
        $user = $generalUsersTable->newEntity([
            'email' => '<EMAIL>',
            'password' => 'password123',
        ]);
        $generalUsersTable->save($user);

        $userTokensTable = TableRegistry::getTableLocator()->get('UserTokens');
        $token = $userTokensTable->newEntity([
            'general_user_id' => $user->id,
            'token' => 'expired_token',
            'type' => UserToken::TYPE_PASSWORD_RESET,
            'expires' => FrozenTime::now()->subHours(1) // 1時間前に期限切れ
        ]);
        $userTokensTable->save($token);

        // パスワードリセット実行
        $result = $this->PasswordManagementService->resetPassword(
            'expired_token',
            'newpassword123'
        );

        $this->assertFalse($result);
    }

    /**
     * 弱いパスワードでのリセットテスト
     */
    public function testResetPasswordWithWeakPassword(): void
    {
        // テストユーザーとトークンを作成
        $generalUsersTable = TableRegistry::getTableLocator()->get('GeneralUsers');
        $user = $generalUsersTable->newEntity([
            'email' => '<EMAIL>',
            'password' => 'password123',
        ]);
        $generalUsersTable->save($user);

        $userTokensTable = TableRegistry::getTableLocator()->get('UserTokens');
        $token = $userTokensTable->createPasswordResetToken($user->id);

        // 弱いパスワードでリセット実行
        $result = $this->PasswordManagementService->resetPassword(
            $token->token,
            'weak' // 弱いパスワード
        );

        $this->assertFalse($result);
    }

    /**
     * Kurocoユーザーの新システム移行処理テスト
     */
    public function testResetPasswordKurocoMigration(): void
    {
        // Kurocoユーザー（password=null）を作成
        $generalUsersTable = TableRegistry::getTableLocator()->get('GeneralUsers');
        $user = $generalUsersTable->newEntity([
            'email' => '<EMAIL>',
            'password' => null, // Kurocoユーザー
        ]);
        $generalUsersTable->save($user);

        $userTokensTable = TableRegistry::getTableLocator()->get('UserTokens');
        $token = $userTokensTable->createPasswordResetToken($user->id);

        // パスワードリセット実行（Kuroco→新システム移行）
        $result = $this->PasswordManagementService->resetPassword(
            $token->token,
            'newpassword123'
        );

        $this->assertTrue($result);

        // パスワードが設定されているかチェック（NULL→ハッシュ化済み）
        $updatedUser = $generalUsersTable->get($user->id);
        $this->assertNotNull($updatedUser->password);

        $passwordHasher = new \Cake\Auth\DefaultPasswordHasher();
        $this->assertTrue($passwordHasher->check('newpassword123', $updatedUser->password));

        // トークンが無効化されているかチェック
        $expiredToken = $userTokensTable->find()->where(['id' => $token->id])->first();
        $this->assertNull($expiredToken); // 削除されている
    }

    /**
     * 一般ユーザーのパスワード変更テスト
     */
    public function testChangePasswordGeneralUser(): void
    {
        // テストユーザーを作成
        $generalUsersTable = TableRegistry::getTableLocator()->get('GeneralUsers');
        $user = $generalUsersTable->newEntity([
            'email' => '<EMAIL>',
            'password' => 'currentpassword123', // 新システムユーザー
        ]);
        $generalUsersTable->save($user);

        // パスワード変更実行
        $result = $this->PasswordManagementService->changePassword(
            '<EMAIL>',
            'currentpassword123',
            'newpassword123',
            PasswordManagementService::USER_TYPE_GENERAL
        );

        $this->assertTrue($result);

        // パスワードが更新されているかチェック
        $updatedUser = $generalUsersTable->get($user->id);
        $passwordHasher = new \Cake\Auth\DefaultPasswordHasher();
        $this->assertTrue($passwordHasher->check('newpassword123', $updatedUser->password));
        $this->assertFalse($passwordHasher->check('currentpassword123', $updatedUser->password));
    }

    /**
     * SWBユーザーのパスワード変更テスト
     */
    public function testChangePasswordSwbUser(): void
    {
        // SWBユーザーを作成
        $swbUsersTable = TableRegistry::getTableLocator()->get('SwbUsers');
        $user = $swbUsersTable->newEntity([
            'authority_id' => 100,
            'email' => '<EMAIL>',
            'password' => 'currentpassword123',
        ]);
        $swbUsersTable->save($user);

        // パスワード変更実行
        $result = $this->PasswordManagementService->changePassword(
            '<EMAIL>',
            'currentpassword123',
            'newpassword123',
            PasswordManagementService::USER_TYPE_SWB
        );

        $this->assertTrue($result);

        // パスワードが更新されているかチェック
        $updatedUser = $swbUsersTable->get($user->id);
        $passwordHasher = new \Cake\Auth\DefaultPasswordHasher();
        $this->assertTrue($passwordHasher->check('newpassword123', $updatedUser->password));
    }

    /**
     * メーカーユーザーのパスワード変更テスト
     */
    public function testChangePasswordMakerUser(): void
    {
        // メーカーユーザーを作成
        $makerUsersTable = TableRegistry::getTableLocator()->get('MakerUsers');
        $user = $makerUsersTable->newEntity([
            'maker_id' => 1,
            'email' => '<EMAIL>',
            'password' => 'currentpassword123',
        ]);
        $makerUsersTable->save($user);

        // パスワード変更実行
        $result = $this->PasswordManagementService->changePassword(
            '<EMAIL>',
            'currentpassword123',
            'newpassword123',
            PasswordManagementService::USER_TYPE_MAKER
        );

        $this->assertTrue($result);

        // パスワードが更新されているかチェック
        $updatedUser = $makerUsersTable->get($user->id);
        $passwordHasher = new \Cake\Auth\DefaultPasswordHasher();
        $this->assertTrue($passwordHasher->check('newpassword123', $updatedUser->password));
    }

    /**
     * 存在しないユーザーでのパスワード変更テスト
     */
    public function testChangePasswordUserNotFound(): void
    {
        $result = $this->PasswordManagementService->changePassword(
            '<EMAIL>',
            'currentpassword123',
            'newpassword123',
            PasswordManagementService::USER_TYPE_GENERAL
        );

        $this->assertFalse($result);
    }

    /**
     * 間違った現在のパスワードでの変更テスト
     */
    public function testChangePasswordWrongCurrentPassword(): void
    {
        // テストユーザーを作成
        $generalUsersTable = TableRegistry::getTableLocator()->get('GeneralUsers');
        $user = $generalUsersTable->newEntity([
            'email' => '<EMAIL>',
            'password' => 'correctpassword123',
        ]);
        $generalUsersTable->save($user);

        // 間違った現在のパスワードで変更実行
        $result = $this->PasswordManagementService->changePassword(
            '<EMAIL>',
            'wrongpassword123', // 間違ったパスワード
            'newpassword123',
            PasswordManagementService::USER_TYPE_GENERAL
        );

        $this->assertFalse($result);

        // パスワードが変更されていないことを確認
        $unchangedUser = $generalUsersTable->get($user->id);
        $passwordHasher = new \Cake\Auth\DefaultPasswordHasher();
        $this->assertTrue($passwordHasher->check('correctpassword123', $unchangedUser->password));
        $this->assertFalse($passwordHasher->check('newpassword123', $unchangedUser->password));
    }

    /**
     * 弱いパスワードでの変更テスト
     */
    public function testChangePasswordWeakPassword(): void
    {
        // テストユーザーを作成
        $generalUsersTable = TableRegistry::getTableLocator()->get('GeneralUsers');
        $user = $generalUsersTable->newEntity([
            'email' => '<EMAIL>',
            'password' => 'currentpassword123',
        ]);
        $generalUsersTable->save($user);

        // 弱いパスワードで変更実行
        $result = $this->PasswordManagementService->changePassword(
            '<EMAIL>',
            'currentpassword123',
            'weak', // 弱いパスワード
            PasswordManagementService::USER_TYPE_GENERAL
        );

        $this->assertFalse($result);

        // パスワードが変更されていないことを確認
        $unchangedUser = $generalUsersTable->get($user->id);
        $passwordHasher = new \Cake\Auth\DefaultPasswordHasher();
        $this->assertTrue($passwordHasher->check('currentpassword123', $unchangedUser->password));
    }

    /**
     * Kurocoユーザーのパスワード変更テスト
     */
    public function testChangePasswordKurocoUser(): void
    {
        $this->setFrontAuthorized(true);
        $member = new Member([
            "details" => [
                "id" => 1,
                "maker_id" => 1,
                "member_id" => 1,
            ],
            "id" => 1,
            "maker_id" => 1,
            "member_id" => 1,
        ]);

        $member->setAccessToken((new AccessToken([
            'access_token' => [
                'value' => "NTEwYjkwM2JlMGVhZTEyYjdiNTk2NGU4NGUwMWRkNmUxODVkODI5MTVmNWZkYTIwM2NlODBlMjUzZGZlMTQ0MeZWz/8w8wCe1GuWURYYSpiuouOTjhOXMwfgXyihsqr7RKSqOzWrpBH5N4l80t97CdROD0adoN4gG5MTzRM1mjMtXnZcelefIWdxR47P/3hVjTMzL+fAeFW36rKTvvo8pg=="
            ]
        ])));

        $this->PasswordManagementService->setIdentity($member);

        // Kurocoユーザー（password=null）を作成
        $generalUsersTable = TableRegistry::getTableLocator()->get('GeneralUsers');
        $user = $generalUsersTable->newEntity([
            'email' => '<EMAIL>',
            'password' => null, // Kurocoユーザー
        ]);
        $generalUsersTable->save($user);

        // パスワード変更実行
        $result = $this->PasswordManagementService->changePassword(
            '<EMAIL>',
            'anypassword',
            'newpassword123',
            PasswordManagementService::USER_TYPE_GENERAL
        );

        $this->assertTrue($result);

        // パスワードが更新されているかチェック
        $updatedUser = $generalUsersTable->get($user->id);
        $passwordHasher = new \Cake\Auth\DefaultPasswordHasher();
        $this->assertTrue($passwordHasher->check('newpassword123', $updatedUser->password));
    }
}
