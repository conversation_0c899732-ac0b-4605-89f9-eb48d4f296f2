<?php

namespace App\Form;

use App\Enums\EValidationErrorMessage;
use Cake\Form\Form;
use Cake\Utility\Hash;

/**
 * APIフォーム共通親クラス
 */
abstract class ApiForm extends Form
{
    /**
     * 許可フィールドPathの配列
     * @return string[]
     */
    abstract protected function _getAllowFieldsPath(): array;

    /**
     * Defines what to execute once the Form is processed
     *
     * @param array $data Form data.
     * @return bool
     */
    protected function _execute(array $data): bool
    {
        $fields = $this->_getAllowFieldsPath();
        foreach ($fields as $path) {
            $data = Hash::remove($data, $path);
        }
        $flattenData = Hash::flatten($data);
        $errors = [];
//        debug($data);
        foreach ($flattenData as $path => $value) {

//            $errors[$path] = [
//                // 不正フィールドエラー
//                '_extraField' => EValidationErrorMessage::EXTRA_FIELD->format(),
//            ];
        }
        if (count($errors)) {
            $this->setErrors($errors);
            return false;
        }
        return true;
    }
}
