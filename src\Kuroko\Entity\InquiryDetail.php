<?php

namespace App\Kuroko\Entity;

use Cake\Utility\Hash;

class InquiryDetail implements IKurokoEntity
{
    use KurokoEntityTrait;

    public function getOrders(): ?array
    {
        $ext01 = json_decode(Hash::get($this->getData(), "details.ext_01"), true);
        return Hash::get($ext01, "order");
    }

    public function getJsonData(): array
    {
        return Hash::get($this->getData(), "details", []);
        // return [
        //     "id" => Hash::get($this->getData(), "id"),
        //     "messages" => Hash::get($this->getData(), "messages"),
        // ];
    }

    /**
     * @param array $data
     * @return array
     */
    public function initialize(array $data): array
    {
        return $data;
    }


}
