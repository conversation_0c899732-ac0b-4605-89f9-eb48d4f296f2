<script setup lang="ts">
import { ref } from "vue";
import VueDatePicker from "@vuepic/vue-datepicker";
import "@vuepic/vue-datepicker/dist/main.css";
import { ClientMyBridge } from "~/src/models/bridge/ClientMyBridge";

const props = defineProps<{
    bridge: ClientMyBridge;
}>();
const form = props.bridge.client_report_search_form;

/**
 * yyyy-MM-dd形式の文字列から年をnumber型で取得
 * @param dateStr
 */
const getYearFromDateString = (dateStr: string): number => {
    return parseInt(dateStr.split("-")[0], 10);
};
/**
 * yyyy-MM-dd形式の文字列から月をnumber型で取得
 * @param dateStr
 */
const getMonthFromDateString = (dateStr: string): number => {
    return parseInt(dateStr.split("-")[1], 10);
};
/**
 * 年月を指定すると月末日を返す（もし将来日付の場合は今日日付に置き換える）
 * @param year
 * @param month
 */
const getLastDayOfMonth = (year: number, month: number): number => {
    const lastDayOfMonth = new Date(year, month + 1, 0).getDate();
    const today = new Date();
    const lastDayDate = new Date(year, month, lastDayOfMonth);

    const todayDateOnly = new Date(
        today.getFullYear(),
        today.getMonth(),
        today.getDate(),
    );
    if (lastDayDate > todayDateOnly) {
        return todayDateOnly.getDate();
    }
    return lastDayOfMonth;
};

//DatePicker設定
const maxDate = new Date();
const minDate = new Date(2024, 1, 1); //2024年10月（カバーミーリリース月）
const searchedStartDate = props.bridge.client_report_search_form.startDate;
const searchedEndDate = props.bridge.client_report_search_form.endDate;
const displayStartMonth = ref({
    month: getMonthFromDateString(searchedStartDate) - 1,
    year: getYearFromDateString(searchedStartDate),
});
const displayEndMonth = ref({
    month: getMonthFromDateString(searchedEndDate) - 1,
    year: getYearFromDateString(searchedEndDate),
});
const jaLocale: { code: string; formatLong: { date: () => string } } = {
    code: "ja",
    formatLong: {
        date: (): string => "yyyy年MM月",
    },
};
const format = (date: Date): string => {
    const month = date.getMonth() + 1;
    const year = date.getFullYear();
    return `${year}-${month}`;
};
const updateStartDate = (modelData: { month: number; year: number }): void => {
    form.startDate = `${modelData.year}-${modelData.month + 1}-01`;
    const validationResult = form.isStartDateValid();
    if (validationResult !== true) {
        alert(validationResult);
    }
};
const updateEndDate = (modelData: { month: number; year: number }): void => {
    form.endDate = `${modelData.year}-${
        modelData.month + 1
    }-${getLastDayOfMonth(modelData.year, modelData.month)}`;
    const validationResult = form.isEndDateValid();
    if (validationResult !== true) {
        alert(validationResult);
    }
};
</script>

<template>
    <div class="main-wrapper">
        <v-card class="main-card">
            <div class="pa-1 pl-2 title-wrapper">
                ■ 条件を指定して注文リストを表示する
            </div>
            <div class="pl-8 product-select-wrapper">
                カタログ：
                <v-col cols="5">
                    <v-select
                        v-model="form.productId"
                        :items="bridge.my_products"
                        item-title="product_name"
                        item-value="product_id"
                        hide-details="auto"
                        bg-color="white"
                        density="compact"
                        prev-icon="mdi-chevron-left"
                        next-icon="mdi-chevron-right"
                    ></v-select>
                </v-col>
                の注文リストを表示する
            </div>
            <v-row class="ma-1">
                <v-col cols="7">
                    <div class="search-date-wrapper">
                        <div class="search-date-title">月指定</div>
                        <div class="pl-2 search-date-form-wrapper">
                            <v-row class="search-date-form-row">
                                <v-spacer />
                                <v-col cols="5">
                                    <VueDatePicker
                                        v-model="displayStartMonth"
                                        month-picker
                                        auto-apply
                                        :clearable="false"
                                        :min-date="minDate"
                                        :max-date="maxDate"
                                        :locale="jaLocale"
                                        :format="format"
                                        @update:model-value="updateStartDate"
                                    />
                                </v-col>
                                <v-col class="text-center" cols="1">～</v-col>
                                <v-col cols="5">
                                    <VueDatePicker
                                        v-model="displayEndMonth"
                                        month-picker
                                        auto-apply
                                        :clearable="false"
                                        :min-date="minDate"
                                        :max-date="maxDate"
                                        :locale="jaLocale"
                                        :format="format"
                                        @update:model-value="updateEndDate"
                                    />
                                </v-col>
                                <v-spacer />
                            </v-row>
                        </div>
                    </div>
                </v-col>
            </v-row>
            <v-row justify="center" class="mt-1">
                <v-btn
                    class="search-btn"
                    :disabled="
                        !bridge.client_report_search_form.isDateValid ||
                        !bridge.client_report_search_form.isChanged
                    "
                    elevation="0"
                    @click="bridge.loadReportOrders()"
                >
                    <v-icon size="large" class="mr-2">mdi-magnify</v-icon>
                    <p class="search-btn-text">指定した条件で表示する</p>
                </v-btn>
            </v-row>
        </v-card>
    </div>
</template>

<style scoped>
.main-wrapper {
    width: 100%;
    min-width: 1030px;
    font-size: small !important;
}
.main-card {
    width: 100%;
    min-width: 1000px;
    overflow: unset !important;
}
.title-wrapper {
    background-color: #eaeaea;
}
.product-select-wrapper {
    background-color: #dcdcdc;
    display: flex;
    align-items: center;
}
.search-date-wrapper {
    min-width: 646px;
    height: 52px;
    display: flex;
    border: #dfdfdf solid 2px;
}
.search-date-form-row {
    padding-top: 2px;
}
.search-date-title {
    background-color: #eaeaea;
    padding: 3px 10px;
    align-content: center;
    border-right: #dfdfdf solid 2px;
}
.search-date-form-wrapper {
    width: 88%;
    padding: 3px 10px;
    display: flex;
}
.text-center {
    align-content: center;
    text-align: center;
}
.search-btn {
    background: #ff8b00;
    color: white;
    transition: opacity 0.3s ease;
    margin-bottom: 16px;
}
.search-btn:hover {
    opacity: 0.7;
}
.search-btn:disabled {
    background: #ffffff !important;
    color: #a5a5a5 !important;
}
.search-btn:disabled .search-btn-text {
    color: #707070 !important;
}
.search-btn-text {
    font-size: 16px;
    font-weight: bold;
    color: white;
}
</style>
