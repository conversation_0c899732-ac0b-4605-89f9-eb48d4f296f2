<?php

namespace App\Kuroko\Entity;

use Cake\Utility\Hash;

class Login implements IKurokoEntity
{
    use KurokoEntityTrait;

    public function getGrantToken(): ?string
    {
        return Hash::get($this->getData(), "grant_token");
    }

    public function getMemberId(): ?string
    {
        return Hash::get($this->getData(), "member_id");
    }

    public function getJsonData(): array
    {
        return [
            "grant_token" => $this->getGrantToken(),
            "member_id" => $this->getMemberId(),
            "messages" => Hash::get($this->getData(), "messages"),
        ];
    }

    /**
     * @param array $data
     * @return array
     */
    public function initialize(array $data): array
    {
        return $data;
    }


}
