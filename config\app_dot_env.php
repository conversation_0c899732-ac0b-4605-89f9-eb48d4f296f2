<?php

use Cake\Utility\Hash;

$addEnv = fn(array $data, $key) => Hash::insert($data, 'DotEnv.' . $key, env($key));
$dotEnv = [];

foreach ([
             'APP_NAME',
             'DEBUG',
             'APP_ENCODING',
             'APP_DEFAULT_LOCALE',
             'APP_DEFAULT_TIMEZONE',

             'SECURITY_SALT',

             'API_TOKEN_NUXT_BUILD',
             'API_TOKEN_FRONT',

             'KUROKO_API_CLIENT_HOST',
             'KUROKO_API_CLIENT_BASE_PATH',
             'KUROKO_API_CLIENT_SCHEME',
             'KUROKO_API_USE_MOCK',
             'KUROKO_API_TOKEN_PRIVATE_STATIC',
             'KUROKO_API_TOKEN_STATIC',
             'KUROKO_API_TOKEN_DYNAMIC_SYS_USER',

             'EMAIL_DEFAULT_FROM_MAIL',
             'EMAIL_DEFAULT_FROM_NAME',
             'NOTIFICATION_EMAIL',

             'EMAIL_TRANSPORT_DEFAULT_HOST',
             'EMAIL_TRANSPORT_DEFAULT_PORT',
             'EMAIL_TRANSPORT_DEFAULT_USERNAME',
             'EMAIL_TRANSPORT_DEFAULT_PASSWORD',
             'EMAIL_TRANSPORT_DEFAULT_TLS',

             'CORS_USE',

             'FRONT_BASE_URL',

             'DATABASE_DEFAULT_HOST',
             'DATABASE_DEFAULT_NAME',
             'DATABASE_DEFAULT_USERNAME',
             'DATABASE_DEFAULT_PASSWORD',
             'DATABASE_DEFAULT_PORT',
             'DATABASE_TEST_HOST',
             'DATABASE_TEST_NAME',
             'DATABASE_TEST_USERNAME',
             'DATABASE_TEST_PASSWORD',
             'DATABASE_TEST_PORT',

         ] as $key) {
    $dotEnv = $addEnv($dotEnv, $key);
}
return $dotEnv;
