<?php

namespace App\Controller\Front;

use App\Controller\FrontController;
use App\Form\Front\SchoolBagFormNewMembersAddFrom;
use App\Form\DisableParametersForm;
use App\Kuroko\Entity\IKurokoEntity;
use App\Model\Entity\TemporaryRegistration;
use App\Service\SchoolBagFormNewMembersService;
use Cake\Event\Event;
use Cake\Event\EventInterface;
use Cake\Log\Log;

class SchoolBagFormNewMembersController extends FrontController
{

    public function beforeFilter(EventInterface $event): void
    {
        parent::beforeFilter($event);
        $this->Authentication->addUnauthenticatedActions(['add', 'edit']);
    }


    public function add(): void
    {
        $this->_onSuccess(function (Event $event, TemporaryRegistration $tempRegistration) {
            $this->_setSuccessResponse([
                'success' => true,
            ]);
        });
        $this->_onError(function (Event $event, array $errors) {
            Log::debug(print_r($errors, true));
        });
        $this->_add((new SchoolBagFormNewMembersService())
                ->setForm(
                    new DisableParametersForm()
                )
        );
    }

    public function edit($id = null): void
    {
        $this->_onSuccess(function (Event $event, IKurokoEntity $response) {
            $this->_setSuccessResponse(
                $response->getJsonData()
            );
        });
        $this->_onError(function (Event $event, array $errors) {
            Log::debug(print_r($errors, true));
        });
        $this->_edit(
            $id,
            (new SchoolBagFormNewMembersService())
                ->setForm(
                    new DisableParametersForm()
                )
        );
    }
}
