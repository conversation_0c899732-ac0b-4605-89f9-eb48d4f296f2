<script setup lang="ts">
import { ref } from "vue";
import VueDatePicker from "@vuepic/vue-datepicker";
import "@vuepic/vue-datepicker/dist/main.css";
import { SwbMyBridge } from "~/src/models/bridge/SwbMyBridge";

const props = defineProps<{
    bridge: SwbMyBridge;
}>();
const form = props.bridge.swb_invoice_adjustment_search_form;

/**
 * yyyy-MM-dd形式の文字列から年をnumber型で取得
 * @param dateStr
 */
const getYearFromDateString = (dateStr: string): number => {
    return parseInt(dateStr.split("-")[0], 10);
};
/**
 * yyyy-MM-dd形式の文字列から月をnumber型で取得
 * @param dateStr
 */
const getMonthFromDateString = (dateStr: string): number => {
    return parseInt(dateStr.split("-")[1], 10);
};
/**
 * 年月を指定すると月末日を返す（もし将来日付の場合は今日日付に置き換える）
 * @param year
 * @param month
 */

//DatePicker設定
const today = new Date();
const maxDate = new Date(today.getFullYear(), today.getMonth() - 1, 1);
const minDate = new Date(2024, 1, 1); //2024年10月（カバーミーリリース月）
const searchedBillingYearMonth = form.billingYearMonth;
const displayBillingYearMonth = ref({
    month: getMonthFromDateString(searchedBillingYearMonth) - 1,
    year: getYearFromDateString(searchedBillingYearMonth),
});
const jaLocale: { code: string; formatLong: { date: () => string } } = {
    code: "ja",
    formatLong: {
        date: (): string => "yyyy年MM月",
    },
};
const format = (date: Date): string => {
    const month = date.getMonth() + 1;
    const year = date.getFullYear();
    return `${year}-${month}`;
};
const updateBillingYearMonth = (modelData: {
    month: number;
    year: number;
}): void => {
    // 月を2桁の文字列に変換 (例: 1 -> "01", 12 -> "12")
    const monthString = String(modelData.month + 1).padStart(2, "0");
    form.billingYearMonth = `${modelData.year}-${monthString}`;
};
</script>

<template>
    <div class="main-wrapper">
        <v-row class="ma-0">
            <v-col cols="12" class="pa-0">
                <div class="search-date-wrapper">
                    <div class="search-date-title">請求月</div>
                    <div class="pl-2 search-date-form-wrapper">
                        <v-row class="search-date-form-row">
                            <v-col cols="3">
                                <VueDatePicker
                                    v-model="displayBillingYearMonth"
                                    month-picker
                                    auto-apply
                                    :clearable="false"
                                    :min-date="minDate"
                                    :max-date="maxDate"
                                    :locale="jaLocale"
                                    :format="format"
                                    @update:model-value="updateBillingYearMonth"
                                />
                            </v-col>
                            <v-col cols="5">
                                <v-btn
                                    class="search-btn"
                                    :disabled="
                                        !bridge
                                            .swb_invoice_adjustment_search_form
                                            .isChanged
                                    "
                                    elevation="0"
                                    @click="bridge.loadInvoiceAdjustments()"
                                >
                                    <v-icon size="large" class="mr-2"
                                        >mdi-magnify</v-icon
                                    >
                                    <p class="search-btn-text">
                                        指定した条件で表示する
                                    </p>
                                </v-btn>
                            </v-col>
                        </v-row>
                    </div>
                </div>
            </v-col>
        </v-row>
    </div>
</template>

<style scoped>
.main-wrapper {
    width: 100%;
    min-width: 1030px;
    font-size: small !important;
}
.title-wrapper {
    background-color: #eaeaea;
}
.product-select-wrapper {
    background-color: #dcdcdc;
    display: flex;
    align-items: center;
}
.search-date-wrapper {
    min-width: 646px;
    height: 52px;
    display: flex;
    border: #dfdfdf solid 2px;
}
.search-date-form-row {
    padding-top: 2px;
}
.search-date-title {
    background-color: #eaeaea;
    padding: 3px 10px;
    align-content: center;
    border-right: #dfdfdf solid 2px;
}
.search-date-form-wrapper {
    width: 88%;
    padding: 3px 10px;
    display: flex;
}
.search-btn {
    background: #ff8b00;
    color: white;
    transition: opacity 0.3s ease;
    margin-bottom: 16px;
}
.search-btn:hover {
    opacity: 0.7;
}
.search-btn:disabled {
    background: #ffffff !important;
    color: #a5a5a5 !important;
}
.search-btn:disabled .search-btn-text {
    color: #707070 !important;
}
.search-btn-text {
    font-size: 16px;
    font-weight: bold;
    color: white;
}
</style>
