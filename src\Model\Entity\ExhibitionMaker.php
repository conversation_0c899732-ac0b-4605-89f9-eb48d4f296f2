<?php
declare(strict_types=1);

namespace App\Model\Entity;

use Cake\ORM\Entity;

/**
 * 展示会メーカーエンティティ
 * 
 * @property int $id
 * @property int $exhibition_id
 * @property int $maker_id
 * @property \Cake\I18n\FrozenTime $created
 * @property \Cake\I18n\FrozenTime $modified
 * @property \App\Model\Entity\Exhibition $exhibition
 * @property \App\Model\Entity\Maker $maker
 */
class ExhibitionMaker extends Entity
{
    protected $_accessible = [
        'exhibition_id' => true,
        'maker_id' => true,
        'created' => true,
        'modified' => true,
        'exhibition' => true,
        'maker' => true,
    ];

    protected $_hidden = [];
}
