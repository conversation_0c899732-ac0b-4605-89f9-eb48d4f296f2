<?php

namespace App\Enums\EntityFields;

enum EUserChangePassword: string
{
    case LOGIN_ID = 'login_id';
    case CURRENT_PASSWORD = 'current_password';
    case NEW_PASSWORD = 'new_password';


    /**
     * @return string
     */
    public function description(): string
    {
        return match ($this) {
            self::LOGIN_ID => 'メールアドレス',
            self::CURRENT_PASSWORD => '現在のパスワード',
            self::NEW_PASSWORD => '新しいパスワード',
        };
    }
}
