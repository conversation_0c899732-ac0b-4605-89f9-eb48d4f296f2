<?php

use App\Config\DotEnvConfig;
use Cake\Mailer\Transport\SmtpTransport;

return [
    'EmailTransport' => [
        'default' => [
            'className' => SmtpTransport::class,
            /*
             * The keys host, port, timeout, username, password, client and tls
             * are used in SMTP transports
             */
            'host' => DotEnvConfig::read('EMAIL_TRANSPORT_DEFAULT_HOST'),
            'port' => DotEnvConfig::read('EMAIL_TRANSPORT_DEFAULT_PORT'),
            'timeout' => 30,
            /*
             * It is recommended to set these options through your environment or app_local.php
             */
            'username' => DotEnvConfig::read('EMAIL_TRANSPORT_DEFAULT_USERNAME', ""),
            'password' => DotEnvConfig::read('EMAIL_TRANSPORT_DEFAULT_PASSWORD', ""),
            'client' => null,
            'tls' => DotEnvConfig::readBoolean('EMAIL_TRANSPORT_DEFAULT_TLS', false),
//            'url' => DotEnvConfig::read('EMAIL_TRANSPORT_DEFAULT_URL', null),
        ],
    ]
];
