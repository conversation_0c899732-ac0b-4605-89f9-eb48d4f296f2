import { Csv } from "~/src/lib/Csv";
import type { ClientMyBridge } from "~/src/models/bridge/ClientMyBridge";
import UpdateStatusOrder from "~/src/models/entry/UpdateStatusOrder";
import { ClientRandselOrders } from "~/src/models/ClientRandselOrders";
import type RandselOrder from "~/src/models/entry/RandselOrder";
import moment from "moment";

export class ClientOrders {
    static downloadOrderCsv(randsel_orders: RandselOrder[]): void {
        Csv.download(
            ClientOrders._convertOrders(randsel_orders),
            ClientOrders._generateCsvFilename(),
            true,
        );
    }

    static async confirmOrderCsv(
        file: File,
        bridge: ClientMyBridge,
        displayInvalidRows: Record<string, string>[],
        displayValidRows: Record<string, string>[],
    ): Promise<void> {
        try {
            const { updateStatusOrders, invalidRows, validRows } =
                await Csv.upload(file, ClientOrders.validateCsvData);
            console.log(updateStatusOrders);
            console.log(invalidRows);

            const postData: TUpdateStatusOrder = {
                randsel_orders: updateStatusOrders.map(
                    (order) => order.mixed_data,
                ),
                validate_only: true,
            };

            const response = await ClientRandselOrders.create(
                bridge.config,
            ).putCsvRandselOrders(postData);
            console.log(response);

            if (response !== null && "errors" in response) {
                validRows.forEach((row: Record<string, string>) => {
                    if (
                        response.errors.failed_ids.includes(
                            Number(row["注文ID"]),
                        )
                    ) {
                        invalidRows.push({
                            ...row,
                            エラーメッセージ:
                                "注文IDは存在しないまたはステータス更新済み",
                        });
                    } else {
                        displayValidRows.push(row);
                    }
                });
            }
            displayInvalidRows.push(...invalidRows);
        } catch (error) {
            console.error("処理中にエラーが発生しました:", error);
            throw error;
        }
    }

    static async updateOrderCsv(
        validRows: Record<string, string>[],
        bridge: ClientMyBridge,
    ): Promise<void> {
        const updateData: UpdateStatusOrder[] = [];
        validRows.forEach((row: Record<string, string>) => {
            const updateStatusOrder = new UpdateStatusOrder();
            updateStatusOrder.id = Number(row["注文ID"]);
            updateStatusOrder.status = Number(row["ステータス値"]);
            updateData.push(updateStatusOrder);
        });

        const postData: TUpdateStatusOrder = {
            randsel_orders: updateData.map((order) => order.mixed_data),
            validate_only: false,
        };
        await this._UpdateStatusOrders(postData, bridge);
        // try {
        //     const response = await ClientRandselOrders.create(
        //         bridge.config,
        //     ).putCsvRandselOrders(postData);
        //     console.log(response);

        //     bridge.loadOrders();
        // } catch (error) {
        //     console.error("処理中にエラーが発生しました:", error);
        // }
    }

    static async approvalOrders(
        selectedOrders: RandselOrder[],
        bridge: ClientMyBridge,
    ): Promise<void> {
        await this._processOrders(selectedOrders, bridge, "approval_data");
    }

    static async denyOrders(
        selectedOrders: RandselOrder[],
        bridge: ClientMyBridge,
    ): Promise<void> {
        await this._processOrders(selectedOrders, bridge, "deny_data");
    }

    private static async _processOrders(
        selectedOrders: RandselOrder[],
        bridge: ClientMyBridge,
        action: "approval_data" | "deny_data",
    ): Promise<void> {
        const client = bridge.member;
        if (!client) {
            throw new Error("Client is null");
        }

        const orders = UpdateStatusOrder.creates(selectedOrders, client);
        const postData: TUpdateStatusOrder = {
            randsel_orders: orders.map((order) => order[action]),
        };

        await this._UpdateStatusOrders(postData, bridge);
    }

    private static async _UpdateStatusOrders(
        postData: TUpdateStatusOrder,
        bridge: ClientMyBridge,
    ): Promise<void> {
        try {
            const response = await ClientRandselOrders.create(
                bridge.config,
            ).putScreenRandselOrders(postData);
            console.log(response);
            bridge.loadOrders();
            bridge.loadReportOrders();
        } catch (error) {
            console.error("処理中にエラーが発生しました:", error);
        }
    }

    private static _convertOrders(
        orders: RandselOrder[],
    ): Record<string, any>[] {
        return orders.reduce(
            (acc: Record<string, any>[], order: RandselOrder) => {
                // const inquiryQuestions = order._inquiry_body
                //     ? ClientOrders._extractInquiryQuestions(order._inquiry_body)
                //     : {};
                const inquiryQuestions = order.survey_json
                    ? ClientOrders._extractInquiryQuestions(order.survey_json)
                    : {};

                // const csvObjData = order._order_details.map((detail: any) =>
                //     ClientOrders._mapOrderToCsv(order, detail, inquiryQuestions),
                // );

                const csvObjData = ClientOrders._mapOrderToCsv(
                    order,
                    {},
                    inquiryQuestions,
                );

                return acc.concat(csvObjData);
            },
            [],
        );
    }

    private static _extractInquiryQuestions(
        inquiryBody: string,
    ): Record<string, any> {
        try {
            const inquiryData = JSON.parse(inquiryBody);
            return inquiryData.questions.reduce(
                (acc: Record<string, any>, question: any) => {
                    acc[question.k] = question.v;
                    return acc;
                },
                {},
            );
        } catch (error) {
            console.error("Invalid _inquiry_body:", inquiryBody);
            return {};
        }
    }

    private static _mapOrderToCsv(
        order: RandselOrder,
        detail: any,
        inquiryQuestions: Record<string, any>,
    ): Record<string, any> {
        // return {
        //     注文ID: order.ec_order_id,
        //     姓: order.order_nm01,
        //     名: order.order_nm02,
        //     セイ: order.order_nm01_kana,
        //     メイ: order.order_nm02_kana,
        //     郵便番号: order.order_zip,
        //     都道府県: order.display_tdfk_cd,
        //     市区町村_町名_番地: `${order.order_addr01} ${order.order_addr02}`,
        //     建物名_部屋番号: order.order_addr03,
        //     電話番号: order.order_tel,
        //     メールアドレス: order.order_email,
        //     請求日時: order.billing_date,
        //     報酬: `${order.payment_total}円`,
        //     カタログ名: detail.product_name || order.product_name,
        //     発送予定: detail.product_memo,
        //     お子さまの性別: inquiryQuestions["お子さまの性別"],
        //     お子さまの生年月日: inquiryQuestions["お子さまの生年月日"],
        //     ご予算: inquiryQuestions["ご予算"],
        //     カタログ請求のきっかけ:
        //         inquiryQuestions["カタログ請求のきっかけ（複数回答可）"],
        //     特に重視するポイント:
        //         inquiryQuestions["特に重視するポイント（複数回答可）"],
        //     残承認期限: "",
        // };
        return {
            注文ID: order.id,
            注文日時: moment(order.created).format("YYYY-MM-DD HH:mm:ss"),
            ステータス: order.display_status,
            ステータス値: order.status === 0 ? "" : order.status,
            承認日時:
                order.status_modified === null
                    ? "0000-00-00 00:00:00"
                    : moment(order.status_modified).format(
                          "YYYY-MM-DD HH:mm:ss",
                      ),
            残承認期限: "",
            姓: order.name1,
            名: order.name2,
            セイ: order.name1_hurigana,
            メイ: order.name2_hurigana,
            郵便番号: order.zip_code,
            都道府県: order.display_tdfk_cd,
            市区町村_町名_番地: `${order.address1} ${order.address2}`,
            建物名_部屋番号: order.address3,
            電話番号: order.tel,
            メールアドレス: order.email,
            請求額: `${order.price}円`,
            カタログ名: order.product_name,
            お子さまの性別: inquiryQuestions["お子さまの性別"],
            お子さまの生年月日: inquiryQuestions["お子さまの生年月日"],
            ご予算: inquiryQuestions["ご予算"],
            カタログ請求のきっかけ:
                inquiryQuestions["カタログ請求のきっかけ（複数回答可）"],
            特に重視するポイント:
                inquiryQuestions["特に重視するポイント（複数回答可）"],
        };
    }

    private static _generateCsvFilename(): string {
        const now = new Date();
        const format = (n: number) => `${n}`.padStart(2, "0");
        return `order_report_${now.getFullYear()}${format(now.getMonth() + 1)}${format(
            now.getDate(),
        )}_${format(now.getHours())}${format(now.getMinutes())}${format(
            now.getSeconds(),
        )}.csv`;
    }

    private static validateCsvData(data: any[]): {
        isValid: boolean;
        invalidRows: Record<string, string>[];
        validRows: Record<string, string>[];
    } {
        const invalidRows: Record<string, string>[] = [];
        const validRows: Record<string, string>[] = [];

        const orderIdsSet = new Set<string>();
        // 1. 必要なキーが存在するかチェック
        const requiredKeys = ["注文ID", "ステータス値"];
        if (
            !Array.isArray(data) ||
            data.length === 0 ||
            !requiredKeys.every((key) => key in data[0])
        ) {
            throw new Error(
                "アップロードファイルが不正です。ファイルの内容をご確認ください。",
            );
        }

        // 2. ステータス値が1か2かどうかチェック
        data.forEach((row: Record<string, string>) => {
            const orderId = row["注文ID"];
            const statusValue = Number(row["ステータス値"]);
            if (statusValue !== 1 && statusValue !== 2) {
                invalidRows.push({
                    ...row,
                    エラーメッセージ: "ステータス値は1か2を指定してください。",
                });
            } else {
                // 3. 重複チェック
                if (!orderIdsSet.has(orderId)) {
                    orderIdsSet.add(orderId);
                    validRows.push(row);
                } else {
                    invalidRows.push({
                        ...row,
                        エラーメッセージ: "同じ注文IDが複数あります。",
                    });
                }
            }
        });

        return {
            isValid: invalidRows.length === 0,
            invalidRows: invalidRows,
            validRows: validRows,
        };
    }
}
