<script setup lang="ts">
import { useRoute } from "#app";
const isClient = useRoute().path.startsWith("/client/");
const isSwb = useRoute().path.startsWith("/swb/");
</script>

<template>
    <v-footer
        class="text-center flex-column coverme-footer"
        :class="
            isSwb
                ? 'bg-swbPrimary'
                : isClient
                  ? 'bg-clientPrimary'
                  : 'bg-footer'
        "
        app
        absolute
    >
        <!-- 一行目：Facebook、Twitterのリンク -->
        <div class="mb-2">
            <a
                href="https://www.facebook.com/profile.php?id=61561124265659"
                target="_blank"
                rel="noopener noreferrer"
                class="footer-link"
            >
                <v-icon class="text-body-2">
                    <svg
                        role="img"
                        viewBox="0 0 24 24"
                        xmlns="http://www.w3.org/2000/svg"
                    >
                        <title>Facebook</title>
                        <path
                            d="M9.101 23.691v-7.98H6.627v-3.667h2.474v-1.58c0-4.085 1.848-5.978 5.858-5.978.401 0 .955.042 1.468.103a8.68 8.68 0 0 1 1.141.195v3.325a8.623 8.623 0 0 0-.653-.036 26.805 26.805 0 0 0-.733-.009c-.707 0-1.259.096-1.675.309a1.686 1.686 0 0 0-.679.622c-.258.42-.374.995-.374 1.752v1.297h3.919l-.386 2.103-.287 1.564h-3.246v8.245C19.396 23.238 24 18.179 24 12.044c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.628 3.874 10.35 9.101 11.647Z"
                            fill="white"
                        />
                    </svg>
                </v-icon>
            </a>
            <a
                href="https://x.com/coverme_randsel"
                target="_blank"
                rel="noopener noreferrer"
                class="footer-link"
            >
                <v-icon class="text-body-2">
                    <svg
                        role="img"
                        viewBox="0 0 24 24"
                        xmlns="http://www.w3.org/2000/svg"
                        class=""
                    >
                        <title>X</title>
                        <path
                            d="M18.901 1.153h3.68l-8.04 9.19L24 22.846h-7.406l-5.8-7.584-6.638 7.584H.474l8.6-9.83L0 1.154h7.594l5.243 6.932ZM17.61 20.644h2.039L6.486 3.24H4.298Z"
                            fill="white"
                        />
                    </svg>
                </v-icon>
            </a>
        </div>
        <!-- 二行目：運営者情報、お問合せのリンク -->
        <div class="mb-2">
            <a
                href="https://www.sowelleber.jp/"
                class="footer-link text-body-2 text-white"
            >
                運営者情報
            </a>
            <a
                href="https://coverme.jp/contact"
                class="footer-link text-body-2 text-white"
            >
                お問合せ
            </a>
        </div>
        <!-- 三行目：著作権表示 -->
        <div class="mb-2 text-white text-body-2">&copy; coverme</div>
    </v-footer>
</template>

<style scoped>
.footer-link {
    margin: 0 10px;
    /* color: white; 必要に応じて色を変更 */
    text-decoration: none;
}
.footer-link v-icon {
    margin-right: 5px;
}
.footer-link:hover {
    text-decoration: underline;
}
.coverme-footer {
    width: 100%;
}
</style>
