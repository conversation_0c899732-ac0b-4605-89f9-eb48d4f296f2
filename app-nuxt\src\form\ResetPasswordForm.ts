import { Validations } from "~~/src/lib/Validations";

export default class ResetPasswordForm {
    private _login_pwd: string;
    private _login_pwd_confirmation: string;
    private _token: string;
    private _temp_pwd: string;
    private _valid: boolean = false;

    constructor(data: TPasswordReminders) {
        this._login_pwd = "";
        this._login_pwd_confirmation = "";
        this._token = data.token as string;
        this._temp_pwd = data.temp_pwd as string;
    }

    get valid(): boolean {
        return this._valid;
    }

    set valid(value: boolean) {
        this._valid = value;
    }

    get login_pwd(): string {
        return this._login_pwd;
    }

    set login_pwd(value: string) {
        this._login_pwd = value;
    }

    get login_pwd_rules(): TValidationFunction[] {
        return [
            Validations.notEmptyString("パスワード"),
            Validations.password("パスワード"),
        ];
    }

    get login_pwd_confirmation(): string {
        return this._login_pwd_confirmation;
    }

    set login_pwd_confirmation(value: string) {
        this._login_pwd_confirmation = value;
    }

    get login_pwd_confirmation_rules(): TValidationFunction[] {
        return [
            Validations.notEmptyString("パスワード確認用"),
            Validations.password("パスワード確認用"),
            Validations.matchedConfirmation(
                this.login_pwd,
                "パスワード",
                "パスワード確認用",
            ),
        ];
    }

    get data(): TPasswordReminders {
        return {
            token: this._token,
            temp_pwd: this._temp_pwd,
            login_pwd: this.login_pwd,
        };
    }
}
