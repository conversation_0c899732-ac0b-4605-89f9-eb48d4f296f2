<?php

namespace App\Kuroko\Entity;

use Cake\Utility\Hash;

class ECOrder implements IKurokoEntity
{
    use KurokoEntityTrait;


    public function getRandselOrderData(): array
    {
        $data = Hash::get($this->getData(), "ext_data");
        return Hash::insert($data, "id", $this->getIds()[0]);
    }

    public function getIds(): ?array
    {
        return Hash::get($this->getData(), "ids");
    }

    public function getMemberEmail(): ?string
    {
        return Hash::get($this->getData(), "member_data.email");
    }

    public function getMemberName1(): ?string
    {
        return Hash::get($this->getData(), "member_data.name1");
    }

    public function getProductName(): ?string
    {
        return Hash::get($this->getData(), "product_info.product_name");
    }

    public function getJsonData(): array
    {
        return [
            "ids" => $this->getIds(),
            "messages" => Hash::get($this->getData(), "messages"),
            "member_data" => Hash::get($this->getData(), "member_data"),
            "product_info" => Hash::get($this->getData(), "product_info"),
        ];
    }

    /**
     * @param array $data
     * @return array
     */
    public function initialize(array $data): array
    {
        return $data;
    }
}
