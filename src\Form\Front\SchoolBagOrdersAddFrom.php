<?php

namespace App\Form\Front;

use App\Enums\EFormFieldType;
use App\Form\ApiForm;
use App\Validation\Validator\EECOrderFormValidator;
use Cake\Form\Schema;
use Cake\Validation\Validator;

class SchoolBagOrdersAddFrom extends ApiForm
{

    public function _buildSchema(Schema $schema): Schema
    {
        $schema->addField('access_token', EFormFieldType::STRING->value);
        return $schema;
    }

    public function validationDefault(Validator $validator): Validator
    {
        return EECOrderFormValidator::buildValidator($validator, [
            'access_token'
        ]);
    }

    protected function _getAllowFieldsPath(): array
    {
        return ['access_token'];
    }
}
