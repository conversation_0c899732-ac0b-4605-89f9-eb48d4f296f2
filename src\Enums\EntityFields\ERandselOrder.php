<?php

namespace App\Enums\EntityFields;

enum ERandselOrder: string
{
    case ID = 'id';
    case MAKER_ID = 'maker_id';
    case MEMBER_ID = 'member_id';
    case GENERAL_USER_ID = 'general_user_id';
    case PRODUCT_ID = 'product_id';
    case TYPE = 'type';
    case PRODUCT_NAME = 'product_name';
    case PRICE = 'price';
    case STATUS = 'status';
    case STATUS_MODIFIED = 'status_modified';
    case APPROVAL_TYPE = 'approval_type';
    case IS_CONFIRMED = 'is_confirmed';
    case CONFIRMED = 'confirmed';
    case NAME1 = 'name1';
    case NAME2 = 'name2';
    case NAME1_HURIGANA = 'name1_hurigana';
    case NAME2_HURIGANA = 'name2_hurigana';
    case ZIP_CODE = 'zip_code';
    case TDFK_CD = 'tdfk_cd';
    case ADDRESS1 = 'address1';
    case ADDRESS2 = 'address2';
    case ADDRESS3 = 'address3';
    case TEL = 'tel';
    case EMAIL = 'email';
    case EMAIL_SEND_NG_FLG = 'email_send_ng_flg';
    case SURVEY_JSON = 'survey_json';
    case CREATED = 'created';
    case MODIFIED = 'modified';

    /**
     * @return string
     */
    public function description(): string
    {
        return match ($this) {
            self::ID => '注文ID（kuroco 注文IDと同じ）',
            self::MAKER_ID => 'メーカーID',
            self::MEMBER_ID => 'メンバーID（kuroco メンバーIDと同じ）',
            self::GENERAL_USER_ID => '一般ユーザーID',
            self::PRODUCT_ID => '注文商品ID',
            self::TYPE => 'カタログタイプ(1:紙, 2:デジタル)',
            self::PRODUCT_NAME => '注文商品名',
            self::PRICE => '金額',
            self::STATUS => '承認状態(1: 承認, 2: 否認, 0: 承認待ち)',
            self::STATUS_MODIFIED => '承認日時',
            self::APPROVAL_TYPE => '承認タイプ(1: csv, 2: 画面, 3: 自動)',
            self::IS_CONFIRMED => '請求確定状態(0: 未確定, 1: 確定)',
            self::CONFIRMED => '請求確定日時',
            self::NAME1 => '姓（暗号化）',
            self::NAME2 => '名（暗号化）',
            self::NAME1_HURIGANA => '姓（ふりがな）（暗号化）',
            self::NAME2_HURIGANA => '名（ふりがな）（暗号化）',
            self::ZIP_CODE => '郵便番号（半角数字7桁）（暗号化）',
            self::TDFK_CD => '都道府県コード（暗号化）',
            self::ADDRESS1 => '住所1（暗号化）',
            self::ADDRESS2 => '住所2（暗号化）',
            self::ADDRESS3 => '住所3（暗号化）',
            self::TEL => '電話番号（暗号化）',
            self::EMAIL => 'メールアドレス（暗号化）',
            self::EMAIL_SEND_NG_FLG => 'メルマガ拒否フラグ (0: 送信可, 1: 送信不可)',
            self::SURVEY_JSON => 'アンケートJSON形式',
            self::CREATED => '作成日時（注文日時）',
            self::MODIFIED => '更新日時',
        };
    }
}
