<script setup lang="ts">
import { ref } from "vue";
import { useRoute } from "vue-router";
import TheFormalRegist from "~/components/member/TheFormalRegist.vue";
import { useAuthStore, isLogin } from "~~/src/stores/auth";

const authStore = useAuthStore();
if (isLogin()) {
    authStore.clearAuth();
}

// ルートを取得
const route = useRoute(); //this.$route

// クエリパラメータのtを格納する変数を定義
const queryToken = ref(route.query.t as string);
</script>

<template>
    <!-- <div>{{ $route.query.t }}</div> -->
    <the-formal-regist :query-token="queryToken"></the-formal-regist>
</template>

<style scoped></style>
