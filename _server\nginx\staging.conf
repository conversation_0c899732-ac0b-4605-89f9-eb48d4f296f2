server {
    server_name  stg.coverme.jp;
    root         /usr/share/nginx/html/wordpress;

    client_max_body_size 20M;

    # Load configuration files for the default server block.
    include /etc/nginx/default.d/*.conf;

    error_page 400 403 404 /40x.html;

    location = /40x.html {
        root /usr/share/nginx/html/wordpress;
    }

    error_page 500 = /500.json;
    location /500.json {
        return 500 "{\"error\":\"System Error\",\"status\":500}";
    }

    location / {
        # スラッグ、パーマリンク対応
        try_files $uri $uri/ /index.php?$args;
    }

    location ~* /\. {
        deny all;
    }

    # CakePHPの設定
    location /app-api {
        alias /opt/coverme/webroot;
        try_files $uri $uri/ /app-api/index.php?$args;
        index index.php index.html;

        # 静的ファイルの処理
        location ~* \.(jpg|jpeg|gif|css|png|js|ico|html|svg|woff|woff2)$ {
            access_log off;
            expires max;
            log_not_found off;
        }

        # PHPファイルの処理
        location ~ ^/app-api/.+\.php$ {
            fastcgi_split_path_info ^/app-api(.+?\.php)(/.*)$;
            fastcgi_pass php-fpm;
            fastcgi_index index.php;
            include fastcgi_params;
            fastcgi_param SCRIPT_FILENAME /opt/coverme/webroot/index.php;
            fastcgi_param APPLICATION_ENV staging;
            fastcgi_buffers 256 128k;
            fastcgi_buffer_size 128k;
            fastcgi_intercept_errors on;
            fastcgi_read_timeout 120s;
            proxy_pass_header Authorization;
        }

        # CakePHPのルーティング対応
        if (!-e $request_filename) {
            rewrite ^/app-api/(.*)$ /app-api/index.php?$1 last;
        }
    }

    # Nuxtコンテンツ
    location /catalog {
        alias /opt/coverme/app-nuxt/.output/public;
        try_files $uri $uri/ /index.html;
    }

}
