# ユーザー管理システムの仕様
1. マルチユーザータイプ設計
    * 3種類のユーザータイプ（一般ユーザー、SWB管理者、メーカーユーザー）
    * 各ユーザータイプは独立したテーブルで管理
    * 各ユーザータイプ専用のトークン管理テーブル
2. 認証システム
    * メールアドレスとパスワードによる認証
    * パスワードはハッシュ化して保存
    * トークンベースの認証（パスワードリセット、API認証など）
    * トークンには有効期限あり
3. 一般ユーザーの登録フロー
    * 仮登録→メール認証→本登録の2段階認証プロセス
    * 仮登録情報は24時間の有効期限付き
    * 同一メールアドレスでの複数仮登録に対応
4. 個人情報管理
    * 個人情報は暗号化して保存
    * プロフィール情報とアンケート情報は分離して管理
    * 論理削除によるユーザー情報の保持
5. 権限管理
    * SWB管理者は権限レベルによる機能制限
    * メーカーユーザーはメーカーIDによる情報アクセス制限
6. Kuroco移行対応
    * 既存Kurocoの *一般*ユーザーの移行機能
    * パスワードnullのユーザーはKurocoユーザーとして識別、今まで通りのKurocoシステム認証のログインフローを維持
    * 初回ログイン時にパスワード設定を促す仕組み

実装時の注意点：
* 引き続きcakephp の Authenticationプラグインを使用
* 既存のコントローラーやミドルウェアの構造を維持
* 暗号化キーについて、今まで通りの`Security::getSalt()`を使用
* 一般ユーザー、SWB管理者、メーカーユーザーの認証ロジックは共通化、コントローラー今まで通りUserAuthenticationsController、SwbAuthenticationsController、ClientAuthenticationsControllerで分ける
* 既存Kurocoの *一般*ユーザーはまだKuroco認証システムでログイン可能、既存のSWB管理者、メーカーユーザーは完全に本システムの認証システムに切り替えて問題ない
* トークンの発行・検証ロジックは共通化

## トークン管理システムの設計と実装方針（優先度: 高）
フェーズ1: 基本認証機能の完成（優先度: 高）
* NewUserAuthenticationsControllerの完成
* 認証フォームクラスの実装
* 認証サービスの拡張

フェーズ2: API認証システムの強化（優先度: 高）
* トークンベース認証の強化
* 認証ミドルウェアの統合

フェーズ3: テストスイートの実装（優先度: 中）
* ユニットテスト・統合テストの実装

フェーズ4: Kuroco移行対応（優先度: 中）
* 移行ロジックの実装