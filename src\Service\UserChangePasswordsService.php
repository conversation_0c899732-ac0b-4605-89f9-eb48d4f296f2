<?php

namespace App\Service;

use App\Enums\EntityFields\ESchoolBagForm;
use App\Kuroko\Entity\Member;
use App\Mailer\AppMailer;
use App\Mailer\Sender\ToUser\PasswordResetCompletedSender;
use Cake\Utility\Hash;

class UserChangePasswordsService extends ChangePasswordsService
{
    protected function sendPasswordResetCompleted(Member $member): void
    {
        AppMailer::sendToUser(new PasswordResetCompletedSender(
            Hash::get($member->getJsonData(), ESchoolBagForm::EMAIL->value),
            Hash::get($member->getJsonData(), ESchoolBagForm::NAME1->value)
        ));
    }
}
