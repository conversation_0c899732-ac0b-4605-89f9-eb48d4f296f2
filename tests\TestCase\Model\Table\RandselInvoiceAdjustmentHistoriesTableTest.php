<?php
declare(strict_types=1);

namespace App\Test\TestCase\Model\Table;

use App\Model\Table\RandselInvoiceAdjustmentHistoriesTable;
use Cake\TestSuite\TestCase;

/**
 * App\Model\Table\RandselInvoiceAdjustmentHistoriesTable Test Case
 */
class RandselInvoiceAdjustmentHistoriesTableTest extends TestCase
{
    /**
     * Test subject
     *
     * @var \App\Model\Table\RandselInvoiceAdjustmentHistoriesTable
     */
    protected $RandselInvoiceAdjustmentHistories;

    /**
     * Fixtures
     *
     * @var array<string>
     */
    protected $fixtures = [
        'app.RandselInvoiceAdjustmentHistories',
        'app.RandselInvoiceAdjustments',
    ];

    /**
     * setUp method
     *
     * @return void
     */
    protected function setUp(): void
    {
        parent::setUp();
        $config = $this->getTableLocator()->exists('RandselInvoiceAdjustmentHistories') ? [] : ['className' => RandselInvoiceAdjustmentHistoriesTable::class];
        $this->RandselInvoiceAdjustmentHistories = $this->getTableLocator()->get('RandselInvoiceAdjustmentHistories', $config);
    }

    /**
     * tearDown method
     *
     * @return void
     */
    protected function tearDown(): void
    {
        unset($this->RandselInvoiceAdjustmentHistories);

        parent::tearDown();
    }

    /**
     * Test validationDefault method
     *
     * @return void
     * @uses \App\Model\Table\RandselInvoiceAdjustmentHistoriesTable::validationDefault()
     */
    public function testValidationDefault(): void
    {
        $this->markTestIncomplete('Not implemented yet.');
    }

    /**
     * Test buildRules method
     *
     * @return void
     * @uses \App\Model\Table\RandselInvoiceAdjustmentHistoriesTable::buildRules()
     */
    public function testBuildRules(): void
    {
        $this->markTestIncomplete('Not implemented yet.');
    }
}
