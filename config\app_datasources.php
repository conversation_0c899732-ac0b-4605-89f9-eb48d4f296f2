<?php

use App\Config\DotEnvConfig;
use Cake\Database\Connection;
use Cake\Database\Driver\Mysql;

return [
    "Datasources" => [
        'default' => [
            'className' => Connection::class,
            'driver' => Mysql::class,
            'persistent' => false,
            'timezone' => 'Asia/Tokyo',
            'encoding' => 'utf8mb4',

            'flags' => [],
            'cacheMetadata' => true,
            'log' => true,
            'quoteIdentifiers' => false,
            //'init' => ['SET GLOBAL innodb_stats_on_metadata = 0'],

            'host' => DotEnvConfig::read('DATABASE_DEFAULT_HOST'),
            'port' => DotEnvConfig::read('DATABASE_DEFAULT_PORT'),
            'username' => DotEnvConfig::read('DATABASE_DEFAULT_USERNAME'),
            'password' => DotEnvConfig::read('DATABASE_DEFAULT_PASSWORD'),
            'database' => DotEnvConfig::read('DATABASE_DEFAULT_NAME'),
        ],
        'test' => [
            'className' => Connection::class,
            'driver' => Mysql::class,
            'persistent' => false,
            'timezone' => 'Asia/Tokyo',
            'encoding' => 'utf8mb4',
            'flags' => [],
            'cacheMetadata' => true,
            'quoteIdentifiers' => false,
            'log' => false,
            //'init' => ['SET GLOBAL innodb_stats_on_metadata = 0'],
            'host' => DotEnvConfig::read('DATABASE_TEST_HOST'),
            'port' => DotEnvConfig::read('DATABASE_TEST_PORT'),
            'username' => DotEnvConfig::read('DATABASE_TEST_USERNAME'),
            'password' => DotEnvConfig::read('DATABASE_TEST_PASSWORD'),
            'database' => DotEnvConfig::read('DATABASE_TEST_NAME'),
        ],
    ]
];
