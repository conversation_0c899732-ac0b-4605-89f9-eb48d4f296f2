<?php

namespace App\Controller\Front;

use App\Controller\FrontController;
use App\Form\DisableParametersForm;
use App\Form\Front\UserDetailsEditForm;
use App\Kuroko\Entity\Member;
use App\Service\UserDetailsService;
use Cake\Event\Event;
use Cake\Log\Log;

class UserDetailsController extends FrontController
{


    /**
     * ユーザ情報
     * @return void
     */
    public function index(): void
    {
        $this->_onSuccess(function (Event $event, Member $member) {
            // 成功時にはトークンとユーザ情報を返却する
            $this->_setSuccessResponse([
                'member' => $member,
            ]);
        });
        $this->_onError(function (Event $event, array $errors) {
            Log::debug(print_r($errors, true));
        });
        $this->_index((new UserDetailsService())
            ->setIdentity($this->Authentication->getIdentity())
            ->setForm(
                new DisableParametersForm()
            ));
    }

    /**
     * @param $id int dummy
     * @return void
     */
    public function edit($id = null): void
    {
        $this->_onSuccess(function (Event $event) {
            $this->_setSuccessResponse([
                'success' => true,
            ]);
        });
        $this->_onError(function (Event $event, array $errors) {
            Log::debug(print_r($errors, true));
        });
        $this->_edit($id, (new UserDetailsService())
            ->setIdentity($this->Authentication->getIdentity())
            ->setForm(
                new DisableParametersForm()
            ));
    }
}
