<?php
declare(strict_types=1);

namespace App\Model\Table;

use Cake\ORM\Query;
use Cake\ORM\RulesChecker;
use Cake\ORM\Table;
use Cake\Validation\Validator;

/**
 * メーカー店舗テーブル
 */
class MakerStoresTable extends Table
{
    public function initialize(array $config): void
    {
        parent::initialize($config);

        $this->setTable('maker_stores');
        $this->setDisplayField('name');
        $this->setPrimaryKey('id');

        // アソシエーション
        $this->belongsTo('Makers', [
            'foreignKey' => 'maker_id',
            'joinType' => 'INNER',
        ]);

        // 論理削除ビヘイビア
        $this->addBehavior('Muffin/Trash.Trash', [
            'field' => 'deleted',
            'events' => ['Model.beforeFind', 'Model.beforeDelete']
        ]);
    }

    public function validationDefault(Validator $validator): Validator
    {
        $validator
            ->integer('maker_id')
            ->requirePresence('maker_id', 'create')
            ->notEmptyString('maker_id');

        $validator
            ->scalar('name')
            ->maxLength('name', 255)
            ->requirePresence('name', 'create', __('店舗名は必須です'))
            ->notEmptyString('name', __('店舗名は必須です'));

        $validator
            ->scalar('zip_code')
            ->maxLength('zip_code', 10)
            ->allowEmptyString('zip_code')
            ->add('zip_code', 'custom', [
                'rule' => function ($value, $context) {
                    if (empty($value)) {
                        return true;
                    }
                    // 郵便番号の形式チェック（123-4567 または 1234567）
                    return preg_match('/^\d{3}-?\d{4}$/', $value);
                },
                'message' => __('郵便番号の形式が正しくありません（例：123-4567）')
            ]);

        $validator
            ->scalar('prefecture')
            ->maxLength('prefecture', 50)
            ->allowEmptyString('prefecture');

        $validator
            ->scalar('city')
            ->maxLength('city', 100)
            ->allowEmptyString('city');

        $validator
            ->scalar('address')
            ->maxLength('address', 255)
            ->allowEmptyString('address');

        $validator
            ->scalar('building')
            ->maxLength('building', 255)
            ->allowEmptyString('building');

        $validator
            ->scalar('tel')
            ->maxLength('tel', 20)
            ->allowEmptyString('tel')
            ->add('tel', 'custom', [
                'rule' => function ($value, $context) {
                    if (empty($value)) {
                        return true;
                    }
                    // 電話番号の形式チェック
                    return preg_match('/^[\d\-\(\)\+\s]+$/', $value);
                },
                'message' => __('電話番号の形式が正しくありません')
            ]);

        $validator
            ->email('email', false, __('有効なメールアドレスを入力してください'))
            ->allowEmptyString('email');

        $validator
            ->scalar('business_hours')
            ->allowEmptyString('business_hours');

        $validator
            ->scalar('holiday')
            ->allowEmptyString('holiday');

        $validator
            ->scalar('access_info')
            ->allowEmptyString('access_info');

        $validator
            ->scalar('description')
            ->allowEmptyString('description');

        return $validator;
    }

    public function buildRules(RulesChecker $rules): RulesChecker
    {
        $rules->add($rules->existsIn(['maker_id'], 'Makers'), __('指定されたメーカーが存在しません'));

        return $rules;
    }

    /**
     * アクティブな店舗を取得するファインダー
     */
    public function findActive(Query $query, array $options): Query
    {
        return $query->where(['deleted IS' => null]);
    }

    /**
     * メーカー別に店舗を取得するファインダー
     */
    public function findByMaker(Query $query, array $options): Query
    {
        $makerId = $options['maker_id'] ?? null;
        if ($makerId !== null) {
            $query->where(['maker_id' => $makerId]);
        }
        return $query;
    }

    /**
     * 都道府県別に店舗を取得するファインダー
     */
    public function findByPrefecture(Query $query, array $options): Query
    {
        $prefecture = $options['prefecture'] ?? null;
        if (!empty($prefecture)) {
            $query->where(['prefecture' => $prefecture]);
        }
        return $query;
    }

    /**
     * 店舗名で検索するファインダー
     */
    public function findByName(Query $query, array $options): Query
    {
        $name = $options['name'] ?? null;
        if (!empty($name)) {
            $query->where(['name LIKE' => '%' . $name . '%']);
        }
        return $query;
    }

    /**
     * 指定メーカーのアクティブな店舗一覧を取得
     */
    public function getActiveStoresByMaker(int $makerId): Query
    {
        return $this->find('active')
            ->where(['maker_id' => $makerId])
            ->order(['name' => 'ASC']);
    }
}
