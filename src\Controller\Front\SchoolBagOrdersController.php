<?php

namespace App\Controller\Front;

use App\Controller\FrontController;
use App\Form\Front\SchoolBagOrdersAddFrom;
use App\Model\Entity\GeneralUser;
use App\Service\SchoolBagOrdersService;
use Cake\Event\Event;
use Cake\Event\EventInterface;
use Cake\Log\Log;

class SchoolBagOrdersController extends FrontController
{

    public function beforeFilter(EventInterface $event): void
    {
        parent::beforeFilter($event);
        $this->Authentication->addUnauthenticatedActions(['add']);
    }


    public function add(): void
    {
        $this->_onSuccess(function (Event $event, GeneralUser $user) {
            $this->_setSuccessResponse([
                'success' => true,
                'member' => $user->email
            ]);
        });
        $this->_onError(function (Event $event, array $errors) {
            Log::debug(print_r($errors, true));
        });
        $this->_add(
            (new SchoolBagOrdersService())->setForm(
                new SchoolBagOrdersAddFrom()
            )
        );
    }
}
