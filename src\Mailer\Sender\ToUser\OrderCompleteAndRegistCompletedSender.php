<?php

namespace App\Mailer\Sender\ToUser;

use App\Mailer\Sender\IToUserSender;
use App\Mailer\Sender\SenderTrait;
use App\Mailer\Sender\ToUserSenderTrait;

class OrderCompleteAndRegistCompletedSender implements IToUserSender
{
    use SenderTrait;
    use ToUserSenderTrait;

    public function getSubject(): string
    {
        return "【カバーミー】カタログ申込・カバーミー会員登録完了のお知らせ";
    }

    public function getTemplate(): string
    {
        return "to_user/OrderCompleteAndRegistCompletedSender";
    }
}
