<script setup lang="ts">
import { ClientMyBridge } from "~~/src/models/bridge/ClientMyBridge";
import { computed } from "vue";
import { format } from "date-fns";

const props = defineProps<{
    bridge: ClientMyBridge;
}>();

const headers = [
    { title: "年月", align: "start", key: "yearMonth", width: "10%" },
    {
        title: "カタログ注文数",
        align: "end",
        key: "totalOrders",
        width: "30%",
        sortable: false,
    },
    {
        title: "承認数",
        align: "end",
        key: "approvalCount",
        width: "30%",
        sortable: false,
    },
    {
        title: "請求（予定）額",
        align: "end",
        key: "totalPrice",
        width: "30%",
        sortable: false,
    },
];

const orders = props.bridge.report_orders;

//TODO:集計などの処理はクラスに切り出してそっちでやった方がいい
interface ReportData {
    yearMonth: string;
    totalOrders: number;
    approvalCount: number;
    totalPrice: number;
}

const startDate = new Date(props.bridge.report_to);
const endDate = new Date(props.bridge.report_from);
endDate.setHours(23, 59, 59, 999);
const isInRange = (date: Date): boolean => startDate <= date && date <= endDate;
const formatYM = (date: Date): string => format(date, "yyyy年MM月");

const aggregatedData = computed(() => {
    const aggregationMap: Record<string, ReportData> = orders.reduce(
        (acc: Record<string, ReportData>, order) => {
            const createdDate = new Date(order.created);
            const createdYearMonth = formatYM(createdDate);

            if (isInRange(createdDate)) {
                acc[createdYearMonth] = acc[createdYearMonth] || {
                    yearMonth: createdYearMonth,
                    totalOrders: 0,
                    approvalCount: 0,
                    totalPrice: 0,
                };
                acc[createdYearMonth].totalOrders++;
            }
            if (order.status_modified) {
                const modifiedDate = new Date(order.status_modified);
                const modifiedYearMonth = formatYM(modifiedDate);
                if (isInRange(modifiedDate)) {
                    acc[modifiedYearMonth] = acc[modifiedYearMonth] || {
                        yearMonth: modifiedYearMonth,
                        totalOrders: 0,
                        approvalCount: 0,
                        totalPrice: 0,
                    };
                    if (order.status === 1) {
                        acc[modifiedYearMonth].approvalCount++;
                        acc[modifiedYearMonth].totalPrice += order.price;
                    }
                }
            }
            return acc;
        },
        {},
    );
    return Object.values(aggregationMap);
});
</script>

<template>
    <div class="monthly-report-wrapper mt-10">
        <v-data-table
            :headers="headers"
            :items="aggregatedData"
            class="elevation-1"
            :sort-by="[{ key: 'yearMonth', order: 'desc' }]"
            :loading="!bridge.is_report_loaded"
        >
            <template #[`item.yearMonth`]="{ item }">
                <span class="color-gray">
                    {{ item.yearMonth }}
                </span>
            </template>
            <template #[`item.totalOrders`]="{ item }">
                <span class="color-gray">
                    {{ item.totalOrders.toLocaleString() }}件
                </span>
            </template>
            <template #[`item.approvalCount`]="{ item }">
                <span class="color-gray">
                    {{ item.approvalCount.toLocaleString() }}件</span
                >
            </template>
            <template #[`item.totalPrice`]="{ item }">
                <span class="color-gray">
                    {{ item.totalPrice.toLocaleString() }}円</span
                >
            </template>
            <template #bottom></template>
        </v-data-table>
    </div>
</template>

<style scoped>
.monthly-report-wrapper {
    position: inherit;
}
.color-gray {
    color: #575757;
}
.v-data-table th {
    font-weight: bold; /* 太字に設定 */
}
</style>
