import CovermeFrontApiClient from "~/src/lib/http/CovermeFrontApiClient";
import axios from "axios";

export default class SwbInvoicePdfApiClient extends CovermeFrontApiClient {
    protected getResource(): string {
        return "swb-invoice-pdf.pdf"; // 元々のリソース名
    }

    public async downloadPdf<T, Blob>(data?: T): Promise<Blob> {
        // レスポンスの型を Blob に
        return new Promise((resolve, reject) => {
            axios
                .post(
                    `${this.config.public.frontApiBase}${this.getResource()}`, // .pdf は getResource() で付与済み
                    data,
                    {
                        headers: this.getHeader(),
                        responseType: "blob", // PDF を Blob として受け取る
                    },
                )
                .then((response) => {
                    resolve(response.data as Blob);
                })
                .catch(reject);
        });
    }
}
