<?php

namespace App\Middleware;

use Authentication\Middleware\AuthenticationMiddleware;
use Cake\Http\ServerRequest;
use Psr\Http\Message\ResponseInterface;
use Psr\Http\Message\ServerRequestInterface;
use Psr\Http\Server\RequestHandlerInterface;

class UserAuthenticationMiddleware extends AuthenticationMiddleware
{
    /**
     * @param ServerRequestInterface $request
     * @param RequestHandlerInterface $handler
     * @return ResponseInterface
     */
    public function process(ServerRequestInterface $request, RequestHandlerInterface $handler): ResponseInterface
    {
        if ($request instanceof ServerRequest) {
            if ($request->getParam("prefix") === "Front") {
                // front側だけでの利用
                return parent::process($request, $handler);
            }
        }
        return $handler->handle($request);

    }
}
