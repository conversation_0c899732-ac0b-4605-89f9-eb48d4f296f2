<?php

namespace App\Mailer\Sender\ToUser;

use App\Mailer\Sender\IToUserSender;
use App\Mailer\Sender\SenderTrait;
use App\Mailer\Sender\ToUserSenderTrait;

class UserDetailUpdateCompletedSender implements IToUserSender
{
    use SenderTrait;
    use ToUserSenderTrait;

    public function getSubject(): string
    {
        return "【カバーミー】登録情報変更完了のお知らせ";
    }

    public function getTemplate(): string
    {
        return "to_user/UserDetailUpdateCompletedSender";
    }
}
