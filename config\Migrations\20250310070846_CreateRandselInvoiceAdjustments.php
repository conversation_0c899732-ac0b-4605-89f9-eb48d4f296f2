<?php

declare(strict_types=1);

use Migrations\AbstractMigration;

class CreateRandselInvoiceAdjustments extends AbstractMigration
{
    /**
     * Change Method.
     *
     * More information on this method is available here:
     * https://book.cakephp.org/phinx/0/en/migrations.html#the-change-method
     * @return void
     */
    public function change(): void
    {
        $table = $this->table('randsel_invoice_adjustments', ['comment' => 'ランドセル請求調整金額']);
        $table->addColumn('maker_id', 'integer', [
            'null' => false,
            'comment' => 'メーカーID',
            'default' => null,
            'limit' => null,
        ])
            ->addColumn('product_id', 'integer', [
                'null' => false,
                'comment' => '商品ID',
                'default' => null,
                'limit' => null,
            ])
            ->addColumn('billing_year_month', 'string', [
                'null' => false,
                'comment' => '請求年月 (YYYY-MM)',
                'default' => null,
                'limit' => 7,
            ])
            ->addColumn('adjustment_unit_price', 'integer', [
                'null' => false,
                'comment' => '調整単価',
                'default' => 0,
                'limit' => null,
            ])
            ->addColumn('adjustment_quantity', 'integer', [
                'null' => false,
                'comment' => '調整数量',
                'default' => 0,
                'limit' => null,
            ])
            ->addColumn('adjustment_note', 'text', [
                'null' => true,
                'comment' => '備考内容',
                'default' => null,
                'limit' => null,
            ])
            ->addColumn('is_confirmed', 'boolean', [
                'null' => true, // TINYINT(1) NULL DEFAULT '0' を boolean NULL DEFAULT FALSE で表現
                'comment' => '確定状態(0: 未確定, 1: 確定)',
                'default' => false,
                'limit' => null,
            ])
            ->addColumn('confirmed_by', 'integer', [
                'null' => true,
                'comment' => '確定者(kuroco member_id)',
                'default' => null,
                'limit' => null,
            ])
            ->addColumn('confirmed', 'datetime', [
                'null' => true,
                'comment' => '確定日時',
                'default' => null,
                'limit' => null,
            ])
            ->addColumn('created_by', 'integer', [
                'null' => true,
                'comment' => '作成者・更新者(kuroco member_id)',
                'default' => null,
                'limit' => null,
            ])
            ->addColumn('created', 'datetime', [
                'null' => true, // DATETIME NULL DEFAULT CURRENT_TIMESTAMP を datetime NULL で表現
                'comment' => '作成日時',
                'default' => 'CURRENT_TIMESTAMP', // デフォルト値はDB側で設定される
                'limit' => null,
            ])
            ->addColumn('modified', 'datetime', [
                'null' => true, // DATETIME NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP を datetime NULL で表現
                'comment' => '更新日時',
                'default' => 'CURRENT_TIMESTAMP', // デフォルト値はDB側で設定される
                'limit' => null,
            ])
            ->addIndex(
                ['maker_id', 'product_id', 'billing_year_month'],
                ['unique' => true, 'name' => 'uniq_maker_product_billing_year_month']
            )
            ->addIndex(
                ['maker_id'],
                ['name' => 'idx_maker_id']
            )
            ->addIndex(
                ['product_id'],
                ['name' => 'idx_product_id']
            )
            ->addIndex(
                ['billing_year_month'],
                ['name' => 'idx_billing_year_month']
            )
            ->create();
    }
}
