<?php

namespace App\Command;

use App\Service\ProductDisplayService;
use App\Service\ProductPerformanceService;
use App\Model\Table\ProductsTable;
use Cake\Command\Command;
use Cake\Console\Arguments;
use Cake\Console\ConsoleIo;
use Cake\Console\ConsoleOptionParser;
use Cake\ORM\TableRegistry;

/**
 * 商品表示制御のパフォーマンステストコマンド
 * 
 * リアルタイム計算の性能を測定・評価する
 * 
 * 使用例:
 * docker-compose exec amazonlinux2 php bin/cake product_performance_test
 * docker-compose exec amazonlinux2 php bin/cake product_performance_test --products=10
 * docker-compose exec amazonlinux2 php bin/cake product_performance_test --report
 */
class ProductPerformanceTestCommand extends Command
{
    /**
     * @var ProductDisplayService
     */
    private ProductDisplayService $productDisplayService;

    /**
     * @var ProductPerformanceService
     */
    private ProductPerformanceService $performanceService;

    /**
     * @var ProductsTable
     */
    private ProductsTable $productsTable;

    public function initialize(): void
    {
        parent::initialize();
        $this->productDisplayService = new ProductDisplayService();
        $this->performanceService = new ProductPerformanceService();
        $this->productsTable = TableRegistry::getTableLocator()->get('Products');
    }

    /**
     * コマンドオプションの設定
     */
    protected function buildOptionParser(ConsoleOptionParser $parser): ConsoleOptionParser
    {
        $parser
            ->setDescription('商品表示制御のパフォーマンステストを実行します')
            ->addOption('products', [
                'help' => 'テストする商品数（デフォルト: 全商品）',
                'short' => 'p',
                'default' => 0,
            ])
            ->addOption('iterations', [
                'help' => '各商品のテスト回数（デフォルト: 3回）',
                'short' => 'i',
                'default' => 3,
            ])
            ->addOption('report', [
                'help' => 'パフォーマンスレポートのみ表示',
                'boolean' => true,
                'default' => false,
            ])
            ->addOption('reset', [
                'help' => 'パフォーマンス統計をリセット',
                'boolean' => true,
                'default' => false,
            ]);

        return $parser;
    }

    /**
     * コマンド実行
     */
    public function execute(Arguments $args, ConsoleIo $io): int
    {
        $io->out('<info>商品表示制御パフォーマンステストを開始します</info>');
        $io->hr();

        try {
            // 統計リセット
            if ($args->getOption('reset')) {
                $this->performanceService->resetStats();
                $io->out('<success>パフォーマンス統計をリセットしました</success>');
                return static::CODE_SUCCESS;
            }

            // レポートのみ表示
            if ($args->getOption('report')) {
                $this->displayPerformanceReport($io);
                return static::CODE_SUCCESS;
            }

            // パフォーマンステスト実行
            $productCount = (int)$args->getOption('products');
            $iterations = (int)$args->getOption('iterations');
            
            $this->runPerformanceTest($io, $productCount, $iterations);
            
            // 結果レポート表示
            $io->hr();
            $this->displayPerformanceReport($io);
            
            $io->out('<success>パフォーマンステストが完了しました</success>');
            
            return static::CODE_SUCCESS;
            
        } catch (\Exception $e) {
            $io->error('エラーが発生しました: ' . $e->getMessage());
            $io->error('スタックトレース: ' . $e->getTraceAsString());
            
            return static::CODE_ERROR;
        }
    }

    /**
     * パフォーマンステストを実行
     */
    private function runPerformanceTest(ConsoleIo $io, int $productCount, int $iterations): void
    {
        // テスト対象商品を取得
        $query = $this->productsTable
            ->find()
            ->where(['is_display' => true])
            ->order(['sort_order' => 'ASC']);
            
        if ($productCount > 0) {
            $query->limit($productCount);
        }
        
        $products = $query->toArray();
        $totalProducts = count($products);
        
        $io->out(sprintf('<info>テスト対象商品数: %d</info>', $totalProducts));
        $io->out(sprintf('<info>各商品のテスト回数: %d</info>', $iterations));
        $io->hr();

        // 商品一覧生成テスト
        $io->out('<info>商品一覧生成テストを実行中...</info>');
        $this->productDisplayService->getAvailableProducts();
        $io->out('<success>商品一覧生成テスト完了</success>');

        // 個別商品テスト
        $io->out('<info>個別商品表示タイプ判定テストを実行中...</info>');
        
        $progressBar = $io->helper('Progress');
        $progressBar->init([
            'total' => $totalProducts * $iterations,
            'width' => 40,
        ]);
        
        foreach ($products as $product) {
            for ($i = 0; $i < $iterations; $i++) {
                $this->productDisplayService->determineDisplayType($product);
                $progressBar->increment(1);
                $progressBar->draw();
            }
        }
        
        $io->out('');
        $io->out('<success>個別商品テスト完了</success>');
    }

    /**
     * パフォーマンスレポートを表示
     */
    private function displayPerformanceReport(ConsoleIo $io): void
    {
        $io->out('<info>パフォーマンスレポート:</info>');
        
        $report = $this->performanceService->generatePerformanceReport();
        
        // サマリー表示
        $summary = $report['summary'];
        $io->out(sprintf('  総リクエスト数: %d', $summary['total_requests']));
        $io->out(sprintf('  平均応答時間: %sms', $summary['avg_response_time_ms']));
        $io->out(sprintf('  最大応答時間: %sms', $summary['max_response_time_ms']));
        $io->out(sprintf('  最小応答時間: %sms', $summary['min_response_time_ms']));
        $io->out(sprintf('  平均DB クエリ数: %s', $summary['avg_db_queries']));
        
        // パフォーマンスグレード
        $grade = $report['performance_grade'];
        $gradeColor = $this->getGradeColor($grade);
        $io->out(sprintf('  パフォーマンスグレード: <%s>%s</%s>', $gradeColor, $grade, $gradeColor));
        
        // 推奨事項
        $io->out('');
        $io->out('<info>推奨事項:</info>');
        foreach ($report['recommendations'] as $recommendation) {
            $io->out('  • ' . $recommendation);
        }
        
        // 詳細統計
        $stats = $this->performanceService->getPerformanceStats();
        $io->out('');
        $io->out('<info>詳細統計:</info>');
        $io->out(sprintf('  稼働時間: %s秒', $stats['uptime_seconds'] ?? 0));
        $io->out(sprintf('  総処理時間: %sms', round($stats['total_time_ms'], 2)));
        
        // パフォーマンス判定
        $avgTime = $summary['avg_response_time_ms'];
        if ($avgTime <= 25) {
            $io->out('<success>✓ パフォーマンスは良好です</success>');
        } elseif ($avgTime <= 100) {
            $io->out('<warning>⚠ パフォーマンスの改善を検討してください</warning>');
        } else {
            $io->out('<error>✗ パフォーマンスの改善が必要です</error>');
        }
    }

    /**
     * グレードに応じた色を取得
     */
    private function getGradeColor(string $grade): string
    {
        switch ($grade) {
            case 'A':
            case 'B':
                return 'success';
            case 'C':
            case 'D':
                return 'warning';
            case 'E':
            case 'F':
                return 'error';
            default:
                return 'info';
        }
    }
}
