<?php
declare(strict_types=1);

namespace App\Model\Entity;

use Cake\ORM\Entity;

/**
 * メーカー店舗エンティティ
 * 
 * @property int $id
 * @property int $maker_id
 * @property string $name
 * @property string|null $zip_code
 * @property string|null $prefecture
 * @property string|null $city
 * @property string|null $address
 * @property string|null $building
 * @property string|null $tel
 * @property string|null $email
 * @property string|null $business_hours
 * @property string|null $holiday
 * @property string|null $access_info
 * @property string|null $description
 * @property \Cake\I18n\FrozenTime|null $deleted
 * @property \Cake\I18n\FrozenTime $created
 * @property \Cake\I18n\FrozenTime $modified
 * @property \App\Model\Entity\Maker $maker
 */
class MakerStore extends Entity
{
    protected $_accessible = [
        'maker_id' => true,
        'name' => true,
        'zip_code' => true,
        'prefecture' => true,
        'city' => true,
        'address' => true,
        'building' => true,
        'tel' => true,
        'email' => true,
        'business_hours' => true,
        'holiday' => true,
        'access_info' => true,
        'description' => true,
        'deleted' => true,
        'created' => true,
        'modified' => true,
        'maker' => true,
    ];

    protected $_hidden = [];

    /**
     * 完全な住所を取得
     */
    public function getFullAddress(): string
    {
        $addressParts = array_filter([
            $this->prefecture,
            $this->city,
            $this->address,
            $this->building
        ]);
        
        return implode(' ', $addressParts);
    }

    /**
     * 郵便番号をフォーマット
     */
    public function getFormattedZipCode(): ?string
    {
        if (empty($this->zip_code)) {
            return null;
        }
        
        // 7桁の郵便番号を〒123-4567形式にフォーマット
        $zipCode = preg_replace('/[^0-9]/', '', $this->zip_code);
        if (strlen($zipCode) === 7) {
            return '〒' . substr($zipCode, 0, 3) . '-' . substr($zipCode, 3);
        }
        
        return '〒' . $this->zip_code;
    }

    /**
     * 論理削除されているかどうかを判定
     */
    public function isDeleted(): bool
    {
        return $this->deleted !== null;
    }

    /**
     * アクティブな店舗かどうかを判定
     */
    public function isActive(): bool
    {
        return !$this->isDeleted();
    }
}
