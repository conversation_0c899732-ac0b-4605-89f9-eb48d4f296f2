<?php
declare(strict_types=1);

namespace App\Model\Table;

use Cake\I18n\FrozenTime;
use Cake\ORM\Query;
use Cake\ORM\RulesChecker;
use Cake\ORM\Table;
use Cake\Validation\Validator;

/**
 * 展示会テーブル
 */
class ExhibitionsTable extends Table
{
    public function initialize(array $config): void
    {
        parent::initialize($config);

        $this->setTable('exhibitions');
        $this->setDisplayField('title');
        $this->setPrimaryKey('id');

        // アソシエーション
        $this->hasMany('ExhibitionMakers', [
            'foreignKey' => 'exhibition_id',
            'dependent' => true,
        ]);

        // 論理削除ビヘイビア
        $this->addBehavior('Muffin/Trash.Trash', [
            'field' => 'deleted',
            'events' => ['Model.beforeFind', 'Model.beforeDelete']
        ]);
    }

    public function validationDefault(Validator $validator): Validator
    {
        $validator
            ->scalar('title')
            ->maxLength('title', 255)
            ->requirePresence('title', 'create', __('展示会タイトルは必須です'))
            ->notEmptyString('title', __('展示会タイトルは必須です'));

        $validator
            ->scalar('description')
            ->allowEmptyString('description');

        $validator
            ->dateTime('start_datetime')
            ->requirePresence('start_datetime', 'create')
            ->notEmptyDateTime('start_datetime');

        $validator
            ->dateTime('end_datetime')
            ->requirePresence('end_datetime', 'create')
            ->notEmptyDateTime('end_datetime');

        $validator
            ->scalar('venue_name')
            ->maxLength('venue_name', 255)
            ->allowEmptyString('venue_name');

        $validator
            ->scalar('address')
            ->allowEmptyString('address');

        $validator
            ->scalar('access_info')
            ->allowEmptyString('access_info');

        $validator
            ->integer('capacity')
            ->allowEmptyString('capacity')
            ->greaterThan('capacity', 0, __('定員数は0より大きい値を入力してください'));

        $validator
            ->boolean('requires_reservation')
            ->notEmptyString('requires_reservation');

        $validator
            ->scalar('reservation_url')
            ->maxLength('reservation_url', 500)
            ->allowEmptyString('reservation_url')
            ->url('reservation_url', __('有効なURLを入力してください'));

        return $validator;
    }

    public function buildRules(RulesChecker $rules): RulesChecker
    {
        // 終了日時が開始日時より後であることをチェック
        $rules->add(function ($entity, $options) {
            if ($entity->start_datetime && $entity->end_datetime) {
                return $entity->end_datetime > $entity->start_datetime;
            }
            return true;
        }, 'validDateTimeRange', [
            'errorField' => 'end_datetime',
            'message' => __('終了日時は開始日時より後の日時を入力してください')
        ]);

        return $rules;
    }

    /**
     * アクティブな展示会を取得するファインダー
     */
    public function findActive(Query $query, array $options): Query
    {
        return $query->where(['deleted IS' => null]);
    }

    /**
     * 開催予定の展示会を取得するファインダー
     */
    public function findUpcoming(Query $query, array $options): Query
    {
        $now = FrozenTime::now();
        return $query->where([
            'start_datetime >' => $now,
            'deleted IS' => null
        ]);
    }

    /**
     * 現在開催中の展示会を取得するファインダー
     */
    public function findCurrentlyRunning(Query $query, array $options): Query
    {
        $now = FrozenTime::now();
        return $query->where([
            'start_datetime <=' => $now,
            'end_datetime >=' => $now,
            'deleted IS' => null
        ]);
    }

    /**
     * 終了した展示会を取得するファインダー
     */
    public function findFinished(Query $query, array $options): Query
    {
        $now = FrozenTime::now();
        return $query->where([
            'end_datetime <' => $now,
            'deleted IS' => null
        ]);
    }

    /**
     * 予約が必要な展示会を取得するファインダー
     */
    public function findRequiringReservation(Query $query, array $options): Query
    {
        return $query->where(['requires_reservation' => true]);
    }

    /**
     * 日付順でソートするファインダー
     */
    public function findSorted(Query $query, array $options): Query
    {
        return $query->order(['start_datetime' => 'ASC']);
    }

    /**
     * タイトルで検索するファインダー
     */
    public function findByTitle(Query $query, array $options): Query
    {
        $title = $options['title'] ?? null;
        if (!empty($title)) {
            $query->where(['title LIKE' => '%' . $title . '%']);
        }
        return $query;
    }

    /**
     * 指定期間内の展示会一覧を取得
     */
    public function getExhibitionsByPeriod(FrozenTime $startDate, FrozenTime $endDate): Query
    {
        return $this->find('active')
            ->where([
                'OR' => [
                    [
                        'start_datetime <=' => $endDate,
                        'end_datetime >=' => $startDate
                    ]
                ]
            ])
            ->order(['start_datetime' => 'ASC']);
    }

    /**
     * 今後開催予定の展示会一覧を取得
     */
    public function getUpcomingExhibitions(int $limit = 10): Query
    {
        return $this->find('upcoming')
            ->find('sorted')
            ->limit($limit);
    }

    /**
     * 現在開催中の展示会一覧を取得
     */
    public function getCurrentExhibitions(): Query
    {
        return $this->find('currentlyRunning')
            ->find('sorted');
    }
}
