import { Model } from "~~/src/models/Model";
import SchoolBagOrdersClient from "~~/src/lib/http/coverme-front/SchoolBagOrdersClient";

export default class SchoolBagOrders extends Model {
    /**
     *
     * @param {{access_token:""} } data
     * @param {string } access_Token
     */
    public add(data: TAccessToken): Promise<boolean> {
        const config = this.config;
        return new Promise((resolve) => {
            SchoolBagOrdersClient.create(config)
                .post<TAccessToken, TResponseSuccess>(data)
                //@todo レスポンスをオブジェクトクラスにする、レスポンス項目は検討が必要
                .then(({ success }) => resolve(!!success))
                .catch(() => resolve(false));
        });
    }
}
