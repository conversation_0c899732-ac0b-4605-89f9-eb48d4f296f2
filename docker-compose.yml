version: "3.7"

services:
  # docker exec -it coverme-amazonlinux2-1  bash --login
  # su ec2-user
  # cd /opt/coverme/
  # http://localhost/
  # chmod 644 /etc/cron.d/* && /usr/sbin/crond start
  amazonlinux2:
    build: ./_docker/amazonlinux2
    entrypoint: /opt/entrypoint.sh
    volumes:
      - ./:/opt/coverme
      - vendor-store:/opt/coverme/vendor
      - ./_docker/amazonlinux2/etc/nginx/conf.d:/etc/nginx/conf.d
      - ./_docker/amazonlinux2/etc/cron.d:/etc/cron.d
    ports:
      - "80:80"
    privileged: true
    environment:
      - APPLICATION_ENV=docker
    links:
      - mysql
      - mysql_test
    depends_on:
      - mysql
      - mysql_test

  mail:
    image: mailhog/mailhog
    ports:
      - 8025:8025
      - 1025:1025
  # http://localhost:8025/

  mysql:
    # mysql -udbuser -ppassword -hmysql coverme_local
    image: mysql:8.0.41
    volumes:
      - ./.data/mysql/:/var/lib/mysql
    environment:
      - MYSQL_DATABASE=coverme_local
      - MYSQL_USER=dbuser
      - MYSQL_PASSWORD=password
      - MYSQL_ROOT_PASSWORD=password
      - TZ=Asia/Tokyo
    ports:
      - "3996:3306"
  mysql_test:
    # mysql -udbuser -ppassword -hmysql_test coverme_local_test
    image: mysql:8.0.41
    environment:
      - MYSQL_DATABASE=coverme_local_test
      - MYSQL_USER=dbuser
      - MYSQL_PASSWORD=password
      - MYSQL_ROOT_PASSWORD=password
      - TZ=Asia/Tokyo
#  redis:
##    redis-cli -h redis SET hoge bar
#    image: "redis:latest"
#    ports:
#      - "6999:6379"
#    volumes:
#      - ./.data/redis:/data
  phpmyadmin:
    # http://localhost:8997/index.php?route=/database/structure&db=coverme_local
    image: phpmyadmin/phpmyadmin
    environment:
      - PMA_ARBITRARY=1
      - PMA_HOST=mysql
      - PMA_USER=dbuser
      - PMA_PASSWORD=password
    ports:
      - 8997:80

volumes:
  vendor-store:
