<?php

namespace App\Kuroko\Http\Client;

use Cake\Http\Client;

class KurokoApiClient extends Client
{
    public function setToken(string $token): static
    {
        $this->setConfig('auth.token', $token);
        return $this;
    }

    public function __construct(array $config = [])
    {
        parent::__construct($config);
    }

//    public function get(string $url, $data = [], array $options = []): Response
//    {
//        $options = $this->_mergeOptions($options);
//        $url = $this->buildUrl($url, $data, $options);
//
//
//        debug([
//            $url, $data, $options
//        ]);
//        return parent::get($url, $data, $options); // TODO: Change the autogenerated stub
//    }

//    public function post(string $url, $data = [], array $options = []): Response
//    {
//        return parent::post($url, $data, $options); // TODO: Change the autogenerated stub
//    }
}
