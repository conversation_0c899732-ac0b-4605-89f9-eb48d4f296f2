<?php

namespace App\Service;

use App\Enums\EntityFields\ESwbAdjustmentForm;
use App\Kuroko\Entity\IKurokoEntity;
use App\Kuroko\Entity\Member;
use BadMethodCallException;
use Cake\Datasource\EntityInterface;
use Cake\Utility\Hash;
use Cake\Log\Log;

class SwbRandselInvoiceAdjustmentsService implements IRestService
{
    use ServiceTrait;

    /**
     * @var array
     */
    protected array $_defaultConfig = [];

    public function initialize(): void {}

    /**
     * @param array $data
     * @return array|EntityInterface|IKurokoEntity|EntityInterface[]|null
     */
    public function index(array $data = []): array|EntityInterface|null|IKurokoEntity
    {
        $options = [];
        foreach (
            [
                ESwbAdjustmentForm::BILLING_YEAR_MONTH,
                ESwbAdjustmentForm::MAKER_ID,
                ESwbAdjustmentForm::PRODUCT_ID,
            ] as $item
        ) {
            if ($value = Hash::get($data, $item->value)) {
                $options = Hash::insert($options, $item->value, $value);
            }
        }
        
        $adjustments = [];
        $memberService = new MembersService();
        $entities = (new RandselInvoiceAdjustmentsService())->getAdjustments($options);
        
        // 全履歴からcreated_byのIDリストを取得
        $createdByIds = array_unique(
            Hash::extract(
                array_map(
                    fn($entity) => $entity->toArray(),
                    $entities
                ),
                '{n}.histories.{n}.created_by'
            )
        );
        
        // ユーザー情報を一括取得
        /** @var Member[] $members */
        $members = $memberService->getSwbList($this->getIdentity(), $createdByIds);
        $memberNames = array_combine(
            array_map(fn(Member $member) => $member->getId(), $members),
            array_map(fn(Member $member) => $member->get('name1'), $members)
        );
        
        foreach ($entities as $key => $entity) {
            $adjustmentData = $entity->toArray();
            if (!empty($adjustmentData['histories'])) {
                $adjustmentData['histories'] = array_map(
                    function($history) use ($memberNames) {
                        return $history + [
                            'created_by_name' => $memberNames[$history['created_by']] ?? null
                        ];
                    },
                    $adjustmentData['histories']
                );
            }
            $adjustments = Hash::insert($adjustments, $key, $adjustmentData);
        }

        return $adjustments;
    }

    public function add(array $data = []): ?IKurokoEntity
    {
        /** @var Member $member */
        $member = $this->getIdentity();
        $memberId = $member->getId();

        $data = Hash::insert($data, ESwbAdjustmentForm::CREATED_BY->value, $memberId);
        $options = [];
        foreach (
            [
                ESwbAdjustmentForm::BILLING_YEAR_MONTH,
                ESwbAdjustmentForm::MAKER_ID,
                ESwbAdjustmentForm::PRODUCT_ID,
            ] as $item
        ) {
            if ($value = Hash::get($data, $item->value)) {
                $options = Hash::insert($options, $item->value, $value);
            }
        }

        $adjustmentsService = new RandselInvoiceAdjustmentsService();
        $existingAdjustment = $adjustmentsService->getAdjustments($options);

        if (count($existingAdjustment) > 0) {
            $this->setErrors([
                '_error' => "既に登録されているデータがあります。",
            ]);
            return null;
        }

        if ($result = $adjustmentsService->create($data)) {
            // ログに記録
            Log::write('info', '調整金額登録', [
                'scope' => ['adjustmentsLog'],
                'data' => [
                    'billing_year_month' => $data[ESwbAdjustmentForm::BILLING_YEAR_MONTH->value],
                    'maker_id' => $data[ESwbAdjustmentForm::MAKER_ID->value],
                    'product_id' => $data[ESwbAdjustmentForm::PRODUCT_ID->value],
                    'adjustment_amount' => $data[ESwbAdjustmentForm::ADJUSTMENT_UNIT_PRICE->value] * $data[ESwbAdjustmentForm::ADJUSTMENT_QUANTITY->value],
                    'adjustment_note' => $data[ESwbAdjustmentForm::ADJUSTMENT_NOTE->value],
                    'created_by' => $memberId,
                    'action' => 'add'
                ]
            ]);
        } else {
            $this->setErrors([
                '_error' => "登録に失敗しました。",
            ]);
        }
        return null;
    }

    public function edit(string $id, array $data = []): EntityInterface|null|IKurokoEntity
    {
        /** @var Member $member */
        $member = $this->getIdentity();
        $memberId = $member->getId();

        $data = Hash::insert($data, ESwbAdjustmentForm::CREATED_BY->value, $memberId);
        $options = [];
        foreach (
            [
                ESwbAdjustmentForm::BILLING_YEAR_MONTH,
                ESwbAdjustmentForm::MAKER_ID,
                ESwbAdjustmentForm::PRODUCT_ID,
            ] as $item
        ) {
            if ($value = Hash::get($data, $item->value)) {
                $options = Hash::insert($options, $item->value, $value);
            }
        }

        $adjustmentsService = new RandselInvoiceAdjustmentsService();
        $adjustmentsService->setIdentity($this->getIdentity());
        $existingAdjustment = $adjustmentsService->getAdjustments($options);

        if (count($existingAdjustment) == 0) {
            $this->setErrors([
                '_error' => "登録されているデータがありません。",
            ]);
            return null;
        }

        $previousAmount = $existingAdjustment[0]->adjustment_unit_price * $existingAdjustment[0]->adjustment_quantity;
        if ($adjustment = $adjustmentsService->updateRandselInvoiceAdjustmentData($existingAdjustment[0], $data)) {
            // ログに記録
            Log::write('info', '調整金額更新', [
                'scope' => ['adjustmentsLog'],
                'data' => [
                    'billing_year_month' => $data[ESwbAdjustmentForm::BILLING_YEAR_MONTH->value],
                    'maker_id' => $data[ESwbAdjustmentForm::MAKER_ID->value],
                    'product_id' => $data[ESwbAdjustmentForm::PRODUCT_ID->value],
                    'adjustment_amount' => $data[ESwbAdjustmentForm::ADJUSTMENT_UNIT_PRICE->value] * $data[ESwbAdjustmentForm::ADJUSTMENT_QUANTITY->value],
                    'adjustment_note' => $data[ESwbAdjustmentForm::ADJUSTMENT_NOTE->value],
                    'created_by' => $memberId,
                    'action' => 'edit',
                    'previous_amount' => $previousAmount
                ]
            ]);
        } else {
            $this->setErrors([
                '_error' => "更新に失敗しました。",
            ]);
        }
        return null;
    }

    public function view(string $id, array $data = []): EntityInterface|null|IKurokoEntity
    {
        throw new BadMethodCallException();
    }


    public function delete(string $id, array $data = []): bool
    {
        $adjustmentsService = new RandselInvoiceAdjustmentsService();
        if (!$adjustmentsService->deleteRandselInvoiceAdjustmentData($id)) {
            $this->setErrors([
                '_error' => "削除に失敗しました。",
            ]);
            return false;
        }

        /** @var Member $member */
        $member = $this->getIdentity();
        $memberId = $member->getId();
        // ログに記録
        Log::write('info', '調整金額削除', [
            'scope' => ['adjustmentsLog'],
            'data' => [
                'id' => $id,
                'deleted_by' => $memberId,
                'action' => 'delete'
            ],
        ]);

        return true;
    }
}
