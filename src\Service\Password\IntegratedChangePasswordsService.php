<?php

namespace App\Service\Password;

use App\Service\IRestService;
use App\Service\ServiceTrait;
use App\Kuroko\Entity\IKurokoEntity;
use App\Kuroko\Entity\Member;
use Cake\Datasource\EntityInterface;
use Cake\Log\Log;
use Cake\Utility\Hash;
use BadMethodCallException;
use Exception;

/**
 * 統合ユーザーパスワード変更サービス
 * PasswordManagementServiceを使用してパスワード変更処理を統合
 */
class IntegratedChangePasswordsService implements IRestService
{
    use ServiceTrait;

    protected array $_defaultConfig = [];
    private PasswordManagementService $passwordManager;

    public function initialize(): void
    {
        $this->passwordManager = new PasswordManagementService();
    }

    public function add(array $data = []): EntityInterface|null|IKurokoEntity
    {
        throw new BadMethodCallException('Add method is not supported');
    }

    /**
     * パスワード変更処理
     * 統合されたPasswordManagementServiceを使用
     */
    public function edit(string $id, array $data = []): EntityInterface|null|IKurokoEntity
    {
        try {
            /** @var Member $member */
            $member = $this->getIdentity();

            $this->passwordManager->setIdentity($member);

            // ユーザータイプを判定
            $userType = $this->getUserTypeFromMember($member);
            
            Log::info("Password change request from member: {$member->getEmail()}, type: {$userType}");

            // 統合されたPasswordManagementServiceを使用
            $result = $this->passwordManager->changePassword(
                Hash::get($data, 'login_id', ''),
                Hash::get($data, 'current_password', ''),
                Hash::get($data, 'new_password', ''),
                $userType
            );

            if (!$result) {
                throw new Exception('パスワード変更に失敗しました');
            }

            Log::info("Password change completed for member: {$member->getEmail()}");
            return null;

        } catch (Exception $exception) {
            Log::error("Password change error: " . $exception->getMessage());
            $this->setErrors([
                '_system' => $exception->getMessage(),
            ]);
            return null;
        }
    }

    public function view(string $id, array $data = []): EntityInterface|null|IKurokoEntity
    {
        throw new BadMethodCallException('View method is not supported');
    }

    public function index(array $data = []): array|EntityInterface|null|IKurokoEntity
    {
        throw new BadMethodCallException('Index method is not supported');
    }

    public function delete(string $id, array $data = []): bool
    {
        throw new BadMethodCallException('Delete method is not supported');
    }

    /**
     * Memberエンティティからユーザータイプを判定
     * 
     * @param Member $member
     */
    private function getUserTypeFromMember(Member $member): string
    {
        // グループIDからユーザータイプを判定
        if (method_exists($member, 'isBelongsToSwbGroup') && $member->isBelongsToSwbGroup()) {
            return PasswordManagementService::USER_TYPE_SWB;
        } elseif (method_exists($member, 'isBelongsToClientGroup') && $member->isBelongsToClientGroup()) {
            return PasswordManagementService::USER_TYPE_MAKER;
        }
        
        return PasswordManagementService::USER_TYPE_GENERAL;
    }
}
