<script setup lang="ts">
import TheMember from "~/components/my/member/TheMember.vue";
import TheMemberChangePassword from "~/components/my/member/TheMemberChangePassword.vue";
import TheMemberUpdate from "~/components/my/member/TheMemberUpdate.vue";
import TheOrders from "~/components/my/TheOrders.vue";
import type { Member } from "~/src/models/entry/Member";
import { ref } from "vue";

const stepDefault = "default";
const stepChangePassword = "change-password";
const stepMemberUpdate = "member-update";

// const emit = defineEmits(["login-success"]);
// const config = useRuntimeConfig();

const step = ref(stepDefault);
const member = ref<Member>();

const gotoChangePassword = (m: Member): void => {
    step.value = stepChangePassword;
    member.value = m;
};

const gotoMemberUpdate = (m: Member): void => {
    step.value = stepMemberUpdate;
    member.value = m;
};

const gotoMy = (): void => {
    step.value = stepDefault;
};
</script>

<template>
    <v-container class="pa-0">
        <p class="text-h5 font-weight-bold mb-10">マイページ</p>
        <template v-if="step === stepDefault">
            <v-row><the-orders></the-orders></v-row>
            <v-row>
                <the-member
                    @change-password="gotoChangePassword"
                    @member-update="gotoMemberUpdate"
                >
                </the-member>
            </v-row>
        </template>
        <template v-else-if="step === stepChangePassword">
            <the-member-change-password
                :member="member as Member"
                @my="gotoMy()"
            ></the-member-change-password>
        </template>
        <template v-else-if="step === stepMemberUpdate">
            <the-member-update
                :member-update-form="member as Member"
                @my="gotoMy()"
            ></the-member-update>
        </template>
    </v-container>
</template>

<style scoped></style>
