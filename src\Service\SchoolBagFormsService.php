<?php

namespace App\Service;

use App\Kuroko\Entity\IKurokoEntity;
use Cake\Datasource\EntityInterface;
use BadMethodCallException;

abstract class SchoolBagFormsService implements IRestService
{
    use ServiceTrait;

    /**
     * @var array
     */
    protected array $_defaultConfig = [
    ];

    public function add(array $data = []): IKurokoEntity | EntityInterface | null
    {
        throw new BadMethodCallException();
    }

    public function edit(string $id, array $data = []): ?IKurokoEntity
    {
        throw new BadMethodCallException();
    }

    public function view(string $id, array $data = []): ?IKurokoEntity
    {
        throw new BadMethodCallException();
    }

    public function index(array $data = []): ?array
    {
        throw new BadMethodCallException();
    }

    public function delete(string $id, array $data = []): bool
    {
        throw new BadMethodCallException();
    }

    public function initialize(): void
    {
    }
}
