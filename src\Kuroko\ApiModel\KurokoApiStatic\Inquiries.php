<?php

namespace App\<PERSON>roko\ApiModel\KurokoApiStatic;

use App\Kuroko\ApiModel\KurokoApiStaticModel;
use App\Kuroko\Entity\Inquiry;
use Cake\Utility\Hash;
use Cake\Log\Log;

class Inquiries extends KurokoApiStaticModel
{
    /**
     * フォーム送信
     * @param array $data
     * @return Inquiry|null
     */
    public function sendInquiry(array $data): ?Inquiry
    {
        //@todo 新しいコンテンツ増えたら、サービス側でやるのがいいかも
        $data = Hash::insert($data, "from_id", $this->getConfig("params.schoolBagForm.fromId"));
        $data = Hash::insert($data, "member_id", $this->getConfig("params.schoolBagForm.memberId"));
        $response = $this->post($this->getEndPoint("sendInquiry"), json_encode($data));
        if ($response->isSuccess() && $body = $response->getStringBody()) {
            if ($data = json_decode($body, true)) {
                if (Hash::check($data, "id")) {
                    return new Inquiry($data);
                }
            }
        }
        Log::debug(__METHOD__ . " request data: " . json_encode($data));
        Log::debug(__METHOD__ . " response: " . $response->getStringBody());
        return null;
    }
}
