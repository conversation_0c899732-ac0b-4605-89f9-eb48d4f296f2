<?php
declare(strict_types=1);

namespace App\Test\Fixture;

use Cake\TestSuite\Fixture\TestFixture;
use Authentication\PasswordHasher\DefaultPasswordHasher;

/**
 * MakerUsersFixture
 */
class MakerUsersFixture extends TestFixture
{
    /**
     * Table name
     */
    public $table = 'maker_users';

    /**
     * Fields
     */
    public $fields = [
        'id' => ['type' => 'integer', 'length' => null, 'unsigned' => false, 'null' => false, 'default' => null, 'comment' => '', 'autoIncrement' => true, 'precision' => null],
        'maker_id' => ['type' => 'integer', 'length' => null, 'unsigned' => false, 'null' => false, 'default' => null, 'comment' => 'メーカーID (MAKERS.id)', 'precision' => null],
        'email' => ['type' => 'string', 'length' => 255, 'null' => false, 'default' => null, 'collate' => 'utf8mb4_unicode_ci', 'comment' => 'メールアドレス（ログイン用）', 'precision' => null],
        'password' => ['type' => 'string', 'length' => 255, 'null' => true, 'default' => null, 'collate' => 'utf8mb4_unicode_ci', 'comment' => 'パスワード（ハッシュ化）', 'precision' => null],
        'created' => ['type' => 'datetime', 'length' => null, 'precision' => null, 'null' => false, 'default' => 'CURRENT_TIMESTAMP', 'comment' => '作成日時'],
        'modified' => ['type' => 'datetime', 'length' => null, 'precision' => null, 'null' => false, 'default' => 'CURRENT_TIMESTAMP', 'comment' => '更新日時'],
        'deleted' => ['type' => 'datetime', 'length' => null, 'null' => true, 'default' => null, 'comment' => '削除日時(論理削除)', 'precision' => null],
        '_indexes' => [
            'idx_maker_users_maker_id' => ['type' => 'index', 'columns' => ['maker_id'], 'length' => []],
            'idx_maker_users_created' => ['type' => 'index', 'columns' => ['created'], 'length' => []],
        ],
        '_constraints' => [
            'primary' => ['type' => 'primary', 'columns' => ['id'], 'length' => []],
            'uk_maker_users_email' => ['type' => 'unique', 'columns' => ['email'], 'length' => []],
        ],
        '_options' => [
            'engine' => 'InnoDB',
            'collation' => 'utf8mb4_unicode_ci'
        ],
    ];

    /**
     * Init method
     */
    public function init(): void
    {
        $hasher = new DefaultPasswordHasher();
        $this->records = [
            [
                'id' => 1,
                'maker_id' => 1,
                'email' => '<EMAIL>',
                'password' => $hasher->hash('password1234'), // password1234
                'created' => '2024-01-01 10:00:00',
                'modified' => '2024-01-01 10:00:00',
                'deleted' => null,
            ],
            [
                'id' => 2,
                'maker_id' => 2,
                'email' => '<EMAIL>',
                'password' => $hasher->hash('password1234'), // password1234
                'created' => '2024-01-02 11:00:00',
                'modified' => '2024-01-02 11:00:00',
                'deleted' => null,
            ],
            [
                'id' => 3,
                'maker_id' => 3,
                'email' => '<EMAIL>',
                'password' => null, // Kurocoユーザー（パスワードなし）
                'created' => '2024-01-03 12:00:00',
                'modified' => '2024-01-03 12:00:00',
                'deleted' => null,
            ],
        ];
        parent::init();
    }
}
