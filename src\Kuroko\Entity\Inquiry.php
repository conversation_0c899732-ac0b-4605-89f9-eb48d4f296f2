<?php

namespace App\Kuroko\Entity;

use Cake\Utility\Hash;

class Inquiry implements IKurokoEntity
{
    use KurokoEntityTrait;

    public function getId(): ?string
    {
        return Hash::get($this->getData(), "id", Hash::get($this->getData(), "inquiry_bn_id"));
    }

    public function getBody(): ?string
    {
        return Hash::get($this->getData(), "body");
    }

    public function getJsonData(): array
    {
        return [
            "id" => Hash::get($this->getData(), "id"),
            "messages" => Hash::get($this->getData(), "messages"),
        ];
    }

    /**
     * @param array $data
     * @return array
     */
    public function initialize(array $data): array
    {
        return $data;
    }


}
