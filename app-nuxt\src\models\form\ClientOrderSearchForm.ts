export class ClientOrderSearchForm {
    protected _startDate: string;
    protected _endDate: string;
    protected _productId: number;
    // private _searchStatus: string;
    protected _searchDateType: string;
    protected _searchStatusType: number[];
    protected _isChanged: boolean;

    constructor() {
        this._startDate = this.getLastMonthFirstDayInJST();
        this._endDate = this.getTodayInJST();
        this._productId = -1;
        // this._searchStatus = "pending";
        this._searchDateType = "order-date";
        this._searchStatusType = [0];
        this._isChanged = false; // 初期状態では変更なし
    }

    get isChanged(): boolean {
        return this._isChanged;
    }

    resetChangedFlag(): void {
        this._isChanged = false;
    }

    get startDate(): string {
        return this._startDate;
    }

    set startDate(value: string) {
        if (this._startDate !== value) {
            this._isChanged = true;
        }
        this._startDate = value;
    }

    get endDate(): string {
        return this._endDate;
    }

    set endDate(value: string) {
        if (this._endDate !== value) {
            this._isChanged = true;
        }
        this._endDate = value;
    }

    get productId(): number {
        return this._productId;
    }

    set productId(value: number) {
        if (this._productId !== value) {
            this._isChanged = true;
        }
        this._productId = value;
    }

    // get searchStatus(): string {
    //     return this._searchStatus;
    // }

    // set searchStatus(value: string) {
    //     if (this._searchStatus !== value) {
    //         this._isChanged = true;
    //     }
    //     this._searchStatus = value;
    // }

    get searchDateType(): string {
        return this._searchDateType;
    }

    set searchDateType(value: string) {
        if (this._searchDateType !== value) {
            this._isChanged = true;
        }
        this._searchDateType = value;
    }

    get searchStatusType(): number[] {
        return this._searchStatusType;
    }

    set searchStatusType(value: number[]) {
        if (this._searchStatusType !== value) {
            this._isChanged = true;
        }
        this._searchStatusType = value;
    }

    get is_id_set(): boolean {
        return this.productId > 0;
    }

    public isStartDateValid(): boolean | string {
        if (!this._startDate || !this._endDate) return true;
        return (
            new Date(this._startDate) <= new Date(this._endDate) ||
            "開始日が不正です"
        );
    }

    public isEndDateValid(): boolean | string {
        if (!this._startDate || !this._endDate) return true;
        return (
            (new Date(this._endDate) >= new Date(this._startDate) &&
                new Date(this._endDate) <= new Date()) ||
            "終了日が不正です"
        );
    }

    get isDateValid(): boolean {
        return (
            this.isStartDateValid() === true && this.isEndDateValid() === true
        );
    }

    /**
     * 1ヶ月1日のyyyy-mm-dd文字列を取得(汎用化してlib移動のがいいか)
     */
    getLastMonthFirstDayInJST(): string {
        const today = new Date();
        const lastMonthFirstDay = new Date(
            today.getFullYear(),
            today.getMonth() - 1,
            1,
        );
        lastMonthFirstDay.setHours(lastMonthFirstDay.getHours() + 9);
        return lastMonthFirstDay.toISOString().split("T")[0];
    }

    /**
     * 6ヶ月1日のyyyy-mm-dd文字列を取得(汎用化してlib移動のがいいか)
     */
    getSixMonthAgoFirstDayInJST(): string {
        const today = new Date();
        const lastMonthFirstDay = new Date(
            today.getFullYear(),
            today.getMonth() - 5,
            1,
        );
        lastMonthFirstDay.setHours(lastMonthFirstDay.getHours() + 9);
        return lastMonthFirstDay.toISOString().split("T")[0];
    }

    /**
     * 今日日付のyyyy-mm-dd文字列を取得(汎用化してlib移動のがいいか)
     */
    getTodayInJST(): string {
        const today = new Date();
        today.setHours(today.getHours() + 9);

        return today.toISOString().split("T")[0];
    }

    get data(): Record<string, string | number | string[] | number[]> {
        return {
            from: this._startDate,
            to: this._endDate,
            product_id: this._productId,
            // searchStatus: this._searchStatus,
            status: this._searchStatusType,
            searchDateType: this._searchDateType,
        };
    }
}
