<?php

namespace App\Kuroko\ApiModel\KurokoApiPrivateStatic;

use App\Kuroko\ApiModel\KurokoApiPrivateStaticModel;
use App\Kuroko\Entity\Maker;
use Cake\Utility\Hash;
use Cake\Core\Configure;
use Cake\Log\Log;

class Makers extends KurokoApiPrivateStaticModel
{
    /**
     * メーカーリスト取得
     *
     * @return \App\Kuroko\Entity\Product[]
     */
    public function getMakerList(): array
    {
        $makers = [];
        $queryString = http_build_query([
            static::CNT => Configure::read("Kuroko.api.allListMaxCnt"),
        ]);
        $fullUrl = $this->getEndPoint("makers") . '?' . $queryString;

        $response = $this->get($fullUrl);
        if ($response->isSuccess() && $body = $response->getStringBody()) {
            if ($data = json_decode($body, true)) {
                if (Hash::check($data, "list")) {
                    foreach (Hash::get($data, "list") as $value) {
                        $maker = Hash::get($value, "parent_id");
                        if ($maker !== "") {
                            $makers[] = new Maker($value);
                        }
                    }
                }
            }
        }

        if (empty($makers)) {
            Log::debug(__METHOD__ . " request data: " . json_encode($data));
            Log::debug(__METHOD__ . " response: " . $response->getStringBody());
        }
        return $makers;
    }
}
