<?php

namespace Mailer;

use App\Enums\EntityFields\EPasswordReminder;
use App\Mailer\AppMailer;
use App\Enums\EntityFields\ESchoolBagForm;
use App\Kuroko\Entity\AccessToken;
use App\Mailer\Sender\ToUser\SchoolBagMemberRegistSender;
use App\Mailer\Sender\ToUser\OrderCompleteAndRegistCompletedSender;
use App\Mailer\Sender\ToUser\PasswordResetSender;
use PHPUnit\Framework\TestCase;
use App\Kuroko\Entity\Member;
use App\Kuroko\Entity\ECOrder;
use Cake\Routing\Router;
use Cake\Utility\Hash;

class AppMailerTest extends TestCase
{
    public function setUp(): void
    {
        parent::setUp();

        Router::reload();
        require CONFIG . 'routes.php';
    }

    // php ./vendor/phpunit/phpunit/phpunit --filter "testSchoolBagMemberRegistSender" ./tests/TestCase/Mailer/AppMailerTest.php
    public function testSchoolBagMemberRegistSender()
    {
        $id = uniqid();
        $data = [
            ESchoolBagForm::NAME1->value => "name-" . $id,
            ESchoolBagForm::NAME2->value => "名-" . $id,
            ESchoolBagForm::EMAIL->value => "test-" . $id . "@sowelleber.jp",
            ESchoolBagForm::LOGIN_PWD->value => $id,
            // ESchoolBagForm::LOGIN_ID->value => "id-" . $id,

            ESchoolBagForm::TEL->value => '********',
            ESchoolBagForm::ZIP_CODE->value => '1232624',
            ESchoolBagForm::TDFK_CD->value => '12',
            ESchoolBagForm::ADDRESS1->value => '住所1',
            ESchoolBagForm::ADDRESS2->value => '住所2',
            ESchoolBagForm::ADDRESS3->value => '住所3',
            ESchoolBagForm::EMAIL_SEND_NG_FLG->value => false,
            ESchoolBagForm::BODY->value => '{"order":[{"mid":"メーカーid","pid":"商品id"}]}',
            ESchoolBagForm::EXT_01->value => '{"order":[{"mid":"メーカーid","pid":"商品id"}]}',
            "access_token" => ["value" => "token1234jkhjkfdsaf"]
        ];
        $menber = new Member($data);
        $accessToken = new AccessToken($data);

        AppMailer::sendToUser((new SchoolBagMemberRegistSender(
            '<EMAIL>',
            "ユーザー"
        ))->setViewVars([
            'member' => $menber,
            'accessToken' => $accessToken,
        ]));
        $this->assertTrue(true);
    }

    // php ./vendor/phpunit/phpunit/phpunit --filter "testOrderCompleteAndRegistCompletedSender" ./tests/TestCase/Mailer/AppMailerTest.php
    public function testOrderCompleteAndRegistCompletedSender()
    {
        $id = uniqid();
        $data = [
            [
                "errors" => [],
                "messages" => [
                    "注文が完了しました。"
                ],
                "ids" => [
                    72
                ],
                "ext_data" => null,
                "member_data" => [
                    "email" => "<EMAIL>",
                    "name1" => "テストせい2"
                ],
                "product_info" => [
                    "product_name" => "天使の羽",
                    "product_id" => 41201
                ]
            ],
            [
                "errors" => [],
                "messages" => [
                    "注文が完了しました。"
                ],
                "ids" => [
                    73
                ],
                "ext_data" => null,
                "member_data" => [
                    "email" => "<EMAIL>",
                    "name1" => "テストせい2"
                ],
                "product_info" => [
                    "product_name" => "ワークマン",
                    "product_id" => 41202
                ]
            ]
        ];

        $orders = [];
        foreach ($data as $key => $value) {
            $orders[] = new ECOrder($value);
        }
        AppMailer::sendToUser((new OrderCompleteAndRegistCompletedSender(
            $orders[0]->getMemberEmail(),
            $orders[0]->getMemberName1()
        ))->setViewVars([
            'orders' => $orders,
        ]));
        $this->assertTrue(true);
    }

    // php ./vendor/phpunit/phpunit/phpunit --filter "testPasswordResetSender" ./tests/TestCase/Mailer/AppMailerTest.php
    public function testPasswordResetSender()
    {
        $id = uniqid();
        $data = [
            "token" => "test_token",
            "temp_pwd" => "1234tptp",
            "member_email" => $id . "@sowelleber.jp",
            "member_name1" => $id . "姓",
        ];

        AppMailer::sendToUser((new PasswordResetSender(
            Hash::get($data, EPasswordReminder::MEMBER_EMAIL->value),
            Hash::get($data, EPasswordReminder::MEMBER_NAME1->value)
        ))->setViewVars([
            'tmpData' => $data,
        ]));

        $this->assertTrue(true);
    }
}
