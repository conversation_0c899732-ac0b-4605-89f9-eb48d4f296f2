<?php

namespace App\Enums\Options;

enum ERandselOrderApprovalType: int implements IEnumOptions
{
    use EnumOptionsTrait;

    case CSV = 1;
    case SCREEN = 2;
    case AUTO = 3;

    /**
     * @return string
     */
    public function description(): string
    {
        return match ($this) {
            self::CSV => 'csv',
            self::SCREEN => '画面',
            self::AUTO => '自動',
        };
    }
}
