<?php
declare(strict_types=1);

namespace App\Test\Fixture;

use Cake\TestSuite\Fixture\TestFixture;

/**
 * UserSurveysFixture
 */
class UserSurveysFixture extends TestFixture
{
    /**
     * Table name
     */
    public $table = 'user_surveys';

    /**
     * Fields
     */
    public $fields = [
        'id' => ['type' => 'integer', 'length' => null, 'unsigned' => false, 'null' => false, 'default' => null, 'comment' => 'アンケートID', 'autoIncrement' => true, 'precision' => null],
        'general_user_id' => ['type' => 'integer', 'length' => null, 'unsigned' => false, 'null' => false, 'default' => null, 'comment' => '一般ユーザーID', 'precision' => null],
        'year' => ['type' => 'integer', 'length' => null, 'unsigned' => false, 'null' => false, 'default' => null, 'comment' => '年度', 'precision' => null],
        'child_sex' => ['type' => 'integer', 'length' => null, 'unsigned' => false, 'null' => true, 'default' => null, 'comment' => 'お子様の性別（1:男, 2:女, 3:その他）', 'precision' => null],
        'budget' => ['type' => 'integer', 'length' => null, 'unsigned' => false, 'null' => true, 'default' => null, 'comment' => 'ご予算（1:1万円未満～3万円, 2:3万円～7万円, 3:7万円～10万円, 4:10万円～30万円, 5:30万円以上）', 'precision' => null],
        'question_1_1' => ['type' => 'boolean', 'length' => null, 'null' => false, 'default' => false, 'comment' => 'きっかけ1', 'precision' => null],
        'question_1_2' => ['type' => 'boolean', 'length' => null, 'null' => false, 'default' => false, 'comment' => 'きっかけ2', 'precision' => null],
        'question_1_3' => ['type' => 'boolean', 'length' => null, 'null' => false, 'default' => false, 'comment' => 'きっかけ3', 'precision' => null],
        'question_1_4' => ['type' => 'boolean', 'length' => null, 'null' => false, 'default' => false, 'comment' => 'きっかけ4', 'precision' => null],
        'question_2_1' => ['type' => 'boolean', 'length' => null, 'null' => false, 'default' => false, 'comment' => '重視するポイント1', 'precision' => null],
        'question_2_2' => ['type' => 'boolean', 'length' => null, 'null' => false, 'default' => false, 'comment' => '重視するポイント2', 'precision' => null],
        'question_2_3' => ['type' => 'boolean', 'length' => null, 'null' => false, 'default' => false, 'comment' => '重視するポイント3', 'precision' => null],
        'question_2_4' => ['type' => 'boolean', 'length' => null, 'null' => false, 'default' => false, 'comment' => '重視するポイント4', 'precision' => null],
        'question_2_5' => ['type' => 'boolean', 'length' => null, 'null' => false, 'default' => false, 'comment' => '重視するポイント5', 'precision' => null],
        'question_2_6' => ['type' => 'boolean', 'length' => null, 'null' => false, 'default' => false, 'comment' => '重視するポイント6', 'precision' => null],
        'question_2_7' => ['type' => 'boolean', 'length' => null, 'null' => false, 'default' => false, 'comment' => '重視するポイント7', 'precision' => null],
        'question_2_8' => ['type' => 'boolean', 'length' => null, 'null' => false, 'default' => false, 'comment' => '重視するポイント8', 'precision' => null],
        'question_2_9' => ['type' => 'boolean', 'length' => null, 'null' => false, 'default' => false, 'comment' => '重視するポイント9', 'precision' => null],
        'question_2_10' => ['type' => 'boolean', 'length' => null, 'null' => false, 'default' => false, 'comment' => '重視するポイント10', 'precision' => null],
        'question_2_11' => ['type' => 'boolean', 'length' => null, 'null' => false, 'default' => false, 'comment' => '重視するポイント11', 'precision' => null],
        'created' => ['type' => 'datetime', 'length' => null, 'precision' => null, 'null' => false, 'default' => 'CURRENT_TIMESTAMP', 'comment' => '作成日時'],
        'modified' => ['type' => 'datetime', 'length' => null, 'precision' => null, 'null' => false, 'default' => 'CURRENT_TIMESTAMP', 'comment' => '更新日時'],
        '_indexes' => [
            'uk_user_surveys_general_user_id' => ['type' => 'index', 'columns' => ['general_user_id'], 'length' => [], 'unique' => true],
            'idx_user_surveys_year' => ['type' => 'index', 'columns' => ['year'], 'length' => []],
            'idx_user_surveys_child_sex' => ['type' => 'index', 'columns' => ['child_sex'], 'length' => []],
            'idx_user_surveys_budget' => ['type' => 'index', 'columns' => ['budget'], 'length' => []],
        ],
        '_constraints' => [
            'primary' => ['type' => 'primary', 'columns' => ['id'], 'length' => []],
            'fk_user_surveys_general_user_id' => ['type' => 'foreign', 'columns' => ['general_user_id'], 'references' => ['general_users', 'id'], 'update' => 'cascade', 'delete' => 'cascade'],
        ],
        '_options' => [
            'engine' => 'InnoDB',
            'collation' => 'utf8mb4_unicode_ci'
        ],
    ];

    /**
     * Init method
     */
    public function init(): void
    {
        $this->records = [
            [
                'id' => 1,
                'general_user_id' => 1,
                'year' => 2025,
                'child_sex' => 1,
                'budget' => 2,
                'question_1_1' => true, // きっかけ1
                'question_1_2' => false, // きっかけ2
                'question_1_3' => true, // きっかけ3
                'question_1_4' => false, // きっかけ4
                'question_2_1' => true, // 重視するポイント1
                'question_2_2' => true, // 重視するポイント2
                'question_2_3' => false, // 重視するポイント3
                'question_2_4' => false, // 重視するポイント4
                'question_2_5' => true, // 重視するポイント5
                'question_2_6' => false, // 重視するポイント6
                'question_2_7' => true, // 重視するポイント7
                'question_2_8' => false, // 重視するポイント8
                'question_2_9' => true, // 重視するポイント9
                'question_2_10' => false, // 重視するポイント10
                'question_2_11' => true, // 重視するポイント11
                'created' => '2024-01-01 10:00:00',
                'modified' => '2024-01-01 10:00:00',
            ],
            [
                'id' => 2,
                'general_user_id' => 3,
                'year' => 2026,
                'child_sex' => 2,
                'budget' => 3,
                'question_1_1' => false, // きっかけ1
                'question_1_2' => true, // きっかけ2
                'question_1_3' => false, // きっかけ3
                'question_1_4' => true, // きっかけ4
                'question_2_1' => false, // 重視するポイント1
                'question_2_2' => false, // 重視するポイント2
                'question_2_3' => true, // 重視するポイント3
                'question_2_4' => true, // 重視するポイント4
                'question_2_5' => false, // 重視するポイント5
                'question_2_6' => true, // 重視するポイント6
                'question_2_7' => false, // 重視するポイント7
                'question_2_8' => true, // 重視するポイント8
                'question_2_9' => false, // 重視するポイント9
                'question_2_10' => true, // 重視するポイント10
                'question_2_11' => false, // 重視するポイント11
                'created' => '2024-01-03 12:00:00',
                'modified' => '2024-01-03 12:00:00',
            ],
        ];
        parent::init();
    }
}


