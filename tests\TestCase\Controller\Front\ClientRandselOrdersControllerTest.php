<?php

namespace Controller\Front;

use App\Enums\EntityFields\EClientOrderForm;
use App\Test\TestCase\Controller\ApiTestCase;
use Cake\Utility\Hash;

class ClientRandselOrdersControllerTest extends ApiTestCase
{

    /**
     * Test index method
     *
     * @return void
     * @uses \App\Controller\Front\ClientRandselOrdersController::index()
     * php ./vendor/phpunit/phpunit/phpunit --filter "testIndex" ./tests/TestCase/Controller/Front/ClientRandselOrdersControllerTest.php
     */
    public function testIndex()
    {
        $this->setFrontAuthorized(true);
        $this->get('/front/client-randsel-orders.json?' . http_build_query([
            EClientOrderForm::PRODUCT_ID->value => 1,
            EClientOrderForm::FROM->value => "2024-10-20",
            EClientOrderForm::TO->value => "2024-10-22",
        ]));
        $response = $this->bodyToArray($this->_response->getBody());
        debug($response);
        $this->assertResponseOk(print_r(Hash::flatten($response), true));
    }
}
