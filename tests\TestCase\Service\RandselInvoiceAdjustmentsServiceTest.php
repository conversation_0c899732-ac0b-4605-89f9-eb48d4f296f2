<?php

namespace Service;

use App\Enums\EntityFields\ESwbAdjustmentForm;
use App\Kuroko\Entity\Member;
use App\Service\RandselInvoiceAdjustmentsService;
use App\Test\TestCase\AppTestCase;

class RandselInvoiceAdjustmentsServiceTest extends AppTestCase
{
    private $service;

    public function setUp(): void
    {
        $this->service = new RandselInvoiceAdjustmentsService();
        parent::setUp();
    }

    // php ./vendor/phpunit/phpunit/phpunit --filter "testCreate" ./tests/TestCase/Service/RandselInvoiceAdjustmentsServiceTest.php
    public function testCreate()
    {
        $data = [
            'maker_id' => 1,
            'product_id' => 1,
            'billing_year_month' => '2024-04',
            'adjustment_unit_price' => 1000,
            'adjustment_quantity' => 2,
            'adjustment_note' => 'テスト登録',
            'integer' => true,
            'created_by' => 1
        ];

        $adjustment = $this->service->create($data);
        $this->assertNotNull($adjustment);

        // $adjustment->histories = [];
        // $adjustment->histories[] = [
        //     'action_type' => 1, // 新規作成
        //     'created_by' => $data['created_by'],
        //     'changes' => null
        // ];

        // $adjustment->setDirty('randsel_invoice_adjustment_histories', true);
        debug($adjustment);
    }

    // php ./vendor/phpunit/phpunit/phpunit --filter "testUpdate" ./tests/TestCase/Service/RandselInvoiceAdjustmentsServiceTest.php
    public function testUpdate()
    {
        // まず登録
        $data = [
            'maker_id' => 1,
            'product_id' => 1,
            'billing_year_month' => '2024-04',
            'adjustment_unit_price' => 1000,
            'adjustment_quantity' => 2,
            'adjustment_note' => 'テスト登録',
            'integer' => true,
            'created_by' => 1
        ];
        $adjustment = $this->service->create($data);
        
        // 更新データ
        $updateData = [
            'adjustment_unit_price' => 2000,
            'adjustment_quantity' => 3,
            'adjustment_note' => 'テスト更新',
            'created_by' => 1
        ];

        $updatedAdjustment = $this->service->updateRandselInvoiceAdjustmentData($adjustment, $updateData);
        $this->assertNotNull($updatedAdjustment);
        debug($updatedAdjustment);
    }

    // php ./vendor/phpunit/phpunit/phpunit --filter "testConfirm" ./tests/TestCase/Service/RandselInvoiceAdjustmentsServiceTest.php
    public function testConfirm()
    {
        // まず登録
        $data = [
            'maker_id' => 1,
            'product_id' => 1,
            'billing_year_month' => '2024-04',
            'adjustment_unit_price' => 1000,
            'adjustment_quantity' => 2,
            'adjustment_note' => 'テスト登録',
            'integer' => true,
            'created_by' => 1
        ];
        $adjustment = $this->service->create($data);

        // メンバー情報をセット
        $member = new Member([
            "details" => [
                "id" => 1,
                "maker_id" => 1,
                "member_id" => 1,
            ],
            "id" => 1,
            "maker_id" => 1,
            "member_id" => 1,
        ]);
        $this->service->setIdentity($member);

        // 確定処理
        $confirmedAdjustment = $this->service->confirmRandselInvoiceAdjustmentData($adjustment);
        $this->assertNotNull($confirmedAdjustment);
        $this->assertEquals(1, $confirmedAdjustment->get(ESwbAdjustmentForm::STATUS->value));
        debug($confirmedAdjustment);
    }

    // php ./vendor/phpunit/phpunit/phpunit --filter "testGetAdjustments" ./tests/TestCase/Service/RandselInvoiceAdjustmentsServiceTest.php
    public function testGetAdjustments()
    {
        $conditions = [];
        $result = $this->service->getAdjustments($conditions);
        $this->assertIsArray($result);
        debug($result);

        $conditions = ['maker_id' => 1];
        $result = $this->service->getAdjustments($conditions);
        $this->assertIsArray($result);
        debug($result);
    }
} 