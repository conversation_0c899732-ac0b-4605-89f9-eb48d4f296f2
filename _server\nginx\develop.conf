server {
    server_name  dev.coverme.jp;
    root         /usr/share/nginx/html/wordpress;

    client_max_body_size 20M;

    satisfy any;
    deny all;
    auth_basic "Restricted";
    auth_basic_user_file /etc/nginx/basic/.htpasswd;

    # Load configuration files for the default server block.
    include /etc/nginx/default.d/*.conf;

    error_page 400 403 404 /40x.html;

    location = /40x.html {
        root /usr/share/nginx/html/wordpress;
    }

    error_page 500 = /500.json;
    location /500.json {
        return 500 "{\"error\":\"System Error\",\"status\":500}";
    }

    location / {
        # スラッグ、パーマリンク対応
        try_files $uri $uri/ /index.php?$args;
    }

    location ~* /\. {
        deny all;
    }

    # CakePHPの設定
    location /app-api {
        alias /opt/coverme/webroot;
        try_files $uri $uri/ /app-api/index.php?$args;
        index index.php index.html;

        # 静的ファイルの処理
        location ~* \.(jpg|jpeg|gif|css|png|js|ico|html|svg|woff|woff2)$ {
            access_log off;
            expires max;
            log_not_found off;
        }

        # PHPファイルの処理
        location ~ ^/app-api/.+\.php$ {
            fastcgi_split_path_info ^/app-api(.+?\.php)(/.*)$;
            fastcgi_pass php-fpm;
            fastcgi_index index.php;
            include fastcgi_params;
            fastcgi_param SCRIPT_FILENAME /opt/coverme/webroot/index.php;
            fastcgi_param APPLICATION_ENV develop;
            fastcgi_buffers 256 128k;
            fastcgi_buffer_size 128k;
            fastcgi_intercept_errors on;
            fastcgi_read_timeout 120s;
            proxy_pass_header Authorization;
        }

        # CakePHPのルーティング対応
        if (!-e $request_filename) {
            rewrite ^/app-api/(.*)$ /app-api/index.php?$1 last;
        }
    }

    # Nuxtコンテンツ
    location /catalog {
        alias /opt/coverme/app-nuxt/.output/public;
        try_files $uri $uri/ /index.html;
    }

    listen [::]:443 ssl ipv6only=on; # managed by Certbot
    listen 443 ssl; # managed by Certbot
    ssl_certificate /etc/letsencrypt/live/dev.coverme.jp/fullchain.pem; # managed by Certbot
    ssl_certificate_key /etc/letsencrypt/live/dev.coverme.jp/privkey.pem; # managed by Certbot
    include /etc/letsencrypt/options-ssl-nginx.conf; # managed by Certbot
    ssl_dhparam /etc/letsencrypt/ssl-dhparams.pem; # managed by Certbot
}

server {
    if ($host = dev.coverme.jp) {
        return 301 https://$host$request_uri;
    } # managed by Certbot


    listen       80;
    listen       [::]:80;
    server_name  dev.coverme.jp;
    return 404; # managed by Certbot
}
