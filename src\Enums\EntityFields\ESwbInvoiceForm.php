<?php

namespace App\Enums\EntityFields;

enum ESwbInvoiceForm: string
{
    case MAKER_ID = 'maker_id';
    case PRODUCT_ID = 'product_id';
    case BILLING_YEAR_MONTH = 'billing_year_month';
    case TOTAL_AMOUNT = 'total_amount';
    case TOTAL_QUANTITY = 'total_quantity';
    case ADJUSTMENT_AMOUNT = 'adjustment_amount';
    case ADJUSTMENT_QUANTITY = 'adjustment_quantity';
    case ADJUSTMENT_NOTE = 'adjustment_note';
    case INVOICE_AMOUNT = 'invoice_amount';
    /**
     * @return string
     */
    public function description(): string
    {
        return match ($this) {
            self::MAKER_ID => 'メーカーID',
            self::PRODUCT_ID => '商品ID',
            self::BILLING_YEAR_MONTH => '請求年月',
            self::TOTAL_AMOUNT => '請求金額',
            self::TOTAL_QUANTITY => '請求数量',
            self::ADJUSTMENT_AMOUNT => '調整金額',
            self::ADJUSTMENT_QUANTITY => '調整数量',
            self::ADJUSTMENT_NOTE => '調整備考内容',
            self::INVOICE_AMOUNT => '請求書発行金額',
        };
    }
}
