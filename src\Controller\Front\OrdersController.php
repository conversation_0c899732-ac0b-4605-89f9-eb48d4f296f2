<?php

namespace App\Controller\Front;

use App\Controller\FrontController;
use App\Form\DisableParametersForm;
use App\Kuroko\Entity\Member;
use App\Service\OrdersService;
use Cake\Event\Event;
use Cake\Event\EventInterface;
use Cake\Log\Log;

class OrdersController extends FrontController
{

    public function index(): void
    {
        $this->_onSuccess(function (Event $event, array $orders) {
            $this->_setSuccessResponse([
                "orders" => $orders
            ]);
        });
        $this->_onError(function (Event $event, array $errors) {
            Log::debug(print_r($errors, true));
        });
        $this->_index((new OrdersService())
            ->setIdentity($this->Authentication->getIdentity())
            ->setForm(
                new DisableParametersForm()
            ));
    }

    public function add(): void
    {
        $this->_onSuccess(function (Event $event,) {
            $this->_setSuccessResponse([
                'success' => true,
                // 'member' => $member->getJsonData()
            ]);
        });
        $this->_onError(function (Event $event, array $errors) {
            Log::debug(print_r($errors, true));
        });
        $this->_add((new OrdersService())
            ->setIdentity($this->Authentication->getIdentity())
            ->setForm(
                new DisableParametersForm()
            ));
    }
}
