<?php

namespace App\Mailer\Sender\ToUser;

use App\Mailer\Sender\IToUserSender;
use App\Mailer\Sender\SenderTrait;
use App\Mailer\Sender\ToUserSenderTrait;

class SchoolBagMemberRegistSender implements IToUserSender
{
    use SenderTrait;
    use ToUserSenderTrait;

    public function getSubject(): string
    {
        return "＜重要＞【カバーミー】認証手続きのお願い";
    }

    public function getTemplate(): string
    {
        return "to_user/SchoolBagMemberRegistSender";
    }
}
