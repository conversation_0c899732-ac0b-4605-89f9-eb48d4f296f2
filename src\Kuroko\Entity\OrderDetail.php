<?php

namespace App\Kuroko\Entity;

class OrderDetail implements IKurokoEntity
{
    use KurokoEntityTrait;

    /**
     * @param array $data
     * @return array
     */
    public function initialize(array $data): array
    {
        return $data;
    }

    public function getOrderDetailId(): int
    {
        return $this->get("order_detail_id");
    }

    public function getProductId(): int
    {
        return $this->get("product_id");
    }

    public function getProductName(): string
    {
        return $this->get("product_name") ?? "";
    }

    public function getProductMemo(): string
    {
        return $this->get("product_memo") ?? "";
    }

    public function getJsonData(): array
    {
        return [
            "order_detail_id" => $this->getOrderDetailId(),
            "product_id" => $this->getProductId(),
            "product_name" => $this->getProductName(),
            "product_memo" => $this->getProductMemo(),
        ];
    }
}
