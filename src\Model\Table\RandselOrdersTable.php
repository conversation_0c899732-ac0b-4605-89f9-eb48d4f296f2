<?php
declare(strict_types=1);

namespace App\Model\Table;

use App\Model\Behavior\RandselOrdersBehavior;
use App\Model\Entity\RandselOrder;
use Cake\Datasource\EntityInterface;
use Cake\Datasource\ResultSetInterface;
use Cake\ORM\Behavior\TimestampBehavior;
use Cake\ORM\RulesChecker;
use Cake\ORM\Table;
use Cake\Validation\Validator;

/**
 * RandselOrders Model
 *
 * @property \App\Model\Table\GeneralUsersTable&\Cake\ORM\Association\BelongsTo $GeneralUsers
 * @property \App\Model\Table\ProductsTable&\Cake\ORM\Association\BelongsTo $Products
 * @property \App\Model\Table\MakersTable&\Cake\ORM\Association\BelongsTo $Makers
 * @property \App\Model\Table\BrandsTable&\Cake\ORM\Association\BelongsTo $Brands
 *
 * @method RandselOrder newEmptyEntity()
 * @method RandselOrder newEntity(array $data, array $options = [])
 * @method RandselOrder[] newEntities(array $data, array $options = [])
 * @method RandselOrder get($primaryKey, $options = [])
 * @method RandselOrder findOrCreate($search, ?callable $callback = null, $options = [])
 * @method RandselOrder patchEntity(EntityInterface $entity, array $data, array $options = [])
 * @method RandselOrder[] patchEntities(iterable $entities, array $data, array $options = [])
 * @method RandselOrder|false save(EntityInterface $entity, $options = [])
 * @method RandselOrder saveOrFail(EntityInterface $entity, $options = [])
 * @method RandselOrder[]|ResultSetInterface|false saveMany(iterable $entities, $options = [])
 * @method RandselOrder[]|ResultSetInterface saveManyOrFail(iterable $entities, $options = [])
 * @method RandselOrder[]|ResultSetInterface|false deleteMany(iterable $entities, $options = [])
 * @method RandselOrder[]|ResultSetInterface deleteManyOrFail(iterable $entities, $options = [])
 *
 * @mixin TimestampBehavior
 * @mixin RandselOrdersBehavior
 */
class RandselOrdersTable extends Table
{
    /**
     * Initialize method
     *
     * @param array $config The configuration for the Table.
     * @return void
     */
    public function initialize(array $config): void
    {
        parent::initialize($config);

        $this->setTable('randsel_orders');
        $this->setDisplayField('id');
        $this->setPrimaryKey('id');

        $this->addBehavior('Timestamp');
        $this->addBehavior('RandselOrders');

        $this->belongsTo('GeneralUsers', [
            'foreignKey' => 'general_user_id',
        ]);

        $this->belongsTo('Products', [
            'foreignKey' => 'product_id',
        ]);

        $this->belongsTo('Makers', [
            'foreignKey' => 'maker_id',
        ]);

        $this->belongsTo('Brands', [
            'foreignKey' => 'brand_id',
        ]);
    }

    /**
     * Default validation rules.
     *
     * @param Validator $validator Validator instance.
     * @return Validator
     */
    public function validationDefault(Validator $validator): Validator
    {
        $validator
            ->integer('maker_id')
            ->requirePresence('maker_id', 'create')
            ->notEmptyString('maker_id');

        $validator
            ->integer('member_id')
            ->allowEmptyString('member_id');

        $validator
            ->integer('general_user_id')
            ->allowEmptyString('general_user_id');

        $validator
            ->integer('product_id')
            ->requirePresence('product_id', 'create')
            ->notEmptyString('product_id');

        $validator
            ->integer('type')
            ->requirePresence('type', 'create')
            ->notEmptyString('type')
            ->inList('type', [1, 2], 'カタログタイプは1（紙）または2（デジタル）を指定してください');

        $validator
            ->scalar('product_name')
            ->maxLength('product_name', 255)
            ->requirePresence('product_name', 'create')
            ->notEmptyString('product_name');

        $validator
            ->integer('price')
            ->requirePresence('price', 'create')
            ->notEmptyString('price');

        $validator
            ->integer('status')
            ->allowEmptyString('status');

        $validator
            ->dateTime('status_modified')
            ->allowEmptyDateTime('status_modified');

        $validator
            ->integer('approval_type')
            ->allowEmptyString('approval_type');

        $validator
            ->boolean('is_confirmed')
            ->allowEmptyString('is_confirmed');

        $validator
            ->dateTime('confirmed')
            ->allowEmptyDateTime('confirmed');

        $validator
            ->scalar('name1')
            ->allowEmptyString('name1');

        $validator
            ->scalar('name2')
            ->allowEmptyString('name2');

        $validator
            ->scalar('name1_hurigana')
            ->allowEmptyString('name1_hurigana');

        $validator
            ->scalar('name2_hurigana')
            ->allowEmptyString('name2_hurigana');

        $validator
            ->scalar('zip_code')
            ->allowEmptyString('zip_code');

        $validator
            ->scalar('tdfk_cd')
            ->allowEmptyString('tdfk_cd');

        $validator
            ->scalar('address1')
            ->allowEmptyString('address1');

        $validator
            ->scalar('address2')
            ->allowEmptyString('address2');

        $validator
            ->scalar('address3')
            ->allowEmptyString('address3');

        $validator
            ->scalar('tel')
            ->allowEmptyString('tel');

        $validator
            ->scalar('email')
            ->allowEmptyString('email');

        $validator
            ->boolean('email_send_ng_flg')
            ->allowEmptyString('email_send_ng_flg');

        $validator
            ->scalar('survey_json')
            ->allowEmptyString('survey_json');

        return $validator;
    }

    /**
     * Returns a rules checker object that will be used for validating
     * application integrity.
     *
     * @param \Cake\ORM\RulesChecker $rules The rules object to be modified.
     * @return \Cake\ORM\RulesChecker
     */
    public function buildRules(RulesChecker $rules): RulesChecker
    {
        $rules->add($rules->existsIn('general_user_id', 'GeneralUsers'), ['errorField' => 'general_user_id']);

        return $rules;
    }
}



