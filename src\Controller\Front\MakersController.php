<?php

declare(strict_types=1);

namespace App\Controller\front;

use App\Controller\FrontController;
use App\Form\DisableParametersForm;
use App\Service\MakersService;
use Cake\Event\Event;
use Cake\Log\Log;

/**
 * Products Controller
 */
class MakersController extends FrontController
{
    /**
     * Index method
     */
    public function index(): void
    {
        $this->_onSuccess(function (Event $event, array $makers) {
            $this->_setSuccessResponse([
                'makers' => $makers
            ]);
        });
        $this->_onError(function (Event $event, array $errors) {
            Log::debug(print_r($errors, true));
        });

        $this->_index((new MakersService())->setForm(
            new DisableParametersForm()
        ));
    }
}
