// https://nuxt.com/docs/api/configuration/nuxt-config
import vuetify, { transformAssetUrls } from "vite-plugin-vuetify";
// import https from "https";
// import KurokoContentListClient from "@/src/lib/http/kuroko/KurokoContentListClient";
// const KRC_API_BASE = process.env.KRC_API_BASE;
const NUXT_API_SECRET = process.env.NUXT_API_SECRET;
const NUXT_BASE_URL = process.env.NUXT_BASE_URL;
const NUXT_STATIC_API_KEY = process.env.NUXT_STATIC_API_KEY;
const NUXT_API_BASE = process.env.NUXT_API_BASE;
const NUXT_APPLICATION_ENV = process.env.NUXT_APPLICATION_ENV;

const NUXT_FRONT_API_BASE = process.env.NUXT_FRONT_API_BASE;
const NUXT_BUILD_API_BASE = process.env.NUXT_BUILD_API_BASE;
const NUXT_FRONT_API_CM_TOKEN = process.env.NUXT_FRONT_API_CM_TOKEN;
const NUXT_BUILD_API_CM_TOKEN = process.env.NUXT_BUILD_API_CM_TOKEN;

const ZIP_CODE_API_ENDPOINT = process.env.ZIP_CODE_API_ENDPOINT;
const ZIP_CODE_API_KEY = process.env.ZIP_CODE_API_KEY;

const IS_PRODUCTION = NUXT_APPLICATION_ENV === "production";
const IS_DEVELOP = NUXT_APPLICATION_ENV === "develop";
const IS_STAGING = NUXT_APPLICATION_ENV === "staging";
// const httpsAgent = new https.Agent({
//     rejectUnauthorized: false, // 証明書検証を無視
// });

// const routes = [
//     { topics_id: 1, content: "ハロー" },
//     { topics_id: 2, content: "ハロー2" },
// ].map((mount: ObjType) => {
//     return `/contents/${mount.topics_id}`;
// });

export default defineNuxtConfig({
    runtimeConfig: {
        // The private keys which are only available within server-side
        apiSecret: NUXT_API_SECRET,
        buildApiBase: NUXT_BUILD_API_BASE,
        buildApiCmToken: NUXT_BUILD_API_CM_TOKEN,
        // Keys within public, will be also exposed to the client-side
        public: {
            apiBase: NUXT_API_BASE,
            staticApiKey: NUXT_STATIC_API_KEY,
            frontApiBase: NUXT_FRONT_API_BASE,
            frontApiCmToken: NUXT_FRONT_API_CM_TOKEN,
            isProduction: IS_PRODUCTION,
            // 郵便番号住所検索API のエンドポイントURL
            zipCodeApiEndpoint: ZIP_CODE_API_ENDPOINT,
            // 郵便番号住所検索API のAPIキー
            zipCodeApiKey: ZIP_CODE_API_KEY,
        },
    },
    app: {
        baseURL: NUXT_BASE_URL ?? "/",
        head: {
            title: IS_PRODUCTION
                ? "ランドセルカタログ一括請求 | カバーミー"
                : IS_STAGING
                  ? "【stg】ランドセルカタログ一括請求 | カバーミー"
                  : IS_DEVELOP
                    ? "【dev】ランドセルカタログ一括請求 | カバーミー"
                    : "【local】ランドセルカタログ一括請求 | カバーミー",
            meta: [
                { charset: "utf-8" },
                {
                    name: "viewport",
                    content: "width=device-width, initial-scale=1",
                },
                {
                    name: "description",
                    content: "ランドセル選びならカバーミー",
                },
                { property: "og:locale", content: "ja_JP" },
                { property: "og:type", content: "website" },
                {
                    property: "og:title",
                    content: "ランドセルカタログ一括請求 | カバーミー",
                },
                {
                    property: "og:description",
                    content: "ランドセル選びならカバーミー",
                },
                {
                    property: "og:url",
                    content: "https://coverme.jp/catalog/form/randsel/",
                },
                { property: "og:site_name", content: "カバーミー" },
                { name: "twitter:card", content: "summary_large_image" },
                { name: "twitter:site", content: "coverme_randsel" },
            ],
            link: [
                {
                    rel: "icon",
                    href: "/wp-content/uploads/2024/07/cmfavi-150x150.png",
                },
            ],
            script: [
                // {
                //     src: "https://www.googletagmanager.com/gtag/js?id=AW-16713629129",
                //     async: true,
                // },
                {
                    innerHTML: `
                        (function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
                        new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
                        j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
                        'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
                        })(window,document,'script','dataLayer','GTM-5MQ3PTHC');
                    `,
                    type: "text/javascript",
                },
            ],
        },
    },
    devtools: { enabled: IS_PRODUCTION ? false : true },
    // alias: {
    //     "~": "./",
    //     "@": "./",
    // },
    build: {
        transpile: ["vuetify"],
    },
    modules: [
        "@nuxtjs/device",
        "@nuxt/eslint",
        "@pinia/nuxt",
        (_options, nuxt): void => {
            nuxt.hooks.hook("vite:extendConfig", (config) => {
                if (!config.plugins) {
                    config.plugins = []; // plugins を初期化してから push
                }
                config.plugins.push(
                    vuetify({
                        // https://stackoverflow.com/questions/73691720/change-default-font-nuxt-3-vuetify-3-project#answer-74980698
                        styles: {
                            configFile: new URL(
                                "assets/styles/utils/vuetifyVariables.scss",
                                import.meta.url,
                            ).pathname,
                        },
                        autoImport: true,
                    }),
                );
            });
        },
    ],
    vite: {
        vue: {
            template: {
                transformAssetUrls,
            },
        },
        css: {
            preprocessorOptions: {
                scss: {
                    additionalData:
                        '@import "~~/assets/styles/utils/_variables.scss";',
                },
            },
        },
    },
    ssr: true, // SSRを有効にしてプリレンダリングを行う
    css: ["~~/assets/styles/main.scss"],
    // generate: {
    //     routes: routes,
    // },
    hooks: {
        // https://zenn.dev/fabon/articles/846efeb379b9a5
        "prerender:routes": (context) => {
            for (const path of [...context.routes]) {
                if (!path.endsWith(".html") && path !== "/") {
                    context.routes.delete(path);
                    context.routes.add(`${path}/`);
                }
            }
        },
        //     async "nitro:config"(nitroConfig) {
        //         // if (nitroConfig.dev) {
        //         //     return;
        //         // }
        //         // const res = await axios.get("https://api.nuxtjs.dev/mountains")
        //         if (nitroConfig.prerender?.routes === undefined) {
        //             return;
        //         }
        //         // const response = await axios.get(
        //         //     "https://swb-sbpg.g.kuroco.app/rcms-api/1/content/list",
        //         //     {
        //         //         headers: {
        //         //             "X-RCMS-API-ACCESS-TOKEN":
        //         //                 "f4eb92aef3293a0e6dc8a2b20fa42e006774a1f8580f423a8256cb80b5f51fa3",
        //         //         },
        //         //         withCredentials: true, // 認証情報を含めるために true に設定
        //         //         httpsAgent,
        //         //     },
        //         // );
        //         // nitroConfig.prerender.routes = response.data.list.map(
        //         nitroConfig.prerender.routes = [
        //             { topics_id: 1, content: "ハロー" },
        //             { topics_id: 2, content: "ハロー2" },
        //         ].map((mount: ObjType) => {
        //             return `/contents/${mount.topics_id}`;
        //         });
        //     },
    },
    experimental: {
        defaults: {
            nuxtLink: {
                activeClass: "nuxt-link-active",
                trailingSlash: "append",
            },
        },
    },
});
