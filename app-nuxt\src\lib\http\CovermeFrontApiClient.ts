import type { RuntimeConfig } from "nuxt/schema";
import {
    API_TOKEN_HEADER_KEY,
    API_TOKEN_HEADER_AUTHORIZATION,
} from "~/src/config";
import { useAuthStore } from "~/src/stores/auth";
import axios from "axios";

export default abstract class CovermeFrontApiClient {
    protected config: RuntimeConfig;

    constructor(protected runTimeConfig: RuntimeConfig) {
        this.config = runTimeConfig;
    }

    static create<T extends CovermeFrontApiClient>(
        this: new (config: RuntimeConfig) => T,
        runTimeConfig: RuntimeConfig,
    ): T {
        return new this(runTimeConfig);
    }

    protected abstract getResource(): string;

    protected getHeader(): ObjType {
        const token = useAuthStore().getAccessToken();
        const headers: ObjType = {
            [API_TOKEN_HEADER_KEY]: String(this.config.public.frontApiCmToken),
        };

        if (token) {
            // headers.Authorization = `Bearer ${token}`;
            headers[API_TOKEN_HEADER_AUTHORIZATION] = `Bearer ${token}`;
        }

        return headers;
    }

    public async post<T, R>(data?: T): Promise<R> {
        return new Promise((resolve, reject) => {
            axios
                .post(
                    `${this.config.public.frontApiBase}${this.getResource()}.json`,
                    data,
                    {
                        headers: this.getHeader(),
                        // withCredentials: true, // 認証情報を含めるために true に設定
                        // httpsAgent,
                    },
                )
                .then((response) => {
                    resolve(response.data as R);
                })
                .catch(reject);
        });
    }

    /**
     * ユーザ情報更新
     * @param id
     * @param data
     */
    public async put<T, R>(id: string, data?: T): Promise<R> {
        return new Promise((resolve, reject) => {
            axios
                .put(
                    `${this.config.public.frontApiBase}${this.getResource()}/${id}.json`,
                    data,
                    {
                        headers: this.getHeader(),
                        // withCredentials: true, // 認証情報を含めるために true に設定
                        // httpsAgent,
                    },
                )
                .then((response) => {
                    resolve(response.data as R);
                })
                .catch(reject);
        });
    }

    public async delete<R>(id: string | number): Promise<R> {
        return new Promise((resolve, reject) => {
            axios
                .delete(
                    `${this.config.public.frontApiBase}${this.getResource()}/${id}.json`,
                    {
                        headers: this.getHeader(),
                    },
                )
                .then((response) => {
                    resolve(response.data as R);
                })
                .catch(reject);
        });
    }

    public async index<T>(
        params?: Record<string, string | number | string[] | number[]>,
    ): Promise<T> {
        const response = await axios.get(
            `${this.config.public.frontApiBase}${this.getResource()}.json`,
            {
                headers: this.getHeader(),
                params,
            },
        );
        return response.data as T;
    }
}
