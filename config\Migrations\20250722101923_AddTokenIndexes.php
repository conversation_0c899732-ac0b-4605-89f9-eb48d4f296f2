<?php
declare(strict_types=1);

use Migrations\AbstractMigration;

class AddTokenIndexes extends AbstractMigration
{
    public function up()
    {
        // user_tokens インデックス
        $this->table('user_tokens')
            ->addIndex(['token', 'type', 'expires'], ['name' => 'idx_user_tokens_token_type_expires'])
            ->addIndex(['general_user_id', 'type'], ['name' => 'idx_user_tokens_general_user_type'])
            ->update();

        // swb_user_tokens インデックス
        $this->table('swb_user_tokens')
            ->addIndex(['token', 'type', 'expires'], ['name' => 'idx_swb_user_tokens_token_type_expires'])
            ->addIndex(['swb_user_id', 'type'], ['name' => 'idx_swb_user_tokens_swb_user_type'])
            ->update();

        // maker_user_tokens インデックス
        $this->table('maker_user_tokens')
            ->addIndex(['token', 'type', 'expires'], ['name' => 'idx_maker_user_tokens_token_type_expires'])
            ->addIndex(['maker_user_id', 'type'], ['name' => 'idx_maker_user_tokens_maker_user_type'])
            ->update();

        // ユーザーテーブルインデックス
        $this->table('general_users')
            ->addIndex(['email', 'deleted'], ['name' => 'idx_general_users_email_deleted'])
            ->update();

        $this->table('swb_users')
            ->addIndex(['email', 'deleted'], ['name' => 'idx_swb_users_email_deleted'])
            ->update();

        $this->table('maker_users')
            ->addIndex(['email', 'deleted'], ['name' => 'idx_maker_users_email_deleted'])
            ->update();
    }

    public function down()
    {
        // インデックスの削除
        $this->table('user_tokens')
            ->removeIndex(['token', 'type', 'expires'])
            ->removeIndex(['general_user_id', 'type'])
            ->update();

        $this->table('swb_user_tokens')
            ->removeIndex(['token', 'type', 'expires'])
            ->removeIndex(['swb_user_id', 'type'])
            ->update();

        $this->table('maker_user_tokens')
            ->removeIndex(['token', 'type', 'expires'])
            ->removeIndex(['maker_user_id', 'type'])
            ->update();

        $this->table('general_users')
            ->removeIndex(['email', 'deleted'])
            ->update();

        $this->table('swb_users')
            ->removeIndex(['email', 'deleted'])
            ->update();

        $this->table('maker_users')
            ->removeIndex(['email', 'deleted'])
            ->update();
    }
}
