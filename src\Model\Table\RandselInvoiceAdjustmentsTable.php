<?php

declare(strict_types=1);

namespace App\Model\Table;

use Cake\ORM\Query;
use Cake\ORM\RulesChecker;
use Cake\ORM\Table;
use Cake\Validation\Validator;

/**
 * RandselInvoiceAdjustments Model
 *
 * @method \App\Model\Entity\RandselInvoiceAdjustment newEmptyEntity()
 * @method \App\Model\Entity\RandselInvoiceAdjustment newEntity(array $data, array $options = [])
 * @method \App\Model\Entity\RandselInvoiceAdjustment[] newEntities(array $data, array $options = [])
 * @method \App\Model\Entity\RandselInvoiceAdjustment get($primaryKey, $options = [])
 * @method \App\Model\Entity\RandselInvoiceAdjustment findOrCreate($search, ?callable $callback = null, $options = [])
 * @method \App\Model\Entity\RandselInvoiceAdjustment patchEntity(\Cake\Datasource\EntityInterface $entity, array $data, array $options = [])
 * @method \App\Model\Entity\RandselInvoiceAdjustment[] patchEntities(iterable $entities, array $data, array $options = [])
 * @method \App\Model\Entity\RandselInvoiceAdjustment|false save(\Cake\Datasource\EntityInterface $entity, $options = [])
 * @method \App\Model\Entity\RandselInvoiceAdjustment saveOrFail(\Cake\Datasource\EntityInterface $entity, $options = [])
 * @method \App\Model\Entity\RandselInvoiceAdjustment[]|\Cake\Datasource\ResultSetInterface|false saveMany(iterable $entities, $options = [])
 * @method \App\Model\Entity\RandselInvoiceAdjustment[]|\Cake\Datasource\ResultSetInterface saveManyOrFail(iterable $entities, $options = [])
 * @method \App\Model\Entity\RandselInvoiceAdjustment[]|\Cake\Datasource\ResultSetInterface|false deleteMany(iterable $entities, $options = [])
 * @method \App\Model\Entity\RandselInvoiceAdjustment[]|\Cake\Datasource\ResultSetInterface deleteManyOrFail(iterable $entities, $options = [])
 *
 * @mixin \Cake\ORM\Behavior\TimestampBehavior
 */
class RandselInvoiceAdjustmentsTable extends Table
{
    /**
     * Initialize method
     *
     * @param array $config The configuration for the Table.
     * @return void
     */
    public function initialize(array $config): void
    {
        parent::initialize($config);

        $this->setTable('randsel_invoice_adjustments');
        $this->setDisplayField('billing_year_month');
        $this->setPrimaryKey('id');

        $this->addBehavior('Timestamp');

        $this->hasMany('RandselInvoiceAdjustmentHistories', [
            'foreignKey' => 'invoice_adjustment_id',
            'dependent' => true,
            'propertyName' => 'histories',
            'saveStrategy' => 'append'
        ]);
    }

    /**
     * Default validation rules.
     *
     * @param \Cake\Validation\Validator $validator Validator instance.
     * @return \Cake\Validation\Validator
     */
    public function validationDefault(Validator $validator): Validator
    {
        $validator
            ->integer('maker_id')
            ->requirePresence('maker_id', 'create')
            ->notEmptyString('maker_id');

        $validator
            ->integer('product_id')
            ->requirePresence('product_id', 'create')
            ->notEmptyString('product_id');

        $validator
            ->scalar('billing_year_month')
            ->maxLength('billing_year_month', 7)
            ->requirePresence('billing_year_month', 'create')
            ->notEmptyString('billing_year_month');

        $validator
            ->integer('adjustment_unit_price')
            ->notEmptyString('adjustment_unit_price');

        $validator
            ->integer('adjustment_quantity')
            ->notEmptyString('adjustment_quantity');

        $validator
            ->scalar('adjustment_note')
            ->allowEmptyString('adjustment_note');

        $validator
            ->boolean('integer')
            ->notEmptyString('integer');

        $validator
            ->integer('confirmed_by')
            ->allowEmptyString('confirmed_by');

        $validator
            ->dateTime('confirmed')
            ->allowEmptyDateTime('confirmed');

        $validator
            ->integer('created_by')
            ->allowEmptyString('created_by');

        return $validator;
    }

    /**
     * Returns a rules checker object that will be used for validating
     * application integrity.
     *
     * @param \Cake\ORM\RulesChecker $rules The rules object to be modified.
     * @return \Cake\ORM\RulesChecker
     */
    public function buildRules(RulesChecker $rules): RulesChecker
    {
        $rules->add($rules->isUnique(['maker_id', 'product_id', 'billing_year_month']), ['errorField' => 'maker_id']);

        return $rules;
    }
}
