<?php

namespace App\Service;

use App\Kuroko\Entity\IKurokoEntity;
use App\Model\Entity\GeneralUser;
use App\Enums\EntityFields\EAccessToken;
use App\Service\Traits\OrderTrait;
use App\Service\Traits\RegistrationOrderTrait;
use App\Service\UserRegistrationService;
use Cake\Datasource\ConnectionManager;
use Cake\Utility\Hash;
use Cake\Log\Log;
use Exception;

class SchoolBagOrdersService extends SchoolBagFormsService
{
    use OrderTrait;
    use RegistrationOrderTrait;
    
    public function initialize(array $config = []): void
    {
        parent::initialize($config);
        $this->initializeOrder();
    }

    public function add(array $data = []): IKurokoEntity | GeneralUser | null
    {
        $connection = ConnectionManager::get('default');
        $token = Hash::get($data, EAccessToken::ACCESS_TOKEN->value);

        try {
            return $connection->transactional(function () use ($token) {
                // ユーザー登録処理
                /** @var array $registrationData: ['user' => GeneralUser, 'temp_registration' => TemporaryRegistration, 'profile' => UserProfile, 'survey' => UserSurvey] */
                $registrationData = $this->createUserRegistrationService()->completeRegistration($token);
                if (!$registrationData) {
                    throw new Exception("ユーザー登録に失敗しました");
                }

                // 注文処理
                $result = $this->processOrder($registrationData['user'], $registrationData);
                if (!$result) {
                    throw new Exception("注文処理に失敗しました");
                }

                return $registrationData['user'];
            });
        } catch (Exception $exception) {
            $this->setErrors([
                '_system' => $exception->getMessage(),
            ]);
            Log::error("SchoolBagOrdersService::add failed: " . $exception->getMessage());
            return null;
        }
    }

    /**
     * UserRegistrationServiceのインスタンスを作成
     */
    protected function createUserRegistrationService(): UserRegistrationService
    {
        return new UserRegistrationService();
    }
}
