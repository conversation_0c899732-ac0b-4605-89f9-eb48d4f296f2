<?php

namespace Service;


use App\Enums\EntityFields\ERandselOrder;
use App\Enums\EntityFields\ESwbOrderForm;
use App\Enums\Options\ERandselOrderStatus;
use App\Kuroko\Entity\Member;
use App\Service\RandselOrdersService;
use App\Test\TestCase\AppTestCase;

class RandselOrdersServiceTest extends AppTestCase
{
    private $service;
    public function setUp(): void
    {
        $this->service = new RandselOrdersService();
        parent::setUp();
    }

    // php ./vendor/phpunit/phpunit/phpunit --filter "testCsvApproval" ./tests/TestCase/Service/RandselOrdersServiceTest.php
    public function testCsvApproval()
    {
        $service = new RandselOrdersService();
        debug($service->getRandselOrders());
        $member = new Member([
            "details" => [
                "id" => 1,
                "maker_id" => 17,
                "member_id" => 1,
            ],
            "id" => 1,
            "maker_id" => 17,
            "member_id" => 1,
        ]);
        $result = $service->setIdentity(
            $member
        )->csvApproval([
            [
                ERandselOrder::ID->value => 1,
                ERandselOrder::STATUS->value => ERandselOrderStatus::APPROVED->value,
            ],
            [
                ERandselOrder::ID->value => 2,
                ERandselOrder::STATUS->value => ERandselOrderStatus::REJECTED->value,
            ],
        ]);
        //        debug($service->getErrors());
        debug($service->getRandselOrders());
        $this->assertTrue($result);
    }

    // php ./vendor/phpunit/phpunit/phpunit --filter "testScreenApproval" ./tests/TestCase/Service/RandselOrdersServiceTest.php
    public function testScreenApproval()
    {
        $service = new RandselOrdersService();
        $member = new Member([
            "details" => [
                "id" => 1,
                "maker_id" => 17,
                "member_id" => 1,
            ],
            "id" => 1,
            "maker_id" => 17,
            "member_id" => 1,
        ]);
        $result = $service->setIdentity(
            $member
        )->screenApproval([
            [
                ERandselOrder::ID->value => 1,
                ERandselOrder::STATUS->value => ERandselOrderStatus::APPROVED->value,
            ],
            [
                ERandselOrder::ID->value => 2,
                ERandselOrder::STATUS->value => ERandselOrderStatus::REJECTED->value,
            ],
        ]);
        //        debug($service->getErrors());
        debug($service->getRandselOrders());
        $this->assertTrue($result);
    }


    // php ./vendor/phpunit/phpunit/phpunit --filter "testCreateOrder" ./tests/TestCase/Service/RandselOrdersServiceTest.php
    public function testCreateOrder()
    {

        $apiResponse = [
            'maker_id' => 1,
            'member_id' => 1,
            'product_id' => 1,
            'product_name' => 'Lorem ipsum dolor sit amet',
            'price' => 1,
            'name1' => '値',
            'name2' => '値',
            'name1_hurigana' => '値',
            'name2_hurigana' => '値',
            'zip_code' => '2222',
            'tdfk_cd' => '01',
            'address1' => '値',
            'address2' => '値',
            'address3' => '値',
            'tel' => '値',
            'email' => '値',
            'email_send_ng_flg' => 1,
            'survey_json' => '{aaaaa:aaaa}',
        ];

        $service = new RandselOrdersService();
        $service->createRandselOrder($apiResponse);
        debug($service->getRandselOrders());
        $this->assertTrue(true);
    }

    // php ./vendor/phpunit/phpunit/phpunit --filter "testGetMonthlyInvoices" ./tests/TestCase/Service/RandselOrdersServiceTest.php
    public function testGetMonthlyInvoices()
    {
        $conditions = [];
        $result = $this->service->getMonthlyInvoices($conditions);
        $this->assertIsArray($result);
        debug($result);

        $conditions = ['maker_id' => 17];
        $result = $this->service->getMonthlyInvoices($conditions);
        $this->assertIsArray($result);
        debug($result);

        $conditions = ['maker_id' => 17, 'group_by' => ESwbOrderForm::PRODUCT_ID->value];
        $result = $this->service->getMonthlyInvoices($conditions);
        $this->assertIsArray($result);
        debug($result);
    }
}
