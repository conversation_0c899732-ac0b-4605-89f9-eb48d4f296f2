<?php
declare(strict_types=1);

namespace App\Model\Table;

use Cake\ORM\Query;
use Cake\ORM\RulesChecker;
use Cake\ORM\Table;
use Cake\Validation\Validator;

/**
 * メーカーテーブル
 */
class MakersTable extends Table
{
    public function initialize(array $config): void
    {
        parent::initialize($config);

        $this->setTable('makers');
        $this->setDisplayField('name');
        $this->setPrimaryKey('id');

        // アソシエーション
        $this->hasMany('MakerStores', [
            'foreignKey' => 'maker_id',
            'dependent' => true,
        ]);
        
        $this->hasMany('Brands', [
            'foreignKey' => 'maker_id',
            'dependent' => true,
        ]);
        
        $this->hasMany('Products', [
            'foreignKey' => 'maker_id',
            'dependent' => true,
        ]);
        
        $this->hasMany('MakerUsers', [
            'foreignKey' => 'maker_id',
            'dependent' => true,
        ]);
        
        $this->hasMany('ExhibitionMakers', [
            'foreignKey' => 'maker_id',
            'dependent' => true,
        ]);
        
        $this->hasMany('LiveStreamMakers', [
            'foreignKey' => 'maker_id',
            'dependent' => true,
        ]);
        
        $this->hasMany('RandselOrders', [
            'foreignKey' => 'maker_id',
        ]);
        
        $this->hasMany('RandselInvoices', [
            'foreignKey' => 'maker_id',
        ]);
        
        $this->hasMany('RandselInvoiceAdjustments', [
            'foreignKey' => 'maker_id',
        ]);

        // 論理削除ビヘイビア
        $this->addBehavior('Muffin/Trash.Trash', [
            'field' => 'deleted',
            'events' => ['Model.beforeFind', 'Model.beforeDelete']
        ]);
    }

    public function validationDefault(Validator $validator): Validator
    {
        $validator
            ->scalar('name')
            ->maxLength('name', 255)
            ->requirePresence('name', 'create', __('メーカー名は必須です'))
            ->notEmptyString('name', __('メーカー名は必須です'));

        $validator
            ->scalar('description')
            ->allowEmptyString('description');

        $validator
            ->scalar('maker_image_url')
            ->maxLength('maker_image_url', 500)
            ->allowEmptyString('maker_image_url')
            ->url('maker_image_url', __('有効なURLを入力してください'));

        $validator
            ->scalar('maker_features_html')
            ->allowEmptyString('maker_features_html');

        $validator
            ->scalar('other_features_html')
            ->allowEmptyString('other_features_html');

        $validator
            ->scalar('billing_address')
            ->allowEmptyString('billing_address');

        $validator
            ->scalar('customer_code')
            ->maxLength('customer_code', 100)
            ->allowEmptyString('customer_code');

        $validator
            ->scalar('customer_name')
            ->maxLength('customer_name', 255)
            ->allowEmptyString('customer_name');

        $validator
            ->integer('billing_cycle')
            ->requirePresence('billing_cycle', 'create')
            ->notEmptyString('billing_cycle')
            ->range('billing_cycle', [1, 3], __('請求サイクルは1〜3の値を入力してください'));

        $validator
            ->scalar('contact_name')
            ->maxLength('contact_name', 255)
            ->allowEmptyString('contact_name');

        return $validator;
    }

    public function buildRules(RulesChecker $rules): RulesChecker
    {
        $rules->add($rules->isUnique(['name'], __('このメーカー名は既に使用されています')));
        $rules->add($rules->isUnique(['customer_code'], __('この顧客コードは既に使用されています')));

        return $rules;
    }

    /**
     * アクティブなメーカーを取得するファインダー
     */
    public function findActive(Query $query, array $options): Query
    {
        return $query->where(['deleted IS' => null]);
    }

    /**
     * 請求サイクル別にメーカーを取得するファインダー
     */
    public function findByBillingCycle(Query $query, array $options): Query
    {
        $billingCycle = $options['billing_cycle'] ?? null;
        if ($billingCycle !== null) {
            $query->where(['billing_cycle' => $billingCycle]);
        }
        return $query;
    }

    /**
     * メーカー名で検索するファインダー
     */
    public function findByName(Query $query, array $options): Query
    {
        $name = $options['name'] ?? null;
        if (!empty($name)) {
            $query->where(['name LIKE' => '%' . $name . '%']);
        }
        return $query;
    }

    /**
     * 顧客コードでメーカーを取得
     */
    public function findByCustomerCode(string $customerCode)
    {
        return $this->find()
            ->where(['customer_code' => $customerCode])
            ->first();
    }
}
