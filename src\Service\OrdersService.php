<?php

namespace App\Service;

use App\Kuroko\Entity\IKurokoEntity;
use App\Kuroko\Entity\Member;
use App\Service\Traits\OrderTrait;
use App\Service\Traits\StandardOrderTrait;
use App\Service\RandselOrdersService;
use BadMethodCallException;
use Cake\Datasource\ConnectionManager;
use Cake\Datasource\EntityInterface;
use Cake\Log\Log;
use Exception;

class OrdersService implements IRestService
{
    use ServiceTrait, OrderTrait, StandardOrderTrait;

    protected array $_defaultConfig = [];

    public function add(array $data = []): EntityInterface|null|IKurokoEntity
    {
        // /** @var Order[] $response */
        // $oldOrders = $this->index();
        // // debug($oldOrders);
        // /** @var Member $member */
        // $member = $this->getIdentity();
        // /** @var ECOrder[] $orders */
        // $orders = $this->_createPurchase($member->getAccessToken(), $data);

        // // debug($orders);

        // AppMailer::sendToUser(
        //     (new OrderCompleteSender(
        //         $orders[0]->getMemberEmail(),
        //         $orders[0]->getMemberName1()
        //     ))->setViewVars(['newOrders' => $orders, 'oldOrders' => $oldOrders,])
        // );

        $connection = ConnectionManager::get('default');
        $kurocoMember = $this->getIdentity();
        Log::debug("OrdersService::add");

        try {
            $result = $connection->transactional(function () use ($kurocoMember, $data) {
                // 注文処理
                $result = $this->processOrder($kurocoMember, $data);
                if (!$result) {
                    Log::error("OrdersService::add processOrder failed");
                    throw new Exception("注文処理に失敗しました");
                }
                Log::debug("OrdersService::add processOrder success. ". print_r($kurocoMember, true));
                return $kurocoMember;
            });
        } catch (Exception $exception) {
            $this->setErrors([
                '_system' => $exception->getMessage(),
            ]);
            Log::error("OrdersService::add failed: " . $exception->getMessage());
            return null;
        }

        return null;
    }

    public function edit(string $id, array $data = []): EntityInterface|null|IKurokoEntity
    {
        throw new BadMethodCallException();
    }

    public function view(string $id, array $data = []): EntityInterface|null|IKurokoEntity
    {
        throw new BadMethodCallException();
    }

    public function index(array $data = []): array|EntityInterface|null|IKurokoEntity
    {
        // $ecOrders = new ECOrders();
        /** @var Member $member */
        $member = $this->getIdentity();
        // /** @var Order[] $response */
        // $response = $ecOrders->list($member->getAccessToken());

        $response = (new RandselOrdersService())->getRandselOrders([
            "general_user_id" => $member->getId(),
        ]);

        Log::debug("OrdersService::index");

        return $response;
    }

    public function delete(string $id, array $data = []): bool
    {
        throw new BadMethodCallException();
    }

    public function initialize(): void
    {
    }
}
