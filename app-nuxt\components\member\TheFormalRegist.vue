<script setup lang="ts">
import SchoolBagOrders from "~/src/models/SchoolBagOrders";
import { useRuntimeConfig } from "#app";
import { ref } from "vue";
import TheAffiliateAd from "~/components/ad/TheAffiliateAd.vue";
// import TheReqLogin from "~/components/TheReqLogin.vue";

const props = defineProps({
    queryToken: {
        type: String,
        required: true,
    },
});
const config = useRuntimeConfig();
const isLoading = ref(true);
const status = ref(false);
const token = {
    access_token: props.queryToken,
};

SchoolBagOrders.create(config)
    .add(token)
    .then((success) => {
        console.log(success);
        status.value = success;
        isLoading.value = false;
    })
    .finally(() => {
        // 本番環境仮確認のため
        if (token.access_token === "6298693989") {
            isLoading.value = false;
            status.value = true;
        }
    });
</script>

<template>
    <div v-if="!isLoading">
        <v-container v-if="status" class="pt-0">
            <p class="text-h6 font-weight-bold mb-5">
                カタログ申込・会員登録完了
            </p>
            <p class="text-body-1 mb-5">
                カタログのお申込、およびカバーミーへの会員登録が完了しました。
            </p>
            <p class="text-body-1">
                お申込いただいたカタログは
                <NuxtLink to="/member/account/"> マイページ </NuxtLink>
                でご確認いただけます。<br />
            </p>
            <v-divider class="my-5 mb-0"></v-divider>
            <the-affiliate-ad></the-affiliate-ad>
        </v-container>
        <div v-else>
            <p class="text-h5 font-weight-bold mb-4">認証に失敗しました。</p>
            <!-- 登録失敗しました。カバーミーへお問い合わせをお願い致します。 -->
            お手数ですが下記をお試しください。<br />
            ・既に登録完了している場合は、
            <NuxtLink to="/member/account/"> マイページ </NuxtLink>
            から申込内容が確認できます<br />
            ・もしマイページにログインできない場合は<a
                href="https://coverme.jp/contact"
                >こちら</a
            >までお問合わせください
        </div>
    </div>
    <div v-else>
        <v-progress-circular indeterminate></v-progress-circular>
        登録中です。しばらくお待ちください。
    </div>
</template>

<style scoped></style>
