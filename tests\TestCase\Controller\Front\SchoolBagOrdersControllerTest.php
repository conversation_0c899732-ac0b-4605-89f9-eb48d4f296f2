<?php

declare(strict_types=1);

namespace App\Test\TestCase\Controller\Front;


use App\Test\TestCase\Controller\ApiTestCase;
use Cake\Utility\Hash;

class SchoolBagOrdersControllerTest extends ApiTestCase
{
    /**
     * Test add method
     *
     * @return void
     * @uses \App\Controller\Front\SchoolBagOrdersController::add()
     * php ./vendor/phpunit/phpunit/phpunit --filter "testAdd" ./tests/TestCase/Controller/Front/SchoolBagOrdersControllerTest.php
     */
    public function testAdd(): void
    {
        $this->setFrontAuthorized();

        $data = [
            'access_token' => 'OTZiMzJkY2U1Y2E3NTlmMmVjN2ZmNTE0NjMyMzI2MGY2Mjk1NTU3MzZhNTlkOWRlZmRjOWZmNmZiZTI2NjM3MprRufcj4UOAbBut1UNOEZGIuCqmUrif4LelgbpFTVjQTcuRPzVq97J2EhpiArqR5iBmRveQUXFX+tnU9c2aNxr/ZrvetqYXu1H7vkM2A+R/qTlL/ogvL9Ihf+/sqQDusA=='
        ];
        $this->post('/front/school-bag-orders.json', $data);
        $response = $this->bodyToArray($this->_response->getBody());
        debug($response);
        $this->assertResponseOk(print_r(Hash::flatten($response), true));
    }
}
