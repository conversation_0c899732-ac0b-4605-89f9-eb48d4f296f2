import { BaseLogins } from "~/src/models/base/BaseLogins";
import ClientAuthenticationsClient from "~/src/lib/http/coverme-front/ClientAuthenticationsClient";
import { SCHOOL_BAG_FORM_FROM_ID } from "~/src/config";

export default class ClientLogins extends BaseLogins {
    // public view() {}
    protected client = ClientAuthenticationsClient;
    /**
     *
     * @param {{email:"", password:""} } data
     */
    // public add(data: TLogin): Promise<string | boolean> {
    //     const config = this.config;
    //     return new Promise((resolve) => {
    //         ClientAuthenticationsClient.create(config)
    //             .post<TLogin, TResponseAccessToken>(data)
    //             //@todo レスポンスをオブジェクトクラスにする、レスポンス項目は検討が必要
    //             .then(({ access_token }) => resolve(access_token))
    //             .catch(() => resolve(false));
    //     });
    // }
    /**
     *
     */
    // public delete(): Promise<boolean> {
    //     const config = this.config;
    //     return new Promise((resolve) => {
    //         ClientAuthenticationsClient.create(config)
    //             .delete<TResponseSuccess>(SCHOOL_BAG_FORM_FROM_ID)
    //             //@todo レスポンスをオブジェクトクラスにする、レスポンス項目は検討が必要
    //             .then(({ success }) => resolve(!!success))
    //             .catch(() => resolve(false));
    //     });
    // }
    public delete(): Promise<boolean> {
        return super.delete(SCHOOL_BAG_FORM_FROM_ID);
    }
}
