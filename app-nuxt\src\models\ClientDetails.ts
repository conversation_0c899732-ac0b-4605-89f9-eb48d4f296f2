import { Model } from "~/src/models/Model";
import { Member } from "~/src/models/entry/Member";
import ClientDetailsClient from "~/src/lib/http/coverme-front/ClientDetailsClient";

export default class ClientDetails extends Model {
    public index(): Promise<Member | null> {
        const config = this.config;
        return new Promise((resolve) => {
            ClientDetailsClient.create(config)
                .index<TMemberResponse>()
                .then(({ member }) => resolve(Member.create(member)))
                .catch(() => resolve(null));
        });
    }
}
