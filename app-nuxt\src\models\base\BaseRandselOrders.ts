import { Model } from "~~/src/models/Model";
import type ClientRandselOrdersClient from "~/src/lib/http/coverme-front/ClientRandselOrdersClient";
import RandselOrder from "~/src/models/entry/RandselOrder";

export abstract class BaseRandselOrders extends Model {
    protected abstract client: ClientRandselOrdersClient;

    public index(
        params?: Record<string, string | number | string[] | number[]>,
    ): Promise<RandselOrder[]> {
        const config = this.config;
        return new Promise((resolve, reject) => {
            this.client
                .create(config)
                .index<TRandselOrdersResponse>(params)
                .then(({ randsel_orders }) =>
                    resolve(RandselOrder.creates(randsel_orders)),
                )
                .catch((error) => reject(new Error(error)));
        });
    }
}
