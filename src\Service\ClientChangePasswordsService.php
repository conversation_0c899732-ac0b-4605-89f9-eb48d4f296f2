<?php

namespace App\Service;

use App\Enums\EntityFields\ESchoolBagForm;
use App\Kuroko\Entity\Member;
use App\Mailer\AppMailer;
use App\Mailer\Sender\ToClient\PasswordResetCompletedSender;
use Cake\Utility\Hash;

class ClientChangePasswordsService extends ChangePasswordsService
{
    protected function sendPasswordResetCompleted(Member $member): void
    {
        AppMailer::sendToClient(new PasswordResetCompletedSender(
            Hash::get($member->getJsonData(), ESchoolBagForm::EMAIL->value),
            Hash::get($member->getJsonData(), ESchoolBagForm::NAME1->value)
        ));
    }
}
