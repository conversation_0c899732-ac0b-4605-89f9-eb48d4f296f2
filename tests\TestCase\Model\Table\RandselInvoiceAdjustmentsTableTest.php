<?php
declare(strict_types=1);

namespace App\Test\TestCase\Model\Table;

use App\Model\Table\RandselInvoiceAdjustmentsTable;
use Cake\TestSuite\TestCase;

/**
 * App\Model\Table\RandselInvoiceAdjustmentsTable Test Case
 */
class RandselInvoiceAdjustmentsTableTest extends TestCase
{
    /**
     * Test subject
     *
     * @var \App\Model\Table\RandselInvoiceAdjustmentsTable
     */
    protected $RandselInvoiceAdjustments;

    /**
     * Fixtures
     *
     * @var array<string>
     */
    protected $fixtures = [
        'app.RandselInvoiceAdjustments',
    ];

    /**
     * setUp method
     *
     * @return void
     */
    protected function setUp(): void
    {
        parent::setUp();
        $config = $this->getTableLocator()->exists('RandselInvoiceAdjustments') ? [] : ['className' => RandselInvoiceAdjustmentsTable::class];
        $this->RandselInvoiceAdjustments = $this->getTableLocator()->get('RandselInvoiceAdjustments', $config);
    }

    /**
     * tearDown method
     *
     * @return void
     */
    protected function tearDown(): void
    {
        unset($this->RandselInvoiceAdjustments);

        parent::tearDown();
    }

    /**
     * Test validationDefault method
     *
     * @return void
     * @uses \App\Model\Table\RandselInvoiceAdjustmentsTable::validationDefault()
     */
    public function testValidationDefault(): void
    {
        $this->markTestIncomplete('Not implemented yet.');
    }

    /**
     * Test buildRules method
     *
     * @return void
     * @uses \App\Model\Table\RandselInvoiceAdjustmentsTable::buildRules()
     */
    public function testBuildRules(): void
    {
        $this->markTestIncomplete('Not implemented yet.');
    }
}
