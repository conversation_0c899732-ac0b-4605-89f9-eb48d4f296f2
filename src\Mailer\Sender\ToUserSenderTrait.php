<?php

namespace App\Mailer\Sender;

trait ToUserSenderTrait
{

    protected string $_toEmail;
    protected string $_toName;

    public function __construct(string $toEmail, string $toName)
    {
        $this->_toEmail = $toEmail;
        $this->_toName = $toName;
    }

    public function getToEmail(): string
    {
        return $this->_toEmail;
    }

    public function getToName(): string
    {
        return $this->_toName;
    }


}
