<?php

declare(strict_types=1);

namespace App\Test\TestCase\Controller\Front;

use App\Test\TestCase\Controller\ApiTestCase;
use Cake\Core\Configure;
use Cake\Utility\Hash;

/**
 * App\Controller\Front\MakersControllerTest Test Case
 *
 * @uses \App\Controller\Front\MakersControllerTest
 */
class MakersControllerTest extends ApiTestCase
{

    /**
     * Test index method
     *
     * @return void
     * @uses \App\Controller\Front\MakersController::index()
     * php ./vendor/phpunit/phpunit/phpunit --filter "testIndex" ./tests/TestCase/Controller/Front/MakersControllerTest.php
     */
    public function testIndex(): void
    {
        //        debug(Configure::read("Kuroko"));

        $this->get('/front/makers.json');
        $response = $this->bodyToArray($this->_response->getBody());
        debug($response);
        $this->assertResponseOk(print_r(Hash::flatten($response), true));
    }
}
