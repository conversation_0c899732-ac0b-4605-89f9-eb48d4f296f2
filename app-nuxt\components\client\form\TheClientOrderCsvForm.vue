<script setup lang="ts">
import { ref } from "vue";
import { ClientMyBridge } from "~/src/models/bridge/ClientMyBridge";
import { ClientOrders } from "~/src/lib/functions/ClientOrders";

const props = defineProps<{
    bridge: ClientMyBridge;
}>();

const dialog = ref(false);
const isFileSelected = ref(false);
const errorMessage = ref("");

const invalidRows = ref<Record<string, string>[]>([]);
const validRows = ref<Record<string, string>[]>([]);

const headers = ref([
    {
        title: "注文ID",
        key: "注文ID",
        value: "注文ID",
    },
    {
        title: "注文日時",
        key: "注文日時",
        value: "注文日時",
    },
    {
        title: "ステータス",
        key: "ステータス",
        value: "ステータス",
    },
    {
        title: "ステータス値",
        key: "ステータス値",
        value: "ステータス値",
    },
    {
        title: "エラーメッセージ",
        key: "エラーメッセージ",
        value: "エラーメッセージ",
    },
]);

const handleCsvFileUpload = async (): Promise<void> => {
    const target = document.getElementById("csv") as HTMLInputElement;
    if (target.files && target.files.length === 1) {
        try {
            await ClientOrders.confirmOrderCsv(
                target.files[0],
                props.bridge,
                invalidRows.value,
                validRows.value,
            );
            dialog.value =
                invalidRows.value.length + validRows.value.length > 0;
        } catch (error: Error) {
            console.error(error);
            errorMessage.value = error?.message;
        }
    }
};

const updateOrderCsv = async (): Promise<void> => {
    await ClientOrders.updateOrderCsv(validRows.value, props.bridge);
    dialog.value = false;
    invalidRows.value = [];
    validRows.value = [];
    const target = document.getElementById("csv") as HTMLInputElement;
    target.value = "";
};
</script>

<template>
    <div class="main-wrapper">
        <v-card class="main-card mb-4">
            <div class="pa-1 pl-2 title-wrapper">CSV一括承認</div>
            <div class="pl-8 csv-wrapper">
                <v-row class="ma-0" align="center">
                    <v-icon size="large" class="mr-1"
                        >mdi-numeric-1-circle-outline</v-icon
                    >
                    <v-btn
                        class="csv-dl-btn"
                        density="compact"
                        :disabled="!bridge.is_order_loaded"
                        elevation="0"
                        @click="
                            ClientOrders.downloadOrderCsv(
                                props.bridge.randsel_orders,
                            )
                        "
                    >
                        <v-icon size="large" class="mr-1"
                            >mdi-file-download</v-icon
                        >
                        CSVダウンロード
                    </v-btn>
                    <v-col class="pa-2">
                        CSVダウンロードしたファイルのD列”ステータス値”に承認する場合は1を、否認する場合は2を記入し、ファイル保存してアップロードしてください。CSV一行目の項目はそのまま残して、2行目以降にデータをご記入ください。
                        <NuxtLink to="/pdf/howtoapproveCSV.pdf" target="_blank"
                            >（詳細はこちら）</NuxtLink
                        >
                    </v-col>
                </v-row>
                <v-row class="ma-0 pb-3 font-weight-bold"
                    >　　↑↑一括承認には①からダウンロードしたCSVをご利用ください。</v-row
                >
                <v-row class="ma-0 py-3">
                    <v-icon size="large" class="mr-1"
                        >mdi-arrow-right-bold</v-icon
                    >
                    <v-icon size="large" class="mr-1"
                        >mdi-numeric-2-circle-outline</v-icon
                    >
                    <input
                        id="csv"
                        type="file"
                        name="csv"
                        accept="text/csv"
                        :disabled="!bridge.is_order_loaded"
                        @change="
                            (e) => {
                                isFileSelected = e.target?.files.length > 0;
                                errorMessage = '';
                            }
                        "
                    />
                    <v-btn
                        class="csv-dl-btn mx-2"
                        density="compact"
                        :disabled="!isFileSelected"
                        elevation="0"
                        @click="handleCsvFileUpload"
                    >
                        <v-icon size="large" class="mr-1"
                            >mdi-file-upload</v-icon
                        >
                        CSVアップロード
                    </v-btn>
                    <span class="text-red font-weight-bold">{{
                        errorMessage
                    }}</span>
                </v-row>
                <!-- ポップアップダイアログ -->
                <v-dialog
                    v-model="dialog"
                    scrollable
                    persistent
                    max-width="1000px"
                >
                    <v-card>
                        <v-card-title>
                            CSV一括承認データをご確認ください
                        </v-card-title>
                        <v-card-text>
                            <div class="pb-3">
                                承認件数：{{
                                    validRows.filter(
                                        (order) => order.ステータス値 === "1",
                                    ).length
                                }}件 / 否認件数：{{
                                    validRows.filter(
                                        (order) => order.ステータス値 === "2",
                                    ).length
                                }}件
                            </div>
                            <h3>更新するデータ</h3>
                            <v-data-table
                                :headers="headers.slice(0, -1)"
                                :items="validRows"
                                :items-per-page="10"
                                items-per-page-text="１ページの表示件数"
                            >
                                <template #top />
                            </v-data-table>
                            <h3>無効なデータ</h3>
                            <v-data-table
                                :headers="headers"
                                :items="invalidRows"
                                :items-per-page="10"
                                items-per-page-text="１ページの表示件数"
                            >
                                <template #top />
                            </v-data-table>
                        </v-card-text>
                        <v-card-actions>
                            <v-layout>
                                <v-btn
                                    variant="tonal"
                                    @click="
                                        dialog = false;
                                        invalidRows = [];
                                        validRows = [];
                                    "
                                    >閉じる</v-btn
                                >
                                <v-spacer></v-spacer>
                                <v-btn
                                    :disabled="validRows.length === 0"
                                    class="csv-dl-btn"
                                    @click="updateOrderCsv()"
                                    >この内容でアップロードする。</v-btn
                                >
                            </v-layout>
                        </v-card-actions>
                    </v-card>
                </v-dialog>
            </div>
        </v-card>
    </div>
</template>

<style scoped>
.title-wrapper {
    background-color: #eaeaea;
}
.csv-wrapper {
    background-color: #dcdcdc;
}

.csv-dl-btn {
    background-color: #85bd2c;
    color: white;
    font-weight: bold;
    padding: 15px;
    align-content: center;
    transition: opacity 0.3s ease;
}
.csv-dl-btn:hover {
    opacity: 0.7;
}
.csv-dl-btn:disabled {
    background-color: #b0b0b0 !important;
    color: white !important;
}

.status-wrapper {
    height: 52px;
    display: flex;
    border: #dfdfdf solid 2px;
}
.status-title {
    background-color: #eaeaea;
    padding: 3px 10px;
    align-content: center;
    border-right: #dfdfdf solid 2px;
}
.status-checkbox-wrapper {
    padding: 3px 10px;
}
.search-date-wrapper {
    min-width: 646px;
    height: 52px;
    display: flex;
    border: #dfdfdf solid 2px;
}
</style>
