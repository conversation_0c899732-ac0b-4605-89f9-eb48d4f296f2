import type { RuntimeConfig } from "nuxt/schema";

export abstract class Model {
    protected config: RuntimeConfig;

    constructor(protected runTimeConfig: RuntimeConfig) {
        this.config = runTimeConfig;
    }

    // 静的メソッドでサブクラスのインスタンスを生成
    static create<T extends Model>(
        this: new (config: RuntimeConfig) => T,
        runTimeConfig: RuntimeConfig,
    ): T {
        return new this(runTimeConfig);
    }
}
