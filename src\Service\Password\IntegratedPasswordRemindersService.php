<?php

namespace App\Service\Password;

use App\Service\IRestService;
use App\Service\ServiceTrait;
use App\Kuroko\Entity\IKurokoEntity;
use Cake\Datasource\EntityInterface;
use BadMethodCallException;
use Cake\Utility\Hash;
use Exception;

/**
 * 統合パスワードリマインダーサービス
 * 既存のUserPasswordRemindersServiceを置き換え
 */
class IntegratedPasswordRemindersService implements IRestService
{
    use ServiceTrait;

    protected array $_defaultConfig = [];
    private PasswordManagementService $passwordManager;

    public function initialize(array $config = []): void
    {
        $this->passwordManager = new PasswordManagementService();
    }

    /**
     * パスワードリマインダー送信
     */
    public function add(array $data = []): EntityInterface|null|IKurokoEntity
    {
        try {
            // ユーザータイプを判定（デフォルトは一般ユーザー）
            $userType = $this->determineUserType($data);

            // パスワードリマインダー送信
            $result = $this->passwordManager->sendPasswordReminder(Hash::get($data, 'email'), $userType);
            
            if (!$result) {
                throw new Exception('パスワードリマインダーの送信に失敗しました');
            }

            return null;
        } catch (Exception $exception) {
            $this->setErrors([
                '_system' => $exception->getMessage(),
            ]);
            return null;
        }
    }

    /**
     * パスワードリセット処理
     */
    public function edit(string $id, array $data = []): EntityInterface|null|IKurokoEntity
    {
        try {
            $token = Hash::get($data, 'token') ?? '';
            $newPassword = Hash::get($data, 'login_pwd') ?? '';

            if (empty($token) || empty($newPassword)) {
                throw new Exception('トークンと新しいパスワードは必須です');
            }

            $result = $this->passwordManager->resetPassword($token, $newPassword);
            
            if (!$result) {
                throw new Exception('パスワードリセットに失敗しました');
            }

            return null;
        } catch (Exception $exception) {
            $this->setErrors([
                '_system' => $exception->getMessage(),
            ]);
            return null;
        }
    }

    public function view(string $id, array $data = []): EntityInterface|null|IKurokoEntity
    {
        throw new BadMethodCallException();
    }

    public function index(array $data = []): array|EntityInterface|null|IKurokoEntity
    {
        throw new BadMethodCallException();
    }

    public function delete(string $id, array $data = []): bool
    {
        throw new BadMethodCallException();
    }

    /**
     * リクエストデータからユーザータイプを判定
     */
    private function determineUserType(array $data): string
    {
        // リクエストパスやパラメータからユーザータイプを判定
        if (Hash::check($data, 'user_type')) {
            return Hash::get($data, 'user_type');
        }

        // デフォルトは一般ユーザー
        return PasswordManagementService::USER_TYPE_GENERAL;
    }
}
