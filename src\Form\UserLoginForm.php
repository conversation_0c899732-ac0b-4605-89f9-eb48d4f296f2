<?php
declare(strict_types=1);

namespace App\Form;

use Cake\Form\Form;
use Cake\Form\Schema;
use Cake\Validation\Validator;

/**
 * ユーザーログインフォーム
 */
class UserLoginForm extends Form
{
    /**
     * スキーマの定義
     */
    protected function _buildSchema(Schema $schema): Schema
    {
        return $schema
            ->addField('email', 'string')
            ->addField('password', 'string');
    }

    /**
     * バリデーションルールの定義
     */
    public function validationDefault(Validator $validator): Validator
    {
        $validator
            ->email('email', false, 'メールアドレスの形式が正しくありません')
            ->requirePresence('email', 'create', 'メールアドレスは必須です')
            ->notEmptyString('email', 'メールアドレスは必須です');

        $validator
            ->scalar('password')
            ->requirePresence('password', 'create', 'パスワードは必須です')
            ->notEmptyString('password', 'パスワードは必須です');

        return $validator;
    }

    /**
     * フォーム実行処理（実際の処理はサービスクラスで行う）
     */
    protected function _execute(array $data): bool
    {
        // 実際の処理はコントローラーでサービスクラスを呼び出して行う
        return true;
    }
}
