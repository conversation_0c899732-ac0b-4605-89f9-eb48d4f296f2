<?php
declare(strict_types=1);

namespace App\Test\TestCase\Service;

use App\Service\AuthenticationService;
use App\Model\Entity\GeneralUser;
use Cake\TestSuite\TestCase;
use Cake\ORM\TableRegistry;

/**
 * AuthenticationService Test Case
 */
class AuthenticationServiceTest extends TestCase
{
    /**
     * Test fixtures
     */
    protected $fixtures = [
        'app.GeneralUsers',
        'app.SwbUsers',
        'app.MakerUsers',
    ];

    private AuthenticationService $authService;

    public function setUp(): void
    {
        parent::setUp();
        $this->authService = new AuthenticationService();
    }

    public function tearDown(): void
    {
        unset($this->authService);
        parent::tearDown();
    }

    /**
     * 一般ユーザーの認証テスト
     */
    public function testAuthenticateGeneralUser(): void
    {
        // テストユーザーを作成
        $generalUsersTable = TableRegistry::getTableLocator()->get('GeneralUsers');
        $user = $generalUsersTable->newEntity([
            'email' => '<EMAIL>',
            'password' => 'password123', // エンティティで自動ハッシュ化
        ]);
        $generalUsersTable->save($user);

        // 正しい認証情報でテスト
        $result = $this->authService->authenticate(
            '<EMAIL>',
            'password123',
            AuthenticationService::USER_TYPE_GENERAL
        );

        $this->assertInstanceOf(GeneralUser::class, $result);
        $this->assertEquals('<EMAIL>', $result->email);

        // 間違ったパスワードでテスト
        $result = $this->authService->authenticate(
            '<EMAIL>',
            'wrongpassword',
            AuthenticationService::USER_TYPE_GENERAL
        );

        $this->assertNull($result);

        // 存在しないユーザーでテスト
        $result = $this->authService->authenticate(
            '<EMAIL>',
            'password123',
            AuthenticationService::USER_TYPE_GENERAL
        );

        $this->assertNull($result);
    }

    /**
     * Kurocoユーザー判定テスト
     */
    public function testIsKurocoUser(): void
    {
        $generalUsersTable = TableRegistry::getTableLocator()->get('GeneralUsers');

        // 新システムユーザー（パスワードあり）
        $newUser = $generalUsersTable->newEntity([
            'email' => '<EMAIL>',
            'password' => 'password123',
        ]);
        $generalUsersTable->save($newUser);

        // Kurocoユーザー（パスワードなし）
        $kurocoUser = $generalUsersTable->newEntity([
            'email' => '<EMAIL>',
            'password' => null,
        ]);
        $generalUsersTable->save($kurocoUser);

        // 新システムユーザーはKurocoユーザーではない
        $this->assertFalse($this->authService->isKurocoUser(
            '<EMAIL>',
            AuthenticationService::USER_TYPE_GENERAL
        ));

        // KurocoユーザーはKurocoユーザーである
        $this->assertTrue($this->authService->isKurocoUser(
            '<EMAIL>',
            AuthenticationService::USER_TYPE_GENERAL
        ));

        // 存在しないユーザーはKurocoユーザーではない
        $this->assertFalse($this->authService->isKurocoUser(
            '<EMAIL>',
            AuthenticationService::USER_TYPE_GENERAL
        ));
    }

    /**
     * パスワード妥当性チェックテスト
     */
    public function testValidatePassword(): void
    {
        // 有効なパスワード
        $errors = $this->authService->validatePassword('password123');
        $this->assertEmpty($errors);

        // 短すぎるパスワード
        $errors = $this->authService->validatePassword('pass1');
        $this->assertNotEmpty($errors);
        $this->assertContains('パスワードは8文字以上で入力してください', $errors);

        // 英字なしパスワード
        $errors = $this->authService->validatePassword('12345678');
        $this->assertNotEmpty($errors);
        $this->assertContains('パスワードには英字を含めてください', $errors);

        // 数字なしパスワード
        $errors = $this->authService->validatePassword('password');
        $this->assertNotEmpty($errors);
        $this->assertContains('パスワードには数字を含めてください', $errors);

        // 複数の問題があるパスワード
        $errors = $this->authService->validatePassword('pass');
        $this->assertCount(2, $errors); // 短い、数字なし
    }

    /**
     * パスワードハッシュ化テスト
     */
    public function testHashPassword(): void
    {
        $password = 'password123';
        $hashedPassword = $this->authService->hashPassword($password);

        $this->assertNotEquals($password, $hashedPassword);
        $this->assertNotEmpty($hashedPassword);
        
        // 同じパスワードでも異なるハッシュが生成される（salt使用のため）
        $hashedPassword2 = $this->authService->hashPassword($password);
        $this->assertNotEquals($hashedPassword, $hashedPassword2);
    }

    /**
     * 無効なユーザータイプでの認証テスト
     */
    public function testAuthenticateWithInvalidUserType(): void
    {
        $result = $this->authService->authenticate(
            '<EMAIL>',
            'password123',
            'invalid_type'
        );

        $this->assertNull($result);
    }
}
