<?php

namespace App\Middleware;

use Cake\Core\Configure;
use Cake\Http\Exception\UnauthorizedException;
use Psr\Http\Message\ResponseInterface;
use Psr\Http\Message\ServerRequestInterface;
use Psr\Http\Server\MiddlewareInterface;
use Psr\Http\Server\RequestHandlerInterface;

class FrontTokenAuthMiddleware implements MiddlewareInterface
{

    public function process(ServerRequestInterface $request, RequestHandlerInterface $handler): ResponseInterface
    {
        $token = $request->getHeaderLine('CM-TOKEN');
//        $token = str_replace('Bearer ', '', $authHeader);
        // トークンの検証
        if ($token !== Configure::readOrFail("Api.front.token")) {
            throw new UnauthorizedException('Invalid token');
        }

        // トークンが有効な場合、次のミドルウェアまたはコントローラを呼び出す
        return $handler->handle($request);
    }
}
