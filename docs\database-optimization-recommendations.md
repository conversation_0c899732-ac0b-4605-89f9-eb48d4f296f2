# データベース最適化推奨事項

## 認証システムのパフォーマンス最適化

### 1. トークンテーブルのインデックス最適化

#### 現在の問題点
- トークン検索時に3つのテーブルを順次検索している
- `token`、`type`、`expires`の複合検索が頻繁に発生
- 期限切れトークンのクリーンアップ処理が非効率

#### 推奨インデックス

```sql
-- user_tokens テーブル
CREATE INDEX idx_user_tokens_token_type_expires ON user_tokens (token, type, expires);
CREATE INDEX idx_user_tokens_expires ON user_tokens (expires);
CREATE INDEX idx_user_tokens_general_user_type ON user_tokens (general_user_id, type);

-- swb_user_tokens テーブル
CREATE INDEX idx_swb_user_tokens_token_type_expires ON swb_user_tokens (token, type, expires);
CREATE INDEX idx_swb_user_tokens_expires ON swb_user_tokens (expires);
CREATE INDEX idx_swb_user_tokens_swb_user_type ON swb_user_tokens (swb_user_id, type);

-- maker_user_tokens テーブル
CREATE INDEX idx_maker_user_tokens_token_type_expires ON maker_user_tokens (token, type, expires);
CREATE INDEX idx_maker_user_tokens_expires ON maker_user_tokens (expires);
CREATE INDEX idx_maker_user_tokens_maker_user_type ON maker_user_tokens (maker_user_id, type);
```

### 2. ユーザーテーブルのインデックス最適化

```sql
-- general_users テーブル
CREATE INDEX idx_general_users_email_deleted ON general_users (email, deleted);

-- swb_users テーブル
CREATE INDEX idx_swb_users_email_deleted ON swb_users (email, deleted);

-- maker_users テーブル
CREATE INDEX idx_maker_users_email_deleted ON maker_users (email, deleted);
```

### 3. パフォーマンス向上の期待効果

1. **トークン検索の高速化**: 複合インデックスにより検索時間を大幅短縮
2. **期限切れトークンクリーンアップの効率化**: expires インデックスにより削除処理を高速化
3. **ユーザー認証の高速化**: email + deleted の複合インデックスにより論理削除を考慮した検索を最適化

### 4. 実装方法

#### CakePHP マイグレーションファイルの作成

```bash
docker-compose exec amazonlinux2 php bin/cake bake migration AddTokenIndexes
```

#### マイグレーションファイルの内容例

```php
<?php
use Migrations\AbstractMigration;

class AddTokenIndexes extends AbstractMigration
{
    public function up()
    {
        // user_tokens インデックス
        $this->table('user_tokens')
            ->addIndex(['token', 'type', 'expires'], ['name' => 'idx_user_tokens_token_type_expires'])
            ->addIndex(['expires'], ['name' => 'idx_user_tokens_expires'])
            ->addIndex(['general_user_id', 'type'], ['name' => 'idx_user_tokens_general_user_type'])
            ->update();

        // swb_user_tokens インデックス
        $this->table('swb_user_tokens')
            ->addIndex(['token', 'type', 'expires'], ['name' => 'idx_swb_user_tokens_token_type_expires'])
            ->addIndex(['expires'], ['name' => 'idx_swb_user_tokens_expires'])
            ->addIndex(['swb_user_id', 'type'], ['name' => 'idx_swb_user_tokens_swb_user_type'])
            ->update();

        // maker_user_tokens インデックス
        $this->table('maker_user_tokens')
            ->addIndex(['token', 'type', 'expires'], ['name' => 'idx_maker_user_tokens_token_type_expires'])
            ->addIndex(['expires'], ['name' => 'idx_maker_user_tokens_expires'])
            ->addIndex(['maker_user_id', 'type'], ['name' => 'idx_maker_user_tokens_maker_user_type'])
            ->update();

        // ユーザーテーブルインデックス
        $this->table('general_users')
            ->addIndex(['email', 'deleted'], ['name' => 'idx_general_users_email_deleted'])
            ->update();

        $this->table('swb_users')
            ->addIndex(['email', 'deleted'], ['name' => 'idx_swb_users_email_deleted'])
            ->update();

        $this->table('maker_users')
            ->addIndex(['email', 'deleted'], ['name' => 'idx_maker_users_email_deleted'])
            ->update();
    }

    public function down()
    {
        // インデックスの削除
        $this->table('user_tokens')
            ->removeIndex(['token', 'type', 'expires'])
            ->removeIndex(['expires'])
            ->removeIndex(['general_user_id', 'type'])
            ->update();

        $this->table('swb_user_tokens')
            ->removeIndex(['token', 'type', 'expires'])
            ->removeIndex(['expires'])
            ->removeIndex(['swb_user_id', 'type'])
            ->update();

        $this->table('maker_user_tokens')
            ->removeIndex(['token', 'type', 'expires'])
            ->removeIndex(['expires'])
            ->removeIndex(['maker_user_id', 'type'])
            ->update();

        $this->table('general_users')
            ->removeIndex(['email', 'deleted'])
            ->update();

        $this->table('swb_users')
            ->removeIndex(['email', 'deleted'])
            ->update();

        $this->table('maker_users')
            ->removeIndex(['email', 'deleted'])
            ->update();
    }
}
```

### 5. 監視とメンテナンス

#### 定期的なクリーンアップジョブの実装

```php
// src/Command/CleanupExpiredTokensCommand.php
<?php
namespace App\Command;

use Cake\Command\Command;
use Cake\Console\Arguments;
use Cake\Console\ConsoleIo;
use Cake\ORM\TableRegistry;

class CleanupExpiredTokensCommand extends Command
{
    public function execute(Arguments $args, ConsoleIo $io)
    {
        $tables = ['UserTokens', 'SwbUserTokens', 'MakerUserTokens'];
        $totalCleaned = 0;

        foreach ($tables as $tableName) {
            $table = TableRegistry::getTableLocator()->get($tableName);
            $cleaned = $table->cleanupExpiredTokens();
            $totalCleaned += $cleaned;
            $io->out("Cleaned {$cleaned} expired tokens from {$tableName}");
        }

        $io->success("Total cleaned: {$totalCleaned} expired tokens");
    }
}
```

#### crontab設定例

```bash
# 毎日午前2時に期限切れトークンをクリーンアップ
0 2 * * * cd /opt/coverme && php bin/cake cleanup_expired_tokens
```

### 6. パフォーマンス測定

実装前後のパフォーマンス測定を推奨します：

```sql
-- 測定クエリ例
EXPLAIN SELECT * FROM user_tokens 
WHERE token = 'sample_token' 
AND type = 'api_access' 
AND expires > NOW();
```

この最適化により、認証システムのレスポンス時間を大幅に改善できると期待されます。
