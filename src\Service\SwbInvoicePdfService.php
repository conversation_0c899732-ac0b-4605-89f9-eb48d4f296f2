<?php

namespace App\Service;

use App\Kuroko\Entity\Product;
use App\Enums\Options\EBillingCycleType;
use App\Enums\EntityFields\ESwbInvoiceForm;
use App\Model\Entity\RandselInvoice;
use App\Utility\Tax;
use Cake\I18n\FrozenDate;
use Cake\Utility\Hash;
use Cake\Log\Log;

class SwbInvoicePdfService implements IService
{
    use ServiceTrait;

    public function initialize(): void {}


    public static function generateInvoiceViewVars(array $data): array
    {
        $billingCycleType = $data['billing_cycle'] ?? EBillingCycleType::END_OF_MONTH_NEXT_MONTH_END_DAY->value;
        $yearMonth = $data['status_modified_year_month'] ?? '';

        $billingDateFormatted = '未設定';
        $lastDayOfMonthFormatted = '';
        $taxRate = Tax::getTaxRate($yearMonth . '-01'); // 税率を計算

        if ($yearMonth) {
            $baseDate = FrozenDate::parse($yearMonth . '-01');
            $lastDayOfMonthFormatted = $baseDate->lastOfMonth()->format('Y年m月d日');

            switch ($billingCycleType) {
                case EBillingCycleType::END_OF_MONTH_NEXT_MONTH_END_DAY->value:
                    $billingDate = $baseDate->addMonths(1)->lastOfMonth();
                    break;
                case EBillingCycleType::END_OF_MONTH_NEXT_NEXT_MONTH_10TH->value:
                    $billingDate = $baseDate->addMonths(2)->day(10);
                    break;
                case EBillingCycleType::END_OF_MONTH_NEXT_NEXT_MONTH_20TH->value:
                    $billingDate = $baseDate->addMonths(2)->day(20);
                    break;
                default:
                    $billingDate = null;
                    break;
            }
            $billingDateFormatted = $billingDate ? $billingDate->format('Y年m月d日') : '未設定';
        }

        $options = [
            ESwbInvoiceForm::BILLING_YEAR_MONTH->value => $yearMonth,
            ESwbInvoiceForm::MAKER_ID->value => $data['maker_id'],
        ];
        $invoices = (new RandselInvoicesService())->getInvoices($options);

        $productList = [];
        foreach ((new AllProductsService())->index([]) as $product) {
            /** @var Product $product */
            $productList[$product->get("product_id")] = $product->get("product_name");
        }

        $invoicesByProducts = [];
        foreach ($invoices as $invoice) {
            /** @var RandselInvoice $invoice */
            $invoicesByProduct = $invoice->toArray();
            $invoicesByProduct = Hash::insert($invoicesByProduct, 'product_name', $productList[$invoice->product_id]);
            $invoicesByProducts[] = $invoicesByProduct;
        }

        Log::debug(json_encode($invoicesByProducts));
        return [
            'customer_code' => $data['customer_code'] ?? '',
            'status_modified_year_month' => $yearMonth ?? '',
            'billing_address' => $data['billing_address'] ?? '',
            'contact_name' => $data['contact_name'] ?? '',
            'billing_cycle' => $billingCycleType,
            'billing_date' => $billingDateFormatted,
            'last_day_of_month' => $lastDayOfMonthFormatted,
            'tax_rate' => $taxRate,
            'invoices_by_product' => $invoicesByProducts,
        ];
    }
}
