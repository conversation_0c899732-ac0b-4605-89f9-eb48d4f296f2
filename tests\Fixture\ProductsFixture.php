<?php
declare(strict_types=1);

namespace App\Test\Fixture;

use Cake\TestSuite\Fixture\TestFixture;
use Cake\I18n\FrozenTime;

/**
 * ProductsFixture
 */
class ProductsFixture extends TestFixture
{
    /**
     * Init method
     *
     * @return void
     */
    public function init(): void
    {
        $now = FrozenTime::now();
        
        $this->records = [
            [
                'id' => 1,
                'maker_id' => 1,
                'brand_id' => 1,
                'is_display' => true,
                'year' => 2026,
                'display_name' => 'テストランドセル1',
                'description_html' => 'テスト用商品1の説明',
                'note_html' => null,
                'mask_image_description' => null,
                'image_url' => 'https://example.com/catalog1.jpg',
                'sort_order' => 1,
                'pdf_url' => 'https://example.com/catalog1.pdf',
                'price_range' => '5万円台',
                'weight_range' => '1000g台',
                'material' => 1,
                'created' => $now->format('Y-m-d H:i:s'),
                'modified' => $now->format('Y-m-d H:i:s'),
            ],
            [
                'id' => 2,
                'maker_id' => 1,
                'brand_id' => 1,
                'is_display' => true,
                'year' => 2026,
                'display_name' => 'テストランドセル2',
                'description_html' => 'テスト用商品2の説明',
                'note_html' => null,
                'mask_image_description' => null,
                'image_url' => 'https://example.com/catalog2.jpg',
                'sort_order' => 2,
                'pdf_url' => 'https://example.com/catalog2.pdf',
                'price_range' => '6万円台',
                'weight_range' => '1100g台',
                'material' => 2,
                'created' => $now->format('Y-m-d H:i:s'),
                'modified' => $now->format('Y-m-d H:i:s'),
            ],
            [
                'id' => 3,
                'maker_id' => 1,
                'brand_id' => 1,
                'is_display' => true,
                'year' => 2026,
                'display_name' => 'テストランドセル3',
                'description_html' => 'テスト用商品3の説明',
                'note_html' => null,
                'mask_image_description' => null,
                'image_url' => 'https://example.com/catalog3.jpg',
                'sort_order' => 3,
                'pdf_url' => 'https://example.com/catalog3.pdf',
                'price_range' => '7万円台',
                'weight_range' => '1200g台',
                'material' => 3,
                'created' => $now->format('Y-m-d H:i:s'),
                'modified' => $now->format('Y-m-d H:i:s'),
            ],
            [
                'id' => 4,
                'maker_id' => 1,
                'brand_id' => 1,
                'is_display' => false,
                'year' => 2026,
                'display_name' => 'テストランドセル4（非表示）',
                'description_html' => 'テスト用商品4の説明（非表示）',
                'note_html' => null,
                'mask_image_description' => null,
                'image_url' => 'https://example.com/catalog4.jpg',
                'sort_order' => 4,
                'pdf_url' => 'https://example.com/catalog4.pdf',
                'price_range' => '4万円台',
                'weight_range' => '900g台',
                'material' => 1,
                'created' => $now->format('Y-m-d H:i:s'),
                'modified' => $now->format('Y-m-d H:i:s'),
            ],
        ];
        
        parent::init();
    }
}
