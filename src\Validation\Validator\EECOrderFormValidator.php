<?php

namespace App\Validation\Validator;

use App\Enums\EntityFields\EECOrder;
use App\Enums\EValidationErrorMessage;
use App\Validation\IValidator;
use App\Validation\ValidatorTrait;
use Cake\Validation\Validator;

class EECOrderFormValidator implements IValidator
{
    use ValidatorTrait;

    static private function accessToken(Validator $validator, array $options = []): Validator
    {
        $insertText = [
            'field' => 'アクセストークン',
        ];
        $validator
            ->requirePresence('access_token', true, EValidationErrorMessage::REQUIRE_PRESENCE->format($insertText))
            ->notEmptyArray('access_token', EValidationErrorMessage::NOT_EMPTY_ARRAY->format($insertText));
        return $validator;
    }
}
