<script setup lang="ts">
import { ClientMyBridge } from "~~/src/models/bridge/ClientMyBridge";

const props = defineProps<{
    bridge: ClientMyBridge;
}>();

const orders = props.bridge.report_orders;

//TODO:集計などの処理はクラスに切り出してそっちでやった方がいい
const approvalStatus = 1;
const waitingForApprovalStatus = 0;
const approvedStatus = 1;

const startDate = new Date(props.bridge.report_to);
const endDate = new Date(props.bridge.report_from);
endDate.setHours(23, 59, 59, 999);

//注文数計算
const totalOrderCount = orders.filter(
    (order) =>
        startDate <= new Date(order.created) &&
        endDate >= new Date(order.created),
).length;
//承認数計算
const approvalCount = orders.filter(
    (order) =>
        order.status === approvalStatus &&
        order.status_modified &&
        startDate <= new Date(order.status_modified) &&
        endDate >= new Date(order.status_modified),
).length;
//未承認数計算
const waitingForApprovalCount = orders.filter(
    (order) => order.status === waitingForApprovalStatus,
).length;
//請求額計算
// const totalPrice = orders
//     .filter(
//         (order) =>
//             order.status === approvedStatus &&
//             order.status_modified &&
//             startDate <= new Date(order.status_modified) &&
//             endDate >= new Date(order.status_modified),
//     )
//     .reduce((sum, order) => sum + order.price, 0);
</script>

<template>
    <v-container fluid class="report-summary-wrapper">
        <v-row class="pt-5 pl-3 pb-0">サマリー</v-row>
        <v-row justify="end" class="summary-text">
            <v-col cols="3" class="pt-1 text-end">
                カタログ注文数：{{ totalOrderCount.toLocaleString() }}件
            </v-col>
            <v-col cols="3" class="pt-1 text-end">
                承認数：{{ approvalCount.toLocaleString() }}件
            </v-col>
            <v-col cols="3" class="pt-1 text-end">
                承認待ち注文数：{{ waitingForApprovalCount.toLocaleString() }}件
            </v-col>
            <!-- <v-col cols="3" class="pt-1 text-end">
                請求（予定) 額：{{ totalPrice.toLocaleString() }}円
            </v-col> -->
        </v-row>
    </v-container>
</template>

<style scoped>
.report-summary-wrapper {
    position: inherit;
    margin-top: 40px;
    padding: 0 20px;
    height: 80px;
    border: #e4e4e4 solid 2px;
    color: #575757;
}
.summary-text {
    font-size: 15px;
}
</style>
