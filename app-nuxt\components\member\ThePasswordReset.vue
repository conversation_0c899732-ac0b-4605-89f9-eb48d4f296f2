<script setup lang="ts">
import ResetPasswordForm from "~~/src/form/ResetPasswordForm";
import { reactive, ref } from "vue";
import { useRuntimeConfig } from "#app";
import PasswordResets from "~/src/models/UserPasswordReminders";

const props = defineProps({
    queryToken: {
        type: Object,
        required: true,
    },
});

const token = {
    token: props.queryToken.tt,
    temp_pwd: props.queryToken.tp,
};

const resetPasswordForm = reactive(new ResetPasswordForm(token));

const stepReset = "reset";
const stepComplete = "complete";

const isLoading = ref(false);
const step = ref(stepReset);
const errorMessage = ref("");
const config = useRuntimeConfig();

const toComplete = (): void => {
    if (!resetPasswordForm.valid) {
        return;
    }
    isLoading.value = true;
    PasswordResets.create(config)
        .put(resetPasswordForm.data)
        .then((response) => {
            if (response) {
                step.value = stepComplete;
            } else {
                errorMessage.value = "パスワードの再設定に失敗しました";
            }
            isLoading.value = false;
        })
        .catch(() => {
            errorMessage.value = "エラーが発生しました。もう一度お試しください";
            isLoading.value = false;
        });
};
</script>

<template>
    <div>
        <template v-if="isLoading">
            <v-progress-circular indeterminate></v-progress-circular>
        </template>
        <template v-else>
            <template v-if="step === stepReset">
                <v-form v-model="resetPasswordForm.valid">
                    <v-container>
                        <p class="text-h5 font-weight-bold mb-10">
                            新パスワード設定画面
                        </p>
                        <v-alert v-if="errorMessage" type="error" class="mt-3">
                            {{ errorMessage }}
                        </v-alert>
                        <v-row>
                            <v-text-field
                                v-model="resetPasswordForm.login_pwd"
                                density="comfortable"
                                :rules="resetPasswordForm.login_pwd_rules"
                                label="新しいパスワード"
                                type="password"
                                required
                            ></v-text-field>
                        </v-row>
                        <v-row>
                            <v-text-field
                                v-model="
                                    resetPasswordForm.login_pwd_confirmation
                                "
                                density="comfortable"
                                :rules="
                                    resetPasswordForm.login_pwd_confirmation_rules
                                "
                                label="新しいパスワード（確認）"
                                type="password"
                                required
                            ></v-text-field>
                        </v-row>
                        <v-row justify="center">
                            <v-col cols="12" md="6">
                                <v-btn
                                    block
                                    color="primary"
                                    :disabled="!resetPasswordForm.valid"
                                    rounded="xl"
                                    size="large"
                                    @click="toComplete"
                                >
                                    再設定
                                </v-btn>
                            </v-col>
                        </v-row>
                        <p class="text-body-1 my-5">注意事項</p>
                        <p class="text-body-1 mb-2">
                            ・セキュリティのため、定期的なパスワードの変更をおすすめします。<br />
                            ・パスワードは他のサービスで使用しているものとは異なるものをお選びください。<br />
                            ・安全性を高めるために、記号や大文字を含めることを推奨します。
                        </p>
                    </v-container>
                </v-form>
            </template>
            <template v-else-if="step === stepComplete">
                <v-container>
                    <p class="text-h5 font-weight-bold mb-10">
                        パスワードの変更が完了しました
                    </p>
                    <p class="text-body-1 my-5">
                        パスワードの変更が正常に完了しました。新しいパスワードでログインをお試しください。
                    </p>
                    <p class="text-body-1 my-5">次のステップ</p>
                    <p class="text-body-1 mb-2">
                        ・セキュリティを維持するため、他のアカウントと異なるパスワードをご利用ください。<br />
                        ・<a href="https://coverme.jp/"
                            >カバーミーTOPページに戻る</a
                        ><br />
                        ・<NuxtLink to="/member/account/">
                            マイページにアクセスする
                        </NuxtLink>
                    </p>
                    <p class="text-body-1 my-5">注意事項</p>
                    <p class="text-body-1 mb-2">
                        ・セキュリティを維持するため、他のアカウントと異なるパスワードをご利用ください。<br />
                        ・万が一心当たりのない変更をされた場合は、直ちに<a
                            href="https://coverme.jp/contact"
                            target="_blank"
                            rel="noopener noreferrer"
                            >こちら</a
                        >までご連絡ください。
                    </p>
                    <p class="text-body-1 my-5">お問い合わせ</p>
                    <p class="text-body-1 mb-2">
                        ・ご不明な点がございましたら、<a
                            href="https://coverme.jp/contact"
                            target="_blank"
                            rel="noopener noreferrer"
                            >こちら</a
                        >までお問い合わせください。
                    </p>
                </v-container>
            </template>
        </template>
    </div>
</template>

<style scoped lang="scss">
.test {
    color: $error-color;
}
</style>
