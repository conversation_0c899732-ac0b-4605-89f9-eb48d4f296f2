<?php
declare(strict_types=1);

namespace App\Validation;

use Cake\Validation\Validator;

/**
 * バリデーション共通処理、定義
 * Interface ValidatorInterface
 * @package App\Validation
 */
interface IValidator
{

    const OPTION_REQUIRE = 'require';
    const OPTION_UNIQUE = 'unique';

    /**
     * int型の最大値
     */
    const INT_MAXIMUM_VALUE = 2147483647;

    /**
     * int型の最小値
     */
    const INT_MINIMUM_VALUE = -2147483648;

    /**
     * float型の最大値
     */
    const FLOAT_MAXIMUM_VALUE = self::INT_MAXIMUM_VALUE;

    /**
     * float型の最小値
     */
    const FLOAT_MINIMUM_VALUE = self::INT_MINIMUM_VALUE;


    /**
     * TEXT型の最大文字数
     */
    const TEXT_MAX_LENGTH = 20000;

    const MEDIUM_TEXT_MAX_LENGTH = 16777215;

    /**
     * @param Validator $validator
     * @param array $fields
     * @return Validator
     */
    static public function buildValidator(Validator $validator, array $fields = []): Validator;
}
