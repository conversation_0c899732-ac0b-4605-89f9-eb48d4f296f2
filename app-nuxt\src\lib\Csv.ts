import Papa from "papaparse";
import encoding from "encoding-japanese";
import UpdateStatusOrder from "~/src/models/entry/UpdateStatusOrder";

const config = {
    delimiter: ",", // 区切り文字
    header: true, // キーをヘッダーとして扱う
    newline: "\r\n", // 改行
    encoding: "SJIS",
    skipEmptyLines: true,
};

export class Csv {
    /**
     * CSVファイルをダウンロードする
     * @param {object[]} data - CSVに変換するデータ
     * @param {string} [name="download.csv"] - ダウンロードするファイル名
     * @param {boolean} [hasHeader=true] - ヘッダー行を含めるか
     * @param {string} [encodingType="SJIS"] - エンコード方式（"SJIS" or "UTF8" など） 任意の引数に設定
     */
    static download(
        data: object[],
        name: string = "download.csv",
        hasHeader: boolean = true,
        encodingType?: string,
    ): void {
        const encodeType = encodingType || "SJIS";

        // CSVを生成
        const csvString = Papa.unparse(data, {
            ...config,
            header: hasHeader,
        });

        let blob;

        if (encodeType === "SJIS") {
            const strArray = encoding.stringToCode(csvString);
            const convertedArray = encoding.convert(
                strArray,
                "SJIS",
                "UNICODE",
            );
            const uintArray = new Uint8Array(convertedArray);
            blob = new Blob([uintArray], { type: "text/csv" });
        } else {
            // SJIS以外であればUTF-8でエンコード
            const bom = new Uint8Array([0xef, 0xbb, 0xbf]);
            blob = new Blob([bom, csvString], { type: "text/csv" });
        }

        const anchor = document.createElement("a");
        anchor.href = window.URL.createObjectURL(blob);
        anchor.download = name;
        anchor.click();
    }

    static upload(
        file: File,
        validate: (data: any[]) => {
            isValid: boolean;
            invalidRows: Record<string, string>[];
            validRows: Record<string, string>[];
        },
    ): Promise<{
        updateStatusOrders: UpdateStatusOrder[];
        invalidRows: Record<string, string>[];
        validRows: Record<string, string>[];
    }> {
        return new Promise((resolve, reject) => {
            Papa.parse(file, {
                ...config,
                complete: (results: any) => {
                    const data = results.data;
                    try {
                        // バリデーションチェック
                        const { isValid, invalidRows, validRows } =
                            validate(data);

                        const postdata: UpdateStatusOrder[] = [];
                        validRows.forEach((row: Record<string, string>) => {
                            const updateStatusOrder = new UpdateStatusOrder();
                            updateStatusOrder.id = Number(row["注文ID"]);
                            updateStatusOrder.status = Number(
                                row["ステータス値"],
                            );
                            postdata.push(updateStatusOrder);
                        });
                        resolve({
                            updateStatusOrders: postdata,
                            invalidRows: invalidRows,
                            validRows: validRows,
                        });
                    } catch (error) {
                        console.error(
                            "CSVの読み込み中にエラーが発生しました:",
                            error,
                        );
                        reject(error);
                    }
                },
                error: (error: any) => {
                    console.error(
                        "CSVの読み込み中にエラーが発生しました:",
                        error,
                    );
                    reject(
                        new Error(
                            "アップロードファイルが不正です。ファイルの内容をご確認ください。",
                        ),
                    );
                },
            });
        });
    }
}
