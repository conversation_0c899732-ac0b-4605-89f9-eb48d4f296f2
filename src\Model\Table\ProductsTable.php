<?php
declare(strict_types=1);

namespace App\Model\Table;

use Cake\ORM\Query;
use Cake\ORM\RulesChecker;
use Cake\ORM\Table;
use Cake\Validation\Validator;

/**
 * 商品テーブル
 */
class ProductsTable extends Table
{
    public function initialize(array $config): void
    {
        parent::initialize($config);

        $this->setTable('products');
        $this->setDisplayField('display_name');
        $this->setPrimaryKey('id');

        // アソシエーション
        $this->belongsTo('Makers', [
            'foreignKey' => 'maker_id',
            'joinType' => 'INNER',
        ]);
        
        $this->belongsTo('Brands', [
            'foreignKey' => 'brand_id',
            'joinType' => 'INNER',
        ]);
        
        $this->hasMany('Budgets', [
            'foreignKey' => 'product_id',
            'dependent' => true,
        ]);
        
        $this->hasMany('RandselOrders', [
            'foreignKey' => 'product_id',
        ]);
        
        $this->hasMany('RandselInvoices', [
            'foreignKey' => 'product_id',
        ]);
        
        $this->hasMany('RandselInvoiceAdjustments', [
            'foreignKey' => 'product_id',
        ]);

        // 論理削除ビヘイビア
        $this->addBehavior('Muffin/Trash.Trash', [
            'field' => 'deleted',
            'events' => ['Model.beforeFind', 'Model.beforeDelete']
        ]);
    }

    public function validationDefault(Validator $validator): Validator
    {
        $validator
            ->integer('maker_id')
            ->requirePresence('maker_id', 'create')
            ->notEmptyString('maker_id');

        $validator
            ->integer('brand_id')
            ->requirePresence('brand_id', 'create')
            ->notEmptyString('brand_id');

        $validator
            ->boolean('is_display')
            ->notEmptyString('is_display');

        $validator
            ->integer('year')
            ->requirePresence('year', 'create')
            ->notEmptyString('year')
            ->range('year', [2020, date('Y') + 5], __('年度は2020年から' . (date('Y') + 5) . '年の間で入力してください'));

        $validator
            ->scalar('display_name')
            ->maxLength('display_name', 255)
            ->requirePresence('display_name', 'create', __('表示商品名は必須です'))
            ->notEmptyString('display_name', __('表示商品名は必須です'));

        $validator
            ->scalar('description_html')
            ->allowEmptyString('description_html');

        $validator
            ->scalar('note_html')
            ->allowEmptyString('note_html');

        $validator
            ->scalar('mask_image_description')
            ->allowEmptyString('mask_image_description');

        $validator
            ->scalar('image_url')
            ->maxLength('image_url', 500)
            ->allowEmptyString('image_url')
            ->url('image_url', __('有効なURLを入力してください'));

        $validator
            ->integer('sort_order')
            ->notEmptyString('sort_order')
            ->greaterThanOrEqual('sort_order', 0, __('表示順序は0以上で入力してください'));

        $validator
            ->scalar('pdf_url')
            ->maxLength('pdf_url', 500)
            ->allowEmptyString('pdf_url')
            ->url('pdf_url', __('有効なURLを入力してください'));

        $validator
            ->scalar('price_range')
            ->allowEmptyString('price_range');

        $validator
            ->scalar('weight_range')
            ->allowEmptyString('weight_range');

        $validator
            ->integer('material')
            ->allowEmptyString('material')
            ->range('material', [1, 4], __('素材は1〜4の値を入力してください'));

        return $validator;
    }

    public function buildRules(RulesChecker $rules): RulesChecker
    {
        $rules->add($rules->existsIn(['maker_id'], 'Makers'), __('指定されたメーカーが存在しません'));
        $rules->add($rules->existsIn(['brand_id'], 'Brands'), __('指定されたブランドが存在しません'));

        return $rules;
    }

    /**
     * アクティブな商品を取得するファインダー
     */
    public function findActive(Query $query, array $options): Query
    {
        return $query->where(['deleted IS' => null]);
    }

    /**
     * 表示可能な商品を取得するファインダー
     */
    public function findDisplayable(Query $query, array $options): Query
    {
        return $query->where(['is_display' => true, 'deleted IS' => null]);
    }

    /**
     * メーカー別に商品を取得するファインダー
     */
    public function findByMaker(Query $query, array $options): Query
    {
        $makerId = $options['maker_id'] ?? null;
        if ($makerId !== null) {
            $query->where(['maker_id' => $makerId]);
        }
        return $query;
    }

    /**
     * ブランド別に商品を取得するファインダー
     */
    public function findByBrand(Query $query, array $options): Query
    {
        $brandId = $options['brand_id'] ?? null;
        if ($brandId !== null) {
            $query->where(['brand_id' => $brandId]);
        }
        return $query;
    }

    /**
     * 年度別に商品を取得するファインダー
     */
    public function findByYear(Query $query, array $options): Query
    {
        $year = $options['year'] ?? null;
        if ($year !== null) {
            $query->where(['year' => $year]);
        }
        return $query;
    }

    /**
     * 素材別に商品を取得するファインダー
     */
    public function findByMaterial(Query $query, array $options): Query
    {
        $material = $options['material'] ?? null;
        if ($material !== null) {
            $query->where(['material' => $material]);
        }
        return $query;
    }

    /**
     * 商品名で検索するファインダー
     */
    public function findByName(Query $query, array $options): Query
    {
        $name = $options['name'] ?? null;
        if (!empty($name)) {
            $query->where(['display_name LIKE' => '%' . $name . '%']);
        }
        return $query;
    }

    /**
     * 指定年度の表示可能な商品一覧を取得
     */
    public function getDisplayableProductsByYear(int $year): Query
    {
        return $this->find('displayable')
            ->where(['year' => $year])
            ->contain(['Makers', 'Brands'])
            ->order(['display_name' => 'ASC']);
    }

    /**
     * 指定メーカーの表示可能な商品一覧を取得
     */
    public function getDisplayableProductsByMaker(int $makerId): Query
    {
        return $this->find('displayable')
            ->where(['maker_id' => $makerId])
            ->contain(['Brands'])
            ->order(['year' => 'DESC', 'display_name' => 'ASC']);
    }
}
