<?php

namespace App\Service\Traits;

use Cake\Core\Configure;
use Cake\Utility\Hash;
use App\Kuroko\ApiModel\KurokoApiDynamic\ECOrders;
use App\Kuroko\Entity\AccessToken;
use App\Kuroko\Entity\Member;
use App\Model\Entity\Product;
use App\Model\Entity\GeneralUser;
use App\Model\Table\RandselOrdersTable;
use App\Model\Table\ProductsTable;
use Cake\ORM\TableRegistry;
use Cake\Log\Log;
use Exception;
use App\Service\ProductDisplayService;

trait OrderTrait
{
    /**
     * RandselOrdersTableインスタンス
     */
    protected ?RandselOrdersTable $randselOrdersTable = null;
    protected ?ProductsTable $productsTable = null;

    /**
     * 注文処理の初期化
     */
    protected function initializeOrder(): void
    {
        $this->randselOrdersTable = TableRegistry::getTableLocator()->get('RandselOrders');
        $this->productsTable = TableRegistry::getTableLocator()->get('Products');
    }

    /**
     * 注文処理を実行
     * 
     * @param GeneralUser|Member $user ユーザー情報
     * @param array $arrayData
     * @return bool 処理結果
     */
    public function processOrder(GeneralUser|Member $user, array $arrayData): bool
    {
        if (!$this->randselOrdersTable) {
            $this->initializeOrder();
        }
        
        // 具象クラスで実装する必要があるメソッド
        if (!method_exists($this, 'getProductIds')) {
            Log::error("getProductIds method not implemented in " . get_class($this));
            return false;
        }
        
        $userId = $this->getUserId($user);

        $productIds = $this->getProductIds($arrayData);
        if (empty($productIds)) {
            Log::warning("No product IDs provided for order processing");
            return false;
        }
        
        $success = true;
        $randselOrders = [];
        
        foreach ($productIds as $productId) {
            $product = $this->productsTable->get($productId, ['contain' => ['Makers', 'Brands']]);
            $budget = (new ProductDisplayService())->calculateDisplayBudget($productId);

            /** @var Product $product */
            $orderData = [
                'product_id' => $productId,
                'general_user_id' => $userId,
                'product_name' => $product->get('display_name'),
                'price' => $budget->price,
                'maker_id' => $product->get('maker_id'),
                'brand_id' => $product->get('brand_id'),
                'type' => $budget->type,
            ];
            
            $randselOrder = $this->randselOrdersTable->newEntity($orderData);
            if (!($randselOrder = $this->randselOrdersTable->save($randselOrder))) {
                $success = false;
                Log::error("Failed to create order for product ID: {$productId}, user ID: {$userId}");
            }
            $randselOrders[] = $randselOrder;
        }
        
        if ($success && method_exists($this, 'sendCompletionEmail')) {
            $this->sendCompletionEmail($user, $randselOrders);
        }
        
        return $success;
    }
    
    /**
     * オーダー登録(kuroco脱却するため、不要になる)
     * @throws Exception
     */
    public function _createPurchase(AccessToken $accessToken, array $orders): array
    {
        if (is_null($orders) || !count($orders)) {
            throw new Exception("ecOrders データがありません");
        }

        $ecOrders = new ECOrders();
        $responses = [];
        foreach ($orders as $order) {
            if ($res = $ecOrders->purchase($accessToken, $this->_createOrderData($order))) {
                $responses[] = $res;
            } else {
                Log::error("kuroco purchase 処理失敗");
            }
        }

        if (empty($responses)) {
            //@todo エラーメッセージをkurokoAPIからとるばあいは要調整
            throw new Exception("ecOrders データ作成しっぱい");
        }
        return $responses;
    }

    protected function _createOrderData(array $data): array
    {
        $response = [];
        $response = Hash::insert($response, "ec_payment_id", Configure::read("Kuroko.api.params.eCOrder.ecPaymentId"));
        $response = Hash::insert($response, "product_id", $data['pid']);
        $response = Hash::insert($response, "quantity", Configure::read("Kuroko.api.params.eCOrder.quantity"));
        return $response;
    }
}
