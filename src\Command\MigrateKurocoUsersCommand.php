<?php

declare(strict_types=1);

namespace App\Command;

use App\Enums\EntityFields\EUserProfile;
use App\Kuroko\ApiModel\KurokoApiDynamic\Members;
use App\Kuroko\ApiModel\KurokoApiDynamic\Inquiries;
use App\Kuroko\Entity\Member;
use App\Model\Table\GeneralUsersTable;
use App\Model\Table\UserProfilesTable;
use App\Model\Table\UserSurveysTable;

use Cake\Command\Command;
use Cake\Console\Arguments;
use Cake\Console\ConsoleIo;
use Cake\Console\ConsoleOptionParser;
use Cake\Datasource\ConnectionManager;
use Cake\I18n\FrozenTime;
use Cake\Log\Log;
use Cake\ORM\TableRegistry;
use Cake\Utility\Hash;
use Exception;

/**
 * Kurocoから新しいMySQLベースのユーザー管理システムへの一般ユーザーデータ移行バッチ
 *
 * 目的：
 * - Kuroco APIから取得した一般ユーザーの個人情報を新しい認証システムに移行
 * - general_usersテーブルとuser_profilesテーブルに適切にデータを分散
 * - user_surveysテーブルにアンケート情報を登録
 * - 暗号化が必要な個人情報フィールドを適切に処理
 *
 * アンケート処理フロー：
 * 1. Members.listForMakerByIds()のレスポンスからpre_form_idを抽出
 * 2. Inquiries.getByPreFormId()でアンケート情報を取得
 * 3. アンケートJSONデータを解析してuser_surveysテーブルにマッピング
 */
class MigrateKurocoUsersCommand extends Command
{
    private GeneralUsersTable $generalUsersTable;
    private UserProfilesTable $userProfilesTable;
    private UserSurveysTable $userSurveysTable;
    private Members $membersApi;
    private Inquiries $inquiriesApi;

    private int $successCount = 0;
    private int $errorCount = 0;
    private int $skippedCount = 0;
    private array $errors = [];

    /**
     * コマンドオプションの定義
     */
    public function buildOptionParser(ConsoleOptionParser $parser): ConsoleOptionParser
    {
        $parser
            ->setDescription([
                'Kurocoから新しいMySQLベースのユーザー管理システムへの一般ユーザーデータ移行バッチ',
                '',
                'Kuroco APIから取得した一般ユーザーの個人情報を、新しい認証システムの',
                'general_usersテーブルとuser_profilesテーブルに移行します。',
                '',
                '注意事項：',
                '- 既存の認証システム（Kuroco）との互換性を維持',
                '- 暗号化が必要な個人情報フィールドは適切に処理',
                '- password=nullでKurocoユーザーを識別',
            ])
            ->addOption('dry-run', [
                'help' => 'ドライランモード（実際の登録は行わない、処理内容のみ表示）',
                'boolean' => true,
                'default' => false,
            ])
            ->addOption('batch-size', [
                'help' => 'バッチサイズ（一度に処理するユーザー数）',
                'short' => 'b',
                'default' => 100,
            ])
            ->addOption('user-ids', [
                'help' => '処理対象のユーザーID（カンマ区切り）。指定しない場合は全ユーザーを処理',
                'short' => 'u',
                'default' => null,
            ])
            ->addOption('verbose', [
                'help' => '詳細な実行ログを出力',
                'short' => 'v',
                'boolean' => true,
                'default' => false,
            ])
            ->addOption('skip-existing', [
                'help' => '既存データをスキップ（重複チェック）',
                'boolean' => true,
                'default' => true,
            ]);

        return $parser;
    }

    /**
     * コマンド実行
     */
    public function execute(Arguments $args, ConsoleIo $io): ?int
    {
        $dryRun = $args->getOption('dry-run');
        $batchSize = (int)$args->getOption('batch-size');
        $userIds = $args->getOption('user-ids');
        $verbose = $args->getOption('verbose');
        $skipExisting = $args->getOption('skip-existing');

        // 初期化
        $this->initializeCommand();
        
        $io->out('<info>Kurocoユーザーデータ移行バッチを開始します...</info>');

        if ($dryRun) {
            $io->out('ドライランモードで実行します（実際の登録は行いません）');
        }

        $io->out("バッチサイズ: {$batchSize}");
        $io->out("既存データスキップ: " . ($skipExisting ? 'ON' : 'OFF'));

        try {
            // 処理対象ユーザーIDの取得
            $targetUserIds = $this->getTargetUserIds($userIds, $io, $verbose);
            
            if (empty($targetUserIds)) {
                $io->warning('処理対象のユーザーが見つかりませんでした。');
                return static::CODE_SUCCESS;
            }

            $totalUsers = count($targetUserIds);
            $io->out("処理対象ユーザー数: {$totalUsers}");

            // バッチ処理実行
            $this->processBatches($targetUserIds, $batchSize, $dryRun, $skipExisting, $io, $verbose);

            // 結果表示
            $this->displayResults($io);

            return static::CODE_SUCCESS;

        } catch (Exception $e) {
            $io->error("移行処理中にエラーが発生しました: " . $e->getMessage());
            Log::error("MigrateKurocoUsersCommand failed: " . $e->getMessage(), [
                'trace' => $e->getTraceAsString()
            ]);
            return static::CODE_ERROR;
        }
    }

    /**
     * 初期化処理
     */
    private function initializeCommand(): void
    {
        $this->generalUsersTable = TableRegistry::getTableLocator()->get('GeneralUsers');
        $this->userProfilesTable = TableRegistry::getTableLocator()->get('UserProfiles');
        $this->userSurveysTable = TableRegistry::getTableLocator()->get('UserSurveys');
        $this->membersApi = new Members();
        $this->inquiriesApi = new Inquiries();

        $this->successCount = 0;
        $this->errorCount = 0;
        $this->skippedCount = 0;
        $this->errors = [];
    }

    /**
     * 処理対象ユーザーIDの取得
     */
    private function getTargetUserIds(?string $userIds, ConsoleIo $io, bool $verbose): array
    {
        if ($userIds) {
            // 指定されたユーザーIDを処理
            $targetIds = array_map('intval', explode(',', $userIds));
            if ($verbose) {
                $io->out("指定されたユーザーID: " . implode(', ', $targetIds));
            }
            return $targetIds;
        }

        // 全ユーザーを対象とする場合、Members.listForMakerByIds()で空配列を渡して全ユーザーを取得
        if ($verbose) {
            $io->out('Kuroco APIから全ユーザーIDを取得中...');
        }

        try {
            // 空配列を渡すことで全ユーザー情報を取得
            $members = $this->membersApi->listForMakerByIds([]);

            if (empty($members)) {
                if ($verbose) {
                    $io->warning('Kuroco APIから取得できたユーザーが0件でした。');
                }
                return [];
            }

            // member_idを抽出してIDリストを作成
            $targetIds = [];
            foreach ($members as $member) {
                $memberId = $member->getId();
                if ($memberId) {
                    $targetIds[] = (int)$memberId;
                }
            }

            if ($verbose) {
                $io->out("Kuroco APIから取得したユーザー数: " . count($targetIds));
            }

            return $targetIds;

        } catch (Exception $e) {
            $error = "全ユーザーID取得中にエラーが発生しました: " . $e->getMessage();
            $io->error($error);
            Log::error($error, ['trace' => $e->getTraceAsString()]);
            return [];
        }
    }

    /**
     * バッチ処理実行
     */
    private function processBatches(
        array $targetUserIds, 
        int $batchSize, 
        bool $dryRun, 
        bool $skipExisting, 
        ConsoleIo $io, 
        bool $verbose
    ): void {
        $batches = array_chunk($targetUserIds, $batchSize);
        $totalBatches = count($batches);

        foreach ($batches as $batchIndex => $batchUserIds) {
            $currentBatch = $batchIndex + 1;
            $io->out("バッチ {$currentBatch}/{$totalBatches} を処理中...");

            try {
                // Kuroco APIからユーザーデータを取得
                $members = $this->membersApi->listForMakerByIds($batchUserIds);
                
                if ($verbose) {
                    $io->out("  取得したユーザー数: " . count($members));
                }

                // 各ユーザーを処理
                foreach ($members as $member) {
                    $this->processUser($member, $dryRun, $skipExisting, $io, $verbose);
                }

            } catch (Exception $e) {
                $this->errorCount += count($batchUserIds);
                $error = "バッチ {$currentBatch} の処理中にエラー: " . $e->getMessage();
                $this->errors[] = $error;
                $io->error($error);
                Log::error($error, ['batch_user_ids' => $batchUserIds]);
            }
        }
    }

    /**
     * 個別ユーザーの処理
     */
    private function processUser(Member $member, bool $dryRun, bool $skipExisting, ConsoleIo $io, bool $verbose): void
    {
        try {
            $memberId = $member->getId();
            $email = $member->getEmail();

            if (empty($email)) {
                $this->skippedCount++;
                if ($verbose) {
                    $io->out("  ユーザーID {$memberId}: メールアドレスが空のためスキップ");
                }
                return;
            }

            // 既存データチェック
            if ($skipExisting && $this->isUserExists($memberId, $email)) {
                $this->skippedCount++;
                if ($verbose) {
                    $io->out("  ユーザーID {$memberId}: 既存データのためスキップ");
                }
                return;
            }

            if ($dryRun) {
                $this->displayDryRunInfo($member, $io, $verbose);
                $this->successCount++;
                return;
            }

            // 実際のデータ移行処理
            $this->migrateUser($member, $io, $verbose);
            $this->successCount++;

            if ($verbose) {
                $io->out("  ユーザーID {$memberId}: 移行完了");
            }

        } catch (Exception $e) {
            $this->errorCount++;
            $error = "ユーザーID {$member->getId()} の処理中にエラー: " . $e->getMessage();
            $this->errors[] = $error;
            $io->error("  " . $error);
            Log::error($error, ['member_data' => $member->getData()]);
        }
    }

    /**
     * 既存ユーザーの存在チェック
     */
    private function isUserExists(string $memberId, string $email): bool
    {
        // IDまたはメールアドレスで既存ユーザーをチェック
        $existingUser = $this->generalUsersTable->find()
            ->where([
                'OR' => [
                    'id' => $memberId,
                    'email' => $email
                ]
            ])
            ->first();

        return $existingUser !== null;
    }

    /**
     * ドライラン情報の表示
     */
    private function displayDryRunInfo(Member $member, ConsoleIo $io, bool $verbose): void
    {
        if (!$verbose) {
            return;
        }

        $memberId = $member->getId();
        $email = $member->getEmail();
        $preFormId = $member->get('pre_form_id');

        $io->out("  ユーザーID {$memberId} ({$email}):");
        $io->out("    general_users: id={$memberId}, email={$email}, password=null");

        $profileData = $this->mapMemberToProfileData($member);
        $io->out("    user_profiles: " . json_encode($profileData, JSON_UNESCAPED_UNICODE));

        // アンケート情報の表示
        if (!empty($preFormId)) {
            $io->out("    user_surveys: pre_form_id={$preFormId} (アンケート情報を処理予定)");
        } else {
            $io->out("    user_surveys: pre_form_id is empty (アンケート情報なし)");
        }
    }

    /**
     * ユーザーデータの実際の移行処理
     */
    private function migrateUser(Member $member, ConsoleIo $io, bool $verbose): void
    {
        $connection = ConnectionManager::get('default');

        $connection->transactional(function () use ($member, $io, $verbose) {
            // general_usersテーブルへの登録
            $generalUser = $this->createOrUpdateGeneralUser($member);

            // user_profilesテーブルへの登録
            $this->createOrUpdateUserProfile($generalUser, $member);

            // user_surveysテーブルへのアンケート情報登録
            $this->processUserSurvey($generalUser, $member, $verbose);

            if ($verbose) {
                Log::info("User migration completed", [
                    'member_id' => $member->getId(),
                    'email' => $member->getEmail()
                ]);
            }
        });
    }

    /**
     * general_usersテーブルへのユーザー作成/更新
     */
    private function createOrUpdateGeneralUser(Member $member): \App\Model\Entity\GeneralUser
    {
        $memberId = $member->getId();
        $email = $member->getEmail();

        // 既存ユーザーの検索
        $existingUser = $this->generalUsersTable->find()
            ->where(['id' => $memberId])
            ->first();

        if ($existingUser) {
            // 既存ユーザーの更新
            $existingUser = $this->generalUsersTable->patchEntity($existingUser, [
                'email' => $email,
                'password' => null, // Kurocoユーザーの識別用
                'modified' => $this->parseKurocoDateTime($member->get('update_ymdhi'))
            ]);

            $user = $this->generalUsersTable->save($existingUser);
        } else {
            // 新規ユーザーの作成
            $userData = [
                'email' => $email,
                'password' => null, // Kurocoユーザーの識別用
                'created' => $this->parseKurocoDateTime($member->get('inst_ymdhi')),
                'modified' => $this->parseKurocoDateTime($member->get('update_ymdhi'))
            ];

            $user = $this->generalUsersTable->newEntity($userData);

            // 手動でIDを設定（Kurocoのmember_idを使用）
            $user->id = $memberId;

            $user = $this->generalUsersTable->save($user);
        }

        if (!$user) {
            throw new Exception("Failed to save general user: " . json_encode($this->generalUsersTable->getErrors()));
        }

        return $user;
    }

    /**
     * user_profilesテーブルへのプロフィール作成/更新
     */
    private function createOrUpdateUserProfile(\App\Model\Entity\GeneralUser $generalUser, Member $member): void
    {
        // 既存プロフィールの検索
        $existingProfile = $this->userProfilesTable->find()
            ->where(['general_user_id' => $generalUser->id])
            ->first();

        $profileData = $this->mapMemberToProfileData($member);
        $profileData['general_user_id'] = $generalUser->id;
        $profileData['created'] = $this->parseKurocoDateTime($member->get('inst_ymdhi'));
        $profileData['modified'] = $this->parseKurocoDateTime($member->get('update_ymdhi'));

        if ($existingProfile) {
            // 既存プロフィールの更新
            $profile = $this->userProfilesTable->patchEntity($existingProfile, $profileData);
        } else {
            // 新規プロフィールの作成
            $profile = $this->userProfilesTable->newEntity($profileData);
        }

        $profile = $this->userProfilesTable->save($profile);

        if (!$profile) {
            throw new Exception("Failed to save user profile: " . json_encode($this->userProfilesTable->getErrors()));
        }
    }

    /**
     * MemberデータをUserProfileフィールドにマッピング
     * UserDetailsService::mapDataToProfileFieldsを参考に実装
     */
    private function mapMemberToProfileData(Member $member): array
    {
        $memberData = $member->getData();
        $profileData = [];

        // フィールドマッピング（UserDetailsServiceと同じマッピング）
        $fieldMapping = [
            'name1' => EUserProfile::LAST_NAME->value,
            'name2' => EUserProfile::FIRST_NAME->value,
            'name1_hurigana' => EUserProfile::LAST_NAME_KANA->value,
            'name2_hurigana' => EUserProfile::FIRST_NAME_KANA->value,
            'zip_code' => EUserProfile::ZIP_CODE->value,
            'tdfk_cd' => EUserProfile::PREFECTURE_CODE->value,
            'address1' => EUserProfile::ADDRESS1->value,
            'address2' => EUserProfile::ADDRESS2->value,
            'address3' => EUserProfile::ADDRESS3->value,
            'tel' => EUserProfile::TEL->value,
            'email_send_ng_flg' => EUserProfile::EMAIL_SEND_NG_FLG->value,
        ];

        foreach ($fieldMapping as $memberKey => $profileField) {
            $value = Hash::get($memberData, $memberKey);
            if ($value !== null && $value !== '') {
                $profileData[$profileField] = $value;
            }
        }

        // デフォルト値の設定
        $this->setDefaultProfileValues($profileData);

        return $profileData;
    }

    /**
     * プロフィールデータのデフォルト値設定
     */
    private function setDefaultProfileValues(array &$profileData): void
    {
        // 必須フィールドのデフォルト値設定
        $requiredFields = [
            EUserProfile::LAST_NAME->value => '',
            EUserProfile::FIRST_NAME->value => '',
            EUserProfile::LAST_NAME_KANA->value => '',
            EUserProfile::FIRST_NAME_KANA->value => '',
            EUserProfile::ZIP_CODE->value => '',
            EUserProfile::PREFECTURE_CODE->value => '',
            EUserProfile::ADDRESS1->value => '',
            EUserProfile::TEL->value => '',
            EUserProfile::EMAIL_SEND_NG_FLG->value => false,
        ];

        foreach ($requiredFields as $field => $defaultValue) {
            if (!isset($profileData[$field]) || $profileData[$field] === null) {
                $profileData[$field] = $defaultValue;
            }
        }

        // nullable フィールドの処理
        $nullableFields = [
            EUserProfile::ADDRESS2->value,
            EUserProfile::ADDRESS3->value,
            EUserProfile::NOTES->value,
        ];

        foreach ($nullableFields as $field) {
            if (!isset($profileData[$field])) {
                $profileData[$field] = null;
            }
        }
    }

    /**
     * Kuroco日時文字列をFrozenTimeに変換
     */
    private function parseKurocoDateTime(?string $dateTimeString): ?FrozenTime
    {
        if (empty($dateTimeString)) {
            return null;
        }

        try {
            return new FrozenTime($dateTimeString);
        } catch (Exception $e) {
            Log::warning("Failed to parse Kuroco datetime: {$dateTimeString}", [
                'error' => $e->getMessage()
            ]);
            return new FrozenTime(); // 現在時刻をデフォルトとする
        }
    }

    /**
     * アンケート情報の取得と登録処理
     */
    private function processUserSurvey(\App\Model\Entity\GeneralUser $generalUser, Member $member, bool $verbose): void
    {
        $preFormId = $member->get('pre_form_id');

        // pre_form_idが空の場合はスキップ
        if (empty($preFormId)) {
            if ($verbose) {
                Log::debug("User {$generalUser->id}: pre_form_id is empty, skipping survey processing");
            }
            return;
        }

        try {
            // Inquiries APIからアンケート情報を取得
            $inquiries = $this->inquiriesApi->getByPreFormId((int)$preFormId);

            if (empty($inquiries)) {
                if ($verbose) {
                    Log::debug("User {$generalUser->id}: No inquiry data found for pre_form_id: {$preFormId}");
                }
                return;
            }

            // 最初のアンケートデータを使用（通常は1件のはず）
            $inquiry = $inquiries[0];
            $surveyData = $this->mapInquiryToSurveyData($inquiry, (int)$generalUser->id);

            if (!empty($surveyData)) {
                $this->createOrUpdateUserSurvey((int)$generalUser->id, $surveyData);

                if ($verbose) {
                    Log::info("User {$generalUser->id}: Survey data processed successfully", [
                        'pre_form_id' => $preFormId,
                        'survey_data' => $surveyData
                    ]);
                }
            }

        } catch (Exception $e) {
            $error = "User {$generalUser->id}: Survey processing error: " . $e->getMessage();
            Log::error($error, [
                'pre_form_id' => $preFormId,
                'trace' => $e->getTraceAsString()
            ]);
            // アンケート処理のエラーは全体の移行を止めない
        }
    }

    /**
     * InquiryデータをUserSurveyデータにマッピング
     */
    private function mapInquiryToSurveyData($inquiry, int $generalUserId): array
    {
        $inquiryData = $inquiry->getData();
        $body = Hash::get($inquiryData, 'body');

        if (empty($body)) {
            return [];
        }

        try {
            $bodyData = json_decode($body, true);
            if (!$bodyData || !isset($bodyData['questions'])) {
                return [];
            }

            $surveyData = [
                'general_user_id' => $generalUserId,
                'year' => date('Y'), // 現在の年度
                'child_sex' => null,
                'budget' => null,
                // きっかけ（question_1_x）
                'question_1_1' => false, // WEB広告
                'question_1_2' => false, // SNS
                'question_1_3' => false, // 知人の紹介
                'question_1_4' => false, // 家族の紹介
                // 重視するポイント（question_2_x）
                'question_2_1' => false, // 色
                'question_2_2' => false, // デザイン
                'question_2_3' => false, // 価格
                'question_2_4' => false, // カスタマイズ性
                'question_2_5' => false, // 安全性
                'question_2_6' => false, // 耐久性
                'question_2_7' => false, // ブランド
                'question_2_8' => false, // その他
                'question_2_9' => false, // 未使用
                'question_2_10' => false, // 未使用
                'question_2_11' => false, // 未使用
            ];

            // 質問データを解析してマッピング
            foreach ($bodyData['questions'] as $question) {
                $key = Hash::get($question, 'k');
                $value = Hash::get($question, 'v');

                if (empty($key)) {
                    continue;
                }

                // 性別のマッピング
                if ($key === 'お子さまの性別') {
                    $surveyData['child_sex'] = $this->mapChildSex($value);
                }

                // 予算のマッピング
                elseif ($key === 'ご予算') {
                    $surveyData['budget'] = $this->mapBudget($value);
                }

                // きっかけのマッピング（複数回答可）
                elseif ($key === 'カタログ請求のきっかけ（複数回答可）') {
                    if (is_array($value)) {
                        foreach ($value as $trigger) {
                            $this->mapTrigger($trigger, $surveyData);
                        }
                    }
                }

                // 重視するポイントのマッピング（複数回答可）
                elseif ($key === '特に重視するポイント（複数回答可）') {
                    if (is_array($value)) {
                        foreach ($value as $point) {
                            $this->mapImportantPoint($point, $surveyData);
                        }
                    }
                }
            }

            return $surveyData;

        } catch (Exception $e) {
            Log::error("Survey data mapping error: " . $e->getMessage(), [
                'inquiry_data' => $inquiryData,
                'body' => $body
            ]);
            return [];
        }
    }

    /**
     * user_surveysテーブルへのアンケートデータ作成/更新
     */
    private function createOrUpdateUserSurvey(int $generalUserId, array $surveyData): void
    {
        // 既存のアンケートデータを検索
        $existingSurvey = $this->userSurveysTable->find()
            ->where(['general_user_id' => $generalUserId])
            ->first();

        if ($existingSurvey) {
            // 既存データの更新
            $survey = $this->userSurveysTable->patchEntity($existingSurvey, $surveyData);
        } else {
            // 新規データの作成
            $survey = $this->userSurveysTable->newEntity($surveyData);
        }

        $result = $this->userSurveysTable->save($survey);

        if (!$result) {
            throw new Exception("Failed to save user survey: " . json_encode($this->userSurveysTable->getErrors()));
        }
    }

    /**
     * 性別のマッピング
     */
    private function mapChildSex(?string $value): ?int
    {
        if (empty($value)) {
            return null;
        }

        switch ($value) {
            case '男の子':
                return 1;
            case '女の子':
                return 2;
            case 'その他':
            case '答えたくない':
                return 3;
            default:
                return null;
        }
    }

    /**
     * 予算のマッピング
     */
    private function mapBudget(?string $value): ?int
    {
        if (empty($value)) {
            return null;
        }

        switch ($value) {
            case '～30,000円':
                return 1;
            case '30,000円～50,000円':
                return 2;
            case '50,000円～100,000円':
                return 3;
            case '100,000円～':
                return 4;
            case '30万円以上':
                return 5;
            default:
                return null;
        }
    }

    /**
     * きっかけのマッピング
     */
    private function mapTrigger(string $trigger, array &$surveyData): void
    {
        switch ($trigger) {
            case 'WEB広告':
                $surveyData['question_1_1'] = true;
                break;
            case 'SNS':
                $surveyData['question_1_2'] = true;
                break;
            case '知人の紹介':
                $surveyData['question_1_3'] = true;
                break;
            case '家族の紹介':
                $surveyData['question_1_4'] = true;
                break;
        }
    }

    /**
     * 重視するポイントのマッピング
     */
    private function mapImportantPoint(string $point, array &$surveyData): void
    {
        switch ($point) {
            case '耐久性':
                $surveyData['question_2_1'] = true;
                break;
            case '色':
                $surveyData['question_2_2'] = true;
                break;
            case 'デザイン':
                $surveyData['question_2_3'] = true;
                break;
            case '機能性':
                $surveyData['question_2_4'] = true;
                break;
            case 'キャラクターコラボ':
                $surveyData['question_2_5'] = true;
                break;
            case '軽量性':
                $surveyData['question_2_6'] = true;
                break;
            case '安全性':
                $surveyData['question_2_7'] = true;
                break;
            case 'ブランド':
                $surveyData['question_2_8'] = true;
                break;
            case 'メーカーの信頼性':
                $surveyData['question_2_9'] = true;
                break;
            case '価格':
                $surveyData['question_2_10'] = true;
                break;
            case 'カスタマイズ性':
                $surveyData['question_2_11'] = true;
                break;
        }
    }

    /**
     * 実行結果の表示
     */
    private function displayResults(ConsoleIo $io): void
    {
        $io->out('');
        $io->out('<info>=== 移行結果 ===</info>');
        $io->out("成功: {$this->successCount} 件");
        $io->out("スキップ: {$this->skippedCount} 件");
        $io->out("エラー: {$this->errorCount} 件");

        if (!empty($this->errors)) {
            $io->out('');
            $io->out('<error>=== エラー詳細 ===</error>');
            foreach ($this->errors as $error) {
                $io->error($error);
            }
        }

        $io->out('');
        $io->success('Kurocoユーザーデータ移行バッチが完了しました。');

        // ログ出力
        Log::info('MigrateKurocoUsersCommand completed', [
            'success_count' => $this->successCount,
            'skipped_count' => $this->skippedCount,
            'error_count' => $this->errorCount,
            'total_errors' => count($this->errors)
        ]);
    }
}
