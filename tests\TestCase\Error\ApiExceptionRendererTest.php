<?php
declare(strict_types=1);

namespace App\Test\TestCase\Error;

use App\Error\ApiExceptionRenderer;
use App\Test\TestCase\AppTestCase;
use Authentication\Authenticator\UnauthenticatedException;
use Cake\Http\ServerRequest;
use Cake\Http\Exception\BadRequestException;
use Cake\Validation\Validator;
use Cake\Http\Exception\HttpException;

class ApiExceptionRendererTest extends AppTestCase
{
    /**
     * Test authentication error
     *
     * @return void
     * 
     */
    public function testAuthenticationError(): void
    {
        $exception = new UnauthenticatedException('Invalid credentials');
        $renderer = new ApiExceptionRenderer($exception, new ServerRequest());
        
        $response = $renderer->render();
        $result = json_decode((string)$response->getBody(), true);
        
        debug($response);
        $this->assertEquals(401, $response->getStatusCode());
        $this->assertFalse($result['success']);
        $this->assertEquals('Invalid credentials', $result['message']);
        $this->assertEquals('FAILURE_CREDENTIALS_INVALID', $result['code']);
    }

    /**
     * Test HTTP error
     *
     * @return void
     * php ./vendor/phpunit/phpunit/phpunit --filter "testHttpError" ./tests/TestCase/Error/ApiExceptionRendererTest.php
     */
    public function testHttpError(): void
    {
        $exception = new BadRequestException('Invalid request');
        $renderer = new ApiExceptionRenderer($exception, new ServerRequest());
        
        $response = $renderer->render();
        $result = json_decode((string)$response->getBody(), true);
        
        debug($response);
        $this->assertEquals(400, $response->getStatusCode());
        $this->assertFalse($result['success']);
        $this->assertEquals('Invalid request', $result['message']);
    }

    /**
     * Test validation error
     *
     * @return void
     * php ./vendor/phpunit/phpunit/phpunit --filter "testValidationError" ./tests/TestCase/Error/ApiExceptionRendererTest.php
     */
    public function testValidationError(): void
    {
        $validator = new Validator();
        $validator->requirePresence('email')
            ->email('email', false, 'Invalid email');
        
        $errors = ['email' => ['_required' => 'Email is required']];
        $exception = new HttpException('Validation failed', 400);
        $exception->validationErrors = $errors;
        
        $renderer = new ApiExceptionRenderer($exception, new ServerRequest());
        
        $response = $renderer->render();
        $result = json_decode((string)$response->getBody(), true);
        
        debug($response);
        $this->assertEquals(400, $response->getStatusCode());
        $this->assertFalse($result['success']);
        $this->assertEquals('Validation failed', $result['message']);
        $this->assertEquals($errors, $result['errors']);
    }

    /**
     * Test internal server error
     *
     * @return void
     * php ./vendor/phpunit/phpunit/phpunit --filter "testInternalError" ./tests/TestCase/Error/ApiExceptionRendererTest.php
     */
    public function testInternalError(): void
    {
        $exception = new \RuntimeException('Something went wrong');
        $renderer = new ApiExceptionRenderer($exception, new ServerRequest());
        
        $response = $renderer->render();
        $result = json_decode((string)$response->getBody(), true);
        
        $this->assertEquals(500, $response->getStatusCode());
        $this->assertFalse($result['success']);
        $this->assertEquals('Something went wrong', $result['message']);
    }
} 