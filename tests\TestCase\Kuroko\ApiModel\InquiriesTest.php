<?php

declare(strict_types=1);

namespace App\Test\TestCase\Kuroko\ApiModel;

use App\Kuroko\ApiModel\KurokoApiDynamic\Inquiries;
use App\Kuroko\Entity\Inquiry;
use Cake\Http\Client\Response;
use Cake\TestSuite\TestCase;
use PHPUnit\Framework\MockObject\MockObject;

/**
 * Inquiriesのテストクラス
 */
class InquiriesTest extends TestCase
{
    private Inquiries $inquiries;

    public function setUp(): void
    {
        parent::setUp();
        $this->inquiries = new Inquiries();
    }

    public function tearDown(): void
    {
        parent::tearDown();
    }

    /**
     * getByPreFormIdメソッドの正常系テスト
     */
    public function testGetByPreFormIdSuccess(): void
    {
        // モックレスポンスデータ
        $responseData = [
            'errors' => [],
            'list' => [
                [
                    'inquiry_bn_id' => 97,
                    'from_mail' => '<EMAIL>',
                    'subject' => '',
                    'body' => json_encode([
                        'questions' => [
                            ['k' => 'お子さまの性別', 'v' => '女の子'],
                            ['k' => 'ご予算', 'v' => '30,000円～50,000円']
                        ]
                    ]),
                    'status' => '0',
                    'name' => 'テストユーザー',
                    'inquiry_id' => 3,
                    'receive_date' => '2024-09-25T20:10:35+09:00'
                ]
            ]
        ];

        // Inquiriesクラスのモック作成
        $inquiriesMock = $this->getMockBuilder(Inquiries::class)
            ->onlyMethods(['get', '_setSysUserToken', 'getEndPoint'])
            ->getMock();

        // モックレスポンス作成
        $responseMock = $this->createMock(Response::class);
        $responseMock->method('isSuccess')->willReturn(true);
        $responseMock->method('getStringBody')->willReturn(json_encode($responseData));

        // メソッドの期待値設定
        $inquiriesMock->expects($this->once())
            ->method('_setSysUserToken');
        
        $inquiriesMock->expects($this->once())
            ->method('getEndPoint')
            ->with('inquiries')
            ->willReturn('/rcms-api/5/inquiry/list');

        $inquiriesMock->expects($this->once())
            ->method('get')
            ->with('/rcms-api/5/inquiry/list', ['inquiry_bn_id' => 1])
            ->willReturn($responseMock);

        // テスト実行
        $result = $inquiriesMock->getByPreFormId(1);

        debug($result);

        // 結果検証
        $this->assertIsArray($result);
        $this->assertCount(1, $result);
        $this->assertInstanceOf(Inquiry::class, $result[0]);
    }

    /**
     * getByPreFormIdメソッドのAPIエラーテスト
     */
    public function testGetByPreFormIdApiError(): void
    {
        // エラーレスポンスデータ
        $responseData = [
            'errors' => ['API Error'],
            'list' => []
        ];

        // Inquiriesクラスのモック作成
        $inquiriesMock = $this->getMockBuilder(Inquiries::class)
            ->onlyMethods(['get', '_setSysUserToken', 'getEndPoint'])
            ->getMock();

        // モックレスポンス作成
        $responseMock = $this->createMock(Response::class);
        $responseMock->method('isSuccess')->willReturn(true);
        $responseMock->method('getStringBody')->willReturn(json_encode($responseData));

        // メソッドの期待値設定
        $inquiriesMock->expects($this->once())
            ->method('_setSysUserToken');
        
        $inquiriesMock->expects($this->once())
            ->method('getEndPoint')
            ->with('inquiries')
            ->willReturn('/rcms-api/5/inquiry/list');

        $inquiriesMock->expects($this->once())
            ->method('get')
            ->with('/rcms-api/5/inquiry/list', ['inquiry_bn_id' => 1])
            ->willReturn($responseMock);

        // テスト実行
        $result = $inquiriesMock->getByPreFormId(1);

        // 結果検証（エラー時は空配列を返す）
        $this->assertIsArray($result);
        $this->assertEmpty($result);
    }

    /**
     * getByPreFormIdメソッドのHTTPエラーテスト
     */
    public function testGetByPreFormIdHttpError(): void
    {
        // Inquiriesクラスのモック作成
        $inquiriesMock = $this->getMockBuilder(Inquiries::class)
            ->onlyMethods(['get', '_setSysUserToken', 'getEndPoint'])
            ->getMock();

        // モックレスポンス作成（HTTPエラー）
        $responseMock = $this->createMock(Response::class);
        $responseMock->method('isSuccess')->willReturn(false);
        $responseMock->method('getStatusCode')->willReturn(500);
        $responseMock->method('getStringBody')->willReturn('Internal Server Error');

        // メソッドの期待値設定
        $inquiriesMock->expects($this->once())
            ->method('_setSysUserToken');
        
        $inquiriesMock->expects($this->once())
            ->method('getEndPoint')
            ->with('inquiries')
            ->willReturn('/rcms-api/5/inquiry/list');

        $inquiriesMock->expects($this->once())
            ->method('get')
            ->with('/rcms-api/5/inquiry/list', ['inquiry_bn_id' => 1])
            ->willReturn($responseMock);

        // テスト実行
        $result = $inquiriesMock->getByPreFormId(1);

        // 結果検証（エラー時は空配列を返す）
        $this->assertIsArray($result);
        $this->assertEmpty($result);
    }

    /**
     * getByPreFormIdメソッドのJSONデコードエラーテスト
     */
    public function testGetByPreFormIdJsonDecodeError(): void
    {
        // Inquiriesクラスのモック作成
        $inquiriesMock = $this->getMockBuilder(Inquiries::class)
            ->onlyMethods(['get', '_setSysUserToken', 'getEndPoint'])
            ->getMock();

        // モックレスポンス作成（不正なJSON）
        $responseMock = $this->createMock(Response::class);
        $responseMock->method('isSuccess')->willReturn(true);
        $responseMock->method('getStringBody')->willReturn('invalid json');

        // メソッドの期待値設定
        $inquiriesMock->expects($this->once())
            ->method('_setSysUserToken');
        
        $inquiriesMock->expects($this->once())
            ->method('getEndPoint')
            ->with('inquiries')
            ->willReturn('/rcms-api/5/inquiry/list');

        $inquiriesMock->expects($this->once())
            ->method('get')
            ->with('/rcms-api/5/inquiry/list', ['inquiry_bn_id' => 1])
            ->willReturn($responseMock);

        // テスト実行
        $result = $inquiriesMock->getByPreFormId(1);

        // 結果検証（エラー時は空配列を返す）
        $this->assertIsArray($result);
        $this->assertEmpty($result);
    }

    /**
     * listForMakerメソッドの既存機能テスト（回帰テスト）
     */
    public function testListForMakerExistingFunctionality(): void
    {
        // 既存のlistForMakerメソッドが正常に動作することを確認
        $responseData = [
            'errors' => [],
            'list' => [
                [
                    'inquiry_bn_id' => 1,
                    'from_mail' => '<EMAIL>',
                    'subject' => 'Test Subject',
                    'body' => 'Test Body',
                    'status' => '0',
                    'name' => 'Test User'
                ]
            ]
        ];

        // Inquiriesクラスのモック作成
        $inquiriesMock = $this->getMockBuilder(Inquiries::class)
            ->onlyMethods(['get', '_setSysUserToken', 'getEndPoint'])
            ->getMock();

        // モックレスポンス作成
        $responseMock = $this->createMock(Response::class);
        $responseMock->method('isSuccess')->willReturn(true);
        $responseMock->method('getStringBody')->willReturn(json_encode($responseData));

        // メソッドの期待値設定
        $inquiriesMock->expects($this->once())
            ->method('_setSysUserToken');
        
        $inquiriesMock->expects($this->once())
            ->method('getEndPoint')
            ->with('inquiries')
            ->willReturn('/rcms-api/5/inquiry/list');

        $inquiriesMock->expects($this->once())
            ->method('get')
            ->with('/rcms-api/5/inquiry/list')
            ->willReturn($responseMock);

        // テスト実行
        $result = $inquiriesMock->listForMaker();

        // 結果検証
        $this->assertIsArray($result);
        $this->assertCount(1, $result);
        $this->assertInstanceOf(Inquiry::class, $result[0]);
    }
}
