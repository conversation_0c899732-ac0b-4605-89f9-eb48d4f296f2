<?php

/**
 * Routes configuration.
 *
 * In this file, you set up routes to your controllers and their actions.
 * Routes are very important mechanism that allows you to freely connect
 * different URLs to chosen controllers and their actions (functions).
 *
 * It's loaded within the context of `Application::routes()` method which
 * receives a `RouteBuilder` instance `$routes` as method argument.
 *
 * CakePHP(tm) : Rapid Development Framework (https://cakephp.org)
 * Copyright (c) Cake Software Foundation, Inc. (https://cakefoundation.org)
 *
 * Licensed under The MIT License
 * For full copyright and license information, please see the LICENSE.txt
 * Redistributions of files must retain the above copyright notice.
 *
 * @copyright     Copyright (c) Cake Software Foundation, Inc. (https://cakefoundation.org)
 * @link          https://cakephp.org CakePHP(tm) Project
 * @license       https://opensource.org/licenses/mit-license.php MIT License
 */

use App\Authentication\UserAuthenticationService;
use App\Controller\Front\SwbAuthenticationsController;
use App\Controller\Front\SwbRandselOrdersController;
use App\Controller\Front\SwbMonthlyInvoicesController;
use App\Controller\Front\SwbRandselInvoiceAdjustmentsController;
use App\Controller\Front\SwbConfirmRandselInvoiceAdjustmentsController;
use App\Controller\Front\SwbInvoicePdfController;
use App\Controller\Front\ClientAuthenticationsController;
use App\Controller\Front\ClientDetailsController;
use App\Controller\Front\ClientOrdersController;
use App\Controller\Front\ClientChangePasswordsController;
use App\Controller\Front\ClientRandselOrdersController;
use App\Controller\Front\ClientScreenRandselOrdersController;
use App\Controller\Front\ClientCsvRandselOrdersController;
use App\Controller\Front\OrdersController;
use App\Controller\Front\SchoolBagFormNewMembersController;
use App\Controller\Front\SchoolBagOrdersController;
use App\Controller\Front\UserAuthenticationsController;
use App\Controller\Front\UserDetailsController;
use App\Controller\Front\UserPasswordRemindersController;
use App\Controller\Front\UserChangePasswordsController;
use App\Controller\Front\MakersController;
use App\Controller\Front\ProductsController;
use App\Controller\NuxtBuild\ProductsController as NuxtBuildProductsController;
use App\Controller\NuxtBuild\AllProductsController;
use App\Middleware\FrontTokenAuthMiddleware;
use App\Middleware\NuxtBuildTokenAuthMiddleware;
use Cake\Core\App;
use Cake\Core\Configure;
use Cake\Routing\Route\DashedRoute;
use Cake\Routing\RouteBuilder;

/*
 * This file is loaded in the context of the `Application` class.
  * So you can use  `$this` to reference the application class instance
  * if required.
 */

return function (RouteBuilder $routes): void {
    $routes->setRouteClass(DashedRoute::class);
    $routes
        ->registerMiddleware('nuxtBuildTokenAuth', new NuxtBuildTokenAuthMiddleware())
        ->registerMiddleware('frontTokenAuth', new FrontTokenAuthMiddleware());
    //        ->registerMiddleware('userAuthentication', new AuthenticationMiddleware(new UserAuthenticationServiceProvider()))

    $routes->scope('/front', function (RouteBuilder $builder) {
        $builder
            ->applyMiddleware('frontTokenAuth');
        //
        //        ->applyMiddleware('userAuthentication')
        $builder->setExtensions(['json', 'pdf']);
        $resources = [
            // 一般ユーザー（既存Kuroco認証と新システム認証）
            ProductsController::class => [
                UserAuthenticationService::ALLOWED_GROUP => [
                    Configure::read("Kuroko.api.userGroupId"),
                    Configure::read("Kuroko.api.clientGroupId"),
                    Configure::read("Kuroko.api.swbGroupId"),
                    Configure::read("Kuroko.api.swbConfirmGroupId")
                ],
                UserAuthenticationService::CHECK_STATUS_FLAG => false
            ],

            UserAuthenticationsController::class => [
                UserAuthenticationService::ALLOWED_GROUP => [
                    Configure::read("Kuroko.api.userGroupId")
                ],
                UserAuthenticationService::CHECK_STATUS_FLAG => true
            ],
            UserDetailsController::class => [
                UserAuthenticationService::ALLOWED_GROUP => [
                    Configure::read("Kuroko.api.userGroupId")
                ],
                UserAuthenticationService::CHECK_STATUS_FLAG => true
            ],
            UserPasswordRemindersController::class => [
                UserAuthenticationService::ALLOWED_GROUP => [
                    Configure::read("Kuroko.api.userGroupId")
                ],
                UserAuthenticationService::CHECK_STATUS_FLAG => false
            ],
            UserChangePasswordsController::class => [
                UserAuthenticationService::ALLOWED_GROUP => [
                    Configure::read("Kuroko.api.userGroupId")
                ],
                UserAuthenticationService::CHECK_STATUS_FLAG => true
            ],
            SchoolBagFormNewMembersController::class => [
                UserAuthenticationService::ALLOWED_GROUP => [
                    Configure::read("Kuroko.api.userGroupId")
                ],
                UserAuthenticationService::CHECK_STATUS_FLAG => false
            ],
            SchoolBagOrdersController::class => [
                UserAuthenticationService::ALLOWED_GROUP => [
                    Configure::read("Kuroko.api.userGroupId")
                ],
                UserAuthenticationService::CHECK_STATUS_FLAG => false
            ],
            OrdersController::class => [
                UserAuthenticationService::ALLOWED_GROUP => [
                    Configure::read("Kuroko.api.userGroupId")
                ],
                UserAuthenticationService::CHECK_STATUS_FLAG => true
            ],

            // メーカー
            ClientAuthenticationsController::class => [
                UserAuthenticationService::ALLOWED_GROUP => [
                    Configure::read("Kuroko.api.clientGroupId")
                ],
                UserAuthenticationService::CHECK_STATUS_FLAG => true
            ],
            ClientOrdersController::class => [
                UserAuthenticationService::ALLOWED_GROUP => [
                    Configure::read("Kuroko.api.clientGroupId")
                ],
                UserAuthenticationService::CHECK_STATUS_FLAG => true
            ],
            ClientDetailsController::class => [
                UserAuthenticationService::ALLOWED_GROUP => [
                    Configure::read("Kuroko.api.clientGroupId")
                ],
                UserAuthenticationService::CHECK_STATUS_FLAG => true
            ],
            ClientChangePasswordsController::class => [
                UserAuthenticationService::ALLOWED_GROUP => [
                    Configure::read("Kuroko.api.clientGroupId")
                ],
                UserAuthenticationService::CHECK_STATUS_FLAG => true
            ],
            ClientRandselOrdersController::class => [
                UserAuthenticationService::ALLOWED_GROUP => [
                    Configure::read("Kuroko.api.clientGroupId")
                ],
                UserAuthenticationService::CHECK_STATUS_FLAG => true
            ],
            ClientScreenRandselOrdersController::class => [
                UserAuthenticationService::ALLOWED_GROUP => [
                    Configure::read("Kuroko.api.clientGroupId")
                ],
                UserAuthenticationService::CHECK_STATUS_FLAG => true
            ],
            ClientCsvRandselOrdersController::class => [
                UserAuthenticationService::ALLOWED_GROUP => [
                    Configure::read("Kuroko.api.clientGroupId")
                ],
                UserAuthenticationService::CHECK_STATUS_FLAG => true
            ],

            // SWB
            MakersController::class => [
                UserAuthenticationService::ALLOWED_GROUP => [
                    Configure::read("Kuroko.api.swbGroupId")
                ],
                UserAuthenticationService::CHECK_STATUS_FLAG => true
            ],
            SwbAuthenticationsController::class => [
                UserAuthenticationService::ALLOWED_GROUP => [
                    Configure::read("Kuroko.api.swbGroupId")
                ],
                UserAuthenticationService::CHECK_STATUS_FLAG => true
            ],
            SwbRandselOrdersController::class => [
                UserAuthenticationService::ALLOWED_GROUP => [
                    Configure::read("Kuroko.api.swbGroupId")
                ],
                UserAuthenticationService::CHECK_STATUS_FLAG => true
            ],
            SwbMonthlyInvoicesController::class => [
                UserAuthenticationService::ALLOWED_GROUP => [
                    Configure::read("Kuroko.api.swbGroupId")
                ],
                UserAuthenticationService::CHECK_STATUS_FLAG => true
            ],
            SwbRandselInvoiceAdjustmentsController::class => [
                UserAuthenticationService::ALLOWED_GROUP => [
                    Configure::read("Kuroko.api.swbGroupId")
                ],
                UserAuthenticationService::CHECK_STATUS_FLAG => true
            ],
            SwbConfirmRandselInvoiceAdjustmentsController::class => [
                UserAuthenticationService::ALLOWED_GROUP => [
                    Configure::read("Kuroko.api.swbConfirmGroupId")
                ],
                UserAuthenticationService::CHECK_STATUS_FLAG => true
            ],
            SwbInvoicePdfController::class => [
                UserAuthenticationService::ALLOWED_GROUP => [
                    Configure::read("Kuroko.api.swbGroupId")
                ],
                UserAuthenticationService::CHECK_STATUS_FLAG => true
            ],

        ];

        foreach ($resources as $resource => $authOption) {
            $builder->resources(
                App::shortName($resource, 'Controller/Front', 'Controller'),
                [
                    'prefix' => 'Front',
                    "connectOptions" =>
                    ["authentication" => $authOption]
                ],
            );
        }
    });
    $routes->scope('/nuxt-build', function (RouteBuilder $builder) {
        $builder->applyMiddleware('nuxtBuildTokenAuth');
        $builder->setExtensions(['json']);
        $resources = [
            NuxtBuildProductsController::class,
            AllProductsController::class,
        ];
        foreach ($resources as $resource) {
            $builder->resources(
                App::shortName($resource, 'Controller/NuxtBuild', 'Controller'),
                ['prefix' => 'NuxtBuild']
            );
        }
    });
};
