<?php
declare(strict_types=1);

namespace App\Error;

use Cake\Error\Renderer\WebExceptionRenderer;
use Cake\Http\Response;
use Authentication\Authenticator\Result;
use Throwable;
use Cake\Controller\Controller;

class ApiExceptionRenderer extends WebExceptionRenderer
{
    /**
     * @inheritDoc
     */
    public function render(): Response
    {
        $exception = $this->error;
        // エラーメッセージの生成
        return $this->_codeMessage($exception);
    }

    /**
     * @inheritDoc
     */
    protected function _template(Throwable $exception, string $method, int $code): string
    {
        return 'error';
    }

    /**
     * @inheritDoc
     */
    protected function _getController(): Controller
    {
        $controller = parent::_getController();
        $controller->viewBuilder()->setClassName('Json');
        return $controller;
    }

    /**
     * エラーレスポンスの生成
     * @param Throwable $error
     * @param int $code
     * @return Response
     */
    protected function _codeMessage(Throwable $error): Response
    {
        $response = $this->controller->getResponse();
        $response = $response->withStatus($this->getHttpCode($error));

        $viewVars = [
            'success' => false,
            'message' => $error->getMessage(),
        ];

        // 認証エラーの特別処理
        if ($error instanceof \Authentication\Authenticator\UnauthenticatedException) {
            $viewVars['code'] = Result::FAILURE_CREDENTIALS_INVALID;
        }

        // バリデーションエラーの特別処理
        if (property_exists($error, 'validationErrors')) {
            $viewVars['errors'] = $error->validationErrors;
        }

        $this->controller->set($viewVars);
        $this->controller->viewBuilder()->setOption('serialize', array_keys($viewVars));
        $this->controller->setResponse($response);
        return $this->_outputMessage($this->_template($error, $this->_method($error), $this->getHttpCode($error)));
    }
} 