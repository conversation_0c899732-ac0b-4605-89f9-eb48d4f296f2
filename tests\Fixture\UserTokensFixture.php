<?php
declare(strict_types=1);

namespace App\Test\Fixture;

use Cake\TestSuite\Fixture\TestFixture;

/**
 * UserTokensFixture
 */
class UserTokensFixture extends TestFixture
{
    /**
     * Init method
     *
     * @return void
     */
    public function init(): void
    {
        $this->records = [
            [
                'id' => 1,
                'general_user_id' => 1,
                'token' => 'test_token_1',
                'type' => 'api_access',
                'expires' => '2025-03-31 23:59:59',
                'created' => '2024-01-01 00:00:00',
                'modified' => '2024-01-01 00:00:00',
            ],
            [
                'id' => 2,
                'general_user_id' => 1,
                'token' => 'test_token_2',
                'type' => 'password_reset',
                'expires' => '2025-12-31 23:59:59',
                'created' => '2024-01-01 00:00:00',
                'modified' => '2024-01-01 00:00:00',
            ],
            [
                'id' => 3,
                'general_user_id' => 2,
                'token' => 'test_token_3',
                'type' => 'api_access',
                'expires' => '2025-12-31 23:59:59',
                'created' => '2024-01-01 00:00:00',
                'modified' => '2024-01-01 00:00:00',
            ],
        ];
        parent::init();
    }
}
