openapi: 3.1.0
info:
  title: static
  version: '1.0'
  description: コンテンツの静的認証用
servers:
  -
    url: 'https://swb-sbpg.g.kuroco.app'
    description: 'API Backend'
paths:
  /rcms-api/3/send-inquiry:
    post:
      tags:
        - フォーム
      summary: ''
      description: |
        
        ### **InquiryMessage::send (v1)**
        
        
        ## Controller parameters
        
        > **id** `1`
        
      parameters:
        -
          name: _output_format
          schema:
            type: string
            format: ''
          in: query
          required: false
          style: form
          explode: true
          description: '形式 (json|xml|csv|zip)'
        -
          name: _lang
          schema:
            type: string
            format: ''
          in: query
          required: false
          style: form
          explode: true
          description: 言語
        -
          name: _charset
          schema:
            type: string
            format: ''
          in: query
          required: false
          style: form
          explode: true
          description: 文字コード
      responses:
        200:
          description: 'Message sent'
        404:
          description: 'Can''t send messages for the provided form ID'
      security:
        -
          Token-Auth: []
      operationId: postRcmsApi3SendInquiry
      requestBody:
        required: true
        description: ''
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                  default: ''
                  example: 'My Name'
                  description: 名前
                email:
                  type: string
                  default: ''
                  example: <EMAIL>
                  description: メールアドレス
                ext_01:
                  type: string
                  format: ''
                  description: Title
                ext_04:
                  anyOf:
                    -
                      type: object
                      properties:
                        key:
                          type: string
                          format: ''
                          enum:
                            - '0'
                            - '1'
                            - '2'
                        label:
                          type: string
                          format: ''
                          enum:
                            - ''
                            - faq
                            - job
                      additionalProperties: false
                    -
                      type: string
                      enum:
                        - ''
                        - '0'
                        - '1'
                        - '2'
                  example: {  }
                  default: ''
                  description: |
                    Inquiry type
                    * 0 => 
                    * 1 => faq
                    * 2 => job
                ext_05:
                  anyOf:
                    -
                      type: object
                      properties:
                        key:
                          type: string
                          format: ''
                          enum:
                            - '1'
                            - '2'
                            - '3'
                        label:
                          type: string
                          format: ''
                          enum:
                            - Radio1
                            - Radio2
                            - Radio3
                      additionalProperties: false
                    -
                      type: string
                      enum:
                        - ''
                        - '1'
                        - '2'
                        - '3'
                  example: {  }
                  default: ''
                  description: |
                    Single choice radio
                    * 1 => Radio1
                    * 2 => Radio2
                    * 3 => Radio3
                ext_06:
                  type: array
                  uniqueItems: true
                  items:
                    anyOf:
                      -
                        type: object
                        properties:
                          key:
                            type: string
                            format: ''
                            enum:
                              - '1'
                              - '2'
                              - '3'
                          label:
                            type: string
                            format: ''
                            enum:
                              - Check1
                              - Check2
                              - Check3
                        additionalProperties: false
                      -
                        type: string
                        enum:
                          - '1'
                          - '2'
                          - '3'
                  description: |
                    Multiple choice
                    * 1 => Check1
                    * 2 => Check2
                    * 3 => Check3
                  default: []
                  example: []
                ext_07:
                  type: string
                  format: ''
                  description: Message
                ext_08:
                  type: object
                  properties:
                    file_id:
                      type: string
                      default: ''
                      pattern: '^(files\/temp\/.+|files\/topics\/.+|https:\/\/.s3.ap-northeast-1.amazonaws.com\/files\/.+|https:\/\/player.vimeo.com\/video\/.+)$'
                      example: ''
                      description: アップロードされたファイルID
                    file_nm:
                      type: string
                      default: ''
                      example: ''
                      description: ファイル名
                    desc:
                      type: string
                      default: ''
                      example: ''
                      description: 説明
                  additionalProperties: false
                body:
                  type: string
                  default: ''
                  example: 'Example Message'
                  description: メッセージ
                parent_inquiry_bn_id:
                  type: integer
                  format: int32
                  minimum: -2147483648
                  maximum: 2147483647
                  description: ''
                from_id:
                  type: integer
                  format: int32
                  minimum: -2147483648
                  maximum: 2147483647
                  description: フォーム対象ID
                from_module:
                  type: string
                  format: ''
                  description: フォーム対象機能
                member_id:
                  type: integer
                  format: int32
                  minimum: -2147483648
                  maximum: 2147483647
                  description: メンバーID
                  example: 0
                user_agent:
                  type: string
                  format: ''
                  description: ブラウザ・OS情報
                  default: ''
                  example: ''
                ip_address:
                  type: string
                  format: ''
                  description: IPアドレス
                  default: ''
                  example: ''
                validate_only:
                  type: boolean
                  format: ''
                  example: false
                  default: false
                  description: 入力チェックする
              additionalProperties: false
  '/rcms-api/3/get-inquiry/{inquiry_id}':
    get:
      tags:
        - フォーム
      summary: ''
      description: |
        
        ### **InquiryForm::details (v1)**
        
        
      parameters:
        -
          name: inquiry_id
          schema:
            type: integer
            format: int32
            minimum: -2147483648
            maximum: 2147483647
          in: path
          required: true
          style: simple
        -
          name: _output_format
          schema:
            type: string
            format: ''
          in: query
          required: false
          style: form
          explode: true
          description: '形式 (json|xml|csv|zip)'
        -
          name: _lang
          schema:
            type: string
            format: ''
          in: query
          required: false
          style: form
          explode: true
          description: 言語
        -
          name: _charset
          schema:
            type: string
            format: ''
          in: query
          required: false
          style: form
          explode: true
          description: 文字コード
        -
          name: filter
          schema:
            type: string
            format: ''
          in: query
          required: false
          style: form
          explode: true
          description: フィルタークエリ
      responses:
        200:
          description: 'Fetched an inquiry form''s details'
        404:
          description: 'An inquiry form for the provided ID could not be found'
      security:
        -
          Token-Auth: []
      operationId: getRcmsApi3GetInquiryInquiryId
components:
  schemas: {  }
  securitySchemes:
    Token-Auth:
      type: apiKey
      in: header
      name: X-RCMS-API-ACCESS-TOKEN
