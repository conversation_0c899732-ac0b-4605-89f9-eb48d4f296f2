<?php
declare(strict_types=1);

namespace App\Model\Table;

use Cake\ORM\Query;
use Cake\ORM\RulesChecker;
use Cake\ORM\Table;
use Cake\Validation\Validator;

class GeneralUsersTable extends Table
{
    public function initialize(array $config): void
    {
        parent::initialize($config);

        $this->setTable('general_users');
        $this->setDisplayField('email');
        $this->setPrimaryKey('id');

        $this->hasOne('UserProfiles', [
            'foreignKey' => 'general_user_id',
            'dependent' => true,
        ]);
        
        $this->hasOne('UserSurveys', [
            'foreignKey' => 'general_user_id',
            'dependent' => true,
        ]);
        
        $this->hasMany('UserTokens', [
            'foreignKey' => 'general_user_id',
            'dependent' => true,
        ]);
        
        $this->hasMany('RandselOrders', [
            'foreignKey' => 'general_user_id',
        ]);
        
        // TrashBehaviorを使用
        $this->addBehavior('Muffin/Trash.Trash', [
            'field' => 'deleted',
            'events' => ['Model.beforeFind', 'Model.beforeDelete']
        ]);
    }

    public function validationDefault(Validator $validator): Validator
    {
        $validator
            ->email('email', false, __('有効なメールアドレスを入力してください'))
            ->requirePresence('email', 'create', __('メールアドレスは必須です'))
            ->notEmptyString('email', __('メールアドレスは必須です'))
            ->add('email', 'unique', [
                'rule' => 'validateUnique', 
                'provider' => 'table',
                'message' => __('このメールアドレスは既に使用されています')
            ]);
            
        $validator
            ->scalar('password')
            ->maxLength('password', 255, __('パスワードは255文字以内で入力してください'))
            ->allowEmptyString('password');
            
        return $validator;
    }

    public function buildRules(RulesChecker $rules): RulesChecker
    {
        $rules->add($rules->isUnique(['email'], __('このメールアドレスは既に使用されています')));
        return $rules;
    }
    
    // アクティブユーザーのみを取得するファインダー
    public function findActive(Query $query, array $options)
    {
        return $query->where(['deleted IS' => null]);
    }
    
    // Kurocoユーザーのみを取得するファインダー
    public function findKurocoUsers(Query $query, array $options)
    {
        return $query->where(['password IS' => null, 'deleted IS' => null]);
    }
}
