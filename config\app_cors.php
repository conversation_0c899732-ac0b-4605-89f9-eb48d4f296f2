<?php

/**
 * AJAX用設定
 * src/Application.php
 * src/Middleware/CorsMiddleware.php
 */

use App\Config\DotEnvConfig;
use Cake\Utility\Hash;

$cors = [];
$cors = Hash::insert($cors, 'use', DotEnvConfig::readBoolean('CORS_USE', false));
$cors = Hash::insert($cors, 'withHeaders.Access-Control-Allow-Origin', "*");
$cors = Hash::insert($cors, 'withHeaders.Access-Control-Allow-Methods', 'OPTIONS,POST,GET,PUT,DELETE');
$cors = Hash::insert($cors, 'withHeaders.Access-Control-Allow-Headers', 'Origin, Content-Type, Authorization, Authorization-coverme, CM-TOKEN');

return [
    'Cors' => $cors
];
