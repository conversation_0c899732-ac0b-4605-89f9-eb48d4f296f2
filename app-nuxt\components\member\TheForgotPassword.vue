<script setup lang="ts">
import ForgotPasswordForm from "~~/src/form/ForgotPasswordForm";
import { reactive, ref } from "vue";
import { useRuntimeConfig } from "#app";
import PasswordResets from "~/src/models/UserPasswordReminders";

const forgotPasswordForm = reactive(new ForgotPasswordForm());

const stepRequest = "request";
const stepComplete = "complete";

const isLoading = ref(false);
const step = ref(stepRequest);
const errorMessage = ref("");
const config = useRuntimeConfig();

const toComplete = (): void => {
    if (!forgotPasswordForm.valid) {
        return;
    }
    isLoading.value = true;
    PasswordResets.create(config)
        .add(forgotPasswordForm.data)
        .then((response) => {
            if (response) {
                step.value = stepComplete;
            } else {
                errorMessage.value = "メールアドレスが見つかりません";
            }
            isLoading.value = false;
        })
        .catch(() => {
            errorMessage.value = "エラーが発生しました。もう一度お試しください";
            isLoading.value = false;
        });
};
</script>

<template>
    <div>
        <div class="text-h5 font-weight-bold pa-4">パスワード再設定</div>
        <template v-if="isLoading">
            <v-progress-circular indeterminate></v-progress-circular>
        </template>
        <template v-else>
            <template v-if="step === stepRequest">
                <v-alert v-if="errorMessage" type="error" class="mt-3">
                    {{ errorMessage }}
                </v-alert>
                <v-form v-model="forgotPasswordForm.valid">
                    <v-container>
                        <p class="text-body-1 mb-5">
                            パスワードを確認・変更するには、本人確認が必要です。<br />カバーミーに登録したメールアドレスを入力して、本人確認メールを送信してください。
                        </p>
                        <v-row>
                            <v-text-field
                                v-model="forgotPasswordForm.email"
                                density="comfortable"
                                class="mt-5"
                                :rules="forgotPasswordForm.email_rules"
                                label="メールアドレス"
                                type="email"
                                required
                            ></v-text-field>
                        </v-row>
                        <v-row justify="center">
                            <v-col cols="12" md="6">
                                <v-btn
                                    block
                                    color="primary"
                                    :disabled="!forgotPasswordForm.valid"
                                    rounded="xl"
                                    size="large"
                                    @click="toComplete"
                                >
                                    このアドレスに送信
                                </v-btn>
                            </v-col>
                        </v-row>
                        <p class="text-body-1 my-5 font-weight-bold">
                            注意事項
                        </p>
                        <!-- <p class="text-body-1 mb-5 font-weight-bold">
                            Gmailをご利用の方
                        </p>
                        <p class="text-body-1 mb-5">
                            メールが届かない場合は迷惑メールボックスをご確認ください。<br />
                            詳しくは<a href="">こちら</a>
                        </p>
                        <p class="text-body-1 mb-5 font-weight-bold">
                            そのほかのメールをご利用の方
                        </p> -->
                        <p class="text-body-1 mb-5">
                            メールが届かない場合は迷惑メールボックスをご確認ください。<br />@coverme.jpから送信したメールが迷惑メールと判断されてメールが届かないことがあります。受信できるように設定してください。<br />
                            それでも解決しない場合は<a
                                href="https://coverme.jp/contact"
                                target="_blank"
                                rel="noopener noreferrer"
                                >こちら</a
                            >をご確認ください。
                        </p>
                    </v-container>
                </v-form>
            </template>
            <template v-else-if="step === stepComplete">
                <v-container>
                    パスワードリセットの手順をメールでお送りしました。
                </v-container>
            </template>
        </template>
    </div>
</template>

<style scoped lang="scss">
.test {
    color: $error-color;
}
</style>
