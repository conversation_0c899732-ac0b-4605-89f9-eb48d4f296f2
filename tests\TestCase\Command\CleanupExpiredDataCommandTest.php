<?php
declare(strict_types=1);

namespace App\Test\TestCase\Command;

use App\Command\CleanupExpiredDataCommand;
use Cake\Console\ConsoleIo;
use Cake\Console\Arguments;
use Cake\Console\TestSuite\ConsoleIntegrationTestTrait;
use Cake\Console\ConsoleOptionParser;
use Cake\I18n\FrozenTime;
use Cake\ORM\TableRegistry;
use App\Test\TestCase\AppTestCase;

/**
 * App\Command\CleanupExpiredDataCommand Test Case
 *
 * @uses \App\Command\CleanupExpiredDataCommand
 */
class CleanupExpiredDataCommandTest extends AppTestCase
{
    use ConsoleIntegrationTestTrait;

    /**
     * テストで使用するフィクスチャ
     *
     * @var array
     */
    protected $fixtures = [
        'app.TemporaryRegistrations',
        'app.UserTokens',
        'app.GeneralUsers',
        'app.SwbUsers',
        'app.MakerUsers',
        'app.SwbUserTokens',
        'app.MakerUserTokens',
    ];

    /**
     * setUp method
     *
     * @return void
     */
    protected function setUp(): void
    {
        parent::setUp();
        $this->useCommandRunner();
    }

    /**
     * Test buildOptionParser method
     *
     * @return void
     * @uses \App\Command\CleanupExpiredDataCommand::buildOptionParser()
     */
    public function testBuildOptionParser(): void
    {
        $command = new CleanupExpiredDataCommand();
        $parser = $command->buildOptionParser(new ConsoleOptionParser('cleanup_expired_data'));
        
        $this->assertInstanceOf(ConsoleOptionParser::class, $parser);
        $this->assertSame('期限切れの仮登録データとトークンをクリーンアップします', $parser->getDescription());
        
        // dry-runオプションのテスト
        $this->assertArrayHasKey('dry-run', $parser->options());
        $this->assertTrue($parser->options()['dry-run']->isBoolean());
        $this->assertFalse($parser->options()['dry-run']->defaultValue());
        
        // verboseオプションのテスト
        $this->assertArrayHasKey('verbose', $parser->options());
        $this->assertTrue($parser->options()['verbose']->isBoolean());
        $this->assertFalse($parser->options()['verbose']->defaultValue());
    }

    /**
     * Test description output
     *
     * @return void
     */
    public function testDescriptionOutput(): void
    {
        $this->exec('cleanup_expired_data --help');
        $this->assertOutputContains('期限切れの仮登録データとトークンをクリーンアップします');
        $this->assertOutputContains('--dry-run');
        $this->assertOutputContains('--verbose');
    }

    /**
     * Test execute method with no options
     *
     * @return void
     */
    public function testExecuteWithNoOptions(): void
    {
        // 期限切れデータを作成
        $this->_createExpiredData();
        
        $this->exec('cleanup_expired_data');
        
        $this->assertExitCode(0);
        $this->assertOutputContains('期限切れデータクリーンアップを開始します');
        $this->assertOutputContains('クリーンアップが完了しました');
        
        // クリーンアップ後のデータを確認
        $this->_verifyCleanedData();
    }

    /**
     * Test execute method with dry-run option
     *
     * @return void
     */
    public function testExecuteWithDryRun1(): void
    {
        // 期限切れデータを作成
        $this->_createExpiredData();
        
        $this->exec('cleanup_expired_data --dry-run');
        
        $this->assertExitCode(0);
        // $this->assertOutputContains('ドライランモードで実行します（実際の削除は行いません）');
        $this->assertOutputContains('件が削除対象です');
        
        // ドライランなのでデータは削除されていないことを確認
        $this->_verifyDataNotCleaned();
    }

    /**
     * Test execute method with verbose option
     *
     * @return void
     */
    public function testExecuteWithVerbose(): void
    {
        // 期限切れデータを作成
        $this->_createExpiredData();
        
        $this->exec('cleanup_expired_data --verbose');
        
        $this->assertExitCode(0);
        $this->assertOutputContains('仮登録データをクリーンアップしています');
        $this->assertOutputContains('ユーザートークンをクリーンアップしています');
        $this->assertOutputContains('SWB管理者トークンをクリーンアップしています');
        $this->assertOutputContains('メーカーユーザートークンをクリーンアップしています');
        $this->assertOutputContains('件を削除しました');
        
        // クリーンアップ後のデータを確認
        $this->_verifyCleanedData();
    }

    /**
     * Test execute method with both dry-run and verbose options
     *
     * @return void
     */
    public function testExecuteWithDryRunAndVerbose(): void
    {
        // 期限切れデータを作成
        $this->_createExpiredData();
        
        $this->exec('cleanup_expired_data --dry-run --verbose');
        
        $this->assertExitCode(0);
        // $this->assertOutputContains('ドライランモードで実行します（実際の削除は行いません）');
        $this->assertOutputContains('仮登録データをクリーンアップしています');
        $this->assertOutputContains('ユーザートークンをクリーンアップしています');
        $this->assertOutputContains('SWB管理者トークンをクリーンアップしています');
        $this->assertOutputContains('メーカーユーザートークンをクリーンアップしています');
        $this->assertOutputContains('件が削除対象です');
        
        // ドライランなのでデータは削除されていないことを確認
        $this->_verifyDataNotCleaned();
    }

    /**
     * Test cleanupTemporaryRegistrations method
     *
     * @return void
     */
    public function testCleanupTemporaryRegistrations(): void
    {
        // 期限切れの仮登録データを作成
        $tempRegistrationsTable = TableRegistry::getTableLocator()->get('TemporaryRegistrations');
        $tempRegistration = $tempRegistrationsTable->newEntity([
            'email' => '<EMAIL>',
            'mask_email' => 'exp***@example.com',
            'mask_password' => 'password123',
            'verification_token' => 'expired_token_' . uniqid(),
            'is_verified' => false,
            'expires' => FrozenTime::now()->subDays(1),
            'profile_data' => json_encode(['name' => 'Expired User']),
            'survey_data' => json_encode(['answer1' => 'Yes']),
            'product_ids' => json_encode([1, 2, 3])
        ]);
        $tempRegistrationsTable->save($tempRegistration);
        
        // コマンドオブジェクトを作成
        $command = new CleanupExpiredDataCommand();
        
        // 引数とオプションを設定
        $args = new Arguments([], ['dry-run' => false, 'verbose' => false], []);
        $io = $this->createMock(ConsoleIo::class);
        
        // メソッドが呼び出されることを確認
        $io->expects($this->atLeastOnce())->method('out');
        
        // コマンドを実行
        $result = $command->execute($args, $io);
        $this->assertEquals(0, $result);
        
        // クリーンアップ後のデータを確認
        $cleanedEntity = $tempRegistrationsTable->get($tempRegistration->id);
        $this->assertNull($cleanedEntity->email);
        $this->assertNull($cleanedEntity->mask_password);
        $this->assertNull($cleanedEntity->profile_data);
        $this->assertNull($cleanedEntity->survey_data);
    }

    /**
     * 期限切れデータを作成するヘルパーメソッド
     */
    private function _createExpiredData(): void
    {
        // 期限切れの仮登録データを作成
        $tempRegistrationsTable = TableRegistry::getTableLocator()->get('TemporaryRegistrations');
        $tempRegistration = $tempRegistrationsTable->newEntity([
            'email' => '<EMAIL>',
            'mask_email' => 'exp***@example.com',
            'mask_password' => 'password123',
            'verification_token' => 'expired_token_' . uniqid(),
            'is_verified' => false,
            'expires' => FrozenTime::now()->subDays(1),
            'profile_data' => json_encode(['name' => 'Expired User']),
            'survey_data' => json_encode(['answer1' => 'Yes']),
            'product_ids' => json_encode([1, 2, 3])
        ]);
        $tempRegistrationsTable->save($tempRegistration);
        
        // 期限切れのユーザートークンを作成
        $userTokensTable = TableRegistry::getTableLocator()->get('UserTokens');
        $userToken = $userTokensTable->newEntity([
            'user_id' => 1,
            'token' => 'expired_user_token_' . uniqid(),
            'expires' => FrozenTime::now()->subDays(1),
            'type' => 'password_reset',
        ]);
        $userTokensTable->save($userToken);
        
        // 期限切れのSWB管理者トークンを作成
        $swbUserTokensTable = TableRegistry::getTableLocator()->get('SwbUserTokens');
        $swbUserToken = $swbUserTokensTable->newEntity([
            'user_id' => 1,
            'token' => 'expired_swb_token_' . uniqid(),
            'expires' => FrozenTime::now()->subDays(1),
            'type' => 'password_reset',
        ]);
        $swbUserTokensTable->save($swbUserToken);
        
        // 期限切れのメーカーユーザートークンを作成
        $makerUserTokensTable = TableRegistry::getTableLocator()->get('MakerUserTokens');
        $makerUserToken = $makerUserTokensTable->newEntity([
            'user_id' => 1,
            'token' => 'expired_maker_token_' . uniqid(),
            'expires' => FrozenTime::now()->subDays(1),
            'type' => 'password_reset',
        ]);
        $makerUserTokensTable->save($makerUserToken);
    }

    /**
     * クリーンアップ後のデータを確認するヘルパーメソッド
     */
    private function _verifyCleanedData(): void
    {
        // 仮登録データがクリーンアップされていることを確認
        $tempRegistrationsTable = TableRegistry::getTableLocator()->get('TemporaryRegistrations');
        $expiredRegistrations = $tempRegistrationsTable->find()
            ->where(['expires <' => FrozenTime::now()])
            ->all();
        
        foreach ($expiredRegistrations as $registration) {
            $this->assertNull($registration->email);
            $this->assertNull($registration->mask_password);
            $this->assertNull($registration->profile_data);
            $this->assertNull($registration->survey_data);
        }
        
        // ユーザートークンがクリーンアップされていることを確認
        $userTokensTable = TableRegistry::getTableLocator()->get('UserTokens');
        $expiredTokens = $userTokensTable->find()
            ->where(['expires <' => FrozenTime::now()])
            ->count();
        $this->assertEquals(0, $expiredTokens);
        
        // SWB管理者トークンがクリーンアップされていることを確認
        $swbUserTokensTable = TableRegistry::getTableLocator()->get('SwbUserTokens');
        $expiredSwbTokens = $swbUserTokensTable->find()
            ->where(['expires <' => FrozenTime::now()])
            ->count();
        $this->assertEquals(0, $expiredSwbTokens);
        
        // メーカーユーザートークンがクリーンアップされていることを確認
        $makerUserTokensTable = TableRegistry::getTableLocator()->get('MakerUserTokens');
        $expiredMakerTokens = $makerUserTokensTable->find()
            ->where(['expires <' => FrozenTime::now()])
            ->count();
        $this->assertEquals(0, $expiredMakerTokens);
    }

    /**
     * ドライラン時にデータが削除されていないことを確認するヘルパーメソッド
     */
    private function _verifyDataNotCleaned(): void
    {
        // 仮登録データが削除されていないことを確認
        $tempRegistrationsTable = TableRegistry::getTableLocator()->get('TemporaryRegistrations');
        $expiredRegistration = $tempRegistrationsTable->find()
            ->where(['expires <' => FrozenTime::now(), 'email IS NOT' => null])
            ->first();
        $this->assertNotNull($expiredRegistration);
        $this->assertNotNull($expiredRegistration->email);
        $this->assertNotNull($expiredRegistration->mask_password);
        
        // ユーザートークンが削除されていないことを確認
        $userTokensTable = TableRegistry::getTableLocator()->get('UserTokens');
        $expiredTokens = $userTokensTable->find()
            ->where(['expires <' => FrozenTime::now()])
            ->count();
        $this->assertGreaterThan(0, $expiredTokens);
        
        // SWB管理者トークンが削除されていないことを確認
        $swbUserTokensTable = TableRegistry::getTableLocator()->get('SwbUserTokens');
        $expiredSwbTokens = $swbUserTokensTable->find()
            ->where(['expires <' => FrozenTime::now()])
            ->count();
        $this->assertGreaterThan(0, $expiredSwbTokens);
        
        // メーカーユーザートークンが削除されていないことを確認
        $makerUserTokensTable = TableRegistry::getTableLocator()->get('MakerUserTokens');
        $expiredMakerTokens = $makerUserTokensTable->find()
            ->where(['expires <' => FrozenTime::now()])
            ->count();
        $this->assertGreaterThan(0, $expiredMakerTokens);
    }
}
