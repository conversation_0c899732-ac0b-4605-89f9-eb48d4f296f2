<?php

namespace App\Service;

use Cake\Log\Log;
use Cake\I18n\FrozenTime;

/**
 * 商品表示制御のパフォーマンス測定サービス
 * 
 * リアルタイム計算のパフォーマンスを監視・分析する
 */
class ProductPerformanceService implements IService
{
    use ServiceTrait;

    protected array $_defaultConfig = [];

    /**
     * パフォーマンス統計
     */
    private static array $performanceStats = [
        'total_requests' => 0,
        'total_time_ms' => 0,
        'max_time_ms' => 0,
        'min_time_ms' => PHP_FLOAT_MAX,
        'db_queries' => 0,
        'start_time' => null
    ];

    public function initialize(): void
    {
        if (self::$performanceStats['start_time'] === null) {
            self::$performanceStats['start_time'] = microtime(true);
        }
    }

    /**
     * 商品表示タイプ計算のパフォーマンスを測定
     * 
     * @param int $productId 商品ID
     * @param callable $callback 実行する処理
     * @return mixed 処理結果
     */
    public function measureDisplayTypeCalculation(int $productId, callable $callback)
    {
        $startTime = microtime(true);
        $startQueries = $this->getQueryCount();
        
        // 処理実行
        $result = $callback();
        
        $endTime = microtime(true);
        $endQueries = $this->getQueryCount();
        
        $processingTime = ($endTime - $startTime) * 1000; // ミリ秒
        $queryCount = $endQueries - $startQueries;
        
        // 統計更新
        $this->updateStats($processingTime, $queryCount);
        
        // 詳細ログ出力
        Log::info('ProductPerformanceService: 商品表示タイプ計算', [
            'product_id' => $productId,
            'processing_time_ms' => round($processingTime, 2),
            'db_queries' => $queryCount,
            'result' => $result,
            'timestamp' => FrozenTime::now()->format('c')
        ]);
        
        // 閾値チェック（100ms以上の場合は警告）
        if ($processingTime > 100) {
            Log::warning('ProductPerformanceService: 処理時間が閾値を超過', [
                'product_id' => $productId,
                'processing_time_ms' => round($processingTime, 2),
                'threshold_ms' => 100
            ]);
        }
        
        return $result;
    }

    /**
     * 商品一覧取得のパフォーマンスを測定
     * 
     * @param callable $callback 実行する処理
     * @return mixed 処理結果
     */
    public function measureProductListGeneration(callable $callback)
    {
        $startTime = microtime(true);
        $startQueries = $this->getQueryCount();
        
        // 処理実行
        $result = $callback();
        
        $endTime = microtime(true);
        $endQueries = $this->getQueryCount();
        
        $processingTime = ($endTime - $startTime) * 1000; // ミリ秒
        $queryCount = $endQueries - $startQueries;
        $productCount = is_array($result) ? count($result) : 0;
        
        // 詳細ログ出力
        Log::info('ProductPerformanceService: 商品一覧生成', [
            'product_count' => $productCount,
            'processing_time_ms' => round($processingTime, 2),
            'db_queries' => $queryCount,
            'avg_time_per_product_ms' => $productCount > 0 ? round($processingTime / $productCount, 2) : 0,
            'timestamp' => FrozenTime::now()->format('c')
        ]);
        
        // 閾値チェック（1秒以上の場合は警告）
        if ($processingTime > 1000) {
            Log::warning('ProductPerformanceService: 商品一覧生成時間が閾値を超過', [
                'product_count' => $productCount,
                'processing_time_ms' => round($processingTime, 2),
                'threshold_ms' => 1000
            ]);
        }
        
        return $result;
    }

    /**
     * パフォーマンス統計を更新
     * 
     * @param float $processingTime 処理時間（ミリ秒）
     * @param int $queryCount クエリ数
     * @return void
     */
    private function updateStats(float $processingTime, int $queryCount): void
    {
        self::$performanceStats['total_requests']++;
        self::$performanceStats['total_time_ms'] += $processingTime;
        self::$performanceStats['db_queries'] += $queryCount;
        
        if ($processingTime > self::$performanceStats['max_time_ms']) {
            self::$performanceStats['max_time_ms'] = $processingTime;
        }
        
        if ($processingTime < self::$performanceStats['min_time_ms']) {
            self::$performanceStats['min_time_ms'] = $processingTime;
        }
    }

    /**
     * パフォーマンス統計を取得
     * 
     * @return array 統計情報
     */
    public function getPerformanceStats(): array
    {
        $stats = self::$performanceStats;
        
        if ($stats['total_requests'] > 0) {
            $stats['avg_time_ms'] = round($stats['total_time_ms'] / $stats['total_requests'], 2);
            $stats['avg_queries_per_request'] = round($stats['db_queries'] / $stats['total_requests'], 2);
        } else {
            $stats['avg_time_ms'] = 0;
            $stats['avg_queries_per_request'] = 0;
        }
        
        // 実行時間
        if ($stats['start_time'] !== null) {
            $stats['uptime_seconds'] = round(microtime(true) - $stats['start_time'], 2);
        }
        
        // 最小値の初期化チェック
        if ($stats['min_time_ms'] === PHP_FLOAT_MAX) {
            $stats['min_time_ms'] = 0;
        }
        
        return $stats;
    }

    /**
     * パフォーマンス統計をリセット
     * 
     * @return void
     */
    public function resetStats(): void
    {
        self::$performanceStats = [
            'total_requests' => 0,
            'total_time_ms' => 0,
            'max_time_ms' => 0,
            'min_time_ms' => PHP_FLOAT_MAX,
            'db_queries' => 0,
            'start_time' => microtime(true)
        ];
        
        Log::info('ProductPerformanceService: パフォーマンス統計をリセット');
    }

    /**
     * データベースクエリ数を取得
     * 
     * @return int クエリ数
     */
    private function getQueryCount(): int
    {
        // CakePHPのクエリログから取得
        // 実装は環境に応じて調整
        return 0; // 簡易実装
    }

    /**
     * パフォーマンスレポートを生成
     * 
     * @return array レポートデータ
     */
    public function generatePerformanceReport(): array
    {
        $stats = $this->getPerformanceStats();
        
        $report = [
            'summary' => [
                'total_requests' => $stats['total_requests'],
                'avg_response_time_ms' => $stats['avg_time_ms'],
                'max_response_time_ms' => round($stats['max_time_ms'], 2),
                'min_response_time_ms' => round($stats['min_time_ms'], 2),
                'avg_db_queries' => $stats['avg_queries_per_request']
            ],
            'performance_grade' => $this->calculatePerformanceGrade($stats),
            'recommendations' => $this->generateRecommendations($stats),
            'timestamp' => FrozenTime::now()->format('c')
        ];
        
        Log::info('ProductPerformanceService: パフォーマンスレポート生成', $report);
        
        return $report;
    }

    /**
     * パフォーマンスグレードを計算
     * 
     * @param array $stats 統計情報
     * @return string グレード（A-F）
     */
    private function calculatePerformanceGrade(array $stats): string
    {
        $avgTime = $stats['avg_time_ms'];
        
        if ($avgTime <= 10) return 'A'; // 優秀
        if ($avgTime <= 25) return 'B'; // 良好
        if ($avgTime <= 50) return 'C'; // 普通
        if ($avgTime <= 100) return 'D'; // 要改善
        if ($avgTime <= 200) return 'E'; // 改善必要
        return 'F'; // 緊急改善必要
    }

    /**
     * 改善提案を生成
     * 
     * @param array $stats 統計情報
     * @return array 提案リスト
     */
    private function generateRecommendations(array $stats): array
    {
        $recommendations = [];
        
        if ($stats['avg_time_ms'] > 50) {
            $recommendations[] = 'データベースインデックスの最適化を検討してください';
        }
        
        if ($stats['avg_queries_per_request'] > 5) {
            $recommendations[] = 'クエリ数の削減を検討してください（N+1問題の可能性）';
        }
        
        if ($stats['max_time_ms'] > 500) {
            $recommendations[] = '最大処理時間が長すぎます。特定商品の処理を調査してください';
        }
        
        if (empty($recommendations)) {
            $recommendations[] = 'パフォーマンスは良好です';
        }
        
        return $recommendations;
    }
}
