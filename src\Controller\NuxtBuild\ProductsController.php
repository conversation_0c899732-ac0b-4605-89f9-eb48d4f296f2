<?php
declare(strict_types=1);

namespace App\Controller\NuxtBuild;

use App\Controller\NuxtBuildController;
use App\Form\DisableParametersForm;
use App\Service\ProductsService;
use Cake\Event\Event;
use Cake\Log\Log;

/**
 * Products Controller
 */
class ProductsController extends NuxtBuildController
{
    /**
     * Index method
     */
    public function index(): void
    {
        $this->_onSuccess(function (Event $event, array $products) {
            $this->_setSuccessResponse([
                'products' => $products
            ]);
        });
        $this->_onError(function (Event $event, array $errors) {
            Log::debug(print_r($errors, true));
        });

        $this->_index((new ProductsService())->setForm(
            new DisableParametersForm()
        ));
    }

}
