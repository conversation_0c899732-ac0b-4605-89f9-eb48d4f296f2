<script setup lang="ts">
import { SwbMyBridge } from "~~/src/models/bridge/SwbMyBridge";
import { RANDSEL_APPROVAL_STATUS_LIST, getLabelsByValues } from "~~/src/list";
import moment from "moment";

const props = defineProps<{
    bridge: SwbMyBridge;
}>();

const searchDateTypeList: {
    [key: string]: string;
} = {
    "order-date": "注文",
    "status-updated-date": "承認",
};
const searchStatusType = props.bridge.swb_order_search_form.searchStatusType;
const orders = props.bridge.randsel_orders;
const searchDateTypeString =
    searchDateTypeList[props.bridge.swb_order_search_form.searchDateType];

//TODO:集計などの処理はクラスに切り出してそっちでやった方がいい
const approvalStatus = 1;

const startDate = props.bridge.swb_order_search_form.startDate;
const endDate = `${props.bridge.swb_order_search_form.endDate} 23:59:59`;

const totalOrderCount = props.bridge.randsel_orders.length;

//請求額計算
const totalPrice = orders
    .filter(
        (order) =>
            moment(
                props.bridge.swb_order_search_form.searchDateType ===
                    "order-date"
                    ? order.created
                    : order.status_modified,
            ).isBetween(startDate, endDate, null, "[]") &&
            order.status === approvalStatus,
    )
    .reduce((sum, order) => sum + order.price, 0);
</script>

<template>
    <v-container fluid class="report-summary-wrapper">
        <v-row class="pt-5 pl-3 pb-0">サマリー</v-row>
        <v-row justify="end" class="summary-text">
            <v-col cols="4" class="pt-1 text-end">
                {{ searchDateTypeString }}期間：{{
                    moment(startDate).format("YYYY-MM-DD")
                }}〜{{ moment(endDate).format("YYYY-MM-DD") }}
            </v-col>
            <v-col cols="3" class="pt-1 text-end">
                {{
                    getLabelsByValues(
                        searchStatusType,
                        RANDSEL_APPROVAL_STATUS_LIST,
                    ).join("、")
                }}件数：{{ totalOrderCount.toLocaleString() }}件
            </v-col>
            <v-col cols="3" class="pt-1 text-end">
                請求予定額：{{ totalPrice.toLocaleString() }}円
            </v-col>
        </v-row>
    </v-container>
</template>

<style scoped>
.report-summary-wrapper {
    position: inherit;
    margin-top: 40px;
    padding: 0 20px;
    height: 80px;
    border: #e4e4e4 solid 2px;
    color: #575757;
}
.summary-text {
    font-size: 15px;
}
</style>
