<?php
declare(strict_types=1);

namespace App\Model\Table;

use Cake\ORM\Query;
use Cake\ORM\RulesChecker;
use Cake\ORM\Table;
use Cake\Validation\Validator;

/**
 * RandselInvoiceAdjustmentHistories Model
 *
 * @property \App\Model\Table\RandselInvoiceAdjustmentsTable&\Cake\ORM\Association\BelongsTo $RandselInvoiceAdjustments
 *
 * @method \App\Model\Entity\RandselInvoiceAdjustmentHistory newEmptyEntity()
 * @method \App\Model\Entity\RandselInvoiceAdjustmentHistory newEntity(array $data, array $options = [])
 * @method \App\Model\Entity\RandselInvoiceAdjustmentHistory[] newEntities(array $data, array $options = [])
 * @method \App\Model\Entity\RandselInvoiceAdjustmentHistory get($primaryKey, $options = [])
 * @method \App\Model\Entity\RandselInvoiceAdjustmentHistory findOrCreate($search, ?callable $callback = null, $options = [])
 * @method \App\Model\Entity\RandselInvoiceAdjustmentHistory patchEntity(\Cake\Datasource\EntityInterface $entity, array $data, array $options = [])
 * @method \App\Model\Entity\RandselInvoiceAdjustmentHistory[] patchEntities(iterable $entities, array $data, array $options = [])
 * @method \App\Model\Entity\RandselInvoiceAdjustmentHistory|false save(\Cake\Datasource\EntityInterface $entity, $options = [])
 * @method \App\Model\Entity\RandselInvoiceAdjustmentHistory saveOrFail(\Cake\Datasource\EntityInterface $entity, $options = [])
 * @method \App\Model\Entity\RandselInvoiceAdjustmentHistory[]|\Cake\Datasource\ResultSetInterface|false saveMany(iterable $entities, $options = [])
 * @method \App\Model\Entity\RandselInvoiceAdjustmentHistory[]|\Cake\Datasource\ResultSetInterface saveManyOrFail(iterable $entities, $options = [])
 * @method \App\Model\Entity\RandselInvoiceAdjustmentHistory[]|\Cake\Datasource\ResultSetInterface|false deleteMany(iterable $entities, $options = [])
 * @method \App\Model\Entity\RandselInvoiceAdjustmentHistory[]|\Cake\Datasource\ResultSetInterface deleteManyOrFail(iterable $entities, $options = [])
 *
 * @mixin \Cake\ORM\Behavior\TimestampBehavior
 */
class RandselInvoiceAdjustmentHistoriesTable extends Table
{
    /**
     * Initialize method
     *
     * @param array $config The configuration for the Table.
     * @return void
     */
    public function initialize(array $config): void
    {
        parent::initialize($config);

        $this->setTable('randsel_invoice_adjustment_histories');
        $this->setDisplayField('id');
        $this->setPrimaryKey('id');

        $this->addBehavior('Timestamp');

        $this->belongsTo('RandselInvoiceAdjustments', [
            'foreignKey' => 'invoice_adjustment_id',
            'joinType' => 'INNER',
        ]);
    }

    /**
     * Default validation rules.
     *
     * @param \Cake\Validation\Validator $validator Validator instance.
     * @return \Cake\Validation\Validator
     */
    public function validationDefault(Validator $validator): Validator
    {
        $validator
            ->integer('invoice_adjustment_id')
            ->notEmptyString('invoice_adjustment_id');

        $validator
            ->requirePresence('action_type', 'create')
            ->notEmptyString('action_type');

        $validator
            ->allowEmptyString('changes');

        $validator
            ->integer('created_by')
            ->requirePresence('created_by', 'create')
            ->notEmptyString('created_by');

        return $validator;
    }

    /**
     * Returns a rules checker object that will be used for validating
     * application integrity.
     *
     * @param \Cake\ORM\RulesChecker $rules The rules object to be modified.
     * @return \Cake\ORM\RulesChecker
     */
    public function buildRules(RulesChecker $rules): RulesChecker
    {
        $rules->add($rules->existsIn('invoice_adjustment_id', 'RandselInvoiceAdjustments'), ['errorField' => 'invoice_adjustment_id']);

        return $rules;
    }
}
