<?php
declare(strict_types=1);

namespace App\Model\Table;

use Cake\ORM\Table;
use Cake\Validation\Validator;
use Cake\I18n\FrozenTime;
use Cake\Log\Log;
use Cake\Utility\Security;
use App\Model\Entity\UserToken;

/**
 * ユーザートークンテーブル
 * 
 * @property \App\Model\Table\GeneralUsersTable&\Cake\ORM\Association\BelongsTo $GeneralUsers
 * @method \App\Model\Entity\UserToken newEmptyEntity()
 * @method \App\Model\Entity\UserToken newEntity(array $data, array $options = [])
 * @method \App\Model\Entity\UserToken[] newEntities(array $data, array $options = [])
 * @method \App\Model\Entity\UserToken get($primaryKey, $options = [])
 * @method \App\Model\Entity\UserToken findOrCreate($search, ?callable $callback = null, $options = [])
 * @method \App\Model\Entity\UserToken patchEntity(\Cake\Datasource\EntityInterface $entity, array $data, array $options = [])
 * @method \App\Model\Entity\UserToken[] patchEntities(iterable $entities, array $data, array $options = [])
 * @method \App\Model\Entity\UserToken|false save(\Cake\Datasource\EntityInterface $entity, $options = [])
 * @method \App\Model\Entity\UserToken saveOrFail(\Cake\Datasource\EntityInterface $entity, $options = [])
 */
class UserTokensTable extends Table
{
    public function initialize(array $config): void
    {
        parent::initialize($config);

        $this->setTable('user_tokens');
        $this->setDisplayField('token');
        $this->setPrimaryKey('id');

        $this->belongsTo('GeneralUsers', [
            'foreignKey' => 'general_user_id',
            'joinType' => 'INNER',
        ]);
    }

    public function validationDefault(Validator $validator): Validator
    {
        $validator
            ->integer('id')
            ->allowEmptyString('id', null, 'create');

        $validator
            ->integer('general_user_id')
            ->requirePresence('general_user_id', 'create')
            ->notEmptyString('general_user_id');

        $validator
            ->scalar('token')
            ->maxLength('token', 255)
            ->requirePresence('token', 'create')
            ->notEmptyString('token')
            ->add('token', 'unique', ['rule' => 'validateUnique', 'provider' => 'table']);

        $validator
            ->scalar('type')
            ->maxLength('type', 50)
            ->requirePresence('type', 'create')
            ->notEmptyString('type')
            ->inList('type', [
                UserToken::TYPE_PASSWORD_RESET,
                UserToken::TYPE_API_ACCESS,
                UserToken::TYPE_EMAIL_VERIFICATION
            ]);

        $validator
            ->dateTime('expires')
            ->requirePresence('expires', 'create')
            ->notEmptyDateTime('expires');

        return $validator;
    }

    /**
     * トークンを生成
     */
    public function generateToken(): string
    {
        return bin2hex(Security::randomBytes(32));
    }

    /**
     * 有効なトークンを検索
     */
    public function findValidToken(string $token, ?string $type = null)
    {
        $query = $this->find()
            ->contain(['GeneralUsers', 'GeneralUsers.UserProfiles'])
            ->where([
                'UserTokens.token' => $token,
                'UserTokens.expires >' => FrozenTime::now()
            ]);

        if ($type) {
            $query->where(['UserTokens.type' => $type]);
        }

        return $query->first();
    }

    /**
     * 期限切れトークンを削除
     */
    public function cleanupExpiredTokens(): int
    {
        return $this->deleteAll([
            'expires <' => FrozenTime::now()
        ]);
    }

    /**
     * ユーザーの特定タイプのトークンを無効化
     * @param int $userId
     * @param string $type
     * @return int 削除件数
     */
    public function invalidateUserTokens(int $userId, string $type): int
    {
        return $this->deleteAll([
            'general_user_id' => $userId,
            'type' => $type
        ]);
    }

    /**
     * パスワードリセットトークンを作成
     * @param int $userId
     * @param int $expirationHours
     * @return \App\Model\Entity\UserToken|false
     */
    public function createPasswordResetToken(int $userId, int $expirationHours = 24): UserToken|false
    {
        // 既存のパスワードリセットトークンを無効化
        $this->invalidateUserTokens($userId, UserToken::TYPE_PASSWORD_RESET);

        $token = $this->newEntity([
            'general_user_id' => $userId,
            'token' => $this->generateToken(),
            'type' => UserToken::TYPE_PASSWORD_RESET,
            'expires' => FrozenTime::now()->addHours($expirationHours)
        ]);

        return $this->save($token);
    }

    /**
     * APIアクセストークンを作成
     * @param int $userId
     * @param int $expirationHours
     * @return \App\Model\Entity\UserToken|false
     */
    public function createApiAccessToken(int $userId, int $expirationHours = 24): UserToken|false
    {
        // 既存のAPIアクセストークンを無効化
        $this->invalidateUserTokens($userId, UserToken::TYPE_API_ACCESS);

        $token = $this->newEntity([
            'general_user_id' => $userId,
            'token' => $this->generateToken(),
            'type' => UserToken::TYPE_API_ACCESS,
            'expires' => FrozenTime::now()->addHours($expirationHours)
        ]);

        return $this->save($token);
    }

    /**
     * 特定のトークンを無効化（削除）
     */
    public function invalidateToken(string $token): bool
    {
        try {
            $tokenEntity = $this->find()
                ->where([
                    'token' => $token,
                ])
                ->first();

            if ($tokenEntity) {
                return $this->delete($tokenEntity);
            }

            return false;
        } catch (\Exception $e) {
            Log::error("UserTokensTable: Error invalidating token: " . $e->getMessage());
            return false;
        }
    }
}
